using AutoMapper;
using ITMCTR.Core.Models;
using ITMCTR.Core.Models2;
using PetaPoco;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;

namespace ConsoleApp
{
    class Program
    {
        public static int Elapsed { get; set; }
        public static string PathSource { get; set; }
        public static string TargetSource { get; set; }
        public static Guid TaskSysUserId { get; set; }
        public static Thread th = null;
        public static void Main()
        {
            Fun3();
            Console.ReadLine();
            return;
            Elapsed = int.Parse(System.Configuration.ConfigurationManager.AppSettings["Elapsed"].ToString());
            PathSource = System.Configuration.ConfigurationManager.AppSettings["PathSource"].ToString();
            TargetSource = System.Configuration.ConfigurationManager.AppSettings["PathTarget"].ToString();
            TaskSysUserId = Guid.Parse(System.Configuration.ConfigurationManager.AppSettings["TaskSysUserId"].ToString());

            th = new Thread(new ThreadStart(Method));
            th.IsBackground = true; //后台运行，主窗体关闭后，可退出程序
            th.Start();
            var cmd = Console.ReadLine();
            while (cmd != "end")
            {
                cmd = Console.ReadLine();
                Thread.Sleep(1000);
            }
            th.Abort();
        }
        static void Method()
        {
            while (true)
            {
                Console.WriteLine("working start");

                using (SourceDB dbs = new SourceDB())
                {
                    using (targetDB dbt = new targetDB())
                    {
                        try
                        {
                            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_NewProject").Where("executeTaskSysUserId=@0", TaskSysUserId);
                            var list = dbt.Query<ITMCTR.Core.Models.SA_NewProject>(sql).ToList();
                            var ids = list.Select(x => x.Pid).ToList();
                            if (ids.Count > 0)
                            {
                                var sql2 = PetaPoco.Sql.Builder.Select("*").From("SAAS_NewProject").Where("[Guid] in (@0)", ids);
                                var dicts = dbs.Query<ITMCTR.Core.Models2.SAAS_StudyDictionary>("").ToList();
                                var listTarget = dbs.Query<ITMCTR.Core.Models2.SAAS_NewProject>(sql2).ToList();
                                foreach (var item in listTarget)
                                {
                                    var find = list.Find(x => x.Pid == item.Guid);
                                    if (find != null)
                                    {
                                        switch (item.status)
                                        {
                                            //case "未填完":
                                            //case "补注册待审":
                                            case "未通过审核":
                                            case "待审核":
                                                if (find.taskStatus == 2)
                                                {
                                                    if (item.modifyTime > find.modifyTime || find.modifyTime == null)
                                                    {
                                                        Console.WriteLine($"update failed pid:{find.Pid} publicTitle:{find.publicTitleCN}");
                                                        RecvUpdateInfo(dicts, find, item);
                                                        find.taskStatus = 4;
                                                        find.status = 2;
                                                        find.Update();
                                                    }
                                                    else
                                                    {
                                                        Console.WriteLine($"send pid:{find.Pid} publicTitle:{find.publicTitleCN}");
                                                        SendUpdateInfo(dicts, item, find, dbs, dbt);
                                                    }
                                                }
                                                break;
                                            case "通过审核":
                                                if (find.taskStatus == 2)
                                                {
                                                    Console.WriteLine($"update pid:{find.Pid} publicTitle:{find.publicTitleCN}");
                                                    RecvUpdateInfo(dicts, find, item);
                                                    find.ReleaseNumber = item.regNumber;
                                                    find.taskStatus = 3;
                                                    find.Update();
                                                }
                                                break;
                                        }
                                        list.Remove(find);
                                    }
                                }
                                foreach (var model in list)
                                {
                                    if (model.taskStatus != 2)
                                        continue;
                                    var proInfo = new ITMCTR.Core.Models2.SAAS_NewProject();
                                    var list1 = dbt.Query<ITMCTR.Core.Models.SA_CollectingSample>("where pid=@0", model.Pid).ToList();
                                    var list2 = dbt.Query<ITMCTR.Core.Models.SA_Diagnostic>("where pid=@0", model.Pid).ToList();
                                    var list3 = dbt.Query<ITMCTR.Core.Models.SA_Interventions>("where pid=@0", model.Pid).ToList();
                                    var list4 = dbt.Query<ITMCTR.Core.Models.SA_Outcomes>("where pid=@0", model.Pid).ToList();
                                    var list5 = dbt.Query<ITMCTR.Core.Models.SA_ResearchAddress>("where pid=@0", model.Pid).ToList();
                                    var list6 = dbt.Query<ITMCTR.Core.Models.SA_SecondarySponsor>("where pid=@0", model.Pid).ToList();

                                    #region proj

                                    proInfo.Guid = model.Pid;
                                    proInfo.regNumber = model.regNumber;
                                    proInfo.regNumberChi = model.regNumberChi;
                                    proInfo.regNumberTime = model.regNumberTime;
                                    proInfo.filloutLanguage = model.filloutLanguage;
                                    proInfo.registrationStatus = model.registrationStatus;
                                    proInfo.publicTitleCN = model.publicTitleCN.Fix2();
                                    proInfo.publicTitleEN = model.publicTitleEN.Fix2();
                                    proInfo.titleAcronymCN = model.titleAcronymCN.Fix2();
                                    proInfo.titleAcronymEN = model.titleAcronymEN.Fix2();
                                    proInfo.scientifirTitleCN = model.scientifirTitleCN.Fix2();
                                    proInfo.scientifirTitleEN = model.scientifirTitleEN.Fix2();
                                    proInfo.scientifirAcronymCN = model.scientifirAcronymCN.Fix2();
                                    proInfo.scientifirAcronymEN = model.scientifirAcronymEN.Fix2();
                                    proInfo.studyID = model.studyID;
                                    proInfo.secondaryID = model.secondaryID;
                                    proInfo.applicantCN = model.applicantCN.Fix2();
                                    proInfo.applicantEN = model.applicantEN.Fix2();
                                    proInfo.studyLeaderCN = model.studyLeaderCN.Fix2();
                                    proInfo.studyLeaderEN = model.studyLeaderEN.Fix2();
                                    proInfo.applicantTelephone = model.applicantTelephone;
                                    proInfo.studyTelephone = model.studyTelephone;
                                    proInfo.applicanFax = model.applicanFax;
                                    proInfo.studyFax = model.studyFax;
                                    proInfo.applicantEmail = model.applicantEmail;
                                    proInfo.studyEmail = model.studyEmail;
                                    proInfo.applicantWebsite = model.applicantWebsite.Fix2();
                                    proInfo.studyWebsite = model.studyWebsite.Fix2();
                                    proInfo.applicantAddressCN = model.applicantAddressCN.Fix2();
                                    proInfo.applicantAddressEN = model.applicantAddressEN.Fix2();
                                    proInfo.studyAddressCN = model.studyAddressCN.Fix2();
                                    proInfo.studyAddressEN = model.studyAddressEN.Fix2();
                                    proInfo.applicantPostcode = model.applicantPostcode;
                                    proInfo.studyPostcode = model.studyPostcode;
                                    proInfo.applicantInstitutionCN = model.applicantInstitutionCN.Fix2();
                                    proInfo.applicantInstitutionEN = model.applicantInstitutionEN.Fix2();
                                    proInfo.studyLeaderCompanyCN = model.studyLeaderCompanyCN.Fix2();
                                    proInfo.studyLeaderCompanyEN = model.studyLeaderCompanyEN.Fix2();
                                    proInfo.approvedCommittee = model.approvedCommittee;
                                    proInfo.ethicalCommitteeFileID = model.ethicalCommitteeFileID.Fix2();
                                    proInfo.fileEthicalCommittee = model.fileEthicalCommittee;
                                    proInfo.ethicalCommitteeName = model.ethicalCommitteeName.Fix2();
                                    proInfo.ethicalCommitteeNameEN = model.ethicalCommitteeNameEN.Fix2();
                                    proInfo.ethicalCommitteeSanctionDate = model.ethicalCommitteeSanctionDate;
                                    proInfo.ethicalCommitteeCName = model.ethicalCommitteeCName.Fix2();
                                    proInfo.ethicalCommitteeCNameEN = model.ethicalCommitteeCNameEN.Fix2();
                                    proInfo.ethicalCommitteeCAddress = model.ethicalCommitteeCAddress.Fix2();
                                    proInfo.ethicalCommitteeCAddressEN = model.ethicalCommitteeCAddressEN.Fix2();
                                    proInfo.ethicalCommitteeCPhone = model.ethicalCommitteeCPhone;
                                    proInfo.ethicalCommitteeCEmail = model.ethicalCommitteeCEmail;
                                    proInfo.of_SFDA = model.of_SFDA;
                                    proInfo.fileSFDA = model.fileSFDA;
                                    proInfo.dataSFDA = model.dataSFDA;
                                    proInfo.studyPlanfile = model.studyPlanfile;
                                    proInfo.informedConsentfile = model.informedConsentfile;
                                    proInfo.primarySponsorCN = model.primarySponsorCN.Fix2();
                                    proInfo.primarySponsorEN = model.primarySponsorEN.Fix2();
                                    proInfo.primarySponsorAddressCN = model.primarySponsorAddressCN.Fix2();
                                    proInfo.primarySponsorAddressEN = model.primarySponsorAddressEN.Fix2();
                                    proInfo.sourceFundingCN = model.sourceFundingCN.Fix2();
                                    proInfo.sourceFundingEN = model.sourceFundingEN.Fix2();
                                    proInfo.targetDiseaseCN = model.targetDiseaseCN.Fix2();
                                    proInfo.targetDiseaseEN = model.targetDiseaseEN.Fix2();
                                    proInfo.targetCode = model.targetCode;
                                    proInfo.studyTypeID = dicts.FirstOrDefault(x => x.StudyValue == model.studyTypeID).Sid;
                                    proInfo.studyDesignID = dicts.FirstOrDefault(x => x.StudyValue == model.studyDesignID).Sid;
                                    proInfo.studyPhaseID = dicts.FirstOrDefault(x => x.StudyValue == model.studyPhaseID).Sid;
                                    proInfo.objectivesStudyCN = model.objectivesStudyCN.Fix2();
                                    proInfo.objectivesStudyEN = model.objectivesStudyEN.Fix2();
                                    proInfo.contentsDrugCN = model.contentsDrugCN.Fix2();
                                    proInfo.contentsDrugEN = model.contentsDrugEN.Fix2();
                                    proInfo.inclusionCriteriaCN = model.inclusionCriteriaCN.Fix2();
                                    proInfo.inclusionCriteriaEN = model.inclusionCriteriaEN.Fix2();
                                    proInfo.exclusionCrteriaCN = model.exclusionCrteriaCN.Fix2();
                                    proInfo.exclusionCrteriaEN = model.exclusionCrteriaEN.Fix2();
                                    proInfo.studyTimeStart = model.studyTimeStart;
                                    proInfo.studyTimeEnd = model.studyTimeEnd;
                                    proInfo.recruitingTimeStart = model.recruitingTimeStart;
                                    proInfo.recruitingTimeEnd = model.recruitingTimeEnd;
                                    proInfo.totalSampleSize = model.totalSampleSize;
                                    proInfo.recruitingStatus = model.recruitingStatus;
                                    proInfo.ageMin = model.ageMin;
                                    proInfo.ageMax = model.ageMax;
                                    proInfo.randomMethodCN = model.randomMethodCN.Fix2();
                                    proInfo.randomMethodEN = model.randomMethodEN.Fix2();
                                    proInfo.sex = model.sex;
                                    proInfo.signConsent = model.signConsent;
                                    proInfo.followupTime = model.followupTime;
                                    proInfo.followup = model.followup;
                                    proInfo.processConcealmentCN = model.processConcealmentCN.Fix2();
                                    proInfo.processConcealmentEN = model.processConcealmentEN.Fix2();
                                    proInfo.blindingCN = model.blindingCN.Fix2();
                                    proInfo.blindingEN = model.blindingEN.Fix2();
                                    proInfo.RulesblindingCN = model.RulesblindingCN.Fix2();
                                    proInfo.RulesblindingEN = model.RulesblindingEN.Fix2();
                                    proInfo.statisticalMethodCN = model.statisticalMethodCN.Fix2();
                                    proInfo.statisticalMethodEN = model.statisticalMethodEN.Fix2();
                                    proInfo.calculatedResultsCN = model.calculatedResultsCN.Fix2();
                                    proInfo.calculatedResultsEN = model.calculatedResultsEN.Fix2();
                                    proInfo.whetherPublic = model.whetherPublic;
                                    proInfo.dataCollectionCN = model.dataCollectionCN.Fix2();
                                    proInfo.dataCollectionEN = model.dataCollectionEN.Fix2();
                                    proInfo.dataManagementCN = model.dataManagementCN.Fix2();
                                    proInfo.dataManagementEN = model.dataManagementEN.Fix2();
                                    proInfo.dataAnalysisCN = model.dataAnalysisCN.Fix2();
                                    proInfo.dataAnalysisEN = model.dataAnalysisEN.Fix2();
                                    proInfo.registrant = null;
                                    proInfo.regIP = model.regIP;
                                    proInfo.regTime = model.regTime;
                                    proInfo.modifyBy = model.modifyBy;
                                    proInfo.modifyTime = model.modifyTime;
                                    proInfo.modifyIP = model.modifyIP;
                                    proInfo.status = "待审核";
                                    proInfo.statusEn = "Not be verified";
                                    proInfo.createUserID = 1;
                                    proInfo.fileExperimentalresults = model.fileExperimentalresults;
                                    proInfo.SubmitStatus = model.SubmitStatus;
                                    proInfo.ProjectOriginCode = model.ProjectOriginCode;
                                    proInfo.ProjectOriginCn = model.ProjectOriginCn.Fix2();
                                    proInfo.ReturnUrl = "";
                                    proInfo.UTN = model.UTN;
                                    proInfo.dataManagemenBoard = "1";
                                    proInfo.studyReport = model.studyReport.Fix2();
                                    proInfo.studyReportEN = model.studyReportEN.Fix2();

                                    if (!proInfo.ethicalCommitteeSanctionDate.HasValue)
                                    {
                                        proInfo.ethicalCommitteeSanctionDate = new DateTime(1990, 01, 01);
                                    }
                                    if (!proInfo.studyTimeStart.HasValue)
                                    {
                                        proInfo.studyTimeStart = new DateTime(1990, 01, 01);
                                    }
                                    if (!proInfo.studyTimeEnd.HasValue)
                                    {
                                        proInfo.studyTimeEnd = new DateTime(1990, 01, 01);
                                    }
                                    if (!proInfo.recruitingTimeStart.HasValue)
                                    {
                                        proInfo.recruitingTimeStart = new DateTime(1990, 01, 01);
                                    }
                                    if (!proInfo.recruitingTimeEnd.HasValue)
                                    {
                                        proInfo.recruitingTimeEnd = new DateTime(1990, 01, 01);
                                    }
                                    if (!proInfo.dataSFDA.HasValue)
                                    {
                                        proInfo.dataSFDA = new DateTime(1990, 01, 01);
                                    }
                                    proInfo.Insert();
                                    #endregion

                                    CopyFile(model.fileEthicalCommittee, 0);
                                    CopyFile(model.studyPlanfile, 0);
                                    CopyFile(model.informedConsentfile, 0);
                                    CopyFile(model.fileSFDA, 0);
                                    CopyFile(model.fileExperimentalresults, 0);

                                    foreach (var item in list1)
                                    {
                                        var mo = new SAAS_CollectingSample();
                                        mo.fateSample = item.fateSample.Fix2();
                                        mo.Pid = proInfo.Pid;
                                        mo.sampleNameCN = item.sampleNameCN.Fix2();
                                        mo.tissueCN = item.tissueCN.Fix2();
                                        mo.fateSample = item.fateSample.Fix2();
                                        mo.noteCN = item.noteCN.Fix2();
                                        mo.sampleNameEN = item.sampleNameEN.Fix2();
                                        mo.tissueEN = item.tissueEN.Fix2();
                                        mo.noteEN = item.noteEN.Fix2();
                                        mo.Insert();
                                    }
                                    foreach (var item in list2)
                                    {
                                        var mo = new SAAS_Diagnostic();
                                        mo.Pid = proInfo.Pid;
                                        mo.standard = item.standard.Fix2();
                                        mo.indexTest = item.indexTest.Fix2();
                                        mo.targetCondition = item.targetCondition.Fix2();
                                        mo.difficultCondition = item.difficultCondition.Fix2();
                                        mo.standardEn = item.standardEn.Fix2();
                                        mo.indexTestEn = item.indexTestEn.Fix2();
                                        mo.targetConditionEn = item.targetConditionEn.Fix2();
                                        mo.difficultConditionEn = item.difficultConditionEn.Fix2();
                                        mo.sampleSizeD = item.sampleSizeD;
                                        mo.sampleSizeT = item.sampleSizeT;
                                        mo.Insert();
                                    }
                                    foreach (var item in list3)
                                    {
                                        var mo = new SAAS_Interventions();
                                        mo.Pid = proInfo.Pid;
                                        mo.groupsCN = item.groupsCN.Fix2();
                                        mo.sampleSize = item.sampleSize;
                                        mo.groupsEN = item.groupsEN.Fix2();
                                        mo.interventionCN = item.interventionCN.Fix2();
                                        mo.interventionCode = item.interventionCode.Fix2();
                                        mo.interventionEN = item.interventionEN.Fix2();
                                        mo.Insert();
                                    }
                                    foreach (var item in list4)
                                    {
                                        var mo = new SAAS_Outcomes();
                                        mo.Pid = proInfo.Pid;
                                        mo.outcomeNameCN = item.outcomeNameCN.Fix2();
                                        mo.pointerType = item.pointerType.Fix2();
                                        mo.outcomeNameEN = item.outcomeNameEN.Fix2();
                                        mo.measureTimeCN = item.measureTimeCN.Fix2();
                                        mo.measureMethodCN = item.measureMethodCN.Fix2();
                                        mo.measureTimeEN = item.measureTimeEN.Fix2();
                                        mo.measureMethodEN = item.measureMethodEN.Fix2();
                                        mo.Insert();
                                    }
                                    foreach (var item in list5)
                                    {
                                        var mo = new SAAS_ResearchAddress();
                                        mo.Pid = proInfo.Pid;
                                        mo.countryCN = item.countryCN.Fix2();
                                        mo.provinceCN = item.provinceCN.Fix2();
                                        mo.cityCN = item.cityCN.Fix2();
                                        mo.countryEN = item.countryEN.Fix2();
                                        mo.provinceEN = item.provinceEN.Fix2();
                                        mo.cityEN = item.cityEN.Fix2();
                                        mo.hospitalCN = item.hospitalCN.Fix2();
                                        mo.levelInstitutionCN = item.levelInstitutionCN.Fix2();
                                        mo.hospitalEN = item.hospitalEN.Fix2();
                                        mo.levelInstitutionEN = item.levelInstitutionEN.Fix2();
                                        mo.Insert();
                                    }
                                    foreach (var item in list6)
                                    {
                                        var mo = new SAAS_SecondarySponsor();
                                        mo.Pid = proInfo.Pid;
                                        mo.countryCN = item.countryCN.Fix2();
                                        mo.provinceCN = item.provinceCN.Fix2();
                                        mo.cityCN = item.cityCN.Fix2();
                                        mo.countryEN = item.countryEN.Fix2();
                                        mo.provinceEN = item.provinceEN.Fix2();
                                        mo.cityEN = item.cityEN.Fix2();
                                        mo.institutionCN = item.institutionCN.Fix2();
                                        mo.specificAddressCN = item.specificAddressCN.Fix2();
                                        mo.institutionEN = item.institutionEN.Fix2();
                                        mo.specificAddressEN = item.specificAddressEN.Fix2();
                                        mo.Insert();
                                    }

                                    Console.WriteLine($"insert pid:{model.Pid} publicTitle:{model.publicTitleCN}");
                                }
                            }
                        }
                        catch (System.Data.SqlClient.SqlException ex)
                        {
                            Console.BackgroundColor = ConsoleColor.Red;
                            Console.WriteLine("--------------ERROR--START--------------");
                            Console.WriteLine(dbs.LastCommand);
                            Console.WriteLine(dbs.LastSQL);
                            Console.WriteLine("-----------------------------------------");
                            Console.WriteLine(string.Join(",", dbs.LastArgs.ToList().Select(x => x.ToString())));
                            Console.WriteLine("--------------ERROR--END----------------");
                            Console.ResetColor();
                            File.AppendAllText(@".\ERROR.LOG", System.Environment.NewLine);
                            File.AppendAllText(@".\ERROR.LOG", $"{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                            File.AppendAllText(@".\ERROR.LOG", dbs.LastSQL);
                            File.AppendAllText(@".\ERROR.LOG", System.Environment.NewLine);
                            File.AppendAllText(@".\ERROR.LOG", string.Join(",", dbs.LastArgs.ToList().Select(x => x.ToString())));
                            File.AppendAllText(@".\ERROR.LOG", System.Environment.NewLine);
                            File.AppendAllText(@".\ERROR.LOG", ex.Message);
                            File.AppendAllText(@".\ERROR.LOG", System.Environment.NewLine);
                            File.AppendAllText(@".\ERROR.LOG", ex.StackTrace);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Find Error");
                        }
                    }
                }
                Console.WriteLine("working end");

                Thread.Sleep(Elapsed * 1000);
                Console.WriteLine("working end");
            }
        }
        static void RecvUpdateInfo(List<ITMCTR.Core.Models2.SAAS_StudyDictionary> dicts, ITMCTR.Core.Models.SA_NewProject proInfo, ITMCTR.Core.Models2.SAAS_NewProject model)
        {
            proInfo.regNumberChi = model.regNumberChi.Fix();
            proInfo.modifyTime = model.modifyTime;
            proInfo.studyTypeID = dicts.FirstOrDefault(x => x.Sid == model.studyTypeID).StudyValue.Fix();
            proInfo.studyDesignID = dicts.FirstOrDefault(x => x.Sid == model.studyDesignID).StudyValue.Fix();
            proInfo.studyPhaseID = dicts.FirstOrDefault(x => x.Sid == model.studyPhaseID).StudyValue.Fix();
            proInfo.studyID = model.studyID.Fix();
            proInfo.secondaryID = model.secondaryID.Fix();
            proInfo.filloutLanguage = model.filloutLanguage.Fix().Fix();
            proInfo.registrationStatus = model.registrationStatus.Fix();
            proInfo.applicantTelephone = model.applicantTelephone.Fix();
            proInfo.studyTelephone = model.studyTelephone.Fix();
            proInfo.applicanFax = model.applicanFax.Fix();
            proInfo.studyFax = model.studyFax.Fix();
            proInfo.applicantEmail = model.applicantEmail.Fix();
            proInfo.studyEmail = model.studyEmail.Fix();
            proInfo.applicantWebsite = model.applicantWebsite.Fix();
            proInfo.studyWebsite = model.studyWebsite.Fix();
            proInfo.applicantPostcode = model.applicantPostcode.Fix();
            proInfo.studyPostcode = model.studyPostcode.Fix();
            proInfo.targetCode = model.targetCode.Fix();
            proInfo.dataSFDA = model.dataSFDA;
            proInfo.studyTimeStart = model.studyTimeStart;
            proInfo.studyTimeEnd = model.studyTimeEnd;
            proInfo.recruitingTimeStart = model.recruitingTimeStart;
            proInfo.recruitingTimeEnd = model.recruitingTimeEnd;
            proInfo.totalSampleSize = model.totalSampleSize.Fix();
            proInfo.recruitingStatus = model.recruitingStatus.Fix();
            proInfo.ageMin = model.ageMin.Fix();
            proInfo.ageMax = model.ageMax.Fix();
            proInfo.sex = model.sex.Fix();
            proInfo.signConsent = model.signConsent;
            proInfo.followupTime = model.followupTime.Fix();
            proInfo.followup = model.followup.Fix();
            proInfo.whetherPublic = model.whetherPublic;
            proInfo.of_SFDA = model.of_SFDA.Fix();
            proInfo.fileSFDA = model.fileSFDA.Fix();
            proInfo.studyPlanfile = model.studyPlanfile.Fix();
            proInfo.informedConsentfile = model.informedConsentfile.Fix();
            proInfo.fileExperimentalresults = model.fileExperimentalresults.Fix();
            proInfo.publicTitleEN = model.publicTitleEN.Fix();
            proInfo.titleAcronymEN = model.titleAcronymEN.Fix();
            proInfo.scientifirTitleEN = model.scientifirTitleEN.Fix();
            proInfo.scientifirAcronymEN = model.scientifirAcronymEN.Fix();
            proInfo.applicantEN = model.applicantEN.Fix();
            proInfo.studyLeaderEN = model.studyLeaderEN.Fix();
            proInfo.applicantAddressEN = model.applicantAddressEN.Fix();
            proInfo.studyAddressEN = model.studyAddressEN.Fix();
            proInfo.applicantInstitutionEN = model.applicantInstitutionEN.Fix();
            proInfo.primarySponsorEN = model.primarySponsorEN.Fix();
            proInfo.primarySponsorAddressEN = model.primarySponsorAddressEN.Fix();
            proInfo.sourceFundingEN = model.sourceFundingEN.Fix();
            proInfo.targetDiseaseEN = model.targetDiseaseEN.Fix();
            proInfo.objectivesStudyEN = model.objectivesStudyEN.Fix();
            proInfo.contentsDrugEN = model.contentsDrugEN.Fix();
            proInfo.inclusionCriteriaEN = model.inclusionCriteriaEN.Fix();
            proInfo.exclusionCrteriaEN = model.exclusionCrteriaEN.Fix();
            proInfo.randomMethodEN = model.randomMethodEN.Fix();
            proInfo.processConcealmentEN = model.processConcealmentEN.Fix();
            proInfo.blindingEN = model.blindingEN.Fix();
            proInfo.RulesblindingEN = model.RulesblindingEN.Fix();
            proInfo.statisticalMethodEN = model.statisticalMethodEN.Fix();
            proInfo.calculatedResultsEN = model.calculatedResultsEN.Fix();
            proInfo.DataCollectionUnit = model.dataCollectionCN == "0" ? "0" : "1".Fix();//这个不知道是啥
            proInfo.dataManagementEN = model.dataManagementEN.Fix();
            proInfo.dataAnalysisEN = model.dataAnalysisEN.Fix();
            proInfo.approvedCommittee = model.approvedCommittee;
            proInfo.ethicalCommitteeFileID = model.ethicalCommitteeFileID.Fix();
            proInfo.fileEthicalCommittee = model.fileEthicalCommittee.Fix();
            proInfo.ethicalCommitteeName = model.ethicalCommitteeName.Fix();
            proInfo.ethicalCommitteeNameEN = model.ethicalCommitteeNameEN.Fix();
            proInfo.ethicalCommitteeSanctionDate = model.ethicalCommitteeSanctionDate;
            proInfo.publicTitleCN = model.publicTitleCN.Fix();
            proInfo.titleAcronymCN = model.titleAcronymCN.Fix();
            proInfo.scientifirTitleCN = model.scientifirTitleCN.Fix();
            proInfo.scientifirAcronymCN = model.scientifirAcronymCN.Fix();
            proInfo.applicantCN = model.applicantCN.Fix();
            proInfo.studyLeaderCN = model.studyLeaderCN.Fix();
            proInfo.applicantAddressCN = model.applicantAddressCN.Fix();
            proInfo.studyAddressCN = model.studyAddressCN.Fix();
            proInfo.applicantInstitutionCN = model.applicantInstitutionCN.Fix();
            proInfo.primarySponsorCN = model.primarySponsorCN.Fix();
            proInfo.primarySponsorAddressCN = model.primarySponsorAddressCN.Fix();
            proInfo.sourceFundingCN = model.sourceFundingCN.Fix();
            proInfo.targetDiseaseCN = model.targetDiseaseCN.Fix();
            proInfo.objectivesStudyCN = model.objectivesStudyCN.Fix();
            proInfo.contentsDrugCN = model.contentsDrugCN.Fix();
            proInfo.inclusionCriteriaCN = model.inclusionCriteriaCN.Fix();
            proInfo.exclusionCrteriaCN = model.exclusionCrteriaCN.Fix();
            proInfo.randomMethodCN = model.randomMethodCN.Fix();
            proInfo.processConcealmentCN = model.processConcealmentCN.Fix();
            proInfo.blindingCN = model.blindingCN.Fix();
            proInfo.RulesblindingCN = model.RulesblindingCN.Fix();
            proInfo.statisticalMethodCN = model.statisticalMethodCN.Fix();
            proInfo.calculatedResultsCN = model.calculatedResultsCN.Fix();
            proInfo.dataManagementCN = model.dataManagementCN.Fix();
            proInfo.dataAnalysisCN = model.dataAnalysisCN.Fix();
            proInfo.UTN = model.UTN.Fix();
            proInfo.studyLeaderCompanyCN = model.studyLeaderCompanyCN.Fix();
            proInfo.studyLeaderCompanyEN = model.studyLeaderCompanyEN.Fix();
            proInfo.DataManagemenBoard = model.dataManagemenBoard.Fix();
            proInfo.studyReport = model.studyReport.Fix();
            proInfo.studyReportEN = model.studyReportEN.Fix();
            proInfo.ethicalCommitteeCName = model.ethicalCommitteeCName.Fix();
            proInfo.ethicalCommitteeCNameEN = model.ethicalCommitteeCNameEN.Fix();
            proInfo.ethicalCommitteeCAddress = model.ethicalCommitteeCAddress.Fix();
            proInfo.ethicalCommitteeCAddressEN = model.ethicalCommitteeCAddressEN.Fix();
            proInfo.ethicalCommitteeCPhone = model.ethicalCommitteeCPhone.Fix();
            proInfo.ethicalCommitteeCEmail = model.ethicalCommitteeCEmail.Fix();

            if (proInfo.ethicalCommitteeSanctionDate == new DateTime(1990, 01, 01))
            {
                proInfo.ethicalCommitteeSanctionDate = null;
            }
            if (proInfo.studyTimeStart == new DateTime(1990, 01, 01))
            {
                proInfo.studyTimeStart = null;
            }
            if (proInfo.studyTimeEnd == new DateTime(1990, 01, 01))
            {
                proInfo.studyTimeEnd = null;
            }
            if (proInfo.recruitingTimeStart == new DateTime(1990, 01, 01))
            {
                proInfo.recruitingTimeStart = null;
            }
            if (proInfo.recruitingTimeEnd == new DateTime(1990, 01, 01))
            {
                proInfo.recruitingTimeEnd = null;
            }
            if (proInfo.dataSFDA == new DateTime(1990, 01, 01))
            {
                proInfo.dataSFDA = null;
            }

            CopyFile(proInfo.fileEthicalCommittee, 1);
            CopyFile(proInfo.studyPlanfile, 1);
            CopyFile(proInfo.informedConsentfile, 1);
            CopyFile(proInfo.fileSFDA, 1);
            CopyFile(proInfo.fileExperimentalresults, 1);
        }
        static void SendUpdateInfo(List<ITMCTR.Core.Models2.SAAS_StudyDictionary> dicts, ITMCTR.Core.Models2.SAAS_NewProject proInfo, ITMCTR.Core.Models.SA_NewProject model, SourceDB dbs, targetDB dbt)
        {
            Console.WriteLine("main begin");
            //proInfo.Guid = model.Pid;
            proInfo.regNumber = model.regNumber;
            proInfo.regNumberChi = model.regNumberChi;
            proInfo.regNumberTime = model.regNumberTime;
            proInfo.filloutLanguage = model.filloutLanguage;
            proInfo.registrationStatus = model.registrationStatus;
            proInfo.publicTitleCN = model.publicTitleCN.Fix2();
            proInfo.publicTitleEN = model.publicTitleEN.Fix2();
            proInfo.titleAcronymCN = model.titleAcronymCN.Fix2();
            proInfo.titleAcronymEN = model.titleAcronymEN.Fix2();
            proInfo.scientifirTitleCN = model.scientifirTitleCN.Fix2();
            proInfo.scientifirTitleEN = model.scientifirTitleEN.Fix2();
            proInfo.scientifirAcronymCN = model.scientifirAcronymCN.Fix2();
            proInfo.scientifirAcronymEN = model.scientifirAcronymEN.Fix2();
            proInfo.studyID = model.studyID;
            proInfo.secondaryID = model.secondaryID;
            proInfo.applicantCN = model.applicantCN.Fix2();
            proInfo.applicantEN = model.applicantEN.Fix2();
            proInfo.studyLeaderCN = model.studyLeaderCN.Fix2();
            proInfo.studyLeaderEN = model.studyLeaderEN.Fix2();
            proInfo.applicantTelephone = model.applicantTelephone;
            proInfo.studyTelephone = model.studyTelephone;
            proInfo.applicanFax = model.applicanFax;
            proInfo.studyFax = model.studyFax;
            proInfo.applicantEmail = model.applicantEmail;
            proInfo.studyEmail = model.studyEmail;
            proInfo.applicantWebsite = model.applicantWebsite.Fix2();
            proInfo.studyWebsite = model.studyWebsite.Fix2();
            proInfo.applicantAddressCN = model.applicantAddressCN.Fix2();
            proInfo.applicantAddressEN = model.applicantAddressEN.Fix2();
            proInfo.studyAddressCN = model.studyAddressCN.Fix2();
            proInfo.studyAddressEN = model.studyAddressEN.Fix2();
            proInfo.applicantPostcode = model.applicantPostcode;
            proInfo.studyPostcode = model.studyPostcode;
            proInfo.applicantInstitutionCN = model.applicantInstitutionCN.Fix2();
            proInfo.applicantInstitutionEN = model.applicantInstitutionEN.Fix2();
            proInfo.studyLeaderCompanyCN = model.studyLeaderCompanyCN.Fix2();
            proInfo.studyLeaderCompanyEN = model.studyLeaderCompanyEN.Fix2();
            proInfo.approvedCommittee = model.approvedCommittee;
            proInfo.ethicalCommitteeFileID = model.ethicalCommitteeFileID.Fix2();
            proInfo.fileEthicalCommittee = model.fileEthicalCommittee;
            proInfo.ethicalCommitteeName = model.ethicalCommitteeName.Fix2();
            proInfo.ethicalCommitteeNameEN = model.ethicalCommitteeNameEN.Fix2();
            proInfo.ethicalCommitteeSanctionDate = model.ethicalCommitteeSanctionDate;
            if (!proInfo.ethicalCommitteeSanctionDate.HasValue)
            {
                proInfo.ethicalCommitteeSanctionDate = new DateTime(2013, 08, 26);
            }
            proInfo.ethicalCommitteeCName = model.ethicalCommitteeCName.Fix2();
            proInfo.ethicalCommitteeCNameEN = model.ethicalCommitteeCNameEN.Fix2();
            proInfo.ethicalCommitteeCAddress = model.ethicalCommitteeCAddress.Fix2();
            proInfo.ethicalCommitteeCAddressEN = model.ethicalCommitteeCAddressEN.Fix2();
            proInfo.ethicalCommitteeCPhone = model.ethicalCommitteeCPhone;
            proInfo.ethicalCommitteeCEmail = model.ethicalCommitteeCEmail;
            proInfo.of_SFDA = model.of_SFDA;
            proInfo.fileSFDA = model.fileSFDA;
            proInfo.dataSFDA = model.dataSFDA;
            proInfo.studyPlanfile = model.studyPlanfile;
            proInfo.informedConsentfile = model.informedConsentfile;
            proInfo.primarySponsorCN = model.primarySponsorCN.Fix2();
            proInfo.primarySponsorEN = model.primarySponsorEN.Fix2();
            proInfo.primarySponsorAddressCN = model.primarySponsorAddressCN.Fix2();
            proInfo.primarySponsorAddressEN = model.primarySponsorAddressEN.Fix2();
            proInfo.sourceFundingCN = model.sourceFundingCN.Fix2();
            proInfo.sourceFundingEN = model.sourceFundingEN.Fix2();
            proInfo.targetDiseaseCN = model.targetDiseaseCN.Fix2();
            proInfo.targetDiseaseEN = model.targetDiseaseEN.Fix2();
            proInfo.targetCode = model.targetCode;
            proInfo.studyTypeID = dicts.FirstOrDefault(x => x.StudyValue == model.studyTypeID).Sid;
            proInfo.studyDesignID = dicts.FirstOrDefault(x => x.StudyValue == model.studyDesignID).Sid;
            proInfo.studyPhaseID = dicts.FirstOrDefault(x => x.StudyValue == model.studyPhaseID).Sid;
            proInfo.objectivesStudyCN = model.objectivesStudyCN.Fix2();
            proInfo.objectivesStudyEN = model.objectivesStudyEN.Fix2();
            proInfo.contentsDrugCN = model.contentsDrugCN.Fix2();
            proInfo.contentsDrugEN = model.contentsDrugEN.Fix2();
            proInfo.inclusionCriteriaCN = model.inclusionCriteriaCN.Fix2();
            proInfo.inclusionCriteriaEN = model.inclusionCriteriaEN.Fix2();
            proInfo.exclusionCrteriaCN = model.exclusionCrteriaCN.Fix2();
            proInfo.exclusionCrteriaEN = model.exclusionCrteriaEN.Fix2();
            proInfo.studyTimeStart = model.studyTimeStart;
            proInfo.studyTimeEnd = model.studyTimeEnd;
            proInfo.recruitingTimeStart = model.recruitingTimeStart;
            proInfo.recruitingTimeEnd = model.recruitingTimeEnd;
            proInfo.totalSampleSize = model.totalSampleSize;
            proInfo.recruitingStatus = model.recruitingStatus;
            proInfo.ageMin = model.ageMin;
            proInfo.ageMax = model.ageMax;
            proInfo.randomMethodCN = model.randomMethodCN.Fix2();
            proInfo.randomMethodEN = model.randomMethodEN.Fix2();
            proInfo.sex = model.sex;
            proInfo.signConsent = model.signConsent;
            proInfo.followupTime = model.followupTime;
            proInfo.followup = model.followup;
            proInfo.processConcealmentCN = model.processConcealmentCN.Fix2();
            proInfo.processConcealmentEN = model.processConcealmentEN.Fix2();
            proInfo.blindingCN = model.blindingCN.Fix2();
            proInfo.blindingEN = model.blindingEN.Fix2();
            proInfo.RulesblindingCN = model.RulesblindingCN.Fix2();
            proInfo.RulesblindingEN = model.RulesblindingEN.Fix2();
            proInfo.statisticalMethodCN = model.statisticalMethodCN.Fix2();
            proInfo.statisticalMethodEN = model.statisticalMethodEN.Fix2();
            proInfo.calculatedResultsCN = model.calculatedResultsCN.Fix2();
            proInfo.calculatedResultsEN = model.calculatedResultsEN.Fix2();
            proInfo.whetherPublic = model.whetherPublic;
            proInfo.dataCollectionCN = model.dataCollectionCN.Fix2();
            proInfo.dataCollectionEN = model.dataCollectionEN.Fix2();
            proInfo.dataManagementCN = model.dataManagementCN.Fix2();
            proInfo.dataManagementEN = model.dataManagementEN.Fix2();
            proInfo.dataAnalysisCN = model.dataAnalysisCN.Fix2();
            proInfo.dataAnalysisEN = model.dataAnalysisEN.Fix2();
            proInfo.registrant = null;
            proInfo.regIP = model.regIP;
            proInfo.regTime = model.regTime;
            proInfo.modifyBy = model.modifyBy;
            proInfo.modifyTime = model.modifyTime;
            proInfo.modifyIP = model.modifyIP;
            proInfo.status = "待审核";
            proInfo.statusEn = "Not be verified";
            proInfo.createUserID = 1;
            proInfo.fileExperimentalresults = model.fileExperimentalresults;
            proInfo.SubmitStatus = model.SubmitStatus;
            proInfo.ProjectOriginCode = model.ProjectOriginCode;
            proInfo.ProjectOriginCn = model.ProjectOriginCn.Fix2();
            proInfo.ReturnUrl = "";
            proInfo.UTN = model.UTN;
            proInfo.dataManagemenBoard = "1";
            proInfo.studyReport = model.studyReport.Fix2();
            proInfo.studyReportEN = model.studyReportEN.Fix2();

            if (!proInfo.ethicalCommitteeSanctionDate.HasValue)
            {
                proInfo.ethicalCommitteeSanctionDate = new DateTime(1990, 01, 01);
            }
            if (!proInfo.studyTimeStart.HasValue)
            {
                proInfo.studyTimeStart = new DateTime(1990, 01, 01);
            }
            if (!proInfo.studyTimeEnd.HasValue)
            {
                proInfo.studyTimeEnd = new DateTime(1990, 01, 01);
            }
            if (!proInfo.recruitingTimeStart.HasValue)
            {
                proInfo.recruitingTimeStart = new DateTime(1990, 01, 01);
            }
            if (!proInfo.recruitingTimeEnd.HasValue)
            {
                proInfo.recruitingTimeEnd = new DateTime(1990, 01, 01);
            }
            if (!proInfo.dataSFDA.HasValue)
            {
                proInfo.dataSFDA = new DateTime(1990, 01, 01);
            }

            proInfo.Update();

            Console.WriteLine("main update");

            CopyFile(model.fileEthicalCommittee, 0);
            CopyFile(model.studyPlanfile, 0);
            CopyFile(model.informedConsentfile, 0);
            CopyFile(model.fileSFDA, 0);
            CopyFile(model.fileExperimentalresults, 0);


            var list1 = dbt.Query<ITMCTR.Core.Models.SA_CollectingSample>("where pid=@0", model.Pid).ToList();
            var list2 = dbt.Query<ITMCTR.Core.Models.SA_Diagnostic>("where pid=@0", model.Pid).ToList();
            var list3 = dbt.Query<ITMCTR.Core.Models.SA_Interventions>("where pid=@0", model.Pid).ToList();
            var list4 = dbt.Query<ITMCTR.Core.Models.SA_Outcomes>("where pid=@0", model.Pid).ToList();
            var list5 = dbt.Query<ITMCTR.Core.Models.SA_ResearchAddress>("where pid=@0", model.Pid).ToList();
            var list6 = dbt.Query<ITMCTR.Core.Models.SA_SecondarySponsor>("where pid=@0", model.Pid).ToList();

            Console.WriteLine("begin clear");

            dbs.Delete<SAAS_CollectingSample>("WHERE Pid=@0", proInfo.Pid);
            dbs.Delete<SAAS_Diagnostic>("WHERE Pid=@0", proInfo.Pid);
            dbs.Delete<SAAS_Interventions>("WHERE Pid=@0", proInfo.Pid);
            dbs.Delete<SAAS_Outcomes>("WHERE Pid=@0", proInfo.Pid);
            dbs.Delete<SAAS_ResearchAddress>("WHERE Pid=@0", proInfo.Pid);
            dbs.Delete<SAAS_SecondarySponsor>("WHERE Pid=@0", proInfo.Pid);

            Console.WriteLine("end clear");

            foreach (var item in list1)
            {
                Console.WriteLine("insert SAAS_CollectingSample");
                var mo = new SAAS_CollectingSample();
                mo.fateSample = item.fateSample.Fix2();
                mo.Pid = proInfo.Pid;
                mo.sampleNameCN = item.sampleNameCN.Fix2();
                mo.tissueCN = item.tissueCN.Fix2();
                mo.fateSample = item.fateSample.Fix2();
                mo.noteCN = item.noteCN.Fix2();
                mo.sampleNameEN = item.sampleNameEN.Fix2();
                mo.tissueEN = item.tissueEN.Fix2();
                mo.noteEN = item.noteEN.Fix2();
                mo.Insert();
            }
            foreach (var item in list2)
            {
                Console.WriteLine("insert SAAS_Diagnostic");
                var mo = new SAAS_Diagnostic();
                mo.Pid = proInfo.Pid;
                mo.standard = item.standard.Fix2();
                mo.indexTest = item.indexTest.Fix2();
                mo.targetCondition = item.targetCondition.Fix2();
                mo.difficultCondition = item.difficultCondition.Fix2();
                mo.standardEn = item.standardEn.Fix2();
                mo.indexTestEn = item.indexTestEn.Fix2();
                mo.targetConditionEn = item.targetConditionEn.Fix2();
                mo.difficultConditionEn = item.difficultConditionEn.Fix2();
                mo.sampleSizeD = item.sampleSizeD;
                mo.sampleSizeT = item.sampleSizeT;
                mo.Insert();
            }
            foreach (var item in list3)
            {
                Console.WriteLine("insert SAAS_Interventions");
                var mo = new SAAS_Interventions();
                mo.Pid = proInfo.Pid;
                mo.groupsCN = item.groupsCN.Fix2();
                mo.sampleSize = item.sampleSize;
                mo.groupsEN = item.groupsEN.Fix2();
                mo.interventionCN = item.interventionCN.Fix2();
                mo.interventionCode = item.interventionCode.Fix2();
                mo.interventionEN = item.interventionEN.Fix2();
                mo.Insert();
            }
            foreach (var item in list4)
            {
                Console.WriteLine("insert SAAS_Outcomes");
                var mo = new SAAS_Outcomes();
                mo.Pid = proInfo.Pid;
                mo.outcomeNameCN = item.outcomeNameCN.Fix2();
                mo.pointerType = item.pointerType.Fix2();
                mo.outcomeNameEN = item.outcomeNameEN.Fix2();
                mo.measureTimeCN = item.measureTimeCN.Fix2();
                mo.measureMethodCN = item.measureMethodCN.Fix2();
                mo.measureTimeEN = item.measureTimeEN.Fix2();
                mo.measureMethodEN = item.measureMethodEN.Fix2();
                mo.Insert();
            }
            foreach (var item in list5)
            {
                Console.WriteLine("insert SAAS_ResearchAddress");
                var mo = new SAAS_ResearchAddress();
                mo.Pid = proInfo.Pid;
                mo.countryCN = item.countryCN.Fix2();
                mo.provinceCN = item.provinceCN.Fix2();
                mo.cityCN = item.cityCN.Fix2();
                mo.countryEN = item.countryEN.Fix2();
                mo.provinceEN = item.provinceEN.Fix2();
                mo.cityEN = item.cityEN.Fix2();
                mo.hospitalCN = item.hospitalCN.Fix2();
                mo.levelInstitutionCN = item.levelInstitutionCN.Fix2();
                mo.hospitalEN = item.hospitalEN.Fix2();
                mo.levelInstitutionEN = item.levelInstitutionEN.Fix2();
                mo.Insert();
            }
            foreach (var item in list6)
            {
                Console.WriteLine("insert SAAS_SecondarySponsor");
                var mo = new SAAS_SecondarySponsor();
                mo.Pid = proInfo.Pid;
                mo.countryCN = item.countryCN.Fix2();
                mo.provinceCN = item.provinceCN.Fix2();
                mo.cityCN = item.cityCN.Fix2();
                mo.countryEN = item.countryEN.Fix2();
                mo.provinceEN = item.provinceEN.Fix2();
                mo.cityEN = item.cityEN.Fix2();
                mo.institutionCN = item.institutionCN.Fix2();
                mo.specificAddressCN = item.specificAddressCN.Fix2();
                mo.institutionEN = item.institutionEN.Fix2();
                mo.specificAddressEN = item.specificAddressEN.Fix2();
                mo.Insert();
            }

            Console.WriteLine($"update send pid:{model.Pid} publicTitle:{model.publicTitleCN}");
        }
        static void CopyFile(string PathFile, int Mode)
        {
            if (string.IsNullOrEmpty(PathFile))
            {
                return;
            }
            if (Mode == 0)
            {
                Console.WriteLine($"dir:0");
                var path = PathSource;
                var pathes = PathFile.Split('/');
                foreach (string p in pathes)
                {
                    path = Path.Combine(path, p);
                }
                Console.WriteLine($"source:{path}");
                if (File.Exists(path))
                {
                    var pathto = TargetSource;
                    foreach (string p in pathes)
                    {
                        pathto = Path.Combine(pathto, p);
                    }
                    var dirs = Path.GetDirectoryName(pathto);
                    if (!Directory.Exists(dirs))
                        Directory.CreateDirectory(dirs);
                    Console.WriteLine($"target:{pathto}");
                    File.Copy(path, pathto, true);
                }
            }
            else
            {
                var path = TargetSource;
                var pathes = PathFile.Split('/');
                foreach (string p in pathes)
                {
                    path = Path.Combine(path, p);
                }
                if (File.Exists(path))
                {
                    var pathto = PathSource;
                    foreach (string p in pathes)
                    {
                        pathto = Path.Combine(pathto, p);
                    }
                    var dirs = Path.GetDirectoryName(pathto);
                    if (!Directory.Exists(dirs))
                        Directory.CreateDirectory(dirs);
                    Console.WriteLine($"dir:1");
                    Console.WriteLine($"source:{path}");
                    Console.WriteLine($"target:{pathto}");
                    File.Copy(path, pathto, true);
                }
            }
        }
        public static void Fun3()
        {
            Console.WriteLine("F3");
            using (SourceDB sourceDB = new SourceDB())
            {
                using (var targetDB = new targetDB())
                {
                    Dictionary<string, string> list = new Dictionary<string, string>();

                    using (StreamReader streamReader = new StreamReader(".\\123.txt"))
                    {
                        while (true)
                        {
                            string text = streamReader.ReadLine();
                            if (string.IsNullOrEmpty(text))
                            {
                                break;
                            }
                            string[] array = text.Split(new char[] { '|', '\t' });
                            if (array.Length == 2)
                            {
                                list.Add(array[0], array[1]);
                            }
                        }
                    }
                    Sql sql = Sql.Builder.Select("*").From("SAAS_NewProject").Where("regNumber in (@0)", list.Keys.Select(x => x));
                    List<SAAS_NewProject> list2 = sourceDB.Query<SAAS_NewProject>(sql).ToList();
                    List<SAAS_StudyDictionary> source = sourceDB.Query<SAAS_StudyDictionary>("", new object[0]).ToList();
                    foreach (SAAS_NewProject model in list2)
                    {
                        SA_NewProject sA_NewProject = new SA_NewProject();
                        List<SAAS_CollectingSample> list3 = sourceDB.Query<SAAS_CollectingSample>("where pid=@0", new object[1] { model.Pid }).ToList();
                        List<SAAS_Diagnostic> list4 = sourceDB.Query<SAAS_Diagnostic>("where pid=@0", new object[1] { model.Pid }).ToList();
                        List<SAAS_Interventions> list5 = sourceDB.Query<SAAS_Interventions>("where pid=@0", new object[1] { model.Pid }).ToList();
                        List<SAAS_Outcomes> list6 = sourceDB.Query<SAAS_Outcomes>("where pid=@0", new object[1] { model.Pid }).ToList();
                        List<SAAS_ResearchAddress> list7 = sourceDB.Query<SAAS_ResearchAddress>("where pid=@0", new object[1] { model.Pid }).ToList();
                        List<SAAS_SecondarySponsor> list8 = sourceDB.Query<SAAS_SecondarySponsor>("where pid=@0", new object[1] { model.Pid }).ToList();
                        if (targetDB.Exists<SA_NewProject>("where ReleaseNumber=@0", model.regNumber))
                        {
                            Console.WriteLine($"duplicate: {model.regNumber} {model.publicTitleCN}");
                            File.AppendAllText(@".\duplicate.LOG", $"duplicate: {model.regNumber} {model.publicTitleCN}");
                            File.AppendAllText(@".\duplicate.LOG", System.Environment.NewLine);
                            continue;
                        }
                        sA_NewProject.Pid = Guid.NewGuid();
                        sA_NewProject.regIP = model.regIP;
                        sA_NewProject.regTime = model.regTime;
                        sA_NewProject.publicTitleCN = list[model.regNumber].Fix();
                        sA_NewProject.ReleaseNumber = model.regNumber;
                        sA_NewProject.regNumber = model.regNumber.Replace("ChiMCTR", "ITMCTR");
                        if (!string.IsNullOrEmpty(model.regNumber))
                        {
                            sA_NewProject.regSerialNumber = long.Parse(Regex.Replace(model.regNumber, "[^0-9]+", ""));
                        }
                        sA_NewProject.executeTaskSysUserId = new Guid("709d664e-5399-4698-b904-4a217f66e94d");
                        sA_NewProject.createUserID = new Guid("5c92f5be-8ffd-43ba-a4a1-be7c2eb7d5e5");
                        sA_NewProject.sendTaskSysUserId = new Guid("df0f4c69-32d1-4db2-a76e-cc885428864b");
                        sA_NewProject.taskStatus = 5;
                        sA_NewProject.status = 3;
                        sA_NewProject.regNumberChi = model.regNumberChi.Fix();
                        sA_NewProject.modifyTime = model.modifyTime;
                        sA_NewProject.studyTypeID = source.FirstOrDefault((SAAS_StudyDictionary x) => x.Sid == model.studyTypeID).StudyValue.Fix();
                        sA_NewProject.studyDesignID = source.FirstOrDefault((SAAS_StudyDictionary x) => x.Sid == model.studyDesignID).StudyValue.Fix();
                        sA_NewProject.studyPhaseID = source.FirstOrDefault((SAAS_StudyDictionary x) => x.Sid == model.studyPhaseID).StudyValue.Fix();
                        sA_NewProject.studyID = model.studyID.Fix();
                        sA_NewProject.secondaryID = model.secondaryID.Fix();
                        sA_NewProject.filloutLanguage = model.filloutLanguage.Fix().Fix();
                        sA_NewProject.registrationStatus = model.registrationStatus.Fix();
                        sA_NewProject.applicantTelephone = model.applicantTelephone.Fix();
                        sA_NewProject.studyTelephone = model.studyTelephone.Fix();
                        sA_NewProject.applicanFax = model.applicanFax.Fix();
                        sA_NewProject.studyFax = model.studyFax.Fix();
                        sA_NewProject.applicantEmail = model.applicantEmail.Fix();
                        sA_NewProject.studyEmail = model.studyEmail.Fix();
                        sA_NewProject.applicantWebsite = model.applicantWebsite.Fix();
                        sA_NewProject.studyWebsite = model.studyWebsite.Fix();
                        sA_NewProject.applicantPostcode = model.applicantPostcode.Fix();
                        sA_NewProject.studyPostcode = model.studyPostcode.Fix();
                        sA_NewProject.targetCode = model.targetCode.Fix();
                        sA_NewProject.dataSFDA = model.dataSFDA;
                        sA_NewProject.studyTimeStart = model.studyTimeStart;
                        sA_NewProject.studyTimeEnd = model.studyTimeEnd;
                        sA_NewProject.recruitingTimeStart = model.recruitingTimeStart;
                        sA_NewProject.recruitingTimeEnd = model.recruitingTimeEnd;
                        sA_NewProject.totalSampleSize = model.totalSampleSize.Fix();
                        sA_NewProject.recruitingStatus = model.recruitingStatus.Fix();
                        sA_NewProject.ageMin = model.ageMin.Fix();
                        sA_NewProject.ageMax = model.ageMax.Fix();
                        sA_NewProject.sex = model.sex.Fix();
                        sA_NewProject.signConsent = model.signConsent;
                        sA_NewProject.followupTime = model.followupTime.Fix();
                        sA_NewProject.followup = model.followup.Fix();
                        sA_NewProject.whetherPublic = model.whetherPublic;
                        sA_NewProject.of_SFDA = model.of_SFDA.Fix();
                        sA_NewProject.fileSFDA = model.fileSFDA.Fix();
                        sA_NewProject.studyPlanfile = model.studyPlanfile.Fix();
                        sA_NewProject.informedConsentfile = model.informedConsentfile.Fix();
                        sA_NewProject.fileExperimentalresults = model.fileExperimentalresults.Fix();
                        sA_NewProject.publicTitleEN = model.publicTitleEN.Fix();
                        sA_NewProject.titleAcronymEN = model.titleAcronymEN.Fix();
                        sA_NewProject.scientifirTitleEN = model.scientifirTitleEN.Fix();
                        sA_NewProject.scientifirAcronymEN = model.scientifirAcronymEN.Fix();
                        sA_NewProject.applicantEN = model.applicantEN.Fix();
                        sA_NewProject.studyLeaderEN = model.studyLeaderEN.Fix();
                        sA_NewProject.applicantAddressEN = model.applicantAddressEN.Fix();
                        sA_NewProject.studyAddressEN = model.studyAddressEN.Fix();
                        sA_NewProject.applicantInstitutionEN = model.applicantInstitutionEN.Fix();
                        sA_NewProject.primarySponsorEN = model.primarySponsorEN.Fix();
                        sA_NewProject.primarySponsorAddressEN = model.primarySponsorAddressEN.Fix();
                        sA_NewProject.sourceFundingEN = model.sourceFundingEN.Fix();
                        sA_NewProject.targetDiseaseEN = model.targetDiseaseEN.Fix();
                        sA_NewProject.objectivesStudyEN = model.objectivesStudyEN.Fix();
                        sA_NewProject.contentsDrugEN = model.contentsDrugEN.Fix();
                        sA_NewProject.inclusionCriteriaEN = model.inclusionCriteriaEN.Fix();
                        sA_NewProject.exclusionCrteriaEN = model.exclusionCrteriaEN.Fix();
                        sA_NewProject.randomMethodEN = model.randomMethodEN.Fix();
                        sA_NewProject.processConcealmentEN = model.processConcealmentEN.Fix();
                        sA_NewProject.blindingEN = model.blindingEN.Fix();
                        sA_NewProject.RulesblindingEN = model.RulesblindingEN.Fix();
                        sA_NewProject.statisticalMethodEN = model.statisticalMethodEN.Fix();
                        sA_NewProject.calculatedResultsEN = model.calculatedResultsEN.Fix();
                        sA_NewProject.DataCollectionUnit = ((model.dataCollectionCN == "0") ? "0" : "1".Fix());
                        sA_NewProject.dataManagementEN = model.dataManagementEN.Fix();
                        sA_NewProject.dataAnalysisEN = model.dataAnalysisEN.Fix();
                        sA_NewProject.SubmitStatus = 1;
                        sA_NewProject.approvedCommittee = model.approvedCommittee;
                        sA_NewProject.ethicalCommitteeFileID = model.ethicalCommitteeFileID.Fix();
                        sA_NewProject.fileEthicalCommittee = model.fileEthicalCommittee.Fix();
                        sA_NewProject.ethicalCommitteeName = model.ethicalCommitteeName.Fix();
                        sA_NewProject.ethicalCommitteeNameEN = model.ethicalCommitteeNameEN.Fix();
                        sA_NewProject.ethicalCommitteeSanctionDate = model.ethicalCommitteeSanctionDate;

                        sA_NewProject.titleAcronymCN = model.titleAcronymCN.Fix();
                        sA_NewProject.scientifirTitleCN = model.scientifirTitleCN.Fix();
                        sA_NewProject.scientifirAcronymCN = model.scientifirAcronymCN.Fix();
                        sA_NewProject.applicantCN = model.applicantCN.Fix();
                        sA_NewProject.studyLeaderCN = model.studyLeaderCN.Fix();
                        sA_NewProject.applicantAddressCN = model.applicantAddressCN.Fix();
                        sA_NewProject.studyAddressCN = model.studyAddressCN.Fix();
                        sA_NewProject.applicantInstitutionCN = model.applicantInstitutionCN.Fix();
                        sA_NewProject.primarySponsorCN = model.primarySponsorCN.Fix();
                        sA_NewProject.primarySponsorAddressCN = model.primarySponsorAddressCN.Fix();
                        sA_NewProject.sourceFundingCN = model.sourceFundingCN.Fix();
                        sA_NewProject.targetDiseaseCN = model.targetDiseaseCN.Fix();
                        sA_NewProject.objectivesStudyCN = model.objectivesStudyCN.Fix();
                        sA_NewProject.contentsDrugCN = model.contentsDrugCN.Fix();
                        sA_NewProject.inclusionCriteriaCN = model.inclusionCriteriaCN.Fix();
                        sA_NewProject.exclusionCrteriaCN = model.exclusionCrteriaCN.Fix();
                        sA_NewProject.randomMethodCN = model.randomMethodCN.Fix();
                        sA_NewProject.processConcealmentCN = model.processConcealmentCN.Fix();
                        sA_NewProject.blindingCN = model.blindingCN.Fix();
                        sA_NewProject.RulesblindingCN = model.RulesblindingCN.Fix();
                        sA_NewProject.statisticalMethodCN = model.statisticalMethodCN.Fix();
                        sA_NewProject.calculatedResultsCN = model.calculatedResultsCN.Fix();
                        sA_NewProject.dataManagementCN = model.dataManagementCN.Fix();
                        sA_NewProject.dataAnalysisCN = model.dataAnalysisCN.Fix();
                        sA_NewProject.UTN = model.UTN.Fix();
                        sA_NewProject.studyLeaderCompanyCN = model.studyLeaderCompanyCN.Fix();
                        sA_NewProject.studyLeaderCompanyEN = model.studyLeaderCompanyEN.Fix();
                        sA_NewProject.DataManagemenBoard = model.dataManagemenBoard.Fix();
                        sA_NewProject.studyReport = model.studyReport.Fix();
                        sA_NewProject.studyReportEN = model.studyReportEN.Fix();
                        sA_NewProject.ethicalCommitteeCName = model.ethicalCommitteeCName.Fix();
                        sA_NewProject.ethicalCommitteeCNameEN = model.ethicalCommitteeCNameEN.Fix();
                        sA_NewProject.ethicalCommitteeCAddress = model.ethicalCommitteeCAddress.Fix();
                        sA_NewProject.ethicalCommitteeCAddressEN = model.ethicalCommitteeCAddressEN.Fix();
                        sA_NewProject.ethicalCommitteeCPhone = model.ethicalCommitteeCPhone.Fix();
                        sA_NewProject.ethicalCommitteeCEmail = model.ethicalCommitteeCEmail.Fix();
                        sA_NewProject.regNumberTime = model.regNumberTime;
                        sA_NewProject.Insert();
                        foreach (SAAS_CollectingSample item in list3)
                        {
                            SA_CollectingSample sA_CollectingSample = new SA_CollectingSample();
                            sA_CollectingSample.csId = Guid.NewGuid();
                            sA_CollectingSample.fateSample = item.fateSample.Fix();
                            sA_CollectingSample.Pid = sA_NewProject.Pid;
                            sA_CollectingSample.sampleNameCN = item.sampleNameCN.Fix();
                            sA_CollectingSample.tissueCN = item.tissueCN.Fix();
                            sA_CollectingSample.fateSample = item.fateSample.Fix();
                            sA_CollectingSample.noteCN = item.noteCN.Fix();
                            sA_CollectingSample.sampleNameEN = item.sampleNameEN.Fix();
                            sA_CollectingSample.tissueEN = item.tissueEN.Fix();
                            sA_CollectingSample.noteEN = item.noteEN.Fix();
                            sA_CollectingSample.Insert();
                        }
                        foreach (SAAS_Diagnostic item2 in list4)
                        {
                            SA_Diagnostic sA_Diagnostic = new SA_Diagnostic();
                            sA_Diagnostic.id = Guid.NewGuid();
                            sA_Diagnostic.Pid = sA_NewProject.Pid;
                            sA_Diagnostic.standard = item2.standard.Fix();
                            sA_Diagnostic.indexTest = item2.indexTest.Fix();
                            sA_Diagnostic.targetCondition = item2.targetCondition.Fix();
                            sA_Diagnostic.difficultCondition = item2.difficultCondition.Fix();
                            sA_Diagnostic.standardEn = item2.standardEn.Fix();
                            sA_Diagnostic.indexTestEn = item2.indexTestEn.Fix();
                            sA_Diagnostic.targetConditionEn = item2.targetConditionEn.Fix();
                            sA_Diagnostic.difficultConditionEn = item2.difficultConditionEn.Fix();
                            sA_Diagnostic.Insert();
                        }
                        foreach (SAAS_Interventions item3 in list5)
                        {
                            SA_Interventions sA_Interventions = new SA_Interventions();
                            sA_Interventions.inId = Guid.NewGuid();
                            sA_Interventions.Pid = sA_NewProject.Pid;
                            sA_Interventions.groupsCN = item3.groupsCN.Fix();
                            sA_Interventions.sampleSize = item3.sampleSize;
                            sA_Interventions.groupsEN = item3.groupsEN.Fix();
                            sA_Interventions.interventionCN = item3.interventionCN.Fix();
                            sA_Interventions.interventionCode = item3.interventionCode.Fix();
                            sA_Interventions.interventionEN = item3.interventionEN.Fix();
                            sA_Interventions.Insert();
                        }
                        foreach (SAAS_Outcomes item4 in list6)
                        {
                            SA_Outcomes sA_Outcomes = new SA_Outcomes();
                            sA_Outcomes.ouId = Guid.NewGuid();
                            sA_Outcomes.Pid = sA_NewProject.Pid;
                            sA_Outcomes.outcomeNameCN = item4.outcomeNameCN.Fix();
                            sA_Outcomes.pointerType = item4.pointerType.Fix();
                            sA_Outcomes.outcomeNameEN = item4.outcomeNameEN.Fix();
                            sA_Outcomes.measureTimeCN = item4.measureTimeCN.Fix();
                            sA_Outcomes.measureMethodCN = item4.measureMethodCN.Fix();
                            sA_Outcomes.measureTimeEN = item4.measureTimeEN.Fix();
                            sA_Outcomes.measureMethodEN = item4.measureMethodEN.Fix();
                            sA_Outcomes.Insert();
                        }
                        foreach (SAAS_ResearchAddress item5 in list7)
                        {
                            SA_ResearchAddress sA_ResearchAddress = new SA_ResearchAddress();
                            sA_ResearchAddress.raId = Guid.NewGuid();
                            sA_ResearchAddress.Pid = sA_NewProject.Pid;
                            sA_ResearchAddress.countryCN = item5.countryCN.Fix();
                            sA_ResearchAddress.provinceCN = item5.provinceCN.Fix();
                            sA_ResearchAddress.cityCN = item5.cityCN.Fix();
                            sA_ResearchAddress.countryEN = item5.countryEN.Fix();
                            sA_ResearchAddress.provinceEN = item5.provinceEN.Fix();
                            sA_ResearchAddress.cityEN = item5.cityEN.Fix();
                            sA_ResearchAddress.hospitalCN = item5.hospitalCN.Fix();
                            sA_ResearchAddress.levelInstitutionCN = item5.levelInstitutionCN.Fix();
                            sA_ResearchAddress.hospitalEN = item5.hospitalEN.Fix();
                            sA_ResearchAddress.levelInstitutionEN = item5.levelInstitutionEN.Fix();
                            sA_ResearchAddress.Insert();
                        }
                        foreach (SAAS_SecondarySponsor item6 in list8)
                        {
                            SA_SecondarySponsor sA_SecondarySponsor = new SA_SecondarySponsor();
                            sA_SecondarySponsor.ssId = Guid.NewGuid();
                            sA_SecondarySponsor.Pid = sA_NewProject.Pid;
                            sA_SecondarySponsor.countryCN = item6.countryCN.Fix();
                            sA_SecondarySponsor.provinceCN = item6.provinceCN.Fix();
                            sA_SecondarySponsor.cityCN = item6.cityCN.Fix();
                            sA_SecondarySponsor.countryEN = item6.countryEN.Fix();
                            sA_SecondarySponsor.provinceEN = item6.provinceEN.Fix();
                            sA_SecondarySponsor.cityEN = item6.cityEN.Fix();
                            sA_SecondarySponsor.institutionCN = item6.institutionCN.Fix();
                            sA_SecondarySponsor.specificAddressCN = item6.specificAddressCN.Fix();
                            sA_SecondarySponsor.institutionEN = item6.institutionEN.Fix();
                            sA_SecondarySponsor.specificAddressEN = item6.specificAddressEN.Fix();
                            sA_SecondarySponsor.Insert();
                        }
                        Console.WriteLine($"complete {sA_NewProject.ReleaseNumber}  {sA_NewProject.regNumber} {sA_NewProject.publicTitleCN}");
                    }
                }
            }
            Console.WriteLine("complete");
        }
        public static void Fun4()
        {
            Console.WriteLine("F4");
            AutoMapper.Mapper.Initialize(cfg =>
            {
                cfg.CreateMap<SA_NewProject, SA_NewProjectHistory>();
                cfg.CreateMap<SA_NewProjectHistory, SA_NewProject>();
                cfg.CreateMap<SA_Interventions, SA_InterventionsHistory>();
                cfg.CreateMap<SA_InterventionsHistory, SA_Interventions>();
                cfg.CreateMap<SA_Outcomes, SA_OutcomesHistory>();
                cfg.CreateMap<SA_OutcomesHistory, SA_Outcomes>();
                cfg.CreateMap<SA_ResearchAddress, SA_ResearchAddressHistory>();
                cfg.CreateMap<SA_ResearchAddressHistory, SA_ResearchAddress>();
                cfg.CreateMap<SA_Diagnostic, SA_DiagnosticHistory>();
                cfg.CreateMap<SA_DiagnosticHistory, SA_Diagnostic>();
                cfg.CreateMap<SA_SecondarySponsor, SA_SecondarySponsorHistory>();
                cfg.CreateMap<SA_SecondarySponsorHistory, SA_SecondarySponsor>();
                cfg.CreateMap<SA_CollectingSample, SA_CollectingSampleHistory>();
                cfg.CreateMap<SA_CollectingSampleHistory, SA_CollectingSample>();
            });
            using (var db = new targetDB())
            {
                var sql = PetaPoco.Sql.Builder.Select("*").From("SA_NewProject").Where("regNumber like 'ITMCTR%' AND ReleaseNumber is not null");
                var list = db.Query<ITMCTR.Core.Models.SA_NewProject>(sql).ToList();
                foreach (var p in list)
                {
                    var ph = new SA_NewProjectHistory();
                    ph = Mapper.Map<SA_NewProjectHistory>(p);
                    ph.Phid = Guid.NewGuid();
                    //ph.PrefId = record.PrefId;
                    var strVersion = "1.0.0";
                    var phlist = db.Query<SA_NewProjectHistory>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.version).ToList();
                    if (phlist.Count == 0)
                    {
                        ph.version = strVersion;
                    }
                    else
                    {
                        var firPh = phlist.FirstOrDefault();
                        if (firPh != null && string.IsNullOrWhiteSpace(firPh.version))
                        {
                            ph.version = strVersion;
                        }
                        else
                        {
                            var version = new Version(phlist.FirstOrDefault().version);
                            var BuildNo = version.Build;
                            var MinorNo = version.Minor;
                            var MajorNo = version.Major;
                            if (version.Build + 1 >= 10)
                            {
                                BuildNo = 0;
                                MinorNo += 1;
                                if (MinorNo >= 10)
                                {
                                    MinorNo = 0;
                                    MajorNo += 1;
                                }
                            }
                            else
                            {
                                BuildNo += 1;
                            }
                            version = new Version($"{MajorNo}.{MinorNo}.{BuildNo}");
                            ph.version = version.ToString();
                        }
                    }
                    ph.operateTime = p.modifyTime;
                    db.Insert(ph);
                    #region 干预措施
                    var InterventionsList = db.Query<SA_Interventions>(" WHERE Pid = @0 ", p.Pid).ToList();
                    foreach (var it in InterventionsList)
                    {
                        var sa_ih = new SA_InterventionsHistory();
                        sa_ih = Mapper.Map<SA_InterventionsHistory>(it);
                        sa_ih.inId = Guid.NewGuid();
                        sa_ih.Phid = ph.Phid;
                        db.Insert(sa_ih);
                    }
                    #endregion
                    #region 测试指标
                    var OutcomesList = db.Query<SA_Outcomes>(" WHERE Pid = @0 ", p.Pid).ToList();
                    foreach (var it in OutcomesList)
                    {
                        var sa_oh = new SA_OutcomesHistory();
                        sa_oh = Mapper.Map<SA_OutcomesHistory>(it);
                        sa_oh.ouId = Guid.NewGuid();
                        sa_oh.Phid = ph.Phid;
                        db.Insert(sa_oh);
                    }
                    #endregion
                    #region 研究实施地点
                    var ResearchAddressList = db.Query<SA_ResearchAddress>(" WHERE Pid = @0 ", p.Pid).ToList();
                    foreach (var it in ResearchAddressList)
                    {
                        var sa_rah = new SA_ResearchAddressHistory();
                        sa_rah = Mapper.Map<SA_ResearchAddressHistory>(it);
                        sa_rah.raId = Guid.NewGuid();
                        sa_rah.Phid = ph.Phid;
                        db.Insert(sa_rah);
                    }
                    #endregion
                    #region 诊断试验
                    var DiagnosticList = db.Query<SA_Diagnostic>(" WHERE Pid = @0 ", p.Pid).ToList();
                    foreach (var it in DiagnosticList)
                    {
                        var sa_rah = new SA_DiagnosticHistory();
                        sa_rah = Mapper.Map<SA_DiagnosticHistory>(it);
                        sa_rah.id = Guid.NewGuid();
                        sa_rah.Phid = ph.Phid;
                        db.Insert(sa_rah);
                    }
                    #endregion
                    #region 试验主办单位
                    var SecondarySponsorList = db.Query<SA_SecondarySponsor>(" WHERE Pid = @0 ", p.Pid).ToList();
                    foreach (var it in SecondarySponsorList)
                    {
                        var sa_ssh = new SA_SecondarySponsorHistory();
                        sa_ssh = Mapper.Map<SA_SecondarySponsorHistory>(it);
                        sa_ssh.ssId = Guid.NewGuid();
                        sa_ssh.Phid = ph.Phid;
                        db.Insert(sa_ssh);
                    }
                    #endregion
                    #region 采集人体标本
                    var CollectingSampleList = db.Query<SA_CollectingSample>(" WHERE Pid = @0 ", p.Pid).ToList();
                    foreach (var it in CollectingSampleList)
                    {
                        var sa_csh = new SA_CollectingSampleHistory();
                        sa_csh = Mapper.Map<SA_CollectingSampleHistory>(it);
                        sa_csh.csId = Guid.NewGuid();
                        sa_csh.Phid = ph.Phid;
                        db.Insert(sa_csh);
                    }
                    #endregion
                }
            }
            Console.WriteLine("complete");
        }
    }
    public static class ExtStr
    {
        public static string Fix(this string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;
            text = text.Replace("&#115;cript", "script");
            text = text.Replace("ma&#115;ter", "master");
            text = text.Replace("&#32;", " ");
            text = text.Replace("ins&#101;&#114;t", "insert");
            text = text.Replace("de&#108;&#101;te", "delete");
            text = text.Replace("u&#112;&#100;ate", "update");
            text = text.Replace("&#101;xec", "exec");
            text = text.Replace("&#39;", "'");
            return text;
        }
        public static string Fix2(this string text)
        {
            var temp = text;
            if (string.IsNullOrEmpty(text))
                return text;
            text = text.Replace("script", "&#115;cript");
            text = text.Replace("master", "ma&#115;ter");
            text = text.Replace(" ", "&#32;");
            text = text.Replace("insert", "ins&#101;&#114;t");
            text = text.Replace("delete", "de&#108;&#101;te");
            text = text.Replace("update", "u&#112;&#100;ate");
            text = text.Replace("exec", "&#101;xec");
            text = text.Replace("'", "&#39;");
            if (text.Length > 500)
                Console.WriteLine($"{text.Length}:{temp}");
            return text;
        }
    }
}
