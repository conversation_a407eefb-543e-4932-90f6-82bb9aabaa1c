<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
	</startup>
	<appSettings>
		<add key="Elapsed" value="10"/>
		<add key="PathSource" value="d:\"/>
		<add key="PathTarget" value="d:\"/>
		<add key="TaskSysUserId" value="bb6383ed-9148-4b16-8153-48ada58fd4b7"/>
	</appSettings>
	<connectionStrings>
		<add name="default" connectionString="data source=.;initial catalog=ITMCTR;persist security info=True;user id=sa;password=aaaaaa;" providerName="System.Data.SqlClient"/>
		<add name="default2" connectionString="data source=.;initial catalog=Chictr;persist security info=True;user id=sa;password=aaaaaa;" providerName="System.Data.SqlClient"/>
		<add name="default3" connectionString="data source=.;initial catalog=Chictr2;persist security info=True;user id=sa;password=aaaaaa;" providerName="System.Data.SqlClient"/>
	</connectionStrings>
</configuration>
