<#@ include file="PetaPoco.Core.ttinclude" #>
<#
	// Settings
    ConnectionStringName = "default2";			// Uses last connection string in config if not specified
    Namespace = "ITMCTR.Core.Models2";
    RepoName = "SourceDB";
    GenerateOperations = true;
	GeneratePocos = true;
	GenerateCommon = true;
	ClassPrefix = "";
	ClassSuffix = "";
	TrackModifiedColumns = false;
	ExplicitColumns = true;
	ExcludePrefix = new string[] {}; // Exclude tables by prefix.

    // Read schema
	var tables = LoadTables();


/*
	// Tweak Schema
	tables["tablename"].Ignore = true;							// To ignore a table
	tables["tablename"].ClassName = "newname";					// To change the class name of a table
	tables["tablename"]["columnname"].Ignore = true;			// To ignore a column
	tables["tablename"]["columnname"].PropertyName="newname";	// To change the property name of a column
	tables["tablename"]["columnname"].PropertyType="bool";		// To change the property type of a column
*/

	// Generate output
	if (tables.Count>0)
	{
#>
<#@ include file="PetaPoco.Generator.ttinclude" #>
<# } #>