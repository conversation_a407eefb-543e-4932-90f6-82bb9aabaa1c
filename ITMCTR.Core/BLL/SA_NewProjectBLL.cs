using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_NewProjectBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_NewProject entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_NewProject entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_NewProject>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_NewProject entity)
        {
			var res = db.Delete<SA_NewProject>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid Pid)
        {
			var res = db.Delete<SA_NewProject>(Pid);
            return res > 0 ? true : false;
        }

        public SA_NewProject GetById(Guid Pid)
        {
            var entity = db.FirstOrDefault<SA_NewProject>("WHERE Pid = @0", Pid);
            return entity;
        }
	    public List<SA_NewProject> GetAll()
        {
            var list = db.Query<SA_NewProject>("SELECT * FROM [SA_NewProject]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_NewProject> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_NewProject>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
