using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_DiagnosticBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_Diagnostic entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_Diagnostic entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_Diagnostic>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_Diagnostic entity)
        {
			var res = db.Delete<SA_Diagnostic>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid id)
        {
			var res = db.Delete<SA_Diagnostic>(id);
            return res > 0 ? true : false;
        }

        public SA_Diagnostic GetById(Guid id)
        {
            var entity = db.FirstOrDefault<SA_Diagnostic>("WHERE id = @0", id);
            return entity;
        }
	    public List<SA_Diagnostic> GetAll()
        {
            var list = db.Query<SA_Diagnostic>("SELECT * FROM [SA_Diagnostic]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_Diagnostic> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_Diagnostic>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
