using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_Diagnostic_copyBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_Diagnostic_copy entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_Diagnostic_copy entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_Diagnostic_copy>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_Diagnostic_copy entity)
        {
			var res = db.Delete<SA_Diagnostic_copy>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid id)
        {
			var res = db.Delete<SA_Diagnostic_copy>(id);
            return res > 0 ? true : false;
        }

        public SA_Diagnostic_copy GetById(Guid id)
        {
            var entity = db.FirstOrDefault<SA_Diagnostic_copy>("WHERE id = @0", id);
            return entity;
        }
	    public List<SA_Diagnostic_copy> GetAll()
        {
            var list = db.Query<SA_Diagnostic_copy>("SELECT * FROM [SA_Diagnostic_copy]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_Diagnostic_copy> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_Diagnostic_copy>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
