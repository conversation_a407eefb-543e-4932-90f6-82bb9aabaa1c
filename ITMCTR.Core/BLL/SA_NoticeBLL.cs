using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_NoticeBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_Notice entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_Notice entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_Notice>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_Notice entity)
        {
			var res = db.Delete<SA_Notice>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid Nid)
        {
			var res = db.Delete<SA_Notice>(Nid);
            return res > 0 ? true : false;
        }

        public SA_Notice GetById(Guid Nid)
        {
            var entity = db.FirstOrDefault<SA_Notice>("WHERE Nid = @0", Nid);
            return entity;
        }
	    public List<SA_Notice> GetAll()
        {
            var list = db.Query<SA_Notice>("SELECT * FROM [SA_Notice]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_Notice> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_Notice>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
