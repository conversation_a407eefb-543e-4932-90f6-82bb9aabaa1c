using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_UsersBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_Users entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_Users entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_Users>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_Users entity)
        {
			var res = db.Delete<SA_Users>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid Uid)
        {
			var res = db.Delete<SA_Users>(Uid);
            return res > 0 ? true : false;
        }

        public SA_Users GetById(Guid Uid)
        {
            var entity = db.FirstOrDefault<SA_Users>("WHERE Uid = @0", Uid);
            return entity;
        }
	    public List<SA_Users> GetAll()
        {
            var list = db.Query<SA_Users>("SELECT * FROM [SA_Users]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_Users> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_Users>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
