using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_OutcomesBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_Outcomes entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_Outcomes entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_Outcomes>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_Outcomes entity)
        {
			var res = db.Delete<SA_Outcomes>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid ouId)
        {
			var res = db.Delete<SA_Outcomes>(ouId);
            return res > 0 ? true : false;
        }

        public SA_Outcomes GetById(Guid ouId)
        {
            var entity = db.FirstOrDefault<SA_Outcomes>("WHERE ouId = @0", ouId);
            return entity;
        }
	    public List<SA_Outcomes> GetAll()
        {
            var list = db.Query<SA_Outcomes>("SELECT * FROM [SA_Outcomes]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_Outcomes> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_Outcomes>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
