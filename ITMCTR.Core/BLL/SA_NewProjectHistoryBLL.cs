using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_NewProjectHistoryBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_NewProjectHistory entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_NewProjectHistory entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_NewProjectHistory>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_NewProjectHistory entity)
        {
			var res = db.Delete<SA_NewProjectHistory>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid Phid)
        {
			var res = db.Delete<SA_NewProjectHistory>(Phid);
            return res > 0 ? true : false;
        }

        public SA_NewProjectHistory GetById(Guid Phid)
        {
            var entity = db.FirstOrDefault<SA_NewProjectHistory>("WHERE Phid = @0", Phid);
            return entity;
        }
	    public List<SA_NewProjectHistory> GetAll()
        {
            var list = db.Query<SA_NewProjectHistory>("SELECT * FROM [SA_NewProjectHistory]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_NewProjectHistory> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_NewProjectHistory>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
