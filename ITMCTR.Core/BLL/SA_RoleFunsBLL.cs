using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_RoleFunsBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_RoleFuns entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_RoleFuns entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_RoleFuns>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_RoleFuns entity)
        {
			var res = db.Delete<SA_RoleFuns>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid RoleFunId)
        {
			var res = db.Delete<SA_RoleFuns>(RoleFunId);
            return res > 0 ? true : false;
        }

        public SA_RoleFuns GetById(Guid RoleFunId)
        {
            var entity = db.FirstOrDefault<SA_RoleFuns>("WHERE RoleFunId = @0", RoleFunId);
            return entity;
        }
	    public List<SA_RoleFuns> GetAll()
        {
            var list = db.Query<SA_RoleFuns>("SELECT * FROM [SA_RoleFuns]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_RoleFuns> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_RoleFuns>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
