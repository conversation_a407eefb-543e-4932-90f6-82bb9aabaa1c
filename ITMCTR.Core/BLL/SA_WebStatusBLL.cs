using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_WebStatusBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_WebStatus entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_WebStatus entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_WebStatus>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_WebStatus entity)
        {
			var res = db.Delete<SA_WebStatus>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid WebStatusId)
        {
			var res = db.Delete<SA_WebStatus>(WebStatusId);
            return res > 0 ? true : false;
        }

        public SA_WebStatus GetById(Guid WebStatusId)
        {
            var entity = db.FirstOrDefault<SA_WebStatus>("WHERE WebStatusId = @0", WebStatusId);
            return entity;
        }
	    public List<SA_WebStatus> GetAll()
        {
            var list = db.Query<SA_WebStatus>("SELECT * FROM [SA_WebStatus]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_WebStatus> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_WebStatus>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
