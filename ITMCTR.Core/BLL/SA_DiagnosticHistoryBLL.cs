using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_DiagnosticHistoryBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_DiagnosticHistory entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_DiagnosticHistory entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_DiagnosticHistory>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_DiagnosticHistory entity)
        {
			var res = db.Delete<SA_DiagnosticHistory>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid id)
        {
			var res = db.Delete<SA_DiagnosticHistory>(id);
            return res > 0 ? true : false;
        }

        public SA_DiagnosticHistory GetById(Guid id)
        {
            var entity = db.FirstOrDefault<SA_DiagnosticHistory>("WHERE id = @0", id);
            return entity;
        }
	    public List<SA_DiagnosticHistory> GetAll()
        {
            var list = db.Query<SA_DiagnosticHistory>("SELECT * FROM [SA_DiagnosticHistory]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_DiagnosticHistory> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_DiagnosticHistory>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
