using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_SysUserBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_SysUser entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_SysUser entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_SysUser>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_SysUser entity)
        {
			var res = db.Delete<SA_SysUser>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid Uid)
        {
			var res = db.Delete<SA_SysUser>(Uid);
            return res > 0 ? true : false;
        }

        public SA_SysUser GetById(Guid Uid)
        {
            var entity = db.FirstOrDefault<SA_SysUser>("WHERE Uid = @0", Uid);
            return entity;
        }
	    public List<SA_SysUser> GetAll()
        {
            var list = db.Query<SA_SysUser>("SELECT * FROM [SA_SysUser]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_SysUser> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_SysUser>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
