using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ResearchAddressHistoryBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_ResearchAddressHistory entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_ResearchAddressHistory entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_ResearchAddressHistory>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_ResearchAddressHistory entity)
        {
			var res = db.Delete<SA_ResearchAddressHistory>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid raId)
        {
			var res = db.Delete<SA_ResearchAddressHistory>(raId);
            return res > 0 ? true : false;
        }

        public SA_ResearchAddressHistory GetById(Guid raId)
        {
            var entity = db.FirstOrDefault<SA_ResearchAddressHistory>("WHERE raId = @0", raId);
            return entity;
        }
	    public List<SA_ResearchAddressHistory> GetAll()
        {
            var list = db.Query<SA_ResearchAddressHistory>("SELECT * FROM [SA_ResearchAddressHistory]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_ResearchAddressHistory> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_ResearchAddressHistory>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
