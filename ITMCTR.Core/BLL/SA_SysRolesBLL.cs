using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_SysRolesBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_SysRoles entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_SysRoles entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_SysRoles>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_SysRoles entity)
        {
			var res = db.Delete<SA_SysRoles>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid RoleId)
        {
			var res = db.Delete<SA_SysRoles>(RoleId);
            return res > 0 ? true : false;
        }

        public SA_SysRoles GetById(Guid RoleId)
        {
            var entity = db.FirstOrDefault<SA_SysRoles>("WHERE RoleId = @0", RoleId);
            return entity;
        }
	    public List<SA_SysRoles> GetAll()
        {
            var list = db.Query<SA_SysRoles>("SELECT * FROM [SA_SysRoles]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_SysRoles> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_SysRoles>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
