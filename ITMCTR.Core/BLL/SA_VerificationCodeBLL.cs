using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_VerificationCodeBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_VerificationCode entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_VerificationCode entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_VerificationCode>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_VerificationCode entity)
        {
			var res = db.Delete<SA_VerificationCode>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid Vid)
        {
			var res = db.Delete<SA_VerificationCode>(Vid);
            return res > 0 ? true : false;
        }

        public SA_VerificationCode GetById(Guid Vid)
        {
            var entity = db.FirstOrDefault<SA_VerificationCode>("WHERE Vid = @0", Vid);
            return entity;
        }
	    public List<SA_VerificationCode> GetAll()
        {
            var list = db.Query<SA_VerificationCode>("SELECT * FROM [SA_VerificationCode]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_VerificationCode> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_VerificationCode>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
