using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_SysFunsBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_SysFuns entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_SysFuns entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_SysFuns>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_SysFuns entity)
        {
			var res = db.Delete<SA_SysFuns>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid FunId)
        {
			var res = db.Delete<SA_SysFuns>(FunId);
            return res > 0 ? true : false;
        }

        public SA_SysFuns GetById(Guid FunId)
        {
            var entity = db.FirstOrDefault<SA_SysFuns>("WHERE FunId = @0", FunId);
            return entity;
        }
	    public List<SA_SysFuns> GetAll()
        {
            var list = db.Query<SA_SysFuns>("SELECT * FROM [SA_SysFuns]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_SysFuns> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_SysFuns>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
