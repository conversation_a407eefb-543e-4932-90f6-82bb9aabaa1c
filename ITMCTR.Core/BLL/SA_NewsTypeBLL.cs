using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_NewsTypeBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_NewsType entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_NewsType entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_NewsType>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_NewsType entity)
        {
			var res = db.Delete<SA_NewsType>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid NtId)
        {
			var res = db.Delete<SA_NewsType>(NtId);
            return res > 0 ? true : false;
        }

        public SA_NewsType GetById(Guid NtId)
        {
            var entity = db.FirstOrDefault<SA_NewsType>("WHERE NtId = @0", NtId);
            return entity;
        }
	    public List<SA_NewsType> GetAll()
        {
            var list = db.Query<SA_NewsType>("SELECT * FROM [SA_NewsType]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_NewsType> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_NewsType>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
