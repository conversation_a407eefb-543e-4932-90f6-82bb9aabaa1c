using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ChangeAuditorRecordBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_ChangeAuditorRecord entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_ChangeAuditorRecord entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_ChangeAuditorRecord>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_ChangeAuditorRecord entity)
        {
			var res = db.Delete<SA_ChangeAuditorRecord>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid CarId)
        {
			var res = db.Delete<SA_ChangeAuditorRecord>(CarId);
            return res > 0 ? true : false;
        }

        public SA_ChangeAuditorRecord GetById(Guid CarId)
        {
            var entity = db.FirstOrDefault<SA_ChangeAuditorRecord>("WHERE CarId = @0", CarId);
            return entity;
        }
	    public List<SA_ChangeAuditorRecord> GetAll()
        {
            var list = db.Query<SA_ChangeAuditorRecord>("SELECT * FROM [SA_ChangeAuditorRecord]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_ChangeAuditorRecord> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_ChangeAuditorRecord>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
