using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_SecondarySponsorBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_SecondarySponsor entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_SecondarySponsor entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_SecondarySponsor>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_SecondarySponsor entity)
        {
			var res = db.Delete<SA_SecondarySponsor>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid ssId)
        {
			var res = db.Delete<SA_SecondarySponsor>(ssId);
            return res > 0 ? true : false;
        }

        public SA_SecondarySponsor GetById(Guid ssId)
        {
            var entity = db.FirstOrDefault<SA_SecondarySponsor>("WHERE ssId = @0", ssId);
            return entity;
        }
	    public List<SA_SecondarySponsor> GetAll()
        {
            var list = db.Query<SA_SecondarySponsor>("SELECT * FROM [SA_SecondarySponsor]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_SecondarySponsor> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_SecondarySponsor>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
