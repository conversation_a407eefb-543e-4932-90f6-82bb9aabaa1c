using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ResearchAddressBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_ResearchAddress entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_ResearchAddress entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_ResearchAddress>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_ResearchAddress entity)
        {
			var res = db.Delete<SA_ResearchAddress>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid raId)
        {
			var res = db.Delete<SA_ResearchAddress>(raId);
            return res > 0 ? true : false;
        }

        public SA_ResearchAddress GetById(Guid raId)
        {
            var entity = db.FirstOrDefault<SA_ResearchAddress>("WHERE raId = @0", raId);
            return entity;
        }
	    public List<SA_ResearchAddress> GetAll()
        {
            var list = db.Query<SA_ResearchAddress>("SELECT * FROM [SA_ResearchAddress]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_ResearchAddress> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_ResearchAddress>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
