using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_CollectingSampleBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_CollectingSample entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_CollectingSample entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_CollectingSample>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_CollectingSample entity)
        {
			var res = db.Delete<SA_CollectingSample>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid csId)
        {
			var res = db.Delete<SA_CollectingSample>(csId);
            return res > 0 ? true : false;
        }

        public SA_CollectingSample GetById(Guid csId)
        {
            var entity = db.FirstOrDefault<SA_CollectingSample>("WHERE csId = @0", csId);
            return entity;
        }
	    public List<SA_CollectingSample> GetAll()
        {
            var list = db.Query<SA_CollectingSample>("SELECT * FROM [SA_CollectingSample]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_CollectingSample> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_CollectingSample>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
