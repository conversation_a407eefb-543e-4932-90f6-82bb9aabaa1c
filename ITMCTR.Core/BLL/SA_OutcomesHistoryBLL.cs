using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_OutcomesHistoryBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_OutcomesHistory entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_OutcomesHistory entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_OutcomesHistory>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_OutcomesHistory entity)
        {
			var res = db.Delete<SA_OutcomesHistory>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid ouId)
        {
			var res = db.Delete<SA_OutcomesHistory>(ouId);
            return res > 0 ? true : false;
        }

        public SA_OutcomesHistory GetById(Guid ouId)
        {
            var entity = db.FirstOrDefault<SA_OutcomesHistory>("WHERE ouId = @0", ouId);
            return entity;
        }
	    public List<SA_OutcomesHistory> GetAll()
        {
            var list = db.Query<SA_OutcomesHistory>("SELECT * FROM [SA_OutcomesHistory]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_OutcomesHistory> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_OutcomesHistory>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
