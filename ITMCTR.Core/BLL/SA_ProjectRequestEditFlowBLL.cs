using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ProjectRequestEditFlowBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_ProjectRequestEditFlow entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_ProjectRequestEditFlow entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_ProjectRequestEditFlow>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_ProjectRequestEditFlow entity)
        {
			var res = db.Delete<SA_ProjectRequestEditFlow>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid PrefId)
        {
			var res = db.Delete<SA_ProjectRequestEditFlow>(PrefId);
            return res > 0 ? true : false;
        }

        public SA_ProjectRequestEditFlow GetById(Guid PrefId)
        {
            var entity = db.FirstOrDefault<SA_ProjectRequestEditFlow>("WHERE PrefId = @0", PrefId);
            return entity;
        }
	    public List<SA_ProjectRequestEditFlow> GetAll()
        {
            var list = db.Query<SA_ProjectRequestEditFlow>("SELECT * FROM [SA_ProjectRequestEditFlow]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_ProjectRequestEditFlow> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_ProjectRequestEditFlow>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
