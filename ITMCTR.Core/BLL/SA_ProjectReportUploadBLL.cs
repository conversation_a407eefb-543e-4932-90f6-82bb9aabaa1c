using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ProjectReportUploadBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_ProjectReportUpload entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_ProjectReportUpload entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_ProjectReportUpload>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_ProjectReportUpload entity)
        {
			var res = db.Delete<SA_ProjectReportUpload>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid PruId)
        {
			var res = db.Delete<SA_ProjectReportUpload>(PruId);
            return res > 0 ? true : false;
        }

        public SA_ProjectReportUpload GetById(Guid PruId)
        {
            var entity = db.FirstOrDefault<SA_ProjectReportUpload>("WHERE PruId = @0", PruId);
            return entity;
        }
	    public List<SA_ProjectReportUpload> GetAll()
        {
            var list = db.Query<SA_ProjectReportUpload>("SELECT * FROM [SA_ProjectReportUpload]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_ProjectReportUpload> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_ProjectReportUpload>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
