using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ProjectVerifyTaskFlowRecordBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_ProjectVerifyTaskFlowRecord entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_ProjectVerifyTaskFlowRecord entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_ProjectVerifyTaskFlowRecord>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_ProjectVerifyTaskFlowRecord entity)
        {
			var res = db.Delete<SA_ProjectVerifyTaskFlowRecord>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid PvtfrId)
        {
			var res = db.Delete<SA_ProjectVerifyTaskFlowRecord>(PvtfrId);
            return res > 0 ? true : false;
        }

        public SA_ProjectVerifyTaskFlowRecord GetById(Guid PvtfrId)
        {
            var entity = db.FirstOrDefault<SA_ProjectVerifyTaskFlowRecord>("WHERE PvtfrId = @0", PvtfrId);
            return entity;
        }
	    public List<SA_ProjectVerifyTaskFlowRecord> GetAll()
        {
            var list = db.Query<SA_ProjectVerifyTaskFlowRecord>("SELECT * FROM [SA_ProjectVerifyTaskFlowRecord]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_ProjectVerifyTaskFlowRecord> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_ProjectVerifyTaskFlowRecord>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
