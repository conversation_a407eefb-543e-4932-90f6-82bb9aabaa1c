using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_WebsiteInformationBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_WebsiteInformation entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_WebsiteInformation entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_WebsiteInformation>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_WebsiteInformation entity)
        {
			var res = db.Delete<SA_WebsiteInformation>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid WiId)
        {
			var res = db.Delete<SA_WebsiteInformation>(WiId);
            return res > 0 ? true : false;
        }

        public SA_WebsiteInformation GetById(Guid WiId)
        {
            var entity = db.FirstOrDefault<SA_WebsiteInformation>("WHERE WiId = @0", WiId);
            return entity;
        }
	    public List<SA_WebsiteInformation> GetAll()
        {
            var list = db.Query<SA_WebsiteInformation>("SELECT * FROM [SA_WebsiteInformation]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_WebsiteInformation> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_WebsiteInformation>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
