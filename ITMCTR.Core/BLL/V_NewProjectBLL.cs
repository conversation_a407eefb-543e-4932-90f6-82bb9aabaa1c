using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class V_NewProjectBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(V_NewProject entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
    public List<V_NewProject> GetAll()
        {
            var list = db.Query<V_NewProject>("SELECT * FROM [V_NewProject]");
            return list.ToList();
        }
    
        public PetaPoco.Page<V_NewProject> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<V_NewProject>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
