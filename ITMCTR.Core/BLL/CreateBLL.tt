<#@ template language="C#" hostspecific="True"#>  
<#@include file="Manager.ttinclude"#>  
<#@include file="PetaPoco.Core.ttinclude"#>  
<#    var manager = Manager.Create(Host, GenerationEnvironment); #> 

<#
    // Settings
    ConnectionStringName = "default";			// Uses last connection string in config if not specified
    Namespace = "ITMCTR.Core.BLL";
    RepoName = "ITMCTRDatabase";
    GenerateOperations = true;
    GeneratePocos = true;
    GenerateCommon = true;
    ClassPrefix = "";
    ClassSuffix = "";
    TrackModifiedColumns = false;
    ExplicitColumns = true;
    ExcludePrefix = new string[] {}; // Exclude tables by prefix.
    var tables = LoadTables();
    
#>
<#
    foreach(Table tbl in from t in tables where !t.Ignore select t)
    {
        manager.StartNewFile(tbl.ClassName+"BLL.cs");
        string PkName= "";
        string PkType= "";
        if (tbl.PK!=null && !tbl.PK.IsAutoIncrement) 
        {
            PkName =tbl.PK.Name;
            PkType = tbl.PK.PropertyType;
        }
		var PkList = tbl.Columns.Where(x=> x.IsPK);

#>
using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace <#=Namespace#>
{
    public partial class <#=tbl.ClassName#>BLL
    {
        public <#=RepoName#> db = new <#=RepoName#>();

        public bool Insert(<#=tbl.ClassName#> entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
<# if(PkList.Count() == 1){#>
        public bool Update(<#=tbl.ClassName#> entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
<#}if(!tbl.IsView){ if(PkList.Count() == 1){#>
        public bool Update(string Sql)
        {
            var res = db.Update<<#=tbl.ClassName#>>(Sql);
            return res > 0 ? true : false;
        }
<#}#>
        public bool Delete(<#=tbl.ClassName#> entity)
        {
<# 
			if(PkList.Count() > 1)
			{
				var fs = "";
				var ps = "";
				var index = 0;
				foreach(Column column in PkList)
				{
					fs += " AND " + column.Name + " = @" + index.ToString();
					ps += ", entity." + column.Name;
					index++;
				}
				fs = fs.Substring(4, fs.Length-4);
#>
			var res = db.Delete<<#=tbl.ClassName#>>("WHERE <#=fs#>"<#=ps#>);	
<#
			}
			else
			{
#>
			var res = db.Delete<<#=tbl.ClassName#>>(entity);
<#
			}
#>
            return res > 0 ? true : false;
        }
		
        public bool Delete(<#=PkType#> <#=PkName#>)
        {
			var res = db.Delete<<#=tbl.ClassName#>>(<#=PkName#>);
            return res > 0 ? true : false;
        }

<# if(PkList.Count() == 1){#>
        public <#=tbl.ClassName#> GetById(<#=PkType#> <#=PkName#>)
        {
            var entity = db.FirstOrDefault<<#=tbl.ClassName#>>("WHERE <#=PkName#> = @0", <#=PkName#>);
            return entity;
        }
<#}#>
	<# }#>
    public List<<#=tbl.ClassName#>> GetAll()
        {
            var list = db.Query<<#=tbl.ClassName#>>("SELECT * FROM [<#=tbl.ClassName#>]");
            return list.ToList();
        }
    
        public PetaPoco.Page<<#=tbl.ClassName#>> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<<#=tbl.ClassName#>>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
<#
        manager.EndBlock(); 
    }
        manager.Process(true); 
#>
