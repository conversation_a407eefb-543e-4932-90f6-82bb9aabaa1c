using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_InterventionsBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_Interventions entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_Interventions entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_Interventions>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_Interventions entity)
        {
			var res = db.Delete<SA_Interventions>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid inId)
        {
			var res = db.Delete<SA_Interventions>(inId);
            return res > 0 ? true : false;
        }

        public SA_Interventions GetById(Guid inId)
        {
            var entity = db.FirstOrDefault<SA_Interventions>("WHERE inId = @0", inId);
            return entity;
        }
	    public List<SA_Interventions> GetAll()
        {
            var list = db.Query<SA_Interventions>("SELECT * FROM [SA_Interventions]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_Interventions> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_Interventions>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
