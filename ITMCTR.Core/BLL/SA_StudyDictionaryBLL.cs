using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_StudyDictionaryBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_StudyDictionary entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_StudyDictionary entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_StudyDictionary>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_StudyDictionary entity)
        {
			var res = db.Delete<SA_StudyDictionary>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid Sid)
        {
			var res = db.Delete<SA_StudyDictionary>(Sid);
            return res > 0 ? true : false;
        }

        public SA_StudyDictionary GetById(Guid Sid)
        {
            var entity = db.FirstOrDefault<SA_StudyDictionary>("WHERE Sid = @0", Sid);
            return entity;
        }
	    public List<SA_StudyDictionary> GetAll()
        {
            var list = db.Query<SA_StudyDictionary>("SELECT * FROM [SA_StudyDictionary]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_StudyDictionary> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_StudyDictionary>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
