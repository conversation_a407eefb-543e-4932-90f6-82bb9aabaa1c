using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_MessagingBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_Messaging entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_Messaging entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_Messaging>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_Messaging entity)
        {
			var res = db.Delete<SA_Messaging>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid MessagingId)
        {
			var res = db.Delete<SA_Messaging>(MessagingId);
            return res > 0 ? true : false;
        }

        public SA_Messaging GetById(Guid MessagingId)
        {
            var entity = db.FirstOrDefault<SA_Messaging>("WHERE MessagingId = @0", MessagingId);
            return entity;
        }
	    public List<SA_Messaging> GetAll()
        {
            var list = db.Query<SA_Messaging>("SELECT * FROM [SA_Messaging]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_Messaging> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_Messaging>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
