using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_CollectingSampleHistoryBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_CollectingSampleHistory entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_CollectingSampleHistory entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_CollectingSampleHistory>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_CollectingSampleHistory entity)
        {
			var res = db.Delete<SA_CollectingSampleHistory>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid csId)
        {
			var res = db.Delete<SA_CollectingSampleHistory>(csId);
            return res > 0 ? true : false;
        }

        public SA_CollectingSampleHistory GetById(Guid csId)
        {
            var entity = db.FirstOrDefault<SA_CollectingSampleHistory>("WHERE csId = @0", csId);
            return entity;
        }
	    public List<SA_CollectingSampleHistory> GetAll()
        {
            var list = db.Query<SA_CollectingSampleHistory>("SELECT * FROM [SA_CollectingSampleHistory]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_CollectingSampleHistory> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_CollectingSampleHistory>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
