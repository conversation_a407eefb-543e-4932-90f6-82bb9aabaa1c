using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ProjectDataUploadBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_ProjectDataUpload entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_ProjectDataUpload entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_ProjectDataUpload>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_ProjectDataUpload entity)
        {
			var res = db.Delete<SA_ProjectDataUpload>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid PduId)
        {
			var res = db.Delete<SA_ProjectDataUpload>(PduId);
            return res > 0 ? true : false;
        }

        public SA_ProjectDataUpload GetById(Guid PduId)
        {
            var entity = db.FirstOrDefault<SA_ProjectDataUpload>("WHERE PduId = @0", PduId);
            return entity;
        }
	    public List<SA_ProjectDataUpload> GetAll()
        {
            var list = db.Query<SA_ProjectDataUpload>("SELECT * FROM [SA_ProjectDataUpload]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_ProjectDataUpload> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_ProjectDataUpload>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
