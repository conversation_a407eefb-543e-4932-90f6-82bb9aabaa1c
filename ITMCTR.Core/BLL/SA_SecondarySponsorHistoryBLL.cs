using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_SecondarySponsorHistoryBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_SecondarySponsorHistory entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_SecondarySponsorHistory entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_SecondarySponsorHistory>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_SecondarySponsorHistory entity)
        {
			var res = db.Delete<SA_SecondarySponsorHistory>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid ssId)
        {
			var res = db.Delete<SA_SecondarySponsorHistory>(ssId);
            return res > 0 ? true : false;
        }

        public SA_SecondarySponsorHistory GetById(Guid ssId)
        {
            var entity = db.FirstOrDefault<SA_SecondarySponsorHistory>("WHERE ssId = @0", ssId);
            return entity;
        }
	    public List<SA_SecondarySponsorHistory> GetAll()
        {
            var list = db.Query<SA_SecondarySponsorHistory>("SELECT * FROM [SA_SecondarySponsorHistory]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_SecondarySponsorHistory> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_SecondarySponsorHistory>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
