using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_InterventionsHistoryBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_InterventionsHistory entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_InterventionsHistory entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_InterventionsHistory>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_InterventionsHistory entity)
        {
			var res = db.Delete<SA_InterventionsHistory>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid inId)
        {
			var res = db.Delete<SA_InterventionsHistory>(inId);
            return res > 0 ? true : false;
        }

        public SA_InterventionsHistory GetById(Guid inId)
        {
            var entity = db.FirstOrDefault<SA_InterventionsHistory>("WHERE inId = @0", inId);
            return entity;
        }
	    public List<SA_InterventionsHistory> GetAll()
        {
            var list = db.Query<SA_InterventionsHistory>("SELECT * FROM [SA_InterventionsHistory]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_InterventionsHistory> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_InterventionsHistory>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
