using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_NewsBLL
    {
        public ITMCTRDatabase db = new ITMCTRDatabase();

        public bool Insert(SA_News entity)
        {
            var res = db.Insert(entity);
            return res != null ? true : false;
        }
        public bool Update(SA_News entity)
        {
            var res = db.Update(entity);
            return res > 0 ? true : false;
        }
        public bool Update(string Sql)
        {
            var res = db.Update<SA_News>(Sql);
            return res > 0 ? true : false;
        }
        public bool Delete(SA_News entity)
        {
			var res = db.Delete<SA_News>(entity);
            return res > 0 ? true : false;
        }
		
        public bool Delete(Guid Nid)
        {
			var res = db.Delete<SA_News>(Nid);
            return res > 0 ? true : false;
        }

        public SA_News GetById(Guid Nid)
        {
            var entity = db.FirstOrDefault<SA_News>("WHERE Nid = @0", Nid);
            return entity;
        }
	    public List<SA_News> GetAll()
        {
            var list = db.Query<SA_News>("SELECT * FROM [SA_News]");
            return list.ToList();
        }
    
        public PetaPoco.Page<SA_News> GetPageList(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<SA_News>(PageIndex, PageSize, Sql);
            return result;
        }

		public PetaPoco.Page<T> GetPageList<T>(int PageIndex, int PageSize, PetaPoco.Sql Sql)
        {
            var result = db.Page<T>(PageIndex, PageSize, Sql);
            return result;
        }
    }
}
