//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace ITMCTR.Core.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ITMCTR.Core.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 国际传统医学临床试验注册平台 的本地化字符串。
        /// </summary>
        internal static string APP_NAME_网站名称 {
            get {
                return ResourceManager.GetString("APP_NAME_网站名称", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 国际传统医学临床试验注册平台 京ICP备07032215号-5   提示：推荐使用IE8.0以上版本 宽屏显示分辨率下使用系统 的本地化字符串。
        /// </summary>
        internal static string APP_TIP_浏览器兼容 {
            get {
                return ResourceManager.GetString("APP_TIP_浏览器兼容", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 传统医学领域临床试验（研究）在线注册。 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_中医特色1 {
            get {
                return ResourceManager.GetString("QT_INDEX_中医特色1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 包括中医、针灸、推拿、草药、阿育吠陀、顺势疗法、 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_中医特色2 {
            get {
                return ResourceManager.GetString("QT_INDEX_中医特色2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 尤那尼医学、补充和替代药物等，不限制地域及国别。 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_中医特色3 {
            get {
                return ResourceManager.GetString("QT_INDEX_中医特色3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 关于我们 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_关于我们 {
            get {
                return ResourceManager.GetString("QT_INDEX_关于我们", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 如何参与 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_如何参与 {
            get {
                return ResourceManager.GetString("QT_INDEX_如何参与", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 如何检索 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_如何检索 {
            get {
                return ResourceManager.GetString("QT_INDEX_如何检索", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 平台介绍 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_平台介绍 {
            get {
                return ResourceManager.GetString("QT_INDEX_平台介绍", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 平台简介 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_平台简介 {
            get {
                return ResourceManager.GetString("QT_INDEX_平台简介", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 可对传统医学领域开展的临床试验（研究）进行在线注册。该平台由中国中医科学院和中国中医药循证中心共同负责，
        ///是一家非营利性机构，并被世界卫生组织认证为一级注册平台，定期为世界卫生组织临床试验注册平台（WHO ICTRP）提供数据。 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_平台详介 {
            get {
                return ResourceManager.GetString("QT_INDEX_平台详介", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 我要检索 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_我要检索 {
            get {
                return ResourceManager.GetString("QT_INDEX_我要检索", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 我要注册 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_我要注册 {
            get {
                return ResourceManager.GetString("QT_INDEX_我要注册", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 找试验 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_找试验 {
            get {
                return ResourceManager.GetString("QT_INDEX_找试验", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 新闻 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_新闻 {
            get {
                return ResourceManager.GetString("QT_INDEX_新闻", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 立即登录 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_立即登录 {
            get {
                return ResourceManager.GetString("QT_INDEX_立即登录", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 试验检索 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_试验检索 {
            get {
                return ResourceManager.GetString("QT_INDEX_试验检索", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 试验注册 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_试验注册 {
            get {
                return ResourceManager.GetString("QT_INDEX_试验注册", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 网站语言 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_语言 {
            get {
                return ResourceManager.GetString("QT_INDEX_语言", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 首页 的本地化字符串。
        /// </summary>
        internal static string QT_INDEX_首页 {
            get {
                return ResourceManager.GetString("QT_INDEX_首页", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 5秒自动跳转 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_5秒自动跳转 {
            get {
                return ResourceManager.GetString("UR_LOGIN_5秒自动跳转", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 忘记密码 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_忘记密码 {
            get {
                return ResourceManager.GetString("UR_LOGIN_忘记密码", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 新用户注册 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_新用户注册 {
            get {
                return ResourceManager.GetString("UR_LOGIN_新用户注册", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 此账号未通过审核请联系管理员 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_此账号未通过审核请联系管理员 {
            get {
                return ResourceManager.GetString("UR_LOGIN_此账号未通过审核请联系管理员", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 此账号正在审核中 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_此账号正在审核中 {
            get {
                return ResourceManager.GetString("UR_LOGIN_此账号正在审核中", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 注册成功，请等待审核 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_注册成功 {
            get {
                return ResourceManager.GetString("UR_LOGIN_注册成功", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 用户名或密码错误 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_用户名或密码错误 {
            get {
                return ResourceManager.GetString("UR_LOGIN_用户名或密码错误", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 用户登录 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_用户登录 {
            get {
                return ResourceManager.GetString("UR_LOGIN_用户登录", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 秒后自动跳转 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_秒后自动跳转 {
            get {
                return ResourceManager.GetString("UR_LOGIN_秒后自动跳转", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 立即登录 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_立即登录 {
            get {
                return ResourceManager.GetString("UR_LOGIN_立即登录", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 系统提示 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_系纺提示 {
            get {
                return ResourceManager.GetString("UR_LOGIN_系纺提示", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请输入密码 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_请输入密码 {
            get {
                return ResourceManager.GetString("UR_LOGIN_请输入密码", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请输入用户名 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_请输入用户名 {
            get {
                return ResourceManager.GetString("UR_LOGIN_请输入用户名", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 返回 的本地化字符串。
        /// </summary>
        internal static string UR_LOGIN_返回 {
            get {
                return ResourceManager.GetString("UR_LOGIN_返回", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 点击消息可查看项目详情 的本地化字符串。
        /// </summary>
        internal static string UR_MSG_项目不通过内容 {
            get {
                return ResourceManager.GetString("UR_MSG_项目不通过内容", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 您的一个项目未能通过审核 的本地化字符串。
        /// </summary>
        internal static string UR_MSG_项目不通过审核主题 {
            get {
                return ResourceManager.GetString("UR_MSG_项目不通过审核主题", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 点击消息可查看项目详情 的本地化字符串。
        /// </summary>
        internal static string UR_MSG_项目通过内容 {
            get {
                return ResourceManager.GetString("UR_MSG_项目通过内容", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 您的一个项目通过审核 的本地化字符串。
        /// </summary>
        internal static string UR_MSG_项目通过审核主题 {
            get {
                return ResourceManager.GetString("UR_MSG_项目通过审核主题", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 固定电话 的本地化字符串。
        /// </summary>
        internal static string UR_REG_固定电话 {
            get {
                return ResourceManager.GetString("UR_REG_固定电话", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 国家 的本地化字符串。
        /// </summary>
        internal static string UR_REG_国家 {
            get {
                return ResourceManager.GetString("UR_REG_国家", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已有帐号 的本地化字符串。
        /// </summary>
        internal static string UR_REG_已有帐号 {
            get {
                return ResourceManager.GetString("UR_REG_已有帐号", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 性别 的本地化字符串。
        /// </summary>
        internal static string UR_REG_性别 {
            get {
                return ResourceManager.GetString("UR_REG_性别", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 您的姓名 的本地化字符串。
        /// </summary>
        internal static string UR_REG_您的姓名 {
            get {
                return ResourceManager.GetString("UR_REG_您的姓名", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 我要注册 的本地化字符串。
        /// </summary>
        internal static string UR_REG_我要注册 {
            get {
                return ResourceManager.GetString("UR_REG_我要注册", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 手机号码 的本地化字符串。
        /// </summary>
        internal static string UR_REG_手机号码 {
            get {
                return ResourceManager.GetString("UR_REG_手机号码", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 注册单位名称 的本地化字符串。
        /// </summary>
        internal static string UR_REG_注册单位名称 {
            get {
                return ResourceManager.GetString("UR_REG_注册单位名称", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 用户名 的本地化字符串。
        /// </summary>
        internal static string UR_REG_用户名 {
            get {
                return ResourceManager.GetString("UR_REG_用户名", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 用户密码 的本地化字符串。
        /// </summary>
        internal static string UR_REG_用户密码 {
            get {
                return ResourceManager.GetString("UR_REG_用户密码", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 电子邮件 的本地化字符串。
        /// </summary>
        internal static string UR_REG_电子邮件 {
            get {
                return ResourceManager.GetString("UR_REG_电子邮件", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 确认密码 的本地化字符串。
        /// </summary>
        internal static string UR_REG_确认密码 {
            get {
                return ResourceManager.GetString("UR_REG_确认密码", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 立即注册 的本地化字符串。
        /// </summary>
        internal static string UR_REG_立即注册 {
            get {
                return ResourceManager.GetString("UR_REG_立即注册", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 联系地址 的本地化字符串。
        /// </summary>
        internal static string UR_REG_联系地址 {
            get {
                return ResourceManager.GetString("UR_REG_联系地址", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请输入 的本地化字符串。
        /// </summary>
        internal static string UR_REG_请输入 {
            get {
                return ResourceManager.GetString("UR_REG_请输入", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 请选择 的本地化字符串。
        /// </summary>
        internal static string UR_REG_请选择 {
            get {
                return ResourceManager.GetString("UR_REG_请选择", resourceCulture);
            }
        }
    }
}
