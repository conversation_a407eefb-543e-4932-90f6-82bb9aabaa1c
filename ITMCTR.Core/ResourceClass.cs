using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Resources;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core
{
    public class ResourceClass
    {
        public string GetResourceValue(string Key)
        {
            var Values = "";
            try
            {
                ResourceManager rm = new ResourceManager(GetType().Namespace + ".Properties.Resources", Assembly.GetExecutingAssembly());
                Values = rm.GetString("QT_INDEX_关于我们");
            }
            catch (Exception ex)
            {

            }
            return Values;
        }
    }
}
