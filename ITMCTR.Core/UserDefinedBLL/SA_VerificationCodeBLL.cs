using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_VerificationCodeBLL
    {
        public SA_VerificationCode GetByUidCode(Guid Uid,string Code,int Type)
        {
            var model = db.Query<SA_VerificationCode>(" WHERE [Uid] = @0 AND RandomCode = @1 AND UseType = @2 ", Uid, Code, Type).FirstOrDefault();
            return model;
        }
        public List<SA_VerificationCode> GetNoUsedList(Guid Uid, int Type)
        {
            var list = db.Query<SA_VerificationCode>(" WHERE [Uid] = @0 AND UseType = @1 AND IsUsed = 'false' ", Uid, Type).ToList();
            return list;
        }

        public bool Insert(SA_VerificationCode model,List<SA_VerificationCode> list)
        {
            try
            {
                db.BeginTransaction();
                foreach (var item in list)
                {
                    item.IsUsed = true;
                    db.Update(item);
                }
                db.Insert(model);
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
    }
}
