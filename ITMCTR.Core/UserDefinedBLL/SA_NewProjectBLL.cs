using AutoMapper;
using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace ITMCTR.Core.BLL
{
    public partial class SA_NewProjectBLL
    {
        public List<SA_NewProject> GetListByIds(string strParams)
        {
            var list = db.Query<SA_NewProject>($" WHERE Pid in ({strParams}) ").ToList();
            return list;
        }
        public List<SA_NewProject> GetListByExecuteTaskSysUserId(Guid user_id)
        {
            var list = db.Query<SA_NewProject>($" WHERE executeTaskSysUserId = @0 AND taskStatus =2", user_id).ToList();
            return list;
        }
        public List<SA_NewProject> GetReportUploadByCreateUserId(Guid user_id)
        {
            var sql = PetaPoco.Sql.Builder.Select("p.*")
                .From("SA_NewProject p")
                .LeftJoin("SA_ProjectReportUpload data")
                .On("p.pid=data.pid");
            sql = sql.Where(" p.createUserID=@0 AND data.PruId is Null AND p.status=@1", user_id, ProjectStatus.通过审核);
            return db.Query<SA_NewProject>(sql).ToList();
        }
        public List<SA_NewProject> GetDataUploadByCreateUserId(Guid user_id)
        {
            var sql = PetaPoco.Sql.Builder.Select("p.*")
                .From("SA_NewProject p")
                .LeftJoin("SA_ProjectDataUpload data")
                .On("p.pid=data.pid");
            sql = sql.Where(" p.createUserID=@0 AND data.PduId is Null AND p.status=@1", user_id, ProjectStatus.通过审核);
            return db.Query<SA_NewProject>(sql).ToList();
        }

        public bool DistributionProject(string strParams, Guid Uid, Guid OptionUid)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');
                foreach (var item in list)
                {
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        p.executeTaskSysUserId = Uid;
                        p.taskStatus = (int)ProjectTaskStatus.已分配;
                        p.sendTaskSysUserId = OptionUid;
                        
                        //p.modifyTime = DateTime.Now;
                        db.Update(p);

                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.VerifyTaskStatus = (int)VerifyTaskStatus.任务分配;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        record.Reason = "";
                        db.Insert(record);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool SingleSendBack(Guid Pid)
        {
            try
            {
                db.BeginTransaction();
                var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", Pid).FirstOrDefault();
                if (p != null)
                {
                    p.isTraditionalMedicine = null;
                    p.taskStatus = (int)ProjectTaskStatus.待分配;
                    p.status = (int)ProjectStatus.待审核;
                    p.sendTaskSysUserId = null;
                    p.executeTaskSysUserId = null;
                    db.Update(p);
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool JudgedProject(string strParams,string userId, int isTraditionalMedicine, Guid OptionUid)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');
                foreach (var item in list)
                {
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        if (!string.IsNullOrWhiteSpace(userId))
                        {
                            var user = db.Query<SA_SysUser>(" WHERE [Uid] = @0 ", userId).FirstOrDefault();
                            if (user.SysRoleId == Guid.Parse("3d64080d-042f-45da-84be-570b360236a6"))//3审
                            {
                                p.taskStatus = (int)ProjectTaskStatus.已分配;
                                p.executeTaskSysUserId = Guid.Parse(userId);
                            }
                            else if (user.SysRoleId == Guid.Parse("49bb2491-2ba5-492d-9f54-6317bcc05baa"))//2审
                            {

                            }
                        }
                        
                        p.firstTaskSysUserId = OptionUid;
                        if (isTraditionalMedicine == 1)
                        {
                            p.sendTaskSysUserId = Guid.Parse(userId);
                        }
                        p.isTraditionalMedicine = isTraditionalMedicine;
                        //p.modifyTime = DateTime.Now;
                        db.Update(p);

                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.VerifyTaskStatus = (int)VerifyTaskStatus.判断任务;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        record.Reason = "";
                        db.Insert(record);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool DistributionChangeProject(string strParams, Guid Uid, Guid OptionUid)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');
                foreach (var item in list)
                {
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        var record = new SA_ChangeAuditorRecord();
                        record.CarId = Guid.NewGuid();
                        record.CreateSysUserId = OptionUid;
                        record.CreateTime = DateTime.Now;
                        record.FirstAuditorSysUserId = p.sendTaskSysUserId;
                        record.NewSencondAuditorSysUserId = Uid;
                        record.OldSecondAuditorSysUserId = p.sendTaskSysUserId;
                        record.Reason = "";
                        db.Insert(record);

                        p.executeTaskSysUserId = Uid;
                        //p.modifyTime = DateTime.Now;
                        db.Update(p);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        /// <summary>
        /// 二审待审核
        /// </summary>
        /// <param name="strParams"></param>
        /// <param name="Type"></param>
        /// <param name="reverifyFailReason"></param>
        /// <param name="OptionUid"></param>
        /// <returns></returns>
        public bool BatchAudit(string strParams, int Type, string reverifyFailReason, Guid OptionUid, int isTraditionalMedicine = 1)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');

                foreach (var item in list)
                {
                    var pid = Guid.Parse(item);
                    var notice = new SA_Notice();
                    notice.Nid = Guid.NewGuid();
                    notice.IsRead = 0;
                    notice.CreateTime = DateTime.Now;
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        p.editRequestResult = null;
                        p.isTraditionalMedicine = isTraditionalMedicine;
                        //p.modifyTime = DateTime.Now;
                        notice.SendUserId = OptionUid;
                        notice.ReceiveUserId = p.createUserID;
                        notice.Pid = p.Pid;
                        notice.Type = 1;
                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.VerifyTaskStatus = (int)VerifyTaskStatus.审核任务执行;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        record.LevelOneSysUserId = p.sendTaskSysUserId;
                        record.LevelSecondSysUserId = p.executeTaskSysUserId;
                        record.SubmitUserId = OptionUid;
                        if (Type == 1)
                        {
                            notice.Title = "您的一个项目通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "One project has been approved.";
                            notice.ContentEN = "One project has been approved.For more details";
                            notice.AccessUrl = p.Pid.ToString();
                            record.ProjectStatus = (int)ProjectStatus.通过审核;
                            if (string.IsNullOrWhiteSpace(p.regNumber))
                            {
                                //有2审 给2审
                                var user = db.Query<SA_SysUser>(" WHERE [Uid] = @0 ", p.sendTaskSysUserId).FirstOrDefault();
                                if (user.SysRoleId == Guid.Parse("49bb2491-2ba5-492d-9f54-6317bcc05baa"))
                                {
                                    p.status = (int)ProjectStatus.待审核;
                                    p.taskStatus = (int)ProjectTaskStatus.待复核;
                                }
                                //没有 最终
                                else
                                {
                                    p.status = (int)ProjectStatus.待审核;
                                    p.taskStatus = (int)ProjectTaskStatus.复核通过;
                                }
                            }
                            else
                            {
                                p.status = (int)ProjectStatus.待审核;
                                p.taskStatus = (int)ProjectTaskStatus.三审最终审核;
                            }
                        }
                        else if (Type == 2)
                        {
                            notice.Title = "您的一个项目未能通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "One project has been rejected.";
                            notice.ContentEN = "One project has been rejected.For more details";
                            notice.AccessUrl = p.Pid.ToString();
                            p.taskStatus = (int)ProjectTaskStatus.复核未通过;
                            p.status = (int)ProjectStatus.未通过审核;
                            p.reverifyFailReason = reverifyFailReason;
                            record.Reason = reverifyFailReason;
                            record.ProjectStatus = (int)ProjectStatus.未通过审核;
                            db.Insert(notice);
                        }
                        db.Update(p);
                        db.Insert(record);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool AgainAudit(string strParams, int Type, string reverifyFailReason, Guid OptionUid)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');

                foreach (var item in list)
                {
                    var notice = new SA_Notice();
                    notice.Nid = Guid.NewGuid();
                    notice.IsRead = 0;
                    notice.CreateTime = DateTime.Now;
                    var pid = Guid.Parse(item);
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        notice.Pid = p.Pid;
                        notice.Type = 1;
                        notice.SendUserId = OptionUid;
                        notice.ReceiveUserId = p.createUserID;

                        var record = new SA_ProjectRequestEditFlow();
                        record.PrefId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.RequestUserId = p.createUserID;
                        record.RequestTime = p.regTime;
                        record.ApplySysUserId = OptionUid;
                        record.ApplyTime = DateTime.Now;
                        if (Type == 1)
                        {
                            notice.Title = "您的一个项目通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "your project is approved";
                            notice.ContentEN = "view the project details";
                            notice.AccessUrl = p.Pid.ToString();
                            p.taskStatus = (int)ProjectTaskStatus.申请通过;
                            p.status = (int)ProjectStatus.未填完;

                            record.RequestStatus = 7;
                            p.fileEthicalCommittee = null;
                            p.studyPlanfile = null;
                            p.informedConsentfile = null;
                            //p.modifyTime = DateTime.Now;
                        }
                        else if (Type == 2)
                        {
                            notice.Title = "您的一个项目未能通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "One project has been rejected. ";
                            notice.ContentEN = "view the project details";
                            notice.AccessUrl = p.Pid.ToString();
                            p.taskStatus = (int)ProjectTaskStatus.申请未通过;
                            p.reverifyFailReason = reverifyFailReason;
                            record.EditRequestResult = reverifyFailReason;
                            record.RequestStatus = 8;
                        }
                        db.Update(p);
                        db.Insert(record);
                        db.Insert(notice);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool SecBatchAudit(string strParams, int Type, string reverifyFailReason, Guid OptionUid)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');

                foreach (var item in list)
                {
                    var notice = new SA_Notice();
                    notice.Nid = Guid.NewGuid();
                    notice.IsRead = 0;
                    notice.CreateTime = DateTime.Now;
                    var pid = Guid.Parse(item);
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        //p.modifyTime = DateTime.Now;
                        notice.Pid = p.Pid;
                        notice.Type = 1;
                        notice.SendUserId = OptionUid;
                        notice.ReceiveUserId = p.createUserID;
                        var PreviousRecord = db.Query<SA_ProjectVerifyTaskFlowRecord>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.CreateTime).FirstOrDefault();
                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        if (Type == 1)
                        {
                            notice.Title = "您的一个项目通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "your project is approved";
                            notice.ContentEN = "view the project details";
                            notice.AccessUrl = p.Pid.ToString();
                            p.taskStatus = (int)ProjectTaskStatus.复核通过;
                            p.status = PreviousRecord.ProjectStatus;
                            record.ProjectStatus = (int)ProjectStatus.通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.复核通过;
                        }
                        else if (Type == 2)
                        {
                            notice.Title = "您的一个项目未能通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "your project is not approved";
                            notice.ContentEN = "view the project details";
                            notice.AccessUrl = p.Pid.ToString();
                            p.taskStatus = (int)ProjectTaskStatus.复核未通过;
                            p.status = (int)ProjectStatus.未通过审核;
                            p.reverifyFailReason = reverifyFailReason;
                            record.Reason = reverifyFailReason;
                            record.ProjectStatus = (int)ProjectStatus.未通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.复核未通过;
                        }
                        db.Update(p);
                        db.Insert(record);
                        db.Insert(notice);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        /// <summary>
        /// 一审 复审
        /// </summary>
        /// <param name="strParams"></param>
        /// <param name="Type"></param>
        /// <param name="reverifyFailReason"></param>
        /// <param name="OptionUid"></param>
        /// <param name="regSerialNumber"></param>
        /// <param name="regNumber"></param>
        /// <returns></returns>
        public bool SingleSecAudit(string strParams, int Type, string reverifyFailReason, Guid OptionUid)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');

                foreach (var item in list)
                {
                    var notice = new SA_Notice();
                    notice.Nid = Guid.NewGuid();
                    notice.IsRead = 0;
                    notice.CreateTime = DateTime.Now;
                    var pid = Guid.Parse(item);
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        //p.modifyTime = DateTime.Now;
                        notice.Pid = p.Pid;
                        notice.Type = 1;
                        notice.SendUserId = OptionUid;
                        notice.ReceiveUserId = p.createUserID;
                        var PreviousRecord = db.Query<SA_ProjectVerifyTaskFlowRecord>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.CreateTime).FirstOrDefault();
                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        record.LevelOneSysUserId = p.sendTaskSysUserId;
                        record.LevelSecondSysUserId = p.executeTaskSysUserId;
                        record.SubmitUserId = OptionUid;
                        if (Type == 1)
                        {
                            notice.Title = "您的一个项目通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "your project is approved";
                            notice.ContentEN = "view the project details";
                            notice.AccessUrl = p.Pid.ToString();
                            if (string.IsNullOrWhiteSpace(p.regNumber))
                            {
                                p.taskStatus = (int)ProjectTaskStatus.复核通过;
                                p.status = (int)ProjectStatus.待审核;
                            }
                            else
                            {
                                p.taskStatus = (int)ProjectTaskStatus.三审最终审核;
                                p.status = (int)ProjectStatus.待审核;
                            }
                            p.modifyTime = DateTime.Now;
                            record.ProjectStatus = (int)ProjectStatus.通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.复核通过;

                            db.Insert(notice);
                        }
                        else if (Type == 2)
                        {
                            p.taskStatus = (int)ProjectTaskStatus.已分配;//复核未通过
                            p.reverifyFailReason = reverifyFailReason;
                            p.status = (int)ProjectStatus.待审核;
                            record.Reason = reverifyFailReason;
                            record.ProjectStatus = (int)ProjectStatus.未通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.审核任务执行;
                        }
                        db.Update(p);
                        db.Insert(record);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool SecBatchAudit(string strParams, int Type, string reverifyFailReason, Guid OptionUid, string regSerialNumber, string regNumber)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');

                foreach (var item in list)
                {
                    var notice = new SA_Notice();
                    notice.Nid = Guid.NewGuid();
                    notice.IsRead = 0;
                    notice.CreateTime = DateTime.Now;
                    var pid = Guid.Parse(item);
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        //p.modifyTime = DateTime.Now;
                        notice.Pid = p.Pid;
                        notice.Type = 1;
                        notice.SendUserId = OptionUid;
                        notice.ReceiveUserId = p.createUserID;
                        var PreviousRecord = db.Query<SA_ProjectVerifyTaskFlowRecord>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.CreateTime).FirstOrDefault();
                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        record.LevelOneSysUserId = p.sendTaskSysUserId;
                        record.LevelSecondSysUserId = p.executeTaskSysUserId;
                        record.SubmitUserId = OptionUid;
                        if (Type == 1)
                        {
                            notice.Title = "您的一个项目通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "your project is approved";
                            notice.ContentEN = "view the project details";
                            notice.AccessUrl = p.Pid.ToString();
                            p.taskStatus = (int)ProjectTaskStatus.复核通过;
                            //p.status = PreviousRecord.ProjectStatus;
                            p.status = 3;
                            p.modifyTime = DateTime.Now;
                            record.ProjectStatus = (int)ProjectStatus.通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.复核通过;
                            if (string.IsNullOrWhiteSpace(p.regNumber) && !p.regSerialNumber.HasValue)
                            {
                                p.regNumber = regNumber + regSerialNumber;
                                p.regSerialNumber = long.Parse(regSerialNumber);
                                p.regNumberTime = DateTime.Now;
                                //p.regTime = DateTime.Now;
                            }

                            var ph = new SA_NewProjectHistory();
                            ph = Mapper.Map<SA_NewProjectHistory>(p);
                            ph.Phid = Guid.NewGuid();
                            //ph.PrefId = record.PrefId;
                            var strVersion = "1.0.0";
                            var phlist = db.Query<SA_NewProjectHistory>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.version).ToList();
                            if (phlist.Count == 0)
                            {
                                ph.version = strVersion;
                            }
                            else
                            {
                                var firPh = phlist.FirstOrDefault();
                                if (firPh != null && string.IsNullOrWhiteSpace(firPh.version))
                                {
                                    ph.version = strVersion;
                                }
                                else
                                {
                                    var version = new Version(phlist.FirstOrDefault().version);
                                    var BuildNo = version.Build;
                                    var MinorNo = version.Minor;
                                    var MajorNo = version.Major;
                                    if (version.Build + 1 >= 10)
                                    {
                                        BuildNo = 0;
                                        MinorNo += 1;
                                        if (MinorNo >= 10)
                                        {
                                            MinorNo = 0;
                                            MajorNo += 1;
                                        }
                                    }
                                    else
                                    {
                                        BuildNo += 1;
                                    }
                                    version = new Version($"{MajorNo}.{MinorNo}.{BuildNo}");
                                    ph.version = version.ToString();
                                }
                            }
                            //版本号
                            //1.0
                            //
                            //var v = new Version("1.0");
                            //v.
                            ph.operateTime = DateTime.Now;
                            db.Insert(ph);
                            #region 干预措施
                            var InterventionsList = db.Query<SA_Interventions>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in InterventionsList)
                            {
                                var sa_ih = new SA_InterventionsHistory();
                                sa_ih = Mapper.Map<SA_InterventionsHistory>(it);
                                sa_ih.inId = Guid.NewGuid();
                                sa_ih.Phid = ph.Phid;
                                db.Insert(sa_ih);
                            }
                            #endregion
                            #region 测试指标
                            var OutcomesList = db.Query<SA_Outcomes>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in OutcomesList)
                            {
                                var sa_oh = new SA_OutcomesHistory();
                                sa_oh = Mapper.Map<SA_OutcomesHistory>(it);
                                sa_oh.ouId = Guid.NewGuid();
                                sa_oh.Phid = ph.Phid;
                                db.Insert(sa_oh);
                            }
                            #endregion
                            #region 研究实施地点
                            var ResearchAddressList = db.Query<SA_ResearchAddress>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in ResearchAddressList)
                            {
                                var sa_rah = new SA_ResearchAddressHistory();
                                sa_rah = Mapper.Map<SA_ResearchAddressHistory>(it);
                                sa_rah.raId = Guid.NewGuid();
                                sa_rah.Phid = ph.Phid;
                                db.Insert(sa_rah);
                            }
                            #endregion
                            #region 诊断试验
                            var DiagnosticList = db.Query<SA_Diagnostic>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in DiagnosticList)
                            {
                                var sa_rah = new SA_DiagnosticHistory();
                                sa_rah = Mapper.Map<SA_DiagnosticHistory>(it);
                                sa_rah.id = Guid.NewGuid();
                                sa_rah.Phid = ph.Phid;
                                db.Insert(sa_rah);
                            }
                            #endregion
                            #region 试验主办单位
                            var SecondarySponsorList = db.Query<SA_SecondarySponsor>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in SecondarySponsorList)
                            {
                                var sa_ssh = new SA_SecondarySponsorHistory();
                                sa_ssh = Mapper.Map<SA_SecondarySponsorHistory>(it);
                                sa_ssh.ssId = Guid.NewGuid();
                                sa_ssh.Phid = ph.Phid;
                                db.Insert(sa_ssh);
                            }
                            #endregion
                            #region 采集人体标本
                            var CollectingSampleList = db.Query<SA_CollectingSample>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in CollectingSampleList)
                            {
                                var sa_csh = new SA_CollectingSampleHistory();
                                sa_csh = Mapper.Map<SA_CollectingSampleHistory>(it);
                                sa_csh.csId = Guid.NewGuid();
                                sa_csh.Phid = ph.Phid;
                                db.Insert(sa_csh);
                            }
                            #endregion

                            db.Insert(notice);
                        }
                        else if (Type == 2)
                        {
                            //notice.Title = "您有一条项目审核未通过";
                            //notice.Content = "您有一条项目审核未通过";
                            //notice.AccessUrl = $"/UserPlatform/ProjectEdit?pid={p.Pid}";
                            p.taskStatus = (int)ProjectTaskStatus.已分配;//复核未通过
                            p.reverifyFailReason = reverifyFailReason;
                            p.status = (int)ProjectStatus.待审核;
                            record.Reason = reverifyFailReason;
                            record.ProjectStatus = (int)ProjectStatus.未通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.审核任务执行;
                        }
                        db.Update(p);
                        db.Insert(record);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool SingleFinalAudit(string strParams, int Type, string reverifyFailReason, Guid OptionUid)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');

                foreach (var item in list)
                {
                    //var notice = new SA_Notice();
                    //notice.Nid = Guid.NewGuid();
                    //notice.IsRead = 0;
                    //notice.CreateTime = DateTime.Now;
                    var pid = Guid.Parse(item);
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        //p.modifyTime = DateTime.Now;
                        //notice.Pid = p.Pid;
                        //notice.Type = 1;
                        //notice.SendUserId = OptionUid;
                        //notice.ReceiveUserId = p.createUserID;
                        var PreviousRecord = db.Query<SA_ProjectVerifyTaskFlowRecord>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.CreateTime).FirstOrDefault();
                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        record.LevelOneSysUserId = p.sendTaskSysUserId;
                        record.LevelSecondSysUserId = p.executeTaskSysUserId;
                        record.SubmitUserId = OptionUid;
                        if (Type == 1)
                        {
                            //notice.Title = "您的一个项目通过审核";
                            //notice.Content = "点击消息可查看项目详情";
                            //notice.TitleEN = "your project is approved";
                            //notice.ContentEN = "view the project details";
                            //notice.AccessUrl = p.Pid.ToString();
                            
                            p.taskStatus = (int)ProjectTaskStatus.复核通过;
                            p.status = (int)ProjectStatus.通过审核;
                            p.modifyTime = DateTime.Now;
                            record.ProjectStatus = (int)ProjectStatus.通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.复核通过;

                            //db.Insert(notice);
                            var ph = new SA_NewProjectHistory();
                            ph = Mapper.Map<SA_NewProjectHistory>(p);
                            ph.Phid = Guid.NewGuid();
                            //ph.PrefId = record.PrefId;
                            var strVersion = "1.0.0";
                            var phlist = db.Query<SA_NewProjectHistory>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.version).ToList();
                            if (phlist.Count == 0)
                            {
                                ph.version = strVersion;
                            }
                            else
                            {
                                var firPh = phlist.FirstOrDefault();
                                if (firPh != null && string.IsNullOrWhiteSpace(firPh.version))
                                {
                                    ph.version = strVersion;
                                }
                                else
                                {
                                    var version = new Version(phlist.FirstOrDefault().version);
                                    var BuildNo = version.Build;
                                    var MinorNo = version.Minor;
                                    var MajorNo = version.Major;
                                    if (version.Build + 1 >= 10)
                                    {
                                        BuildNo = 0;
                                        MinorNo += 1;
                                        if (MinorNo >= 10)
                                        {
                                            MinorNo = 0;
                                            MajorNo += 1;
                                        }
                                    }
                                    else
                                    {
                                        BuildNo += 1;
                                    }
                                    version = new Version($"{MajorNo}.{MinorNo}.{BuildNo}");
                                    ph.version = version.ToString();
                                }
                            }
                            //版本号
                            //1.0
                            //
                            //var v = new Version("1.0");
                            //v.
                            ph.operateTime = DateTime.Now;
                            db.Insert(ph);
                        }
                        else if (Type == 2)
                        {
                            p.taskStatus = (int)ProjectTaskStatus.已分配;//复核未通过
                            p.reverifyFailReason = reverifyFailReason;
                            p.status = (int)ProjectStatus.待审核;
                            record.Reason = reverifyFailReason;
                            record.ProjectStatus = (int)ProjectStatus.未通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.审核任务执行;
                        }

                        db.Update(p);
                        db.Insert(record);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool SendNumber(string strParams, int Type, string reverifyFailReason, Guid OptionUid, string regSerialNumber, string regNumber)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');

                foreach (var item in list)
                {
                    var notice = new SA_Notice();
                    notice.Nid = Guid.NewGuid();
                    notice.IsRead = 0;
                    notice.CreateTime = DateTime.Now;
                    var pid = Guid.Parse(item);
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        //p.modifyTime = DateTime.Now;
                        notice.Pid = p.Pid;
                        notice.Type = 1;
                        notice.SendUserId = OptionUid;
                        notice.ReceiveUserId = p.createUserID;
                        var PreviousRecord = db.Query<SA_ProjectVerifyTaskFlowRecord>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.CreateTime).FirstOrDefault();
                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        record.LevelOneSysUserId = p.sendTaskSysUserId;
                        record.LevelSecondSysUserId = p.executeTaskSysUserId;
                        record.SubmitUserId = OptionUid;
                        if (Type == 1)
                        {
                            notice.Title = "您的一个项目通过审核";
                            notice.Content = "点击消息可查看项目详情";
                            notice.TitleEN = "your project is approved";
                            notice.ContentEN = "view the project details";
                            notice.AccessUrl = p.Pid.ToString();
                            p.taskStatus = (int)ProjectTaskStatus.复核通过;
                            //p.status = PreviousRecord.ProjectStatus;
                            p.status = 3;
                            p.modifyTime = DateTime.Now;
                            record.ProjectStatus = (int)ProjectStatus.通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.复核通过;
                            if (string.IsNullOrWhiteSpace(p.regNumber) && !p.regSerialNumber.HasValue)
                            {
                                p.regNumber = regNumber + regSerialNumber;
                                p.regSerialNumber = long.Parse(regSerialNumber);
                                p.regNumberTime = DateTime.Now;
                                //p.regTime = DateTime.Now;
                            }

                            var ph = new SA_NewProjectHistory();
                            ph = Mapper.Map<SA_NewProjectHistory>(p);
                            ph.Phid = Guid.NewGuid();
                            //ph.PrefId = record.PrefId;
                            var strVersion = "1.0.0";
                            var phlist = db.Query<SA_NewProjectHistory>(" WHERE Pid = @0 ", p.Pid).OrderByDescending(x => x.version).ToList();
                            if (phlist.Count == 0)
                            {
                                ph.version = strVersion;
                            }
                            else
                            {
                                var firPh = phlist.FirstOrDefault();
                                if (firPh != null && string.IsNullOrWhiteSpace(firPh.version))
                                {
                                    ph.version = strVersion;
                                }
                                else
                                {
                                    var version = new Version(phlist.FirstOrDefault().version);
                                    var BuildNo = version.Build;
                                    var MinorNo = version.Minor;
                                    var MajorNo = version.Major;
                                    if (version.Build + 1 >= 10)
                                    {
                                        BuildNo = 0;
                                        MinorNo += 1;
                                        if (MinorNo >= 10)
                                        {
                                            MinorNo = 0;
                                            MajorNo += 1;
                                        }
                                    }
                                    else
                                    {
                                        BuildNo += 1;
                                    }
                                    version = new Version($"{MajorNo}.{MinorNo}.{BuildNo}");
                                    ph.version = version.ToString();
                                }
                            }
                            //版本号
                            //1.0
                            //
                            //var v = new Version("1.0");
                            //v.
                            ph.operateTime = DateTime.Now;
                            db.Insert(ph);
                            #region 干预措施
                            var InterventionsList = db.Query<SA_Interventions>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in InterventionsList)
                            {
                                var sa_ih = new SA_InterventionsHistory();
                                sa_ih = Mapper.Map<SA_InterventionsHistory>(it);
                                sa_ih.inId = Guid.NewGuid();
                                sa_ih.Phid = ph.Phid;
                                db.Insert(sa_ih);
                            }
                            #endregion
                            #region 测试指标
                            var OutcomesList = db.Query<SA_Outcomes>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in OutcomesList)
                            {
                                var sa_oh = new SA_OutcomesHistory();
                                sa_oh = Mapper.Map<SA_OutcomesHistory>(it);
                                sa_oh.ouId = Guid.NewGuid();
                                sa_oh.Phid = ph.Phid;
                                db.Insert(sa_oh);
                            }
                            #endregion
                            #region 研究实施地点
                            var ResearchAddressList = db.Query<SA_ResearchAddress>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in ResearchAddressList)
                            {
                                var sa_rah = new SA_ResearchAddressHistory();
                                sa_rah = Mapper.Map<SA_ResearchAddressHistory>(it);
                                sa_rah.raId = Guid.NewGuid();
                                sa_rah.Phid = ph.Phid;
                                db.Insert(sa_rah);
                            }
                            #endregion
                            #region 诊断试验
                            var DiagnosticList = db.Query<SA_Diagnostic>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in DiagnosticList)
                            {
                                var sa_rah = new SA_DiagnosticHistory();
                                sa_rah = Mapper.Map<SA_DiagnosticHistory>(it);
                                sa_rah.id = Guid.NewGuid();
                                sa_rah.Phid = ph.Phid;
                                db.Insert(sa_rah);
                            }
                            #endregion
                            #region 试验主办单位
                            var SecondarySponsorList = db.Query<SA_SecondarySponsor>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in SecondarySponsorList)
                            {
                                var sa_ssh = new SA_SecondarySponsorHistory();
                                sa_ssh = Mapper.Map<SA_SecondarySponsorHistory>(it);
                                sa_ssh.ssId = Guid.NewGuid();
                                sa_ssh.Phid = ph.Phid;
                                db.Insert(sa_ssh);
                            }
                            #endregion
                            #region 采集人体标本
                            var CollectingSampleList = db.Query<SA_CollectingSample>(" WHERE Pid = @0 ", p.Pid).ToList();
                            foreach (var it in CollectingSampleList)
                            {
                                var sa_csh = new SA_CollectingSampleHistory();
                                sa_csh = Mapper.Map<SA_CollectingSampleHistory>(it);
                                sa_csh.csId = Guid.NewGuid();
                                sa_csh.Phid = ph.Phid;
                                db.Insert(sa_csh);
                            }
                            #endregion

                            db.Insert(notice);
                        }
                        else if (Type == 2)
                        {
                            //notice.Title = "您有一条项目审核未通过";
                            //notice.Content = "您有一条项目审核未通过";
                            //notice.AccessUrl = $"/UserPlatform/ProjectEdit?pid={p.Pid}";
                            p.taskStatus = (int)ProjectTaskStatus.已分配;//复核未通过
                            p.reverifyFailReason = reverifyFailReason;
                            p.status = (int)ProjectStatus.待审核;
                            record.Reason = reverifyFailReason;
                            record.ProjectStatus = (int)ProjectStatus.未通过审核;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.审核任务执行;
                        }
                        db.Update(p);
                        db.Insert(record);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool SendNumberBack(Guid pid)
        {
            try
            {
                db.BeginTransaction();
                var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", pid).FirstOrDefault();
                if (p != null)
                {
                    var user = db.Query<SA_SysUser>(" WHERE [Uid] = @0 ", p.sendTaskSysUserId).FirstOrDefault();
                    if(user.SysRoleId == Guid.Parse("49bb2491-2ba5-492d-9f54-6317bcc05baa"))//二审
                    {
                        p.taskStatus = (int)ProjectTaskStatus.待复核;
                        p.status = (int)ProjectStatus.待审核;
                    }
                    else if (user.SysRoleId == Guid.Parse("3d64080d-042f-45da-84be-570b360236a6"))//三审
                    {
                        p.taskStatus = (int)ProjectTaskStatus.已分配;
                        p.status = (int)ProjectStatus.待审核;
                    }
                    db.Update(p);
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool RequestEditFlow(Guid Pid, string Reason, string attrfile, string attrfile2, Guid UserID)
        {
            var proj = GetById(Pid);
            if (proj == null)
            {
                return false;
            }
            if (proj.status != (int)Core.ProjectStatus.通过审核)
            {
                return false;
            }
            try
            {
                db.BeginTransaction();
                proj.taskStatus = (int)Core.ProjectTaskStatus.再修改申请;
                proj.editRequestReason = Reason;
                proj.editRequestAttachment = attrfile;

                SA_ProjectRequestEditFlow info = new SA_ProjectRequestEditFlow();
                info.PrefId = Guid.NewGuid();
                info.RequestTime = DateTime.Now;
                info.RequestUserId = UserID;
                info.EditRequestReason = Reason;
                info.Pid = Pid;
                info.EditRequestAttachment = attrfile;
                info.EditRequestAttachment2 = attrfile2;
                info.RequestStatus = (int)Core.ProjectTaskStatus.再修改申请;
                db.Update(proj);
                db.Insert(info);
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool CheckExistTrialDataUpload(Guid pid)
        {
            return db.Exists<SA_ProjectDataUpload>($" WHERE Pid =@0 ", pid);
        }
        public bool CheckExistProjectReportUpload(Guid pid)
        {
            return db.Exists<SA_ProjectReportUpload>($" WHERE Pid =@0 ", pid);
        }
        public void ProjectStatusChange(SA_NewProject projectInfo, ProjectStatus status)
        {
            switch (status)
            {
                case ProjectStatus.未填完://未填完
                    projectInfo.status = (int)ProjectStatus.未填完;
                    break;
                case ProjectStatus.待审核://待审核
                    if (projectInfo.taskStatus == (int)ProjectTaskStatus.复核未通过)
                    {
                        projectInfo.taskStatus = (int)ProjectTaskStatus.已分配;
                        projectInfo.status = (int)ProjectStatus.待审核;
                    }
                    else if (projectInfo.taskStatus == (int)ProjectTaskStatus.申请通过)
                    {
                        projectInfo.taskStatus = (int)ProjectTaskStatus.已分配;
                        projectInfo.status = (int)ProjectStatus.待审核;
                    }
                    else
                    {
                        projectInfo.taskStatus = (int)ProjectTaskStatus.待分配;
                        projectInfo.status = (int)ProjectStatus.待审核;
                    }
                    break;
                case ProjectStatus.未通过审核://未通过审核
                    if (projectInfo.taskStatus == (int)ProjectTaskStatus.复核未通过)
                    {
                        projectInfo.taskStatus = (int)ProjectTaskStatus.已分配;
                        projectInfo.status = (int)ProjectStatus.待审核;
                    }
                    break;
                case ProjectStatus.通过审核://通过审核
                    projectInfo.status = (int)ProjectStatus.通过审核;
                    break;
                default:
                    break;
            }
        }

        public string GetMaxRegNumber(int year)
        {
            string num = "";
            var intNum = 1;
            bool isFind = true;
            while (isFind)
            {
                var temp = db.Query<SA_NewProject>(" WHERE regSerialNumber like @0 ", $"{year}{intNum.ToString().PadLeft(6, '0')}").Count();
                if (temp == 0)
                {
                    num = intNum.ToString().PadLeft(6, '0');
                    isFind = false;
                }
                else
                {
                    intNum++;
                }
            }
            return num;
        }

        public bool CheckRegNumber(string regSerialNumber, string regNumber, Guid Pid)
        {
            var model = db.Query<SA_NewProject>(" WHERE regNumber = @0 AND Pid <> @1", regNumber + regSerialNumber, Pid).FirstOrDefault();
            if (model == null)
                return true;
            return false;
        }

        public bool ModifyAlternativeMedicineState(Guid Pid,int state)
        {
            var proj = GetById(Pid);
            if (proj == null)
            {
                return false;
            }
            try
            {
                if (state == 0)
                {
                    proj.isTraditionalMedicine = 0;
                }
                else if (state == 1)
                {
                    proj.isTraditionalMedicine = 1;
                    if (proj.status == 1 && proj.taskStatus == 3)
                    {
                        proj.status = 1;
                        proj.taskStatus = 2;
                    }
                }
                db.Update(proj);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public bool Recall(string strParams, Guid OptionUid)
        {
            try
            {
                db.BeginTransaction();
                var list = strParams.Split(',');
                foreach (var item in list)
                {
                    var p = db.Query<SA_NewProject>(" WHERE [Pid] = @0 ", item).FirstOrDefault();
                    if (p != null)
                    {
                        p.executeTaskSysUserId = null;
                        p.firstTaskSysUserId = null;
                        p.sendTaskSysUserId = null;
                        p.taskStatus = (int)ProjectTaskStatus.待分配;
                        p.status = (int)ProjectStatus.待审核;
                        p.isTraditionalMedicine = null;
                        db.Update(p);

                        var record = new SA_ProjectVerifyTaskFlowRecord();
                        record.PvtfrId = Guid.NewGuid();
                        record.Pid = p.Pid;
                        record.VerifyTaskStatus = (int)VerifyTaskStatus.召回;
                        record.CreateTime = DateTime.Now;
                        record.TaskFlowCreateSysUserId = OptionUid.ToString();
                        record.Reason = "";
                        db.Insert(record);
                    }
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
    }
}
