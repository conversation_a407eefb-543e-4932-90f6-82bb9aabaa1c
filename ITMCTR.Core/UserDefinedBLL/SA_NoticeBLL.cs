using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_NoticeBLL
    {
        public List<SA_Notice> GetListByReceiveUserId(Guid ReceiveUserId,int IsRead)
        {
            var list = db.Query<SA_Notice>($" WHERE ReceiveUserId = @0 AND IsRead = @1 ", ReceiveUserId, IsRead).OrderByDescending(x=> x.CreateTime).ToList();
            return list;
        }

        public List<SA_Notice> GetListByReceiveUserId(Guid ReceiveUserId)
        {
            var list = db.Query<SA_Notice>($" WHERE ReceiveUserId = @0 ", ReceiveUserId).OrderByDescending(x => x.CreateTime).ToList();
            return list;
        }

        public List<SA_Notice> GetNoReadList(Guid ReceiveUserId)
        {
            var list = db.Query<SA_Notice>($" WHERE ReceiveUserId = @0 AND IsRead = 0 ", ReceiveUserId).OrderByDescending(x => x.CreateTime).ToList();
            return list;
        }
    }
}
