using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_SysRolesBLL
    {
        public string GetRoleName(Guid RoleId)
        {
            string name = "";
            var model = db.Query<SA_SysRoles>(" WHERE RoleId = @0 ", RoleId).FirstOrDefault();
            if (model != null)
                name = model.RoleName;
            return name;
        }
    }
}
