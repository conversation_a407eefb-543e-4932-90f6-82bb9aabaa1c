using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_UsersBLL
    {
        public string GetSalt(string Username)
        {
            return "%JoN#&kkEbcZVYt$";
        }
        public string ExecutePassword(string Username, string password)
        {
            byte[] passwordAndSaltBytes = System.Text.Encoding.UTF8.GetBytes(password + GetSalt(Username));
            byte[] hashBytes = new MD5CryptoServiceProvider().ComputeHash(passwordAndSaltBytes);
            return Convert.ToBase64String(hashBytes);
        }
        public SA_Users Login(string Username, string Password)
        {
            var Cryspwd = ExecutePassword(Username, Password);
            var model = db.Query<SA_Users>(" WHERE Username = @0 AND Password = @1 ", Username, Cryspwd).FirstOrDefault();
            return model;
        }

        public bool CheckUserName(string Username)
        {
            var model = db.Query<SA_Users>(" WHERE Username = @0 ", Username).FirstOrDefault();
            if (model == null)
                return true;
            else
                return false;
        }
        public SA_Users GetByUsername(string Username)
        {
            var model = db.Query<SA_Users>(" WHERE Username = @0 ", Username).FirstOrDefault();
            return model;
        }
        public string GetUserName(Guid Uid)
        {
            var name = "";
            var model = db.Query<SA_Users>(" WHERE Uid = @0 ", Uid).FirstOrDefault();
            if (model != null)
                name = model.Name;
            return name;
        }

        public bool CheckEmail(string Email, string Uid = "")
        {
            SA_Users model;
            if (!string.IsNullOrWhiteSpace(Uid))
            {
                model = db.Query<SA_Users>(" WHERE Uid <> @0 and Email = @1", Uid, Email).FirstOrDefault();
            }
            else
            {
                model = db.Query<SA_Users>(" WHERE Email = @0 ", Email).FirstOrDefault();
            }
            if (model == null)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}
