using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_OutcomesBLL
    {
        public List<SA_Outcomes> GetByProjectId(Guid pid)
        {
            var list = db.Query<SA_Outcomes>(" WHERE [pid] = @0 ", pid).ToList();
            return list;
        }
    }
}
