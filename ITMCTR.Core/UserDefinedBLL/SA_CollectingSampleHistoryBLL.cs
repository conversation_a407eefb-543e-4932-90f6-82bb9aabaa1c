using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_CollectingSampleHistoryBLL
    {
        public List<SA_CollectingSampleHistory> GetByProjectId(Guid Phid)
        {
            var list = db.Query<SA_CollectingSampleHistory>(" WHERE [Phid] = @0 ", Phid).ToList();
            return list;
        }
    }
}
