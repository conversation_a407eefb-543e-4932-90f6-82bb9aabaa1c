using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ResearchAddressBLL
    {
        public List<SA_ResearchAddress> GetByProjectId(Guid pid)
        {
            var list = db.Query<SA_ResearchAddress>(" WHERE [pid] = @0 ", pid).ToList();
            return list;
        }
    }
}
