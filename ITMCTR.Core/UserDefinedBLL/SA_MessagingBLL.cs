using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_MessagingBLL
    {
        public List<SA_Messaging> GetListByPid(Guid Pid)
        {
            var list = db.Query<SA_Messaging>(" WHERE Pid = @0 ", Pid).OrderBy(x=> x.CreateTime).ToList();
            return list;
        }
        public List<SA_Messaging> GetOwnListByUid(Guid Uid)
        {
            var list = db.Query<SA_Messaging>(" WHERE ToUser = @0 ", Uid).OrderBy(x => x.CreateTime).ToList();
            return list;
        }
        public bool UpdataManagerRead(Guid Pid)
        {
            try
            {
                db.BeginTransaction();
                var list = db.Query<SA_Messaging>(" WHERE Pid = @0 ", Pid).ToList();
                foreach (var item in list)
                {
                    item.ManagerRead = 1;
                    db.Update(item);
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
        public bool UpdataUserRead(Guid Pid)
        {
            try
            {
                db.BeginTransaction();
                var list = db.Query<SA_Messaging>(" WHERE Pid = @0 ", Pid).ToList();
                foreach (var item in list)
                {
                    item.UserRead = 1;
                    db.Update(item);
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }
    }
}
