using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_SecondarySponsorHistoryBLL
    {
        public List<SA_SecondarySponsorHistory> GetByProjectId(Guid Phid)
        {
            var list = db.Query<SA_SecondarySponsorHistory>(" WHERE [Phid] = @0 ", Phid).ToList();
            return list;
        }
    }
}
