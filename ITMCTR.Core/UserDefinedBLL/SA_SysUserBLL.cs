using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_SysUserBLL
    {
        public string GetSalt(string Username)
        {
            return "0uqzeC1o!ItKf%7q";
        }
        public string ExecutePassword(string Username, string password)
        {
            byte[] passwordAndSaltBytes = Encoding.UTF8.GetBytes(password + GetSalt(Username));
            byte[] hashBytes = new MD5CryptoServiceProvider().ComputeHash(passwordAndSaltBytes);
            return Convert.ToBase64String(hashBytes);
        }
        public SA_SysUser Login(string Username, string Password)
        {
            var Cryspwd = ExecutePassword(Username, Password);
            var model = db.Query<SA_SysUser>(" WHERE Username = @0 AND Password = @1 ", Username, Cryspwd).FirstOrDefault();
            return model;
        }

        public int GetCountBySysRoleId(Guid SysRoleId)
        {
            var count = db.Query<SA_SysUser>(" WHERE SysRoleId = @0 ", SysRoleId).ToList().Count();
            return count;
        }

        public bool HasUsername(string Username)
        {
            var list = db.Query<SA_SysUser>(" WHERE Username = @0 ", Username).ToList();
            if (list.Count > 0)
                return true;
            else
                return false;
        }

        public string GetUserName(Guid Uid)
        {
            var name = "";
            var model = db.Query<SA_SysUser>(" WHERE Uid = @0 ", Uid).FirstOrDefault();
            if (model != null)
                name = model.Name;
            return name;
        }
    }
}
