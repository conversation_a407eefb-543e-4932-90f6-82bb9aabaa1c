using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ResearchAddressHistoryBLL
    {
        public List<SA_ResearchAddressHistory> GetByProjectId(Guid Phid)
        {
            var list = db.Query<SA_ResearchAddressHistory>(" WHERE [Phid] = @0 ", Phid).ToList();
            return list;
        }
    }
}
