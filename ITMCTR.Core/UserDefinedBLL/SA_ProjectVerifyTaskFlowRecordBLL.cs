
using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ProjectVerifyTaskFlowRecordBLL
    {
        public SA_ProjectVerifyTaskFlowRecord GetLastByProjectId(Guid pid)
        {
            var model = db.Query<SA_ProjectVerifyTaskFlowRecord>(" WHERE [pid] = @0 AND VerifyTaskStatus<>5", pid).OrderByDescending(x => x.CreateTime).FirstOrDefault();
            return model;
        }

        public List<SA_ProjectVerifyTaskFlowRecord> GetListByProjectId(Guid pid)
        {
            var list = db.Query<SA_ProjectVerifyTaskFlowRecord>(" WHERE [pid] = @0 ", pid).OrderByDescending(x => x.CreateTime).ToList();
            return list;
        }
    }
}
