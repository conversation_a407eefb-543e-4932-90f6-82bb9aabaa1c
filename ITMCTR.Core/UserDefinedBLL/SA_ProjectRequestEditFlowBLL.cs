using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_ProjectRequestEditFlowBLL
    {
        public SA_ProjectRequestEditFlow GetByLastModelByPid(Guid pid)
        {
            var model = db.Query<SA_ProjectRequestEditFlow>(" WHERE [pid] = @0", pid).OrderByDescending(x => x.RequestTime).FirstOrDefault();
            return model;
        }
        public SA_ProjectRequestEditFlow GetByLastModelByPid(Guid pid, int Status)
        {
            var model = db.Query<SA_ProjectRequestEditFlow>(" WHERE [pid] = @0 AND [RequestStatus]  =@1", pid, Status).OrderByDescending(x => x.RequestTime).FirstOrDefault();
            return model;
        }
        public List<SA_ProjectRequestEditFlow> GetByListByPid(Guid pid)
        {
            var list = db.Query<SA_ProjectRequestEditFlow>(" WHERE [pid] = @0 ", pid).OrderByDescending(x => x.RequestTime).ToList();
            return list;
        }
    }
}
