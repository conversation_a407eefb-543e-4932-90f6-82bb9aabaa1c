using System;
using System.Collections.Generic;
using System.Linq;
using ITMCTR.Core.Models;

namespace ITMCTR.Core.BLL
{
    public partial class SA_NewProjectHistoryBLL
    {
        public List<SA_NewProjectHistory> GetProjectHistory(Guid pid)
        {
            var list = db.Query<SA_NewProjectHistory>("SELECT * FROM [SA_NewProjectHistory] where pid =@0 order by [version] asc ", pid);
            return list.ToList();
        }
    }
}
