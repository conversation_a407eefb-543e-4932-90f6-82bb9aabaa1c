using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.BLL
{
    public partial class SA_RoleFunsBLL
    {

        public bool Insert(List<SA_RoleFuns> list, Guid RoleId)
        {
            try
            {
                db.BeginTransaction();
                db.Delete<SA_RoleFuns>(" WHERE [RoleId] = @0 ", RoleId);
                foreach (var item in list)
                {
                    db.Insert(item);
                }
                db.CompleteTransaction();
                return true;
            }
            catch (Exception ex)
            {
                db.AbortTransaction();
                return false;
            }
        }

        public List<SA_RoleFuns> GetByRoleId(Guid RoleId)
        {
            var list = db.Query<SA_RoleFuns>(" WHERE [RoleId] = @0 ", RoleId).ToList();
            return list;
        }
    }
}
