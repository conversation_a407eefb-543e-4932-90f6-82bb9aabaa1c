using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core.Models
{
    public class MessageModel
    {
        public Guid MessagingId { get; set; }
        public Guid? Pid { get; set; }
        public Guid? ToUser { get; set; }
        public string ToUserName { get; set; }
        public Guid? FromUser { get; set; }
        public string FromUserName { get; set; }
        public string CreateTime { get; set; }
        public int UserRead { get; set; }
        public int ManagerRead { get; set; }
        public string Content { get; set; }
        public int isIn { get; set; }
    }
}
