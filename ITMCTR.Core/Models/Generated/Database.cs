




















// This file was automatically generated by the PetaPoco T4 Template
// Do not make changes directly to this file - edit the template instead
// 
// The following connection settings were used to generate this file
// 
//     Connection String Name: `default`
//     Provider:               `System.Data.SqlClient`
//     Connection String:      `data source=10.10.10.7;initial catalog=ITMCTR_Clinical;persist security info=True;user id=sa;password=**********;`
//     Schema:                 ``
//     Include Views:          `True`



using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using PetaPoco;

namespace ITMCTR.Core.Models
{

	public partial class ITMCTRDatabase : Database
	{
		public ITMCTRDatabase() 
			: base("default")
		{
			CommonConstruct();
		}

		public ITMCTRDatabase(string connectionStringName) 
			: base(connectionStringName)
		{
			CommonConstruct();
		}
		
		partial void CommonConstruct();
		
		public interface IFactory
		{
			ITMCTRDatabase GetInstance();
		}
		
		public static IFactory Factory { get; set; }
        public static ITMCTRDatabase GetInstance()
        {
			if (_instance!=null)
				return _instance;
				
			if (Factory!=null)
				return Factory.GetInstance();
			else
				return new ITMCTRDatabase();
        }

		[ThreadStatic] static ITMCTRDatabase _instance;
		
		public override void OnBeginTransaction()
		{
			if (_instance==null)
				_instance=this;
		}
		
		public override void OnEndTransaction()
		{
			if (_instance==this)
				_instance=null;
		}
        

        [Serializable]
		public class Record<T> where T:new()
		{
			public static ITMCTRDatabase repo { get { return ITMCTRDatabase.GetInstance(); } }
			public bool IsNew() { return repo.IsNew(this); }
			public object Insert() { return repo.Insert(this); }

			public void Save() { repo.Save(this); }
			public int Update() { return repo.Update(this); }

			public int Update(IEnumerable<string> columns) { return repo.Update(this, columns); }
			public static int Update(string sql, params object[] args) { return repo.Update<T>(sql, args); }
			public static int Update(Sql sql) { return repo.Update<T>(sql); }
			public int Delete() { return repo.Delete(this); }
			public static int Delete(string sql, params object[] args) { return repo.Delete<T>(sql, args); }
			public static int Delete(Sql sql) { return repo.Delete<T>(sql); }
			public static int Delete(object primaryKey) { return repo.Delete<T>(primaryKey); }
			public static bool Exists(object primaryKey) { return repo.Exists<T>(primaryKey); }
			public static bool Exists(string sql, params object[] args) { return repo.Exists<T>(sql, args); }
			public static T SingleOrDefault(object primaryKey) { return repo.SingleOrDefault<T>(primaryKey); }
			public static T SingleOrDefault(string sql, params object[] args) { return repo.SingleOrDefault<T>(sql, args); }
			public static T SingleOrDefault(Sql sql) { return repo.SingleOrDefault<T>(sql); }
			public static T FirstOrDefault(string sql, params object[] args) { return repo.FirstOrDefault<T>(sql, args); }
			public static T FirstOrDefault(Sql sql) { return repo.FirstOrDefault<T>(sql); }
			public static T Single(object primaryKey) { return repo.Single<T>(primaryKey); }
			public static T Single(string sql, params object[] args) { return repo.Single<T>(sql, args); }
			public static T Single(Sql sql) { return repo.Single<T>(sql); }
			public static T First(string sql, params object[] args) { return repo.First<T>(sql, args); }
			public static T First(Sql sql) { return repo.First<T>(sql); }
			public static List<T> Fetch(string sql, params object[] args) { return repo.Fetch<T>(sql, args); }
			public static List<T> Fetch(Sql sql) { return repo.Fetch<T>(sql); }
			public static List<T> Fetch(long page, long itemsPerPage, string sql, params object[] args) { return repo.Fetch<T>(page, itemsPerPage, sql, args); }
			public static List<T> Fetch(long page, long itemsPerPage, Sql sql) { return repo.Fetch<T>(page, itemsPerPage, sql); }
			public static List<T> SkipTake(long skip, long take, string sql, params object[] args) { return repo.SkipTake<T>(skip, take, sql, args); }
			public static List<T> SkipTake(long skip, long take, Sql sql) { return repo.SkipTake<T>(skip, take, sql); }
			public static Page<T> Page(long page, long itemsPerPage, string sql, params object[] args) { return repo.Page<T>(page, itemsPerPage, sql, args); }
			public static Page<T> Page(long page, long itemsPerPage, Sql sql) { return repo.Page<T>(page, itemsPerPage, sql); }
			public static IEnumerable<T> Query(string sql, params object[] args) { return repo.Query<T>(sql, args); }
			public static IEnumerable<T> Query(Sql sql) { return repo.Query<T>(sql); }

		}

	}
	




	[TableName("dbo.[SA_ChangeAuditorRecord]")]



	[PrimaryKey("CarId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_ChangeAuditorRecord : ITMCTRDatabase.Record<SA_ChangeAuditorRecord>  
    {



		[Column] public Guid CarId { get; set; }





		[Column] public Guid? FirstAuditorSysUserId { get; set; }





		[Column] public Guid? OldSecondAuditorSysUserId { get; set; }





		[Column] public Guid? NewSencondAuditorSysUserId { get; set; }





		[Column] public DateTime? CreateTime { get; set; }





		[Column] public string Reason { get; set; }





		[Column] public Guid? CreateSysUserId { get; set; }



	}


	[TableName("dbo.[SA_CollectingSample]")]



	[PrimaryKey("csId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_CollectingSample : ITMCTRDatabase.Record<SA_CollectingSample>  
    {



		[Column] public Guid csId { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string sampleNameCN { get; set; }





		[Column] public string sampleNameEN { get; set; }





		[Column] public string tissueCN { get; set; }





		[Column] public string tissueEN { get; set; }





		[Column] public string fateSample { get; set; }





		[Column] public string noteCN { get; set; }





		[Column] public string noteEN { get; set; }



	}


	[TableName("dbo.[SA_CollectingSampleHistory]")]



	[PrimaryKey("csId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_CollectingSampleHistory : ITMCTRDatabase.Record<SA_CollectingSampleHistory>  
    {



		[Column] public Guid csId { get; set; }





		[Column] public Guid Phid { get; set; }





		[Column] public string sampleNameCN { get; set; }





		[Column] public string sampleNameEN { get; set; }





		[Column] public string tissueCN { get; set; }





		[Column] public string tissueEN { get; set; }





		[Column] public string fateSample { get; set; }





		[Column] public string noteCN { get; set; }





		[Column] public string noteEN { get; set; }



	}


	[TableName("dbo.[SA_Diagnostic]")]



	[PrimaryKey("id", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_Diagnostic : ITMCTRDatabase.Record<SA_Diagnostic>  
    {



		[Column] public Guid id { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string standard { get; set; }





		[Column] public string standardEn { get; set; }





		[Column] public string indexTest { get; set; }





		[Column] public string indexTestEn { get; set; }





		[Column] public string targetCondition { get; set; }





		[Column] public string targetConditionEn { get; set; }





		[Column] public int? sampleSizeT { get; set; }





		[Column] public string difficultCondition { get; set; }





		[Column] public string difficultConditionEn { get; set; }





		[Column] public int? sampleSizeD { get; set; }



	}


	[TableName("dbo.[SA_Diagnostic_copy]")]



	[PrimaryKey("id", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_Diagnostic_copy : ITMCTRDatabase.Record<SA_Diagnostic_copy>  
    {



		[Column] public Guid id { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string standard { get; set; }





		[Column] public string standardEn { get; set; }





		[Column] public string indexTest { get; set; }





		[Column] public string indexTestEn { get; set; }





		[Column] public string targetCondition { get; set; }





		[Column] public string targetConditionEn { get; set; }





		[Column] public int? sampleSizeT { get; set; }





		[Column] public string difficultCondition { get; set; }





		[Column] public string difficultConditionEn { get; set; }





		[Column] public int? sampleSizeD { get; set; }



	}


	[TableName("dbo.[SA_DiagnosticHistory]")]



	[PrimaryKey("id", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_DiagnosticHistory : ITMCTRDatabase.Record<SA_DiagnosticHistory>  
    {



		[Column] public Guid id { get; set; }





		[Column] public Guid Phid { get; set; }





		[Column] public string standard { get; set; }





		[Column] public string standardEn { get; set; }





		[Column] public string indexTest { get; set; }





		[Column] public string indexTestEn { get; set; }





		[Column] public string targetCondition { get; set; }





		[Column] public string targetConditionEn { get; set; }





		[Column] public int? sampleSizeT { get; set; }





		[Column] public string difficultCondition { get; set; }





		[Column] public string difficultConditionEn { get; set; }





		[Column] public int? sampleSizeD { get; set; }



	}


	[TableName("dbo.[SA_Interventions]")]



	[PrimaryKey("inId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_Interventions : ITMCTRDatabase.Record<SA_Interventions>  
    {



		[Column] public Guid inId { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string groupsCN { get; set; }





		[Column] public string groupsEN { get; set; }





		[Column] public int? sampleSize { get; set; }





		[Column] public string interventionCN { get; set; }





		[Column] public string interventionEN { get; set; }





		[Column] public string interventionCode { get; set; }



	}


	[TableName("dbo.[SA_InterventionsHistory]")]



	[PrimaryKey("inId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_InterventionsHistory : ITMCTRDatabase.Record<SA_InterventionsHistory>  
    {



		[Column] public Guid inId { get; set; }





		[Column] public Guid Phid { get; set; }





		[Column] public string groupsCN { get; set; }





		[Column] public string groupsEN { get; set; }





		[Column] public int? sampleSize { get; set; }





		[Column] public string interventionCN { get; set; }





		[Column] public string interventionEN { get; set; }





		[Column] public string interventionCode { get; set; }



	}


	[TableName("dbo.[SA_Messaging]")]



	[PrimaryKey("MessagingId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_Messaging : ITMCTRDatabase.Record<SA_Messaging>  
    {



		[Column] public Guid MessagingId { get; set; }





		[Column] public Guid? Pid { get; set; }





		[Column] public Guid? ToUser { get; set; }





		[Column] public string ToUserName { get; set; }





		[Column] public Guid? FromUser { get; set; }





		[Column] public string FromUserName { get; set; }





		[Column] public DateTime CreateTime { get; set; }





		[Column] public int UserRead { get; set; }





		[Column] public int ManagerRead { get; set; }





		[Column] public string Content { get; set; }





		[Column] public int? isIn { get; set; }



	}


	[TableName("dbo.[SA_NewProject]")]



	[PrimaryKey("Pid", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_NewProject : ITMCTRDatabase.Record<SA_NewProject>  
    {



		[Column] public Guid Pid { get; set; }





		[Column] public string regNumber { get; set; }





		[Column] public long? regSerialNumber { get; set; }





		[Column] public string regNumberChi { get; set; }





		[Column] public DateTime? regNumberTime { get; set; }





		[Column] public string filloutLanguage { get; set; }





		[Column] public string registrationStatus { get; set; }





		[Column] public string publicTitleCN { get; set; }





		[Column] public string publicTitleEN { get; set; }





		[Column] public string titleAcronymCN { get; set; }





		[Column] public string titleAcronymEN { get; set; }





		[Column] public string scientifirTitleCN { get; set; }





		[Column] public string scientifirTitleEN { get; set; }





		[Column] public string scientifirAcronymCN { get; set; }





		[Column] public string scientifirAcronymEN { get; set; }





		[Column] public string studyID { get; set; }





		[Column] public string applicantCN { get; set; }





		[Column] public string applicantEN { get; set; }





		[Column] public string studyLeaderCN { get; set; }





		[Column] public string studyLeaderEN { get; set; }





		[Column] public string applicantTelephone { get; set; }





		[Column] public string studyTelephone { get; set; }





		[Column] public string applicanFax { get; set; }





		[Column] public string studyFax { get; set; }





		[Column] public string applicantEmail { get; set; }





		[Column] public string studyEmail { get; set; }





		[Column] public string applicantWebsite { get; set; }





		[Column] public string studyWebsite { get; set; }





		[Column] public string applicantAddressCN { get; set; }





		[Column] public string applicantAddressEN { get; set; }





		[Column] public string studyAddressCN { get; set; }





		[Column] public string studyAddressEN { get; set; }





		[Column] public string applicantPostcode { get; set; }





		[Column] public string studyPostcode { get; set; }





		[Column] public string applicantInstitutionCN { get; set; }





		[Column] public string applicantInstitutionEN { get; set; }





		[Column] public string studyLeaderCompanyCN { get; set; }





		[Column] public string studyLeaderCompanyEN { get; set; }





		[Column] public int? approvedCommittee { get; set; }





		[Column] public string ethicalCommitteeFileID { get; set; }





		[Column] public string fileEthicalCommittee { get; set; }





		[Column] public string ethicalCommitteeName { get; set; }





		[Column] public string ethicalCommitteeNameEN { get; set; }





		[Column] public DateTime? ethicalCommitteeSanctionDate { get; set; }





		[Column] public string ethicalCommitteeCName { get; set; }





		[Column] public string ethicalCommitteeCNameEN { get; set; }





		[Column] public string ethicalCommitteeCAddress { get; set; }





		[Column] public string ethicalCommitteeCAddressEN { get; set; }





		[Column] public string ethicalCommitteeCPhone { get; set; }





		[Column] public string ethicalCommitteeCEmail { get; set; }





		[Column] public string of_SFDA { get; set; }





		[Column] public string fileSFDA { get; set; }





		[Column] public DateTime? dataSFDA { get; set; }





		[Column] public string studyPlanfile { get; set; }





		[Column] public string informedConsentfile { get; set; }





		[Column] public string primarySponsorCN { get; set; }





		[Column] public string primarySponsorEN { get; set; }





		[Column] public string primarySponsorAddressCN { get; set; }





		[Column] public string primarySponsorAddressEN { get; set; }





		[Column] public string sourceFundingCN { get; set; }





		[Column] public string sourceFundingEN { get; set; }





		[Column] public string targetDiseaseCN { get; set; }





		[Column] public string targetDiseaseEN { get; set; }





		[Column] public string targetCode { get; set; }





		[Column] public string studyTypeID { get; set; }





		[Column] public string studyDesignID { get; set; }





		[Column] public string studyPhaseID { get; set; }





		[Column] public string objectivesStudyCN { get; set; }





		[Column] public string objectivesStudyEN { get; set; }





		[Column] public string contentsDrugCN { get; set; }





		[Column] public string contentsDrugEN { get; set; }





		[Column] public string inclusionCriteriaCN { get; set; }





		[Column] public string inclusionCriteriaEN { get; set; }





		[Column] public string exclusionCrteriaCN { get; set; }





		[Column] public string exclusionCrteriaEN { get; set; }





		[Column] public DateTime? studyTimeStart { get; set; }





		[Column] public DateTime? studyTimeEnd { get; set; }





		[Column] public DateTime? recruitingTimeStart { get; set; }





		[Column] public DateTime? recruitingTimeEnd { get; set; }





		[Column] public string totalSampleSize { get; set; }





		[Column] public string recruitingStatus { get; set; }





		[Column] public string ageMin { get; set; }





		[Column] public string ageMax { get; set; }





		[Column] public string randomMethodCN { get; set; }





		[Column] public string randomMethodEN { get; set; }





		[Column] public string sex { get; set; }





		[Column] public int? signConsent { get; set; }





		[Column] public string followupTime { get; set; }





		[Column] public string followup { get; set; }





		[Column] public string processConcealmentCN { get; set; }





		[Column] public string processConcealmentEN { get; set; }





		[Column] public string blindingCN { get; set; }





		[Column] public string blindingEN { get; set; }





		[Column] public string RulesblindingCN { get; set; }





		[Column] public string RulesblindingEN { get; set; }





		[Column] public string statisticalMethodCN { get; set; }





		[Column] public string statisticalMethodEN { get; set; }





		[Column] public string calculatedResultsCN { get; set; }





		[Column] public string calculatedResultsEN { get; set; }





		[Column] public int? whetherPublic { get; set; }





		[Column] public string dataCollectionCN { get; set; }





		[Column] public string dataManagementCN { get; set; }





		[Column] public string dataManagementEN { get; set; }





		[Column] public string dataAnalysisCN { get; set; }





		[Column] public string dataAnalysisEN { get; set; }





		[Column] public string regIP { get; set; }





		[Column] public DateTime? regTime { get; set; }





		[Column] public string modifyBy { get; set; }





		[Column] public DateTime? modifyTime { get; set; }





		[Column] public string modifyIP { get; set; }





		[Column] public int? status { get; set; }





		[Column] public Guid? createUserID { get; set; }





		[Column] public string fileExperimentalresults { get; set; }





		[Column] public int? SubmitStatus { get; set; }





		[Column] public string studyReport { get; set; }





		[Column] public string studyReportEN { get; set; }





		[Column] public string ProjectOriginCode { get; set; }





		[Column] public string ProjectOriginCn { get; set; }





		[Column] public string verifyStatusDesc { get; set; }





		[Column] public int? taskStatus { get; set; }





		[Column] public Guid? executeTaskSysUserId { get; set; }





		[Column] public Guid? sendTaskSysUserId { get; set; }





		[Column] public string reverifyFailReason { get; set; }





		[Column] public int? isDeleted { get; set; }





		[Column] public string editRequestResult { get; set; }





		[Column] public string editRequestReason { get; set; }





		[Column] public string secondaryID { get; set; }





		[Column] public string UTN { get; set; }





		[Column] public string DataManagemenBoard { get; set; }





		[Column] public string dataCollectionEN { get; set; }





		[Column] public string DataCollectionUnit { get; set; }





		[Column] public string editRequestAttachment { get; set; }





		[Column] public string ReleaseNumber { get; set; }





		[Column] public int? sourcefrom { get; set; }





		[Column] public int? isTraditionalMedicine { get; set; }





		[Column] public Guid? firstTaskSysUserId { get; set; }



	}


	[TableName("dbo.[SA_NewProjectHistory]")]



	[PrimaryKey("Phid", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_NewProjectHistory : ITMCTRDatabase.Record<SA_NewProjectHistory>  
    {



		[Column] public Guid Phid { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string regNumber { get; set; }





		[Column] public long? regSerialNumber { get; set; }





		[Column] public string regNumberChi { get; set; }





		[Column] public DateTime? regNumberTime { get; set; }





		[Column] public string filloutLanguage { get; set; }





		[Column] public string registrationStatus { get; set; }





		[Column] public string publicTitleCN { get; set; }





		[Column] public string publicTitleEN { get; set; }





		[Column] public string titleAcronymCN { get; set; }





		[Column] public string titleAcronymEN { get; set; }





		[Column] public string scientifirTitleCN { get; set; }





		[Column] public string scientifirTitleEN { get; set; }





		[Column] public string scientifirAcronymCN { get; set; }





		[Column] public string scientifirAcronymEN { get; set; }





		[Column] public string studyID { get; set; }





		[Column] public string applicantCN { get; set; }





		[Column] public string applicantEN { get; set; }





		[Column] public string studyLeaderCN { get; set; }





		[Column] public string studyLeaderEN { get; set; }





		[Column] public string applicantTelephone { get; set; }





		[Column] public string studyTelephone { get; set; }





		[Column] public string applicanFax { get; set; }





		[Column] public string studyFax { get; set; }





		[Column] public string applicantEmail { get; set; }





		[Column] public string studyEmail { get; set; }





		[Column] public string applicantWebsite { get; set; }





		[Column] public string studyWebsite { get; set; }





		[Column] public string applicantAddressCN { get; set; }





		[Column] public string applicantAddressEN { get; set; }





		[Column] public string studyAddressCN { get; set; }





		[Column] public string studyAddressEN { get; set; }





		[Column] public string applicantPostcode { get; set; }





		[Column] public string studyPostcode { get; set; }





		[Column] public string applicantInstitutionCN { get; set; }





		[Column] public string applicantInstitutionEN { get; set; }





		[Column] public string studyLeaderCompanyCN { get; set; }





		[Column] public string studyLeaderCompanyEN { get; set; }





		[Column] public int? approvedCommittee { get; set; }





		[Column] public string ethicalCommitteeFileID { get; set; }





		[Column] public string fileEthicalCommittee { get; set; }





		[Column] public string ethicalCommitteeName { get; set; }





		[Column] public string ethicalCommitteeNameEN { get; set; }





		[Column] public DateTime? ethicalCommitteeSanctionDate { get; set; }





		[Column] public string ethicalCommitteeCName { get; set; }





		[Column] public string ethicalCommitteeCNameEN { get; set; }





		[Column] public string ethicalCommitteeCAddress { get; set; }





		[Column] public string ethicalCommitteeCAddressEN { get; set; }





		[Column] public string ethicalCommitteeCPhone { get; set; }





		[Column] public string ethicalCommitteeCEmail { get; set; }





		[Column] public string of_SFDA { get; set; }





		[Column] public string fileSFDA { get; set; }





		[Column] public DateTime? dataSFDA { get; set; }





		[Column] public string studyPlanfile { get; set; }





		[Column] public string informedConsentfile { get; set; }





		[Column] public string primarySponsorCN { get; set; }





		[Column] public string primarySponsorEN { get; set; }





		[Column] public string primarySponsorAddressCN { get; set; }





		[Column] public string primarySponsorAddressEN { get; set; }





		[Column] public string sourceFundingCN { get; set; }





		[Column] public string sourceFundingEN { get; set; }





		[Column] public string targetDiseaseCN { get; set; }





		[Column] public string targetDiseaseEN { get; set; }





		[Column] public string targetCode { get; set; }





		[Column] public string studyTypeID { get; set; }





		[Column] public string studyDesignID { get; set; }





		[Column] public string studyPhaseID { get; set; }





		[Column] public string objectivesStudyCN { get; set; }





		[Column] public string objectivesStudyEN { get; set; }





		[Column] public string contentsDrugCN { get; set; }





		[Column] public string contentsDrugEN { get; set; }





		[Column] public string inclusionCriteriaCN { get; set; }





		[Column] public string inclusionCriteriaEN { get; set; }





		[Column] public string exclusionCrteriaCN { get; set; }





		[Column] public string exclusionCrteriaEN { get; set; }





		[Column] public DateTime? studyTimeStart { get; set; }





		[Column] public DateTime? studyTimeEnd { get; set; }





		[Column] public DateTime? recruitingTimeStart { get; set; }





		[Column] public DateTime? recruitingTimeEnd { get; set; }





		[Column] public string totalSampleSize { get; set; }





		[Column] public string recruitingStatus { get; set; }





		[Column] public string ageMin { get; set; }





		[Column] public string ageMax { get; set; }





		[Column] public string randomMethodCN { get; set; }





		[Column] public string randomMethodEN { get; set; }





		[Column] public string sex { get; set; }





		[Column] public int? signConsent { get; set; }





		[Column] public string followupTime { get; set; }





		[Column] public string followup { get; set; }





		[Column] public string processConcealmentCN { get; set; }





		[Column] public string processConcealmentEN { get; set; }





		[Column] public string blindingCN { get; set; }





		[Column] public string blindingEN { get; set; }





		[Column] public string RulesblindingCN { get; set; }





		[Column] public string RulesblindingEN { get; set; }





		[Column] public string statisticalMethodCN { get; set; }





		[Column] public string statisticalMethodEN { get; set; }





		[Column] public string calculatedResultsCN { get; set; }





		[Column] public string calculatedResultsEN { get; set; }





		[Column] public int? whetherPublic { get; set; }





		[Column] public string dataCollectionCN { get; set; }





		[Column] public string dataManagementCN { get; set; }





		[Column] public string dataManagementEN { get; set; }





		[Column] public string dataAnalysisCN { get; set; }





		[Column] public string dataAnalysisEN { get; set; }





		[Column] public string regIP { get; set; }





		[Column] public DateTime? regTime { get; set; }





		[Column] public string modifyBy { get; set; }





		[Column] public DateTime? modifyTime { get; set; }





		[Column] public string modifyIP { get; set; }





		[Column] public int? status { get; set; }





		[Column] public Guid? createUserID { get; set; }





		[Column] public string fileExperimentalresults { get; set; }





		[Column] public int? SubmitStatus { get; set; }





		[Column] public string studyReport { get; set; }





		[Column] public string studyReportEN { get; set; }





		[Column] public string ProjectOriginCode { get; set; }





		[Column] public string ProjectOriginCn { get; set; }





		[Column] public string vefifyStatusDesc { get; set; }





		[Column] public DateTime? operateTime { get; set; }





		[Column] public Guid? operateUserId { get; set; }





		[Column] public int? taskStatus { get; set; }





		[Column] public Guid? sendTaskSysUserId { get; set; }





		[Column] public Guid? executeTaskSysUserId { get; set; }





		[Column] public string reverifyFailReason { get; set; }





		[Column] public string editRequestReason { get; set; }





		[Column] public string editRequestResult { get; set; }





		[Column] public Guid? PrefId { get; set; }





		[Column] public string secondaryID { get; set; }





		[Column] public string UTN { get; set; }





		[Column] public string DataManagemenBoard { get; set; }





		[Column] public string dataCollectionEN { get; set; }





		[Column] public string DataCollectionUnit { get; set; }





		[Column] public string editRequestAttachment { get; set; }





		[Column] public string version { get; set; }





		[Column] public string ReleaseNumber { get; set; }



	}


	[TableName("dbo.[SA_News]")]



	[PrimaryKey("Nid", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_News : ITMCTRDatabase.Record<SA_News>  
    {



		[Column] public Guid Nid { get; set; }





		[Column] public Guid NtId { get; set; }





		[Column] public string Title { get; set; }





		[Column] public string Subtitle { get; set; }





		[Column] public string Content { get; set; }





		[Column] public DateTime? ReleaseTime { get; set; }





		[Column] public string Author { get; set; }





		[Column] public string AuthorUnit { get; set; }





		[Column] public string Source { get; set; }





		[Column] public Guid? CreateSysUserId { get; set; }





		[Column] public DateTime? CreateTime { get; set; }





		[Column] public int? IsRelease { get; set; }





		[Column] public int? IsDeleted { get; set; }





		[Column] public int? IsViewIndex { get; set; }





		[Column] public int? Sort { get; set; }





		[Column] public int? IsTop { get; set; }





		[Column] public string TitleEN { get; set; }





		[Column] public string SubtitleEN { get; set; }





		[Column] public string ContentEN { get; set; }





		[Column] public string AuthorEN { get; set; }





		[Column] public string AuthorUnitEN { get; set; }





		[Column] public string SourceEN { get; set; }



	}


	[TableName("dbo.[SA_NewsType]")]



	[PrimaryKey("NtId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_NewsType : ITMCTRDatabase.Record<SA_NewsType>  
    {



		[Column] public Guid NtId { get; set; }





		[Column] public string Name { get; set; }





		[Column] public string Desc { get; set; }





		[Column] public DateTime? CreateTime { get; set; }





		[Column] public Guid? CreateSysUserId { get; set; }





		[Column] public int? IsView { get; set; }





		[Column] public int? IsIndexView { get; set; }





		[Column] public string NameEN { get; set; }





		[Column] public string DescEN { get; set; }



	}


	[TableName("dbo.[SA_Notice]")]



	[PrimaryKey("Nid", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_Notice : ITMCTRDatabase.Record<SA_Notice>  
    {



		[Column] public Guid Nid { get; set; }





		[Column] public string Title { get; set; }





		[Column] public string Content { get; set; }





		[Column] public string AccessUrl { get; set; }





		[Column] public Guid? SendUserId { get; set; }





		[Column] public Guid? ReceiveUserId { get; set; }





		[Column] public int? IsRead { get; set; }





		[Column] public DateTime? CreateTime { get; set; }





		[Column] public string TitleEN { get; set; }





		[Column] public string ContentEN { get; set; }





		[Column] public Guid? Pid { get; set; }





		[Column] public int? Type { get; set; }



	}


	[TableName("dbo.[SA_Outcomes]")]



	[PrimaryKey("ouId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_Outcomes : ITMCTRDatabase.Record<SA_Outcomes>  
    {



		[Column] public Guid ouId { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string outcomeNameCN { get; set; }





		[Column] public string outcomeNameEN { get; set; }





		[Column] public string pointerType { get; set; }





		[Column] public string measureTimeCN { get; set; }





		[Column] public string measureMethodCN { get; set; }





		[Column] public string measureTimeEN { get; set; }





		[Column] public string measureMethodEN { get; set; }



	}


	[TableName("dbo.[SA_OutcomesHistory]")]



	[PrimaryKey("ouId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_OutcomesHistory : ITMCTRDatabase.Record<SA_OutcomesHistory>  
    {



		[Column] public Guid ouId { get; set; }





		[Column] public Guid Phid { get; set; }





		[Column] public string outcomeNameCN { get; set; }





		[Column] public string outcomeNameEN { get; set; }





		[Column] public string pointerType { get; set; }





		[Column] public string measureTimeCN { get; set; }





		[Column] public string measureMethodCN { get; set; }





		[Column] public string measureTimeEN { get; set; }





		[Column] public string measureMethodEN { get; set; }



	}


	[TableName("dbo.[SA_ProjectDataUpload]")]



	[PrimaryKey("PduId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_ProjectDataUpload : ITMCTRDatabase.Record<SA_ProjectDataUpload>  
    {



		[Column] public Guid PduId { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string DataTitle { get; set; }





		[Column] public string DataDesc { get; set; }





		[Column] public DateTime? CreateTime { get; set; }





		[Column] public Guid? CreateUserId { get; set; }





		[Column] public string FilePath { get; set; }





		[Column] public string DataTitleEN { get; set; }





		[Column] public string DataDescEN { get; set; }



	}


	[TableName("dbo.[SA_ProjectReportUpload]")]



	[PrimaryKey("PruId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_ProjectReportUpload : ITMCTRDatabase.Record<SA_ProjectReportUpload>  
    {



		[Column] public Guid PruId { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string ReportTitle { get; set; }





		[Column] public string ReportDesc { get; set; }





		[Column] public DateTime? CreateTime { get; set; }





		[Column] public Guid? CreateUserId { get; set; }





		[Column] public string FilePath { get; set; }





		[Column] public string ReportTitleEN { get; set; }





		[Column] public string ReportDescEN { get; set; }



	}


	[TableName("dbo.[SA_ProjectRequestEditFlow]")]



	[PrimaryKey("PrefId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_ProjectRequestEditFlow : ITMCTRDatabase.Record<SA_ProjectRequestEditFlow>  
    {



		[Column] public Guid PrefId { get; set; }





		[Column] public Guid? RequestUserId { get; set; }





		[Column] public Guid? Pid { get; set; }





		[Column] public DateTime? RequestTime { get; set; }





		[Column] public Guid? ApplySysUserId { get; set; }





		[Column] public DateTime? ApplyTime { get; set; }





		[Column] public int? RequestStatus { get; set; }





		[Column] public string EditRequestResult { get; set; }





		[Column] public string EditRequestReason { get; set; }





		[Column] public string EditRequestAttachment { get; set; }





		[Column] public string EditRequestAttachment2 { get; set; }



	}


	[TableName("dbo.[SA_ProjectVerifyTaskFlowRecord]")]



	[PrimaryKey("PvtfrId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_ProjectVerifyTaskFlowRecord : ITMCTRDatabase.Record<SA_ProjectVerifyTaskFlowRecord>  
    {



		[Column] public Guid PvtfrId { get; set; }





		[Column] public Guid? Pid { get; set; }





		[Column] public int? VerifyTaskStatus { get; set; }





		[Column] public DateTime? CreateTime { get; set; }





		[Column] public string TaskFlowCreateSysUserId { get; set; }





		[Column] public string Reason { get; set; }





		[Column] public int? ProjectStatus { get; set; }





		[Column] public Guid? LevelOneSysUserId { get; set; }





		[Column] public Guid? LevelSecondSysUserId { get; set; }





		[Column] public Guid? SubmitUserId { get; set; }



	}


	[TableName("dbo.[SA_ResearchAddress]")]



	[PrimaryKey("raId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_ResearchAddress : ITMCTRDatabase.Record<SA_ResearchAddress>  
    {



		[Column] public Guid raId { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string countryCN { get; set; }





		[Column] public string countryEN { get; set; }





		[Column] public string provinceCN { get; set; }





		[Column] public string provinceEN { get; set; }





		[Column] public string cityCN { get; set; }





		[Column] public string cityEN { get; set; }





		[Column] public string hospitalCN { get; set; }





		[Column] public string hospitalEN { get; set; }





		[Column] public string levelInstitutionCN { get; set; }





		[Column] public string levelInstitutionEN { get; set; }



	}


	[TableName("dbo.[SA_ResearchAddressHistory]")]



	[PrimaryKey("raId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_ResearchAddressHistory : ITMCTRDatabase.Record<SA_ResearchAddressHistory>  
    {



		[Column] public Guid raId { get; set; }





		[Column] public Guid Phid { get; set; }





		[Column] public string countryCN { get; set; }





		[Column] public string countryEN { get; set; }





		[Column] public string provinceCN { get; set; }





		[Column] public string provinceEN { get; set; }





		[Column] public string cityCN { get; set; }





		[Column] public string cityEN { get; set; }





		[Column] public string hospitalCN { get; set; }





		[Column] public string hospitalEN { get; set; }





		[Column] public string levelInstitutionCN { get; set; }





		[Column] public string levelInstitutionEN { get; set; }



	}


	[TableName("dbo.[SA_RoleFuns]")]



	[PrimaryKey("RoleFunId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_RoleFuns : ITMCTRDatabase.Record<SA_RoleFuns>  
    {



		[Column] public Guid RoleFunId { get; set; }





		[Column] public Guid? RoleId { get; set; }





		[Column] public Guid? FunId { get; set; }



	}


	[TableName("dbo.[SA_SecondarySponsor]")]



	[PrimaryKey("ssId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_SecondarySponsor : ITMCTRDatabase.Record<SA_SecondarySponsor>  
    {



		[Column] public Guid ssId { get; set; }





		[Column] public Guid Pid { get; set; }





		[Column] public string countryCN { get; set; }





		[Column] public string countryEN { get; set; }





		[Column] public string provinceCN { get; set; }





		[Column] public string provinceEN { get; set; }





		[Column] public string cityCN { get; set; }





		[Column] public string cityEN { get; set; }





		[Column] public string institutionCN { get; set; }





		[Column] public string institutionEN { get; set; }





		[Column] public string specificAddressCN { get; set; }





		[Column] public string specificAddressEN { get; set; }



	}


	[TableName("dbo.[SA_SecondarySponsorHistory]")]



	[PrimaryKey("ssId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_SecondarySponsorHistory : ITMCTRDatabase.Record<SA_SecondarySponsorHistory>  
    {



		[Column] public Guid ssId { get; set; }





		[Column] public Guid Phid { get; set; }





		[Column] public string countryCN { get; set; }





		[Column] public string countryEN { get; set; }





		[Column] public string provinceCN { get; set; }





		[Column] public string provinceEN { get; set; }





		[Column] public string cityCN { get; set; }





		[Column] public string cityEN { get; set; }





		[Column] public string institutionCN { get; set; }





		[Column] public string institutionEN { get; set; }





		[Column] public string specificAddressCN { get; set; }





		[Column] public string specificAddressEN { get; set; }



	}


	[TableName("dbo.[SA_StudyDictionary]")]



	[PrimaryKey("Sid", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_StudyDictionary : ITMCTRDatabase.Record<SA_StudyDictionary>  
    {



		[Column] public Guid Sid { get; set; }





		[Column] public string StudyName { get; set; }





		[Column] public string StudyValue { get; set; }





		[Column] public string DescriptionCn { get; set; }





		[Column] public string DescriptionEn { get; set; }





		[Column] public DateTime? CreateDatetime { get; set; }





		[Column] public int? Ishide { get; set; }





		[Column] public int? Sort { get; set; }



	}


	[TableName("dbo.[SA_SysFuns]")]



	[PrimaryKey("FunId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_SysFuns : ITMCTRDatabase.Record<SA_SysFuns>  
    {



		[Column] public Guid FunId { get; set; }





		[Column] public string FunName { get; set; }





		[Column] public string FunFloor { get; set; }





		[Column] public int? FunKind { get; set; }





		[Column] public string FunURL { get; set; }





		[Column] public string FunNameEN { get; set; }



	}


	[TableName("dbo.[SA_SysRoles]")]



	[PrimaryKey("RoleId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_SysRoles : ITMCTRDatabase.Record<SA_SysRoles>  
    {



		[Column] public Guid RoleId { get; set; }





		[Column] public string RoleName { get; set; }





		[Column] public string RoleDesc { get; set; }





		[Column] public int? IsSysRole { get; set; }



	}


	[TableName("dbo.[SA_SysUser]")]



	[PrimaryKey("Uid", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_SysUser : ITMCTRDatabase.Record<SA_SysUser>  
    {



		[Column] public Guid Uid { get; set; }





		[Column] public string Username { get; set; }





		[Column] public string Password { get; set; }





		[Column] public string Name { get; set; }





		[Column] public string Email { get; set; }





		[Column] public DateTime? RegDate { get; set; }





		[Column] public string RegIP { get; set; }





		[Column] public DateTime? LastLoginDate { get; set; }





		[Column] public string LastLoginIp { get; set; }





		[Column] public string Sex { get; set; }





		[Column] public string Phone { get; set; }





		[Column] public string Country { get; set; }





		[Column] public string CellPhone { get; set; }





		[Column] public string RegUnit { get; set; }





		[Column] public string RegAddress { get; set; }





		[Column] public Guid? SysRoleId { get; set; }





		[Column] public string Photograph { get; set; }





		[Column] public int? IsSysUser { get; set; }





		[Column] public string ParentId { get; set; }



	}


	[TableName("dbo.[SA_Users]")]



	[PrimaryKey("Uid", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_Users : ITMCTRDatabase.Record<SA_Users>  
    {



		[Column] public Guid Uid { get; set; }





		[Column] public string Username { get; set; }





		[Column] public string Password { get; set; }





		[Column] public string Name { get; set; }





		[Column] public string Email { get; set; }





		[Column] public DateTime? RegDate { get; set; }





		[Column] public string RegIP { get; set; }





		[Column] public DateTime? LastLoginDate { get; set; }





		[Column] public string LastLoginIp { get; set; }





		[Column] public string Sex { get; set; }





		[Column] public string Phone { get; set; }





		[Column] public string Country { get; set; }





		[Column] public string CellPhone { get; set; }





		[Column] public string RegUnit { get; set; }





		[Column] public string RegAddress { get; set; }





		[Column] public string Photograph { get; set; }





		[Column] public int? Status { get; set; }



	}


	[TableName("dbo.[SA_VerificationCode]")]



	[PrimaryKey("Vid", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_VerificationCode : ITMCTRDatabase.Record<SA_VerificationCode>  
    {



		[Column] public Guid Vid { get; set; }





		[Column] public Guid? Uid { get; set; }





		[Column] public string RandomCode { get; set; }





		[Column] public DateTime CreateTime { get; set; }





		[Column] public int UseType { get; set; }





		[Column] public bool IsUsed { get; set; }



	}


	[TableName("dbo.[SA_WebsiteInformation]")]



	[PrimaryKey("WiId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_WebsiteInformation : ITMCTRDatabase.Record<SA_WebsiteInformation>  
    {



		[Column] public Guid WiId { get; set; }





		[Column] public string InfoCode { get; set; }





		[Column] public string Title { get; set; }





		[Column] public string TitleEN { get; set; }





		[Column] public string Subtitle { get; set; }





		[Column] public string SubtitleEN { get; set; }





		[Column] public string Content { get; set; }





		[Column] public string ContentEN { get; set; }





		[Column] public DateTime? ReleaseTime { get; set; }





		[Column] public string Author { get; set; }





		[Column] public string AuthorEN { get; set; }





		[Column] public string AuthorUnit { get; set; }





		[Column] public string AuthorUnitEN { get; set; }





		[Column] public string Source { get; set; }





		[Column] public string SourceEN { get; set; }





		[Column] public Guid? CreateSysUserId { get; set; }





		[Column] public DateTime? CreateTime { get; set; }





		[Column] public int? IsRelease { get; set; }





		[Column] public int? IsDeleted { get; set; }





		[Column] public int? IsViewIndex { get; set; }





		[Column] public int? Sort { get; set; }





		[Column] public int? IsTop { get; set; }



	}


	[TableName("dbo.[SA_WebStatus]")]



	[PrimaryKey("WebStatusId", AutoIncrement=false)]


	[ExplicitColumns]

	[Serializable]
    public partial class SA_WebStatus : ITMCTRDatabase.Record<SA_WebStatus>  
    {



		[Column] public Guid WebStatusId { get; set; }





		[Column] public string ContentEn { get; set; }





		[Column] public string Content { get; set; }





		[Column] public bool IsOpen { get; set; }





		[Column] public DateTime? EndTime { get; set; }





		[Column] public bool IsShowLogin { get; set; }



	}


	[TableName("dbo.[V_NewProject]")]




	[ExplicitColumns]

	[Serializable]
    public partial class V_NewProject : ITMCTRDatabase.Record<V_NewProject>  
    {



		[Column] public Guid Pid { get; set; }





		[Column] public string regNumber { get; set; }





		[Column] public long? regSerialNumber { get; set; }





		[Column] public string regNumberChi { get; set; }





		[Column] public DateTime? regNumberTime { get; set; }





		[Column] public string filloutLanguage { get; set; }





		[Column] public string registrationStatus { get; set; }





		[Column] public string publicTitleCN { get; set; }





		[Column] public string publicTitleEN { get; set; }





		[Column] public string titleAcronymCN { get; set; }





		[Column] public string titleAcronymEN { get; set; }





		[Column] public string scientifirTitleCN { get; set; }





		[Column] public string scientifirTitleEN { get; set; }





		[Column] public string scientifirAcronymCN { get; set; }





		[Column] public string scientifirAcronymEN { get; set; }





		[Column] public string studyID { get; set; }





		[Column] public string applicantCN { get; set; }





		[Column] public string applicantEN { get; set; }





		[Column] public string studyLeaderCN { get; set; }





		[Column] public string studyLeaderEN { get; set; }





		[Column] public string applicantTelephone { get; set; }





		[Column] public string studyTelephone { get; set; }





		[Column] public string applicanFax { get; set; }





		[Column] public string studyFax { get; set; }





		[Column] public string applicantEmail { get; set; }





		[Column] public string studyEmail { get; set; }





		[Column] public string applicantWebsite { get; set; }





		[Column] public string studyWebsite { get; set; }





		[Column] public string applicantAddressCN { get; set; }





		[Column] public string applicantAddressEN { get; set; }





		[Column] public string studyAddressCN { get; set; }





		[Column] public string studyAddressEN { get; set; }





		[Column] public string applicantPostcode { get; set; }





		[Column] public string studyPostcode { get; set; }





		[Column] public string applicantInstitutionCN { get; set; }





		[Column] public string applicantInstitutionEN { get; set; }





		[Column] public string studyLeaderCompanyCN { get; set; }





		[Column] public string studyLeaderCompanyEN { get; set; }





		[Column] public int? approvedCommittee { get; set; }





		[Column] public string ethicalCommitteeFileID { get; set; }





		[Column] public string fileEthicalCommittee { get; set; }





		[Column] public string ethicalCommitteeName { get; set; }





		[Column] public string ethicalCommitteeNameEN { get; set; }





		[Column] public DateTime? ethicalCommitteeSanctionDate { get; set; }





		[Column] public string ethicalCommitteeCName { get; set; }





		[Column] public string ethicalCommitteeCNameEN { get; set; }





		[Column] public string ethicalCommitteeCAddress { get; set; }





		[Column] public string ethicalCommitteeCAddressEN { get; set; }





		[Column] public string ethicalCommitteeCPhone { get; set; }





		[Column] public string ethicalCommitteeCEmail { get; set; }





		[Column] public string of_SFDA { get; set; }





		[Column] public string fileSFDA { get; set; }





		[Column] public DateTime? dataSFDA { get; set; }





		[Column] public string studyPlanfile { get; set; }





		[Column] public string informedConsentfile { get; set; }





		[Column] public string primarySponsorCN { get; set; }





		[Column] public string primarySponsorEN { get; set; }





		[Column] public string primarySponsorAddressCN { get; set; }





		[Column] public string primarySponsorAddressEN { get; set; }





		[Column] public string sourceFundingCN { get; set; }





		[Column] public string sourceFundingEN { get; set; }





		[Column] public string targetDiseaseCN { get; set; }





		[Column] public string targetDiseaseEN { get; set; }





		[Column] public string targetCode { get; set; }





		[Column] public string studyTypeID { get; set; }





		[Column] public string studyDesignID { get; set; }





		[Column] public string studyPhaseID { get; set; }





		[Column] public string objectivesStudyCN { get; set; }





		[Column] public string objectivesStudyEN { get; set; }





		[Column] public string contentsDrugCN { get; set; }





		[Column] public string contentsDrugEN { get; set; }





		[Column] public string inclusionCriteriaCN { get; set; }





		[Column] public string inclusionCriteriaEN { get; set; }





		[Column] public string exclusionCrteriaCN { get; set; }





		[Column] public string exclusionCrteriaEN { get; set; }





		[Column] public DateTime? studyTimeStart { get; set; }





		[Column] public DateTime? studyTimeEnd { get; set; }





		[Column] public DateTime? recruitingTimeStart { get; set; }





		[Column] public DateTime? recruitingTimeEnd { get; set; }





		[Column] public string totalSampleSize { get; set; }





		[Column] public string recruitingStatus { get; set; }





		[Column] public string ageMin { get; set; }





		[Column] public string ageMax { get; set; }





		[Column] public string randomMethodCN { get; set; }





		[Column] public string randomMethodEN { get; set; }





		[Column] public string sex { get; set; }





		[Column] public int? signConsent { get; set; }





		[Column] public string followupTime { get; set; }





		[Column] public string followup { get; set; }





		[Column] public string processConcealmentCN { get; set; }





		[Column] public string processConcealmentEN { get; set; }





		[Column] public string blindingCN { get; set; }





		[Column] public string blindingEN { get; set; }





		[Column] public string RulesblindingCN { get; set; }





		[Column] public string RulesblindingEN { get; set; }





		[Column] public string statisticalMethodCN { get; set; }





		[Column] public string statisticalMethodEN { get; set; }





		[Column] public string calculatedResultsCN { get; set; }





		[Column] public string calculatedResultsEN { get; set; }





		[Column] public int? whetherPublic { get; set; }





		[Column] public string dataCollectionCN { get; set; }





		[Column] public string dataManagementCN { get; set; }





		[Column] public string dataManagementEN { get; set; }





		[Column] public string dataAnalysisCN { get; set; }





		[Column] public string dataAnalysisEN { get; set; }





		[Column] public string regIP { get; set; }





		[Column] public DateTime? regTime { get; set; }





		[Column] public string modifyBy { get; set; }





		[Column] public DateTime? modifyTime { get; set; }





		[Column] public string modifyIP { get; set; }





		[Column] public int? status { get; set; }





		[Column] public Guid? createUserID { get; set; }





		[Column] public string fileExperimentalresults { get; set; }





		[Column] public int? SubmitStatus { get; set; }





		[Column] public string studyReport { get; set; }





		[Column] public string studyReportEN { get; set; }





		[Column] public string ProjectOriginCode { get; set; }





		[Column] public string ProjectOriginCn { get; set; }





		[Column] public string verifyStatusDesc { get; set; }





		[Column] public int? taskStatus { get; set; }





		[Column] public Guid? executeTaskSysUserId { get; set; }





		[Column] public Guid? sendTaskSysUserId { get; set; }





		[Column] public string reverifyFailReason { get; set; }





		[Column] public int? isDeleted { get; set; }





		[Column] public string editRequestResult { get; set; }





		[Column] public string editRequestReason { get; set; }





		[Column] public string secondaryID { get; set; }





		[Column] public string UTN { get; set; }





		[Column] public string DataManagemenBoard { get; set; }





		[Column] public string dataCollectionEN { get; set; }





		[Column] public string DataCollectionUnit { get; set; }





		[Column] public string editRequestAttachment { get; set; }





		[Column] public string ReleaseNumber { get; set; }





		[Column] public int? sourcefrom { get; set; }





		[Column] public int? isTraditionalMedicine { get; set; }





		[Column] public Guid? firstTaskSysUserId { get; set; }





		[Column] public DateTime? flowRecordTime { get; set; }





		[Column] public DateTime? flowRecordBackTime { get; set; }





		[Column] public DateTime? flowLastSubmitTime { get; set; }





		[Column] public string sendTaskUser { get; set; }





		[Column] public string executeTaskUser { get; set; }





		[Column] public string firstTaskUser { get; set; }



	}


}
