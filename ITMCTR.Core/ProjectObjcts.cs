using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITMCTR.Core
{
    public enum ProjectStatus : int
    {
        未填完 = 0,
        待审核 = 1,
        未通过审核 = 2,
        通过审核 = 3
    }
    public enum ProjectTaskStatus : int
    {
        无 = 0,
        待分配 = 1,
        已分配 = 2,
        待复核 = 3,
        复核未通过 = 4,
        复核通过 = 5,
        再修改申请 = 6,
        申请通过 = 7,
        申请未通过 = 8,
        三审最终审核 = 9
    }
    public enum VerifyTaskStatus : int
    {
        无 = 0,
        任务分配 = 1,
        审核任务执行 = 2,
        复核未通过 = 3,
        复核通过 = 4,
        用户提交=5,
        判断任务 = 6,
        召回 = 7
    }

}
