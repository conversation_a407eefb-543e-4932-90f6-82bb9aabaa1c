@using System.Globalization;
<div class="zhuce_top">
    <div style="width:1150px;margin:0px auto; padding-top:25px;">
        <div class="top2">
            <div class="top3"><a href="@Url.Action("index","Home")"><img src="~/Content/images/ny_logo.png" width="243" height="102" /></a></div>
            <div class="top4">
                <a href="@Url.Action("Register", "UserPlatform")">@Resources.Global.QT_INDEX_我要注册</a><div class="top5_a"><img src="~/Content/images/top02.png" width="30" height="30" /></div>

                <a href="@Url.Action("Login", "UserPlatform")">@Resources.Global.QT_INDEX_立即登录</a><div class="top5_a"><img src="~/Content/images/top01.png" width="26" height="30" /></div>
            </div>
        </div>

        <div class="dh">
            <div class="dh_left"></div>
            <div class="dh_right">

                <div class="index_en">
                    <a href="@Url.Action("SetCulture",  new { culture = CultureInfo.CurrentUICulture.Name, toculture = "zh-CN", url = Request.Url.ToString()})" style=" padding:0 10px; box-sizing:border-box;font-size:18px;">中文</a>/<a href="@Url.Action("SetCulture",  new { culture = CultureInfo.CurrentUICulture.Name, toculture = "en-US", url = Request.Url.ToString()})" style=" padding: 0 10px; box-sizing: border-box; font-size: 18px;">English</a>
                </div>
                <div class="dh_right_a">
                    <a href="@Url.Action("InformationCode","Home",new { code="one"})" class="dh_right_aa">@Resources.Global.QT_INDEX_平台介绍</a>|<a href="@Url.Action("TrialSearch","Home")" class="dh_right_aa">@Resources.Global.QT_INDEX_试验检索</a>|<a href="@Url.Action("InformationCode","Home",new { code="two"})" class="dh_right_aa">@Resources.Global.QT_INDEX_关于我们</a>|<a href="@Url.Action("NewsList")" class="dh_right_aa">@Resources.Global.QT_INDEX_新闻</a>
                </div>

            </div>
        </div>
    </div>
</div>

