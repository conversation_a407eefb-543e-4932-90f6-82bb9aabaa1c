@model ITMCTR.App.Models.WebsiteInformationViewModel
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}

@section Css{
    <link href="~/Content/CssProject.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="~/Content/assets/global/plugins/simditor/styles/simditor.css" />
}
@section Scripts{
    <script src="~/Content/assets/global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/jquery.validate.js"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/localization/messages_zh.min.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datetimepicker/js/locales/bootstrap-datetimepicker.zh-CN.js"></script>

    <script type="text/javascript" src="~/Content/assets/global/plugins/simditor/lib/module.js"></script>
    <script type="text/javascript" src="~/Content/assets/global/plugins/simditor/lib/uploader.js"></script>
    <script type="text/javascript" src="~/Content/assets/global/plugins/simditor/lib/hotkeys.js"></script>
    <script type="text/javascript" src="~/Content/assets/global/plugins/simditor/lib/dompurify.js"></script>
    <script type="text/javascript" src="~/Content/assets/global/plugins/simditor/lib/simditor.js"></script>
    <script>

        $(function () {
            toolbar = ['title', 'bold', 'italic', 'underline', 'strikethrough',
			'color', '|', 'ol', 'ul', 'blockquote', 'code', 'table', '|',
			'link', 'image', 'hr', '|', 'indent', 'outdent'];
            var editor = new Simditor({
                textarea: $('#Content,#ContentEN'),
                placeholder: '这里输入内容,input please...',
                toolbar: toolbar,  //工具栏
                defaultImage: '/img/logo.png', //编辑器插入图片时使用的默认图片
                upload: {
                    url: '@Url.Action("UploadMediaImage")', //文件上传的接口地址
                    params: { types: 'information' }, //键值对,指定文件上传接口的额外参数,上传的时候随文件一起提交
                    fileKey: 'uploadfile', //服务器端获取文件数据的参数名
                    connectionCount: 3,
                    leaveConfirm: '正在上传文件 upload now'
                }
            });
            var editor2 = new Simditor({
                textarea: $('#ContentEN'),
                placeholder: '这里输入内容,input please...',
                toolbar: toolbar,  //工具栏
                defaultImage: '/img/logo.png', //编辑器插入图片时使用的默认图片
                upload: {
                    url: '@Url.Action("UploadMediaImage")', //文件上传的接口地址
                    params: { types: 'information' }, //键值对,指定文件上传接口的额外参数,上传的时候随文件一起提交
                    fileKey: 'uploadfile', //服务器端获取文件数据的参数名
                    connectionCount: 3,
                    leaveConfirm: '正在上传文件 upload now'
                }
            });
            if (jQuery().datepicker) {
                $('.date-picker').datepicker({
                    rtl: App.isRTL(),
                    language: 'zh-CN',
                    orientation: "left",
                    autoclose: true,
                    format: "yyyy-mm-dd",
                    todayBtn: true
                });
            }
            var form1 = $('#formInformationAdd');
            var error1 = $('.alert-danger', form1);
            var success1 = $('.alert-success', form1);
            form1.validate({
                errorElement: 'span', //default input error message container
                errorClass: 'help-block help-block-error', // default input error message class
                focusInvalid: false, // do not focus the last invalid input
                ignore: "",  // validate all fields including form hidden input
                invalidHandler: function (event, validator) { //display error alert on form submit
                    success1.hide();
                    error1.show();
                    App.scrollTo(error1, -200);
                },

                highlight: function (element) { // hightlight error inputs
                    $(element)
                        .closest('.form-group').addClass('has-error'); // set error class to the control group
                },

                unhighlight: function (element) { // revert the change done by hightlight
                    $(element)
                        .closest('.form-group').removeClass('has-error'); // set error class to the control group
                },

                success: function (label) {
                    label
                        .closest('.form-group').removeClass('has-error'); // set success class to the control group
                },

                submitHandler: function (form) {
                    success1.show();
                    error1.hide();
                    form.submit();
                }
            });
        })
    </script>
}
@using (Html.BeginForm("InformationAdd", "Manager", FormMethod.Post, new { id = "formInformationAdd", @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">新建内容/news content</span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                <div class="alert alert-danger display-hide">
                    <button class="close" data-close="alert"></button> 请检测输入项目/input please
                </div>
                <div class="alert alert-success display-hide">
                    <button class="close" data-close="alert"></button> 验证成功!/validtion success
                </div>
                <div class="form-body">
                    <div class="form-group">
                        <label class="col-md-3 control-label">功能代码/Fun Code</label>
                        <div class="col-md-9">
                            @Html.DropDownListFor(x => x.InfoCode, ViewBag.InfoCodeDict as SelectList, new { @class = "form-control input-large input-inline" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">内容标题</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Title, new { @class = "form-control input-xlarge", maxlength = "300", required = "required", placeholder = "请输入标题 input please" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Title EN</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.TitleEN, new { @class = "form-control input-xlarge", maxlength = "300", required = "required", placeholder = "请输入标题 input please" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">副标题</label>
                        <div class="col-md-9">
                            @Html.TextAreaFor(x => x.Subtitle, new { @class = "form-control input-xlarge", maxlength = "500", required = "required", placeholder = "请输入副标题 input please" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Subtitle EN</label>
                        <div class="col-md-9">
                            @Html.TextAreaFor(x => x.SubtitleEN, new { @class = "form-control input-xlarge", maxlength = "500", required = "required", placeholder = "请输入副标题 input please" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">来源</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Source, new { @class = "form-control input-large", maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Source EN</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.SourceEN, new { @class = "form-control input-large", maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">发布时间/PublishTime</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.ReleaseTime, "{0:yyyy-MM-dd}", new { @class = "form-control input-large date-picker", maxlength = "100" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">排序/Sort</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Sort, new { @class = "form-control input-large", digits = "true", minlength = 1, maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">是否发布/IsPublished</label>
                        <div class="col-md-9">
                            @Html.CheckBoxFor(x => x.IsReleaseOption, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">首页显示/IsHomeDisplay</label>
                        <div class="col-md-9">
                            @Html.CheckBoxFor(x => x.IsViewIndexOption, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">置顶显示/IsHomeDisplay</label>
                        <div class="col-md-9">
                            @Html.CheckBoxFor(x => x.IsTopOption, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">作者</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Author, new { @class = "form-control input-large", maxlength = "100" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Author EN</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.AuthorEN, new { @class = "form-control input-large", maxlength = "100" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">作者单位</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.AuthorUnit, new { @class = "form-control input-large", maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Author Unit</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.AuthorUnitEN, new { @class = "form-control input-large", maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">新闻内容</label>
                        <div class="col-md-9">
                            @Html.TextAreaFor(x => x.Content)
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Content EN</label>
                        <div class="col-md-9">
                            @Html.TextAreaFor(x => x.ContentEN)
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-3 col-md-9">
                            <button type="submit" class="btn green">保存/Save</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("InformationList")'">返回/Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}