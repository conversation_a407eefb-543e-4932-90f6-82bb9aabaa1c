@model ITMCTR.App.Models.UserProjectModel

@{
    ViewBag.Title = "";
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/CssProject.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />

}
@section Scripts{
    <script src="~/Content/assets/global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/jquery.validate.js"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/localization/messages_zh.min.js"></script>
    <script src="~/Content/ViewJScript.js"></script>
    <script src="~/Scripts/jquery.tmpl.js"></script>

    <script>
        function showalert(btn, title, conent, url) {
            $("#divAlertModal").modal();
            var $modal = $('#divAlertModal');
            $modal.find(".modal-title").text(title);//标题
            $modal.find("#pcontent").text(conent);//内容
            $modal.find("#btnConfirm").data("url", url);//Url
            $modal.find("#btnConfirm").text(btn);//按钮文字

            $modal.on('click', '.update', function () {
                if (url && url.length > 0) {
                    window.location.href = url;
                }
            });
            $modal.modal();
        }
        $(function () {
            $("#DivRejectReason").hide();
            $("#TaskFlowCreateSysUserId").removeAttr("disabled");
            $("#AuditState").change(function () {
                if ($(this).val() == "4") {
                    $("#DivRejectReason").show();
                }
                else {
                    $("#DivRejectReason").hide();
                }
            });
            $("#btnSave1").click(function () {

                var AuditState = $("#AuditState").val();
                var RejectReason = $("#RejectReason").val();
                if (AuditState == "") {
                    showalert("确定", "提示", "请选择审核状态", "")
                    return false;
                }
                if (AuditState == "4" && RejectReason == "") {
                    showalert("确定", "提示", "请填写未通过原因", "")
                    return false;
                }
                if (!confirm("是否确认该操作？confirm to submit?")) {
                    return false;
                }
                    $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "@Url.Action("SingleAudit", "Manager")",
                        data: { Pid: $("#pid").val(), AuditState: AuditState, RejectReason: RejectReason },
                    success: function (data) {
                        if (data.success) {
                            showalert("确定", "提示", data.message, '@Url.Action("ProFirReview")');
                        }
                        else {
                            if (data.IsRedirect) {
                                showalert("确定", "提示", data.message, '');
                            }
                            else {
                                showalert("确定", "提示", data.message, '');
                            }

                        }
                    }
                });
            });
            $("#btnSave2").click(function () {
                var AuditState = $("#AuditState").val();
                var RejectReason = $("#RejectReason").val();
                if (AuditState == "") {
                    showalert("确定", "提示", "请选择审核状态", "")
                    return false;
                }
                if (AuditState == "4" && RejectReason == "") {
                    showalert("确定", "提示", "请填写未通过原因", "")
                    return false;
                }
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "@Url.Action("SingleAuditTemp", "Manager")",
                    data: { Pid: $("#pid").val(), AuditState: AuditState, RejectReason: RejectReason },
                    success: function (data) {
                        if (data.success) {
                            showalert("确定", "提示", data.message, '');
                        }
                        else {
                                showalert("确定", "提示", data.message, '');
                        }
                    }
                });
            });

        });
    </script>
}
@using (Html.BeginForm("SingleAudit", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    <div id="divAlertModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <p id="pcontent"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
                </div>
            </div>
        </div>
    </div>
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>审核项目/Audit items</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="row">
            <div class="col-md-12">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <span class="caption-subject font-red-sunglo bold">审核项目/audit project</span>
                        </div>
                        <div class="tools">
                        </div>
                    </div>
                    <div class="portlet-body form">
                        <div class="form-body">
                            <div class="ProjetInfo_ms form-group">
                                @Html.Action("ProLastRequestEdit", new { Pid = Model.pid })
                                @Html.Action("SecondBackResultView", new { Pid = Model.pid, @ViewName = "View" })
                                @Html.Action("ProModifyTimeView", new { Pid = Model.pid })

                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    填写语言：
                                                </p>
                                                <p class="en">
                                                    Language：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.listLang, ViewBag.listLangDict as SelectList, new { @class = "form-control input-large  input-inline", disabled = "disabled" })

                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    注册号状态：
                                                </p>
                                                <p class="en">
                                                    Registration Status：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.listRegStatus, ViewBag.listRegStatusDict as SelectList, new { @class = "form-control input-large  input-inline", disabled = "disabled" })

                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    注册题目：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtTitle
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Public title：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtTitleEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    注册题目简写：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtTitleAcronym
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    English Acronym：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtTitleAcronymEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究课题的正式科学名称：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtOfficialName
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Scientific title：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtOfficialNameEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究课题的正式科学名称简写：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtOfficialNameAcronym
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Scientific title acronym：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtOfficialNameAcronymEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究课题代号(代码)：
                                                </p>
                                                <p class="en">
                                                    Study subject ID：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtSubjectID
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <!--
                    <p class="cn">
                        在其它机构的注册号：</p>
                    <p class="en">
                        Secondary ID：
                    </p>-->
                                                <p class="cn">
                                                    在二级注册机构或其它机构的注册号：
                                                </p>
                                                <p class="en">
                                                    The registration number of the Partner Registry or other register：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtSecondaryID
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    申请注册联系人：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Model.txtApplier
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究负责人：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtStudyLeader
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Applicant：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Model.txtApplierEn
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Study leader：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtStudyLeaderEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    申请注册联系人电话：
                                                </p>
                                                <p class="en">
                                                    Applicant telephone：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Model.txtApplierPhone
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究负责人电话：
                                                </p>
                                                <p class="en">
                                                    Study leader's telephone：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtStudyLeaderPhone
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    申请注册联系人传真 ：
                                                </p>
                                                <p class="en">
                                                    Applicant Fax：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Model.txtApplierFax
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究负责人传真：
                                                </p>
                                                <p class="en">
                                                    Study leader's fax：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Model.txtStudyLeaderFax
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    申请注册联系人电子邮件：
                                                </p>
                                                <p class="en">
                                                    Applicant E-mail：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Model.txtApplierEmail
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究负责人电子邮件：
                                                </p>
                                                <p class="en">
                                                    Study leader's E-mail：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtStudyLeaderEmail
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    申请单位网址(自愿提供)：
                                                </p>
                                                <p class="en">
                                                    Study leader's website(voluntary supply)：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Model.txtApplierWebsite
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究负责人网址(自愿提供)：
                                                </p>
                                                <p class="en">
                                                    Study leader's website<br />
                                                    (voluntary supply)：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtStudyLeaderWebsite
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    申请注册联系人通讯地址：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Model.txtApplierAddress
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究负责人通讯地址：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtStudyLeaderAddress
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Applicant address：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Model.txtApplierAddressEn
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Study leader's address：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtStudyLeaderAddressEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    申请注册联系人邮政编码：
                                                </p>
                                                <p class="en">
                                                    Applicant postcode：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtApplierPostcode
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究负责人邮政编码：
                                                </p>
                                                <p class="en">
                                                    Study leader's postcode：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtStudyLeaderPostcode
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    申请人所在单位：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Model.txtApplierCompany
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究负责人所在单位：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Model.txtStudyLeaderCompany
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Applicant's institution：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Model.txtApplierCompanyEn
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Affiliation of the Leader：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Model.txtStudyLeaderCompanyEn
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    是否获伦理委员会批准：
                                                </p>
                                                <p class="en">
                                                    Approved by ethic committee：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <span id="listEthicalCommitteeSanction">
                                                    @if (Model.listEthicalCommitteeSanction == 1)
                                                    {
                                                        <label for="listEthicalCommitteeSanction_0">
                                                            是/Yes
                                                        </label>
                                                    }
                                                    else
                                                    {
                                                        <label for="listEthicalCommitteeSanction_1">
                                                            否/No
                                                        </label>
                                                    }
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                    @if (Model.listEthicalCommitteeSanction == 1)
                                    {
                                        <tbody id="tbodyEcs">
                                            <tr>
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        伦理委员会批件文号：
                                                    </p>
                                                    <p class="en">
                                                        Approved No. of ethic committee：
                                                    </p>
                                                </td>
                                                <td style="width: 303px;">
                                                    @Model.txtEthicalCommitteeFileID
                                                </td>
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        伦理委员会批件附件：
                                                    </p>
                                                    <p class="en">
                                                        Approved file of Ethical Committee：
                                                    </p>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(Model.fileEthicalCommittee))
                                                    {
                                                        <p>
                                                            @Html.ActionLink("查看附件View", "DownFile", new { path = Model.fileEthicalCommittee, pid = Model.pid }, new { target = "_blank", @class = "download" })

                                                        </p>
                                                    }
                                                </td>
                                            </tr>
                                            <tr class="cn">
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        批准本研究的伦理委员会名称：
                                                    </p>
                                                </td>
                                                <td colspan="3">
                                                    <p class="cn">
                                                        @Model.txtEthicalCommitteeName
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr class="en">
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="en">
                                                        Name of the ethic committee：
                                                    </p>
                                                </td>
                                                <td colspan="3">
                                                    <p class="en">
                                                        @Model.txtEthicalCommitteeNameEn
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        伦理委员会批准日期：
                                                    </p>
                                                    <p class="en">
                                                        Date of approved by ethic committee：
                                                    </p>
                                                </td>
                                                <td colspan="3">
                                                    @Model.txtEthicalCommitteeSanctionDate
                                                </td>
                                            </tr>
                                            <!--联系人 -->
                                            <tr class="cn">
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        伦理委员会联系人：
                                                    </p>
                                                </td>
                                                <td colspan="3">
                                                    <p class="cn">
                                                        @Model.txtEthicalCommitteeCName
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr class="en">
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="en">
                                                        Contact Name of the ethic committee：
                                                    </p>
                                                </td>
                                                <td colspan="3">
                                                    <p class="en">
                                                        @Model.txtEthicalCommitteeCNameEN
                                                    </p>
                                                </td>
                                            </tr>
                                            <!--联系地址 -->
                                            <tr class="cn">
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        伦理委员会联系地址：
                                                    </p>
                                                </td>
                                                <td colspan="3">
                                                    <p class="cn">
                                                        @Model.txtEthicalCommitteeCAddress
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr class="en">
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="en">
                                                        Contact Address of the ethic committee：
                                                    </p>
                                                </td>
                                                <td colspan="3">
                                                    <p class="en">
                                                        @Model.txtEthicalCommitteeCAddressEN
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <!-- 联系人电话  -->
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        伦理委员会联系人电话：
                                                    </p>
                                                    <p class="en">
                                                        Contact phone of the ethic committee：
                                                    </p>
                                                </td>
                                                <td>
                                                    @Model.txtEthicalCommitteeCPhone
                                                </td>

                                                <!-- 联系人邮箱  -->
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        伦理委员会联系人邮箱：
                                                    </p>
                                                    <p class="en">
                                                        Contact email of the ethic committee：
                                                    </p>
                                                </td>
                                                <td>
                                                    @Model.txtEthicalCommitteeCEmail
                                                </td>
                                            </tr>
                                        </tbody>
                                    }
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    国家药监局批准文号：
                                                </p>
                                                <p class="en">
                                                    Approved No. of MPA：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Model.txtNationalFDASanctionNO
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    国家药监局批准附件：
                                                </p>
                                                <p class="en">
                                                    Approved file of MPA：
                                                </p>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.fileNationalFDASanction))
                                                {
                                                    <p>
                                                        @Html.ActionLink("查看附件View", "DownFile", new { path = Model.fileNationalFDASanction, pid = Model.pid }, new { target = "_blank", @class = "download" })
                                                    </p>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    国家药监局批准日期：
                                                </p>
                                                <p class="en">
                                                    Date of approved by MPA：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Model.txtNationalFDASanctionDate
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究方案：
                                                </p>
                                                <p class="en">
                                                    Study protocol：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @if (!string.IsNullOrEmpty(Model.fileStudyPlan))
                                                {
                                                    <p>
                                                        @Html.ActionLink("查看附件View", "DownFile", new { path = Model.fileStudyPlan, pid = Model.pid }, new { target = "_blank", @class = "download" })
                                                    </p>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    知情同意书：
                                                </p>
                                                <p class="en">
                                                    Informed consent file：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @if (!string.IsNullOrEmpty(Model.fileInformedConsent))
                                                {
                                                    <p>
                                                        @Html.ActionLink("查看附件View", "DownFile", new { path = Model.fileInformedConsent, pid = Model.pid }, new { target = "_blank", @class = "download" })

                                                    </p>
                                                }
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究实施负责（组长）单位：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Model.txtSponsor
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Primary sponsor：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Model.txtSponsorEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究实施负责（组长）单位地址：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Model.txtSponsorAddress
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Primary sponsor's address：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Model.txtSponsorAddressEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    试验主办单位(项目批准或申办者)：
                                                </p>
                                                <p class="en">
                                                    Secondary sponsor：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.HiddenFor(x => x.hdnSecSponsorCount)
                                                <div class="tblist">
                                                    <div class="noComma subitem">
                                                        <p>
                                                            <span class="cn">已填写试验主办单位如下</span><span class="en">added secondary sponsor as follows</span>
                                                        </p>
                                                        <div id="tabSs">
                                                            @for (int i = 0; i < Model.SecondarySponsor.Count; i++)
                                                            {
                                                                <table id="table_ss@(Model.SecondarySponsor[i].ssId)" width="900" cellspacing="0" cellpadding="0" border="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td width="100" class="t">
                                                                                <p class="cn">
                                                                                    国家：
                                                                                </p>
                                                                            </td>
                                                                            <td width="200">
                                                                                <p id="pcn_country@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                                    @Model.SecondarySponsor[i].countryCN
                                                                                </p>
                                                                            </td>
                                                                            <td width="70" class="t">
                                                                                <p class="cn">
                                                                                    省(直辖市)：
                                                                                </p>
                                                                            </td>
                                                                            <td width="200">
                                                                                <p id="pcn_province@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                                    @Model.SecondarySponsor[i].provinceCN
                                                                                </p>
                                                                            </td>
                                                                            <td width="60" class="t">
                                                                                <p class="cn">
                                                                                    市(区县)：
                                                                                </p>
                                                                            </td>
                                                                            <td width="200">
                                                                                <p id="pcn_city@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                                    @Model.SecondarySponsor[i].cityCN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Country：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_country@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                                    @Model.SecondarySponsor[i].countryEN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Province：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_province@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                                    @Model.SecondarySponsor[i].provinceEN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    City：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_city@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                                    @Model.SecondarySponsor[i].cityEN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="cn">
                                                                                    单位(医院)：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pcn_Institution@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                                    @Model.SecondarySponsor[i].institutionCN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="cn">
                                                                                    具体地址：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pcn_Address@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                                    @Model.SecondarySponsor[i].specificAddressCN
                                                                                </p>
                                                                            </td>
                                                                            <td rowspan="2" colspan="2">
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Institution<br>
                                                                                    hospital：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_Institution@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                                    @Model.SecondarySponsor[i].institutionEN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Address：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_Address@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                                    @Model.SecondarySponsor[i].specificAddressEN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            }
                                                        </div>

                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    经费或物资来源：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Model.txtSourceOfSpends
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Source(s) of funding：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Model.txtSourceOfSpendsEn
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究疾病：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Model.txtStudyAilment
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究疾病代码：
                                                </p>
                                            </td>
                                            <td rowspan="2">
                                                @Model.txtStudyAilmentCode
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Target disease：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Model.txtStudyAilmentEn
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Target disease code：
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究类型：
                                                </p>
                                                <p class="en">
                                                    Study type：
                                                </p>
                                            </td>
                                            <!--id="listStudyType"-->
                                            <td style="width: 303px;">
                                                @Html.DropDownList("listStudyType", ViewBag.PagelistStudyType as IEnumerable<SelectListItem>
                                                    , "", new { @class = "form-control form-control input-xlarge input-inline", disabled = "disabled" })

                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究设计：
                                                </p>
                                                <p class="en">
                                                    Study design：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownList("listStudyDesign", ViewBag.PagelistStudyDesign as IEnumerable<SelectListItem>
                                                    , "", new { @class = "form-control form-control input-xlarge input-inline", disabled = "disabled" })

                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究所处阶段：
                                                </p>
                                                <p class="en">
                                                    Study phase：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.DropDownList("listStudyStage", ViewBag.PagelistStudyStage as IEnumerable<SelectListItem>, "", new { @class = "form-control form-control input-xlarge input-inline", disabled = "disabled" })
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究目的：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Model.txtStudyAim
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Objectives of Study：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Model.txtStudyAimEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    药物成份或治疗方案详述：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Model.txtDrugsComposition
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Description for medicine or protocol of treatment in detail：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Model.txtDrugsCompositionEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    纳入标准：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Model.txtSelectionCriteria
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Inclusion criteria
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Model.txtSelectionCriteriaEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    排除标准：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Model.txtEliminateCriteria
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Exclusion criteria：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Model.txtEliminateCriteriaEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究实施时间：
                                                </p>
                                                <p class="en">
                                                    Study execute time：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p>
                                                    <span class="cn">从</span><span class="en">From</span>
                                                    @Html.FormatValue(Model.txtStudyExecuteTime, "{0:yyyy-MM-dd}")
                                                </p>
                                                <p>
                                                    <span class="cn">至</span><span class="en">To</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    @Html.FormatValue(Model.txtStudyEndTime, "{0:yyyy-MM-dd}")
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    征募观察对象时间：
                                                </p>
                                                <p class="en">
                                                    Recruiting time：
                                                </p>
                                            </td>
                                            <td>
                                                <p>
                                                    <span class="cn">从</span><span class="en">From</span>
                                                    @Html.FormatValue(Model.txtEnlistBeginTime, "{0:yyyy-MM-dd}")
                                                </p>
                                                <p>
                                                    <span class="cn">至</span><span class="en">To</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    @Html.FormatValue(Model.txtEnlistEndTime, "{0:yyyy-MM-dd}")
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group" id="divInter">
                                <div class="tblist">
                                    <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                        <tbody>
                                            <tr>
                                                <td style="width: 200px;" rowspan="2" class="left_title">
                                                    <p class="cn">
                                                        干预措施：
                                                    </p>
                                                    <p class="en">
                                                        Interventions：
                                                    </p>
                                                </td>
                                                <td>
                                                    <div id="up2">
                                                        <div class="noComma subitem">
                                                            <p>
                                                                <span class="cn">已填写干预措施如下</span><span class="en">added interventions as follows</span>
                                                            </p>
                                                            <div id="tabInter">
                                                                @for (int i = 0; i < Model.Interventions.Count; i++)
                                                                {
                                                                    <table id="table_inter@(Model.Interventions[i].inId)" width="900" cellspacing="0" cellpadding="0" border="0">
                                                                        <tbody>
                                                                            <tr>
                                                                                <td width="60" class="t">
                                                                                    <p class="cn">
                                                                                        组别：
                                                                                    </p>
                                                                                </td>
                                                                                <td width="200">
                                                                                    <p id="pcn_groups@(Model.Interventions[i].inId)" class="cn">
                                                                                        @Model.Interventions[i].groupsCN
                                                                                    </p>
                                                                                </td>
                                                                                <td width="110" class="t">
                                                                                    <p class="cn">
                                                                                        样本量：
                                                                                    </p>
                                                                                </td>
                                                                                <td id="p_sampleSize@(Model.Interventions[i].inId)" width="200" rowspan="2">
                                                                                    @Model.Interventions[i].sampleSize
                                                                                </td>
                                                                                <td rowspan="4">
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td width="60" class="t">
                                                                                    <p class="en">
                                                                                        Group：
                                                                                    </p>
                                                                                </td>
                                                                                <td id="pen_groups@(Model.Interventions[i].inId)" width="200">
                                                                                    @Model.Interventions[i].groupsEN
                                                                                </td>
                                                                                <td width="110" class="t">
                                                                                    <p class="en">
                                                                                        Sample size：
                                                                                    </p>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td width="60" class="t">
                                                                                    <p class="cn">
                                                                                        干预措施：
                                                                                    </p>
                                                                                </td>
                                                                                <td width="200">
                                                                                    <p id="pcn_intervention@(Model.Interventions[i].inId)" class="cn">
                                                                                        @Model.Interventions[i].interventionCN
                                                                                    </p>
                                                                                </td>
                                                                                <td width="110" class="t">
                                                                                    <p class="cn">
                                                                                        干预措施代码：
                                                                                    </p>
                                                                                </td>
                                                                                <td id="p_interventionCode@(Model.Interventions[i].inId)" rowspan="2">
                                                                                    @Model.Interventions[i].interventionCode
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td width="60" class="t">
                                                                                    <p class="en">
                                                                                        Intervention：
                                                                                    </p>
                                                                                </td>
                                                                                <td id="pen_intervention@(Model.Interventions[i].inId)" width="200">
                                                                                    @Model.Interventions[i].interventionEN
                                                                                </td>
                                                                                <td width="110" class="t">
                                                                                    <p class="en">
                                                                                        Intervention code：
                                                                                    </p>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                }
                                                            </div>

                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="3">
                                                    <span class="cn">样本总量</span><span class="en">
                                                        Total sample
                                                        size
                                                    </span>：
                                                    @Model.txtTotalSampleSize
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="ProjetInfo_ms form-group" id="divDiagnostic">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td rowspan="2" class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    诊断试验：
                                                </p>
                                                <p class="en">
                                                    Diagnostic Tests：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <div id="Div2" class="noComma subitem">
                                                    <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                                        <tbody>
                                                            <tr>
                                                                <td style="width: 250px;padding:10px">
                                                                    <p class="cn">
                                                                        金标准或参考标准（即可准确诊断某疾病的单项方法或多项联合方法，在本研究中用于诊断是否有该病的临床参考标准）：
                                                                    </p>

                                                                </td>
                                                                <td colspan="3">
                                                                    <p class="cn">
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().standard
                                                                        }
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 250px;padding:10px">
                                                                    <p class="en">
                                                                        Gold Standard or Reference Standard (The clinical reference standards required to establish the presence or absence of the target condition in the tested population in present study):
                                                                    </p>

                                                                </td>
                                                                <td colspan="3">
                                                                    <p class="en">
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().standardEn
                                                                        }
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 250px;padding:10px">
                                                                    <p class="cn">
                                                                        指标试验（即本研究的待评估诊断试验，无论为方法、生物标志物或设备，均请列出名称）：
                                                                    </p>
                                                                </td>
                                                                <td colspan="3">
                                                                    <p class="cn">
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().indexTest
                                                                        }
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 250px;padding:10px">
                                                                    <p class="en">
                                                                        Index test:
                                                                    </p>
                                                                </td>
                                                                <td colspan="3">
                                                                    <p class="en">
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().indexTestEn
                                                                        }
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding:10px">
                                                                    <p class="cn">
                                                                        目标人群（可以是某种疾病患者或正常人群，详细描述其疾病特征，注意应纳入符合分布特点的全序列病例，具有良好的代表性）
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().targetCondition
                                                                        }
                                                                    </p>
                                                                </td>
                                                                <td style="width: 80px;padding-left:10px" rowspan="2">
                                                                    <p class="cn">
                                                                        例数:
                                                                    </p>
                                                                    <p class="en">
                                                                        Sample size:
                                                                    </p>

                                                                    <p>
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().sampleSizeT
                                                                        }
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding:10px">
                                                                    <p class="en">
                                                                        Target condition (The target condition is a particular disease or disease stage that the index test will be intended to identify. Please specify the characteristics in detail; the population should has a complete spectrum and good representative):
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().targetConditionEn
                                                                        }
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding:10px">
                                                                    <p class="cn">
                                                                        容易混淆的疾病人群（即与目标疾病不易区分的一种或多种不同疾病，应避免采用正常人群对照的病例-对照设计）：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().difficultCondition
                                                                        }
                                                                    </p>
                                                                </td>
                                                                <td style="width: 80px;padding-left:10px" rowspan="2">
                                                                    <p class="cn">
                                                                        例数:
                                                                    </p>
                                                                    <p class="en">
                                                                        Sample size:
                                                                    </p>
                                                                    <p>
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().sampleSizeD
                                                                        }
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding:10px">
                                                                    <p class="en">
                                                                        Population with condition difficult to distinguish from the target condition, the normal population in a case-control study design should be avoid:
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        @if (Model.Diagnostic.Count > 0)
                                                                        {
                                                                            @Model.Diagnostic.FirstOrDefault().difficultConditionEn
                                                                        }
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究实施地点：
                                                </p>
                                                <p class="en">
                                                    Countries of recruitment
                                                    <br />
                                                    and research settings：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.HiddenFor(x => x.hdnPlaceCount)
                                                <div id="up1">
                                                    <input type="hidden" value="0" id="hdnPlaceCount" name="hdnPlaceCount">
                                                    <div id="tbPlace" class="noComma subitem">
                                                        <p>
                                                            <span class="cn">已填写实施地点如下</span><span class="en">added places as follows</span>
                                                        </p>
                                                        <div id="tabra">
                                                            @for (int i = 0; i < Model.ResearchAddress.Count; i++)
                                                            {
                                                                <table id="table_ra@(Model.ResearchAddress[i].raId)" width="900" cellspacing="0" cellpadding="0" border="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td width="100" class="t">
                                                                                <p class="cn">
                                                                                    国家：
                                                                                </p>
                                                                            </td>
                                                                            <td width="200">
                                                                                <p id="pcn_country_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                                    @Model.ResearchAddress[i].countryCN
                                                                                </p>
                                                                            </td>
                                                                            <td width="70" class="t">
                                                                                <p class="cn">
                                                                                    省(直辖市)：
                                                                                </p>
                                                                            </td>
                                                                            <td width="200">
                                                                                <p id="pcn_province_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                                    @Model.ResearchAddress[i].provinceCN
                                                                                </p>
                                                                            </td>
                                                                            <td width="60" class="t">
                                                                                <p class="cn">
                                                                                    市(区县)：
                                                                                </p>
                                                                            </td>
                                                                            <td width="200">
                                                                                <p id="pcn_city_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                                    @Model.ResearchAddress[i].cityCN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Country：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_country_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                                    @Model.ResearchAddress[i].countryEN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Province：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_province_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                                    @Model.ResearchAddress[i].provinceEN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    City：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_city_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                                    @Model.ResearchAddress[i].cityEN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="cn">
                                                                                    单位(医院)：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pcn_hospital_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                                    @Model.ResearchAddress[i].hospitalCN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="cn">
                                                                                    单位级别：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pcn_institution_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                                    @Model.ResearchAddress[i].levelInstitutionCN
                                                                                </p>
                                                                            </td>
                                                                            <td rowspan="2" colspan="2">
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Institution/hospital：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_hospital_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                                    @Model.ResearchAddress[i].hospitalEN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Level of the institution：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_institution_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                                    @Model.ResearchAddress[i].levelInstitutionEN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            }
                                                        </div>

                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    测量指标：
                                                </p>
                                                <p class="en">
                                                    Outcomes：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <div id="upIndex">
                                                    @Html.HiddenFor(x => x.hdnIndexCount)
                                                    <div class="noComma subitem">
                                                        <p>
                                                            <span class="cn">已填写测量指标如下</span> <span class="en">added index as follows</span>
                                                        </p>
                                                        <div id="tabout">
                                                            @for (int i = 0; i < Model.Outcomes.Count; i++)
                                                            {
                                                                <table id="table_out@(Model.Outcomes[i].ouId)" width="900" cellspacing="0" cellpadding="0" border="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td width="73" class="t">
                                                                                <input type="hidden" value="@(Model.Outcomes[i].ouId)" id="txtOuid@(i)" name="txtOuid">
                                                                                <p class="cn ">
                                                                                    指标中文名：
                                                                                </p>
                                                                            </td>
                                                                            <td width="200">
                                                                                <p class="cn" id="pcn_name@(Model.Outcomes[i].ouId)">
                                                                                    @Model.Outcomes[i].outcomeNameCN
                                                                                </p>
                                                                            </td>
                                                                            <td width="40" class="t">
                                                                                <p class="cn">
                                                                                    指标类型：
                                                                                </p>
                                                                            </td>
                                                                            <td width="200">
                                                                                <p class="cn" id="pcn_type@(Model.Outcomes[i].ouId)">
                                                                                    @switch (Model.Outcomes[i].pointerType)
                                                                                    {
                                                                                        case "4002001":@("主要指标"); break;
                                                                                        case "4002002": @("次要指标"); break;
                                                                                        case "4002003": @("附加指标"); break;
                                                                                        case "4002004": @("副作用指标"); break;
                                                                                        default:
                                                                                            break;
                                                                                    }
                                                                                </p>
                                                                            </td>
                                                                            <td rowspan="4">
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Outcome：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="en" id="pen_name@(Model.Outcomes[i].ouId)">
                                                                                    @Model.Outcomes[i].outcomeNameEN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Type：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="en" id="pen_type@(Model.Outcomes[i].ouId)">
                                                                                    @switch (Model.Outcomes[i].pointerType)
                                                                                    {
                                                                                        case "4002001":@("Primary indicator"); break;
                                                                                        case "4002002": @("Secondary indicator"); break;
                                                                                        case "4002003": @("Additional indicator"); break;
                                                                                        case "4002004": @("Adverse events"); break;
                                                                                        default:
                                                                                            break;
                                                                                    }
                                                                                </p>
                                                                            </td>
                                                                        </tr>

                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="cn">
                                                                                    测量时间点：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="cn" id="pcn_time@(Model.Outcomes[i].ouId)">
                                                                                    @Model.Outcomes[i].measureTimeCN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="cn">
                                                                                    测量方法：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="cn" id="pcn_method@(Model.Outcomes[i].ouId)">
                                                                                    @Model.Outcomes[i].measureMethodCN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Measure time point of outcome：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="en" id="pen_time@(Model.Outcomes[i].ouId)">
                                                                                    @Model.Outcomes[i].measureTimeEN
                                                                                </p>
                                                                            </td>
                                                                            <td class="t">
                                                                                <p class="en">
                                                                                    Measure method：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="en" id="pen_method@(Model.Outcomes[i].ouId)">
                                                                                    @Model.Outcomes[i].measureMethodEN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            }
                                                        </div>

                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    采集人体标本：
                                                </p>
                                                <p class="en">
                                                    Collecting sample(s)
                                                    <br />
                                                    from participants：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <div id="tbSpecimen" class="noComma subitem">
                                                    @Html.HiddenFor(x => x.hdnSpecimenCount)
                                                    <div id="tbSpecimen" class="noComma subitem">
                                                        <p>
                                                            <span class="cn">已填写标本如下</span><span class="en">added sample(s) as follows</span>
                                                        </p>
                                                        <div id="tabcoll">
                                                            @for (int i = 0; i < Model.CollectingSample.Count; i++)
                                                            {
                                                                <table id="table_cs@(Model.CollectingSample[i].csId)" width="900" cellspacing="0" cellpadding="0" border="0">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td width="93">
                                                                                <input type="hidden" style="display: none" value="@(Model.CollectingSample[i].csId)" id="txtCSid_@(i)" name="txtcsid">
                                                                                <p class="cn">
                                                                                    标本中文名：
                                                                                </p>
                                                                            </td>
                                                                            <td width="240">
                                                                                <p id="pcn_SaName@(Model.CollectingSample[i].csId)" class="cn">
                                                                                    @Model.CollectingSample[i].sampleNameCN
                                                                                </p>
                                                                            </td>
                                                                            <td width="50">
                                                                                <p class="cn">
                                                                                    组织：
                                                                                </p>
                                                                            </td>
                                                                            <td width="270">
                                                                                <p id="pcn_tissue@(Model.CollectingSample[i].csId)" class="cn">
                                                                                    @Model.CollectingSample[i].tissueCN
                                                                                </p>
                                                                            </td>
                                                                            <td rowspan="4">
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>
                                                                                <p class="en">
                                                                                    Sample Name：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_SaName@(Model.CollectingSample[i].csId)" class="en">
                                                                                    @Model.CollectingSample[i].sampleNameEN
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="en">
                                                                                    Tissue：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_tissue@(Model.CollectingSample[i].csId)" class="en">
                                                                                    @Model.CollectingSample[i].tissueEN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>
                                                                                <p class="cn">
                                                                                    人体标本去向
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <input type="hidden" id="fate@(Model.CollectingSample[i].csId)" value="@Model.CollectingSample[i].fateSample">
                                                                                <p id="pcn_fate@(Model.CollectingSample[i].csId)" class="cn">
                                                                                    @switch (Model.CollectingSample[i].fateSample)
                                                                                    {
                                                                                        case "1012001":@("使用后销毁"); break;
                                                                                        case "1012002":@("使用后保存"); break;
                                                                                        case "1012003":@("其它"); break;
                                                                                        default:
                                                                                            break;
                                                                                    }

                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="cn">
                                                                                    说明
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pcn_note@(Model.CollectingSample[i].csId)" class="cn">
                                                                                    @Model.CollectingSample[i].noteCN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>
                                                                                <p class="en">
                                                                                    Fate of sample&nbsp;
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_fate@(Model.CollectingSample[i].csId)" class="en">
                                                                                    @switch (Model.CollectingSample[i].fateSample)
                                                                                    {
                                                                                        case "1012001":@("Destruction after use"); break;
                                                                                        case "1012002":@("Preservation after use"); break;
                                                                                        case "1012003":@("Others"); break;
                                                                                        default:
                                                                                            break;
                                                                                    }
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p class="en">
                                                                                    Note：
                                                                                </p>
                                                                            </td>
                                                                            <td>
                                                                                <p id="pen_note@(Model.CollectingSample[i].csId)" class="en">
                                                                                    @Model.CollectingSample[i].noteEN
                                                                                </p>
                                                                            </td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            }
                                                        </div>

                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label heigthLef">
                                                <p class="cn">
                                                    征募研究对象情况：
                                                </p>
                                            </td>
                                            <td rowspan="2" style="width: 300px;">
                                                @Html.DropDownListFor(x => x.listRecruitmentStatus, ViewBag.listRecruitmentStatusDict as SelectList, new { @class = "form-control input-large input-inline", disabled = "disabled" })
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    年龄范围：
                                                </p>
                                            </td>
                                            <td rowspan="2">
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                <span class="cn">最小</span>
                                                            </td>
                                                            <td rowspan="2">
                                                                @Model.txtMinAge
                                                            </td>
                                                            <td>
                                                                <span class="cn">岁</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <span class="en">Min age</span>
                                                            </td>
                                                            <td>
                                                                <span class="en">years</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <span class="cn">最大</span>
                                                            </td>
                                                            <td rowspan="2">
                                                                @Model.txtMaxAge
                                                            </td>
                                                            <td>
                                                                <span class="cn">岁</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <span class="en">Max age</span>
                                                            </td>
                                                            <td>
                                                                <span class="en">years</span>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Recruiting status：
                                                </p>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Participant age：
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    性别：
                                                </p>
                                            </td>
                                            <td rowspan="2" colspan="2">
                                                @Html.DropDownListFor(x => x.listGender, ViewBag.listGenderDict as SelectList, new { @class = "form-control input-large input-inline", disabled = "disabled" })
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Gender：
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    随机方法（请说明由何人用什么方法产生随机序列）：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Model.txtGenerafionMethod
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Randomization Procedure (please state who generates the random number sequence and
                                                    by what method)：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Model.txtGenerafionMethodEn
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究对象是否签署知情同意书：
                                                </p>
                                                <p class="en">
                                                    Sign the informed consent：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <span id="listAgreeToSign">
                                                    @if (Model.listAgreeToSign == 1)
                                                    {
                                                        <label for="listAgreeToSign_0">
                                                            是/Yes
                                                        </label>
                                                    }
                                                    else
                                                    {
                                                        <label for="listAgreeToSign_1">
                                                            否/No
                                                        </label>
                                                    }
                                                </span>
                                            </td>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    随访时间：
                                                </p>
                                                <p class="en">
                                                    Length of follow-up (include time point of outcome measure)：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtFollowUpFrequency
                                                @Html.DropDownListFor(x => x.listFollowUpTimeUnit, ViewBag.listFollowUpTimeUnitDict as SelectList, new { @class = "form-control input-medium input-inline", disabled = "disabled" })
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    隐蔽分组方法和过程：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtConcealment
                                                </p>
                                                <p class="cn">
                                                    <span class="help-block">请描述您采用的隐蔽分组方法和过程</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Process of allocation
                                                    <br />
                                                    concealment：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtConcealmentEn
                                                </p>
                                                <p class="en">
                                                    <span class="help-block">Please describe the process of allocation concealment you will use.</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    盲法：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtBlinding
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Blinding：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtBlindingEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    揭盲或破盲原则和方法：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtUncoverPrinciple
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Rules of uncover or
                                                    <br />
                                                    ceasing blinding：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtUncoverPrincipleEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    统计方法名称：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtStatisticalMethod
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Statistical method：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtStatisticalMethodEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn publicshow">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    试验完成后的统计结果（上传文件）：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtStatisticalEffect
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en publicshow">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Calculated Results ater
                                                    <br />
                                                    the Study Completed：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtStatisticalEffectEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="publicshow">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    上传试验完成后的统计结果：
                                                </p>
                                                <p class="en">
                                                    Statistical results after completion of the test file upload
                                                </p>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.fileExperimentalresults))
                                                {
                                                    <p>
                                                        @Html.ActionLink("查看附件View", "DownFile", new { path = Model.fileExperimentalresults, pid = Model.pid }, new { target = "_blank", @class = "download" })

                                                    </p>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    是否公开试验完成后的统计结果：
                                                </p>
                                                <p class="en">
                                                    Calculated Results after
                                                    <br>
                                                    the Study Completed(upload file)：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.listStatisticalEffectChiCTRPublic, ViewBag.listStatisticalEffectChiCTRPublicDict as SelectList, new { @class = "form-control input-large input-inline", disabled = "disabled" })
                                                &nbsp;&nbsp;<span style="color:#ff0000">注册时无需上传，可在试验完成之后再选择是否公开</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p>
                                                    UTN(全球唯一识别码)：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.txtUTN
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    是否共享原始数据：
                                                </p>
                                                <p class="en">
                                                    IPD sharing：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.txtDataCollectionUnit, ViewBag.txtDataCollectionUnitDict as SelectList, new { @class = "form-control input-large input-inline", disabled = "disabled" })
                                            </td>
                                        </tr>

                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    <!-- replace by follow -liubo 2016-06-14 -->
                                                    <!--公开原始数据计划（说明：请填入公开原始数据日期和方式，如何让公众查询）：-->
                                                    共享原始数据的方式（说明：请填入公开原始数据日期和方式，如采用网络平台，需填该网络平台名称和网址）：
                                                    <!-- end replace -->
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtDataChargeUnit
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    <!-- replace by follow -liubo 2016-06-14 -->
                                                    <!--Individual Participant Data sharing plan(include metadata and protocol, and the way to allow public to be able to access the data)：-->
                                                    The way of sharing IPD”(include metadata and protocol, If use web-based public database, please provide the url)：
                                                    <!-- end replace -->
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtDataChargeUnitEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    数据采集和管理（说明：数据采集和管理由两部分组成，一为病例记录表(Case Record Form, CRF)，二为电子采集和管理系统(Electronic Data Capture, EDC)，如ResMan即为一种基于互联网的EDC：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtDataAnalysisUnit
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Data collection and Management (A standard data collection and management system include a CRF and an electronic data capture：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtDataAnalysisUnitEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    数据管理委员会：
                                                </p>
                                                <p class="en">
                                                    Data Managemen Committee：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.txtDataManagemenBoard, ViewBag.txtDataManagemenBoardDict as SelectList, new { @class = "form-control input-large input-inline", disabled = "disabled" })
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    研究计划书或研究结果报告发表信息<br />（杂志名称、期、卷、页，时间；或网址）：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtStudyReport
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td class="left_title control-label col-lg-2">
                                                <p class="en">
                                                    Publication information of the protocol/research results report<br />(name of the journal, volume, issue, pages, time; or website):
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtStudyReportEN
                                                </p>
                                            </td>
                                        </tr>
                                        <!-- end replace -->
                                    </tbody>
                                </table>
                            </div>

                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    注册人：
                                                </p>
                                                <p class="en">
                                                    Name of Registration：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.createUserName
                                            </td>
                                        </tr>

                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    首次提交时间：
                                                </p>
                                                <p class="en">
                                                    First Submission：
                                                </p>
                                            </td>
                                            <td>
                                                @Model.RegTime
                                            </td>
                                        </tr>

                                        @if (!string.IsNullOrWhiteSpace(Model.regNumber))
                                        {
                                            <tr>
                                                <td class="left_title control-label col-lg-2">
                                                    <p class="cn">
                                                        注册号：
                                                    </p>
                                                    <p class="en">
                                                        Registration Number：
                                                    </p>
                                                </td>
                                                <td>
                                                    @Model.regNumber
                                                </td>
                                            </tr>
                                        }

                                        <tr>
                                            <td class="left_title control-label col-lg-2">
                                                <p class="cn">
                                                    审核状态：
                                                </p>
                                                <p class="en">
                                                    Audit State：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.AuditState, ViewBag.AuditStateList as SelectList, new { @class = "form-control input-large input-inline" })
                                            </td>
                                        </tr>
                                        @if (!string.IsNullOrWhiteSpace(Model.Reason))
                                        {
                                            <tr id="trRejectReason">
                                                <td style="width: 200px;" class="left_title control-label">
                                                    <p class="cn">
                                                        拒绝原因：
                                                    </p>
                                                    <p class="en">
                                                        Reasons for refusal by superiors：
                                                    </p>
                                                </td>
                                                <td>
                                                    @Model.Reason
                                                </td>
                                            </tr>
                                        }
                                        <tr id="DivRejectReason">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    拒绝原因：
                                                </p>
                                                <p class="en">
                                                    Reject Reason：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextArea("RejectReason", Model.editRequestResult, 5, 5, new { @id = "RejectReason", @class = "form-control input-large input-inline" })
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-md-offset-3 col-md-9">
                                            @Html.HiddenFor(x => x.pid)
                                            @Html.Hidden("btn")

                                            <button class="btn green" type="button" id="btnSave1">保存Save</button>
                                            <button class="btn green-jungle" type="button" id="btnSave2">暂存TempSave</button>
                                            <a class="btn default" href="javascript:location.href=document.referrer;">返回back</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6"> </div>
                            </div>
                        </div>
                        @Html.Action("Message", "Manager", new { Pid = Model.pid })

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
}