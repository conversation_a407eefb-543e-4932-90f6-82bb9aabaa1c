@model PetaPoco.Page<ITMCTR.Core.Models.SA_Users>
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
}
@using (Html.BeginForm("RegUserCheckList", "Manager", FormMethod.Get))
{
    <!-- B<PERSON>IN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>注册用户审核/ReveiwUser</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="m-heading-1 border-green m-bordered">
            <h3 class="form-section">
                筛选条件/Filter Options
            </h3>
            <div class="horizontal-form">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>登录账户/Account</label>
                            <input type="text" id="txtUsername" name="txtUsername" value="@ViewBag.txtUsername" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>姓名/Username</label>
                            <input type="text" id="txtName" name="txtName" value="@ViewBag.txtName" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="submit" class="btn blue">
                        <i class="fa fa-check"></i> 查询/Query
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">注册用户审核/ReveiwUser</span>

                        </div>
                        <div class="actions">
                            
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>操作<br />Operate</th>
                                    <th>登录账户<br />Account</th>
                                    <th>姓名<br />UserName</th>
                                    <th>邮箱<br />Email</th>
                                    <th>所属国家<br />Country</th>
                                    <th>联系电话<br />Phone</th>
                                    <th>注册单位名称<br />RegUnit</th>
                                    <th>注册单位地址<br />RegAddress</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                <tr>
                                    <td>
                                        <a href="@Url.Action("RegUserCheck", "Manager",new { Uid=item.Uid} )">审核/Review</a>
                                    </td>
                                    <td>@item.Username</td>
                                    <td>@item.Name</td>
                                    <td>@item.Email</td>
                                    <td>@item.Country</td>
                                    <td>@item.CellPhone</td>
                                    <td>@item.RegUnit</td>
                                    <td>@item.RegAddress</td>
                                </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("RegUserCheckList", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
}
