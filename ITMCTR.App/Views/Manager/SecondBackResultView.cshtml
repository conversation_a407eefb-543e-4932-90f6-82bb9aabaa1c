@model List<ITMCTR.Core.Models.SA_ProjectVerifyTaskFlowRecord>
@{
    Layout = null;
}
@if (Model.Count() > 0)
{
    <table width="100%" cellspacing="0" cellpadding="0" border="0">
        <tbody>
            @{
                foreach (var item in Model)
                {
                        <tr>
                            @if (ViewData["ViewName"] == "Edit")
                            {
                                if (item.VerifyTaskStatus == 5)
                                {
                                    <td style="width: 200px;" class="left_title control-label">
                                        <p class="cn">
                                            需要额外说明的事宜 @Html.Raw(item.CreateTime.Value.ToString("yyyy-MM-dd"))：
                                        </p>
                                        <p class="en">
                                            Matters that need additional explanation：
                                        </p>
                                    </td>
                                }
                                else
                                {
                                    <td style="width: 200px;" class="left_title control-label">
                                        <p class="cn">
                                            修改建议 @Html.Raw(item.CreateTime.Value.ToString("yyyy-MM-dd"))：
                                        </p>
                                        <p class="en">
                                            Review comment：
                                        </p>
                                    </td>

                                }
                            }
                            else
                            {
                                if (item.VerifyTaskStatus == 5)
                                {
                                    <td class="left_title control-label col-lg-2">
                                        <p class="cn">
                                            需要额外说明的事宜 @Html.Raw(item.CreateTime.Value.ToString("yyyy-MM-dd"))：
                                        </p>
                                        <p class="en">
                                            Matters that need additional explanation：
                                        </p>
                                    </td>
                                }
                                else
                                {
                                    <td class="left_title control-label col-lg-2">
                                        <p class="cn">
                                            修改建议 @Html.Raw(item.CreateTime.Value.ToString("yyyy-MM-dd"))：
                                        </p>
                                        <p class="en">
                                            Review comment：
                                        </p>
                                    </td>
                                }
                            }
                            <td>
                                @item.Reason
                            </td>
                        </tr>
                }
            }
        </tbody>
    </table>
}
