@model PetaPoco.Page<ITMCTR.App.Models.UserProjectViewModel>
@{
    ViewBag.Title = "ProjectList";
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/jquery.validate.js"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/localization/messages_zh.min.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
    <script src="~/Content/assets/pages/scripts/form-validation.js"></script>
    <script type="text/javascript">
        $(function () {
            if (jQuery().datepicker) {
                $('.date-picker').datepicker({
                    rtl: App.isRTL(),
                    language: 'zh-CN',
                    orientation: "left",
                    autoclose: true,
                    format: "yyyy-mm-dd",
                    todayBtn: "linked"
                });
            }
        });
    </script>
}

@using (Html.BeginForm("AllProjectsQuery", "Manager", FormMethod.Post))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>项目查询/Query</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="m-heading-1 border-green m-bordered">
            <h3 class="form-section">
                筛选条件/Filter Options
            </h3>
            <div class="horizontal-form">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>
                                注册号/Registration number
                            </label>
                            <input type="text" id="txtRegNo" name="txtRegNo" value="@ViewBag.txtRegNo" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>
                                注册题目/Public title
                            </label>
                            <input type="text" id="txtTitle" name="txtTitle" value="@ViewBag.txtTitle" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label>
                                二审审批退回时间/Refuse Time
                            </label>
                            <div class="form-group">
                                <div class="input-group input-xlarge date-picker input-daterange" data-date-format="yyyy-mm-dd">
                                    <input type="text" class="form-control" name="modifyRefusefrom" value="@ViewBag.modifyRefusefrom">
                                    <span class="input-group-addon">
                                        to
                                    </span>
                                    <input type="text" class="form-control" name="modifyRefuseto" value="@ViewBag.modifyRefuseto">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3">
                        <label>
                            更新日期时间/Modify Time
                        </label>
                        <div class="form-group">
                            <div class="input-group input-xlarge date-picker input-daterange" data-date-format="yyyy-mm-dd">
                                <input type="text" class="form-control" name="modifyTimefrom" value="@ViewBag.modifyTimefrom">
                                <span class="input-group-addon">
                                    to
                                </span>
                                <input type="text" class="form-control" name="modifyTimeto" value="@ViewBag.modifyTimeto">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 hidden">
                        <label>
                            注册时间/registration date
                        </label>
                        <div class="form-group">
                            <div class="input-group input-xlarge date-picker input-daterange" data-date-format="yyyy-mm-dd">
                                <input type="text" class="form-control" name="regfrom" value="@ViewBag.regfrom">
                                <span class="input-group-addon">
                                    to
                                </span>
                                <input type="text" class="form-control" name="regto" value="@ViewBag.regto">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 hidden">
                        <label>
                            注册号更新时间/Updated registration number
                        </label>
                        <div class="form-group">
                            <div class="input-group input-xlarge date-picker input-daterange" data-date-format="yyyy-mm-dd">
                                <input type="text" class="form-control" name="numberfrom" value="@ViewBag.numberfrom">
                                <span class="input-group-addon">
                                    to
                                </span>
                                <input type="text" class="form-control" name="numberto" value="@ViewBag.numberto">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label>
                            批量注册号
                        </label>
                        <div class="form-group">
                            <textarea id="txtRegNo2" name="txtRegNo2" rows="6" cols="20" class="form-control input-xlarge">@ViewBag.txtRegNo2</textarea>
                            <span class="help-block">注册号逗号分隔";"例如 itxxxx;itxxx1;itxxx2</span>
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="submit" class="btn blue">
                        <i class="fa fa-check"></i> 查询/Query
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">项目信息/Information</span>
                        </div>
                        <div class="actions">
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        序号<br />Index
                                    </th>
                                    <th>
                                        注册号<br />Registration number
                                    </th>
                                    <th>
                                        B网注册号<br />Release number
                                    </th>
                                    <th class="col-md-6">
                                        注册题目<br />Public title
                                    </th>
                                    @*<th>
                                            研究阶段<br />Phase
                                        </th>
                                        <th>
                                            完成情况<br />Level
                                        </th>*@
                                    <th>
                                        审核状态<br />Review Status
                                    </th>
                                    <th>
                                        发号日期<br />Update Date
                                    </th>
                                    <th>
                                        最后更新时间<br />Last Modify Date
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                    <tr>
                                        <td>
                                            @(Model.Items.IndexOf(item)+1)
                                        </td>
                                        <td>
                                            @item.regNumber
                                        </td>
                                        <td>
                                            @item.ReleaseNumber
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.publicTitleCN) && item.IsChinese)
                                            {
                                                @Html.ActionLink(item.publicTitleCN, "ProjectView", new { pid = item.pid })
                                                <br />
                                            }

                                            @if (!string.IsNullOrEmpty(item.publicTitleEN))
                                            {
                                                @Html.ActionLink(item.publicTitleEN, "ProjectView", new { pid = item.pid })
                                                <br />
                                            }

                                            @if (!string.IsNullOrEmpty(item.applierCompanyEN) && item.IsEnglist)
                                            {
                                                @item.applierCompanyEN
                                            }
                                            else if (!string.IsNullOrEmpty(item.applierCompanyCN) && item.IsChinese)
                                            {
                                                @item.applierCompanyCN
                                            }
                                        </td>
                                        @*<td>
                                                @item.studyPhaseNameCN
                                                <br />
                                                @item.studyPhaseNameEN
                                            </td>
                                            <td>
                                                @item.recruitingStatusNameCN
                                                <br />
                                                @item.recruitingStatusNameEN
                                            </td>*@
                                        <td>
                                            @switch (item.status)
                                            {
                                                case 0:
                                                    <span class="label label-sm label-warning">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                    break;
                                                case 1:
                                                    <span class="label label-sm label-info">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                    break;
                                                case 3:
                                                    <span class="label label-sm label-success">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                    break;
                                                case 2:
                                                    <span class="label label-sm label-danger">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                    break;
                                                default:
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            @Html.FormatValue(item.regNumberTime, "{0:yyyy/MM/dd}")
                                        </td>
                                        <td>
                                            @Html.FormatValue(item.modifyTime, "{0:yyyy/MM/dd}")
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
}