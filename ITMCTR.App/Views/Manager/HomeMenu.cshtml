@model List<ITMCTR.App.Models.MenuModel>

<ul class="nav navbar-nav">

    @foreach (var item in Model)
    {
        var strClass = Model.IndexOf(item) == 0 ? " active" : "";
        var url = string.IsNullOrWhiteSpace(item.Url) ? "javascript:;" : item.Url;
        if (item.Child.Count > 0)
        {
            <li class="menu-dropdown classic-menu-dropdown @Html.Raw(strClass)">
                <a href="@Html.Raw(url)" class="nav-link">
                    @item.Name<span class="arrow"></span>
                </a>

                <ul class="dropdown-menu pull-left">
                    @foreach (var c in item.Child)
                    {
                        var childUrl = string.IsNullOrWhiteSpace(c.Url) ? "javascript:;" : c.Url;
                        <li class="">
                            <a href="@Html.Raw(childUrl)" class="nav-link active"> @c.Name </a>
                        </li>
                    }
                </ul>
            </li>
        }
        else
        {
            <li class="menu-dropdown classic-menu-dropdown @Html.Raw(strClass)">
                <a href="@Html.Raw(url)" class="nav-link">
                    @item.Name<span class="arrow"></span>
                </a>
            </li>
        }
    }
</ul>