@model ITMCTR.App.Models.NewsEditModel
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@*@Url.Action("UploadMediaImage")*@
@section Css{
    <link href="~/Content/CssProject.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/wangedit.css" rel="stylesheet" />
}
@section Scripts{
    <script src="~/Content/assets/global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/jquery.validate.js"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/localization/messages_zh.min.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datetimepicker/js/locales/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="~/Scripts/wangedit.js"></script>

    <script>
        function wangeditCN() {
            const { createEditor, createToolbar } = window.wangEditor;
            
            const editorConfig = {
                placeholder: 'Type here...',
                MENU_CONF: {
                    uploadImage: {
                        fieldName: 'your-fileName',
                        base64LimitSize: 10 * 1024 * 1024 // 10M 以下插入 base64
                    }
                },
                onChange(editor) {
                    const html = editor.getHtml()
                    $("#Content").val(html)
                }
            }

            const editor = createEditor({
                selector: '#editor-text-area-1',
                html: '<p><br></p>',
                config: editorConfig,
                mode: 'default', // or 'simple'
            })
            console.log(editor.getAllMenuKeys());
            const toolbarConfig = {

            }

            const toolbar = createToolbar({
                editor,
                selector: '#editor-toolbar-1',
                config: toolbarConfig,
                mode: 'default', // or 'simple'
            })
        }
        function wangeditEN() {
            const { createEditor, createToolbar } = window.wangEditor



            const editorConfig = {
                placeholder: 'Type here...',
                MENU_CONF: {
                    uploadImage: {
                        fieldName: 'your-fileName',
                        base64LimitSize: 10 * 1024 * 1024 // 10M 以下插入 base64
                    }
                },
                onChange(editor) {
                    const html = editor.getHtml()
                    $("#ContentEN").val(html)
                }
            }

            const editor = createEditor({
                selector: '#editor-text-area-2',
                html: '<p><br></p>',
                config: editorConfig,
                mode: 'default', // or 'simple'
            })

            const toolbarConfig = {

            }

            const toolbar = createToolbar({
                editor,
                selector: '#editor-toolbar-2',
                config: toolbarConfig,
                mode: 'default', // or 'simple'
            })
        }
        $(function () {
            wangeditCN();
            wangeditEN();


            if (jQuery().datepicker) {
                $('.date-picker').datepicker({
                    rtl: App.isRTL(),
                    language: 'zh-CN',
                    orientation: "left",
                    autoclose: true,
                    format: "yyyy-mm-dd",
                    todayBtn: true
                });
            }
            var form1 = $('#formNewsAdd');
            var error1 = $('.alert-danger', form1);
            var success1 = $('.alert-success', form1);
            form1.validate({
                errorElement: 'span', //default input error message container
                errorClass: 'help-block help-block-error', // default input error message class
                focusInvalid: false, // do not focus the last invalid input
                ignore: "",  // validate all fields including form hidden input
                invalidHandler: function (event, validator) { //display error alert on form submit
                    success1.hide();
                    error1.show();
                    App.scrollTo(error1, -200);
                },

                highlight: function (element) { // hightlight error inputs
                    $(element)
                        .closest('.form-group').addClass('has-error'); // set error class to the control group
                },

                unhighlight: function (element) { // revert the change done by hightlight
                    $(element)
                        .closest('.form-group').removeClass('has-error'); // set error class to the control group
                },

                success: function (label) {
                    label
                        .closest('.form-group').removeClass('has-error'); // set success class to the control group
                },

                submitHandler: function (form) {
                    success1.show();
                    error1.hide();
                    $("#submit").attr("disabled", "disabled");
                    form.submit();

                }
            });
        })
    </script>
}
@using (Html.BeginForm("NewsAdd", "Manager", FormMethod.Post, new { id = "formNewsAdd", @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">新建新闻/Add</span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                <div class="alert alert-danger display-hide">
                    <button class="close" data-close="alert"></button> 请检测输入项目/check input.
                </div>
                <div class="alert alert-success display-hide">
                    <button class="close" data-close="alert"></button> 验证成功/vaildition success!
                </div>
                <div class="form-body">
                    <div class="form-group">
                        <label class="col-md-3 control-label">新闻标题</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Title, new { @class = "form-control input-xlarge", maxlength = "300", required = "required", placeholder = "请输入新闻标题" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Title EN</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.TitleEN, new { @class = "form-control input-xlarge", maxlength = "300", required = "required", placeholder = "请输入新闻标题" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">新闻子标题</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Subtitle, new { @class = "form-control input-large", maxlength = "500", placeholder = "请输子标题" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">SubTitle</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.SubtitleEN, new { @class = "form-control input-large", maxlength = "500", placeholder = "请输子标题" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">新闻分类/Classification</label>
                        <div class="col-md-9">
                            @Html.DropDownListFor(x => x.NtId, ViewBag.listNtId as SelectList, new { @class = "form-control input-large" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">作者</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Author, new { @class = "form-control input-large", maxlength = "100" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Author</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.AuthorEN, new { @class = "form-control input-large", maxlength = "100" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">作者单位</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.AuthorUnit, new { @class = "form-control input-large", maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">AuthorUnit EN</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.AuthorUnitEN, new { @class = "form-control input-large", maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">来源 </label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Source, new { @class = "form-control input-large", maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Source EN </label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.SourceEN, new { @class = "form-control input-large", maxlength = "200" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">发布时间/PublishTime</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.ReleaseTime, "{0:yyyy-MM-dd}", new { @class = "form-control input-large date-picker", maxlength = "100" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">是否发布/IsPublished</label>
                        <div class="col-md-9">
                            @Html.CheckBoxFor(x => x.IsReleaseOption, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">首页显示/HomeDisplay</label>
                        <div class="col-md-9">
                            @Html.CheckBoxFor(x => x.IsViewIndexOption, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">新闻内容</label>
                        <div class="col-md-9">
                            @Html.HiddenFor(x => x.Content)
                            <div style="border: 1px solid #ccc; margin-right: 5px;">
                                <div id="editor-toolbar-1" style="border-bottom: 1px solid #ccc;"></div>
                                <div id="editor-text-area-1" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">Information EN</label>
                        <div class="col-md-9">
                            @Html.HiddenFor(x => x.ContentEN)
                            <div style="border: 1px solid #ccc; margin-left: 5px;">
                                <div id="editor-toolbar-2" style="border-bottom: 1px solid #ccc;"></div>
                                <div id="editor-text-area-2" style="height: 400px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-3 col-md-9">
                            <button type="submit" id="submit" class="btn green">保存/Save</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("NewsList")'">返回/Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}