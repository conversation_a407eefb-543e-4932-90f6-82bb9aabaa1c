@model ITMCTR.App.Models.NewsTypeEditModel
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}

@section Css{
    <link href="~/Content/CssProject.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />

}
@section Scripts{
    <script src="~/Content/assets/global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/jquery.validate.js"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/localization/messages_zh.min.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datetimepicker/js/locales/bootstrap-datetimepicker.zh-CN.js"></script>


    <script>

        $(function () {
            if (jQuery().datepicker) {
                $('.date-picker').datepicker({
                    rtl: App.isRTL(),
                    language: 'zh-CN',
                    orientation: "left",
                    autoclose: true,
                    format: "yyyy-mm-dd",
                    todayBtn: true
                });
            }
            var form1 = $('#formNewsTypeEdit');
            var error1 = $('.alert-danger', form1);
            var success1 = $('.alert-success', form1);
            form1.validate({
                errorElement: 'span', //default input error message container
                errorClass: 'help-block help-block-error', // default input error message class
                focusInvalid: false, // do not focus the last invalid input
                ignore: "",  // validate all fields including form hidden input
                invalidHandler: function (event, validator) { //display error alert on form submit
                    success1.hide();
                    error1.show();
                    App.scrollTo(error1, -200);
                },

                highlight: function (element) { // hightlight error inputs
                    $(element)
                        .closest('.form-group').addClass('has-error'); // set error class to the control group
                },

                unhighlight: function (element) { // revert the change done by hightlight
                    $(element)
                        .closest('.form-group').removeClass('has-error'); // set error class to the control group
                },

                success: function (label) {
                    label
                        .closest('.form-group').removeClass('has-error'); // set success class to the control group
                },

                submitHandler: function (form) {
                    success1.show();
                    error1.hide();
                    form.submit();
                }
            });
        })
    </script>
}
@using (Html.BeginForm("NewsTypeEdit", "Manager", FormMethod.Post, new { id = "formNewsTypeEdit", @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">编辑新闻分类/Edit</span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                <div class="alert alert-danger display-hide">
                    <button class="close" data-close="alert"></button> 请检测输入项目./input please
                </div>
                <div class="alert alert-success display-hide">
                    <button class="close" data-close="alert"></button> 验证成功!/validition success
                </div>
                <div class="form-body">
                    <div class="form-group">
                        <label class="col-md-3 control-label">分类名称/Class Name</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.Name, new { @class = "form-control input-xlarge", maxlength = "30", required = "required", placeholder = "请输入新闻标题 input please" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">分类简介/Class Desc</label>
                        <div class="col-md-9">
                            @Html.TextAreaFor(x => x.Desc, new { @class = "form-control input-large", maxlength = "500", placeholder = "请输入简介 input please" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">显示/Used</label>
                        <div class="col-md-9">
                            @Html.CheckBoxFor(x => x.IsViewOption, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">首页显示/HomeDisplay</label>
                        <div class="col-md-9">
                            @Html.CheckBoxFor(x => x.IsIndexViewOption, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-3 col-md-9">
                            @Html.HiddenFor(x => x.NtId)
                            <button type="submit" class="btn green">保存/Save</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("NewsTypeList")'">返回/Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}