@model ITMCTR.Core.Models.SA_StudyDictionary
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@using (Html.BeginForm("StudyDictionaryEdit", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(x=> x.Sid)
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">编辑研究字典/Edit research dictionary</span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                <div class="form-body">
                    <div class="form-group">
                        <label class="col-md-3 control-label">研究分类/Research classification</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.StudyName, new { @class = "form-control", maxlength = "300", required = "required", placeholder = "请输入字典类型标识" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">研究编号/Study number</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.StudyValue, new { @class = "form-control", maxlength = "300", required = "required", placeholder = "请输入字典标识值" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">中文描述/Chinese description</label>
                        <div class="col-md-9">
                            @Html.TextAreaFor(x => x.DescriptionCn, new { @class = "form-control", maxlength = "500", required = "required", placeholder = "请输入中文描述" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">英文描述/Chinese description</label>
                        <div class="col-md-9">
                            @Html.TextAreaFor(x => x.DescriptionEn, new { @class = "form-control", maxlength = "500", required = "required", placeholder = "请输入英文描述" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">是否使用/Whether to use</label>
                        <div class="col-md-9 mt-radio-inline">
                            <label class="mt-radio">
                                @Html.RadioButtonFor(x => x.Ishide, 1, new { @id = "rdoIshideYes", @name = "Ishide", @checked = "checked" })启用/Enable
                                <span></span>
                            </label>
                            <label class="mt-radio">
                                @Html.RadioButtonFor(x => x.Ishide, 0, new { @id = "rdoIshideNo", @name = "Ishide" })停用/Disable
                                <span></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-3 col-md-9">
                            <button type="submit" class="btn green">保存/save</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("StudyDictionaryList")'">返回/Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

}