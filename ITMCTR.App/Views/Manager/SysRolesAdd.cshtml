@model ITMCTR.Core.Models.SA_SysRoles
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@using (Html.BeginForm("SysRolesAdd", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">新建角色/New role</span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                <div class="form-body">
                    <div class="form-group">
                        <label class="col-md-3 control-label">角色名称/Role Name</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.RoleName, new { @class = "form-control", maxlength = "30", required = "required", placeholder = "请输入角色名称" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">角色说明/Explain</label>
                        <div class="col-md-9">
                            @Html.TextBoxFor(x => x.RoleDesc, new { @class = "form-control", maxlength = "500", placeholder = "请输入角色说明" })
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-3 col-md-9">
                            <button type="submit" class="btn green">保存/save</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("SysRolesList")'">返回/Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

}