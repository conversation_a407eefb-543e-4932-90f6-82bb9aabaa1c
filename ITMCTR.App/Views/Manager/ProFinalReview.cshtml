@model PetaPoco.Page<ITMCTR.Core.Models.V_NewProject>

@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@using (Html.BeginForm("ProFinalReview", "Manager", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>
                待审核项目/To be Reviewed
            </span>
        </li>
    </ul>
    <!-- <PERSON><PERSON> PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
<div class="page-content-inner">
    <div class="m-heading-1 border-green m-bordered">
        <h3 class="form-section">
            筛选条件/Filter Options
        </h3>
        <div class="horizontal-form">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>
                            注册题目/Public title
                        </label>
                        <input type="text" id="txtTitle" name="txtTitle" value="@ViewBag.txtTitle" class="form-control">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>
                            注册号/Registration number
                        </label>
                        <input type="text" id="txtRegNo" name="txtRegNo" value="@ViewBag.txtRegNo" class="form-control">
                    </div>
                </div>
            </div>
            <div class="form-actions right">
                <button type="submit" class="btn blue">
                    <i class="fa fa-check"></i> 查询/Query
                </button>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <!-- BEGIN EXAMPLE TABLE PORTLET-->
            <div class="portlet light ">
                <div class="portlet-title">
                    <div class="caption font-dark">
                        <i class="icon-settings font-dark"></i>
                        <span class="caption-subject bold uppercase">待审核项目/To be Reviewed</span>
                    </div>
                    <div class="actions">

                    </div>
                </div>
                <div class="portlet-body">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>操作<br />Operate</th>
                                <th>
                                    注册号<br />Registration number
                                </th>
                                <th>
                                    项目名称<br />Public title
                                </th>
                                <th>
                                    @Html.ActionLink("首次提交时间/First Submission", "ProFinalReview", new { pageNo = 0, SortName = "regTime", OrderByDescending = ViewData["sort"] })

                                </th>
                                <th>
                                    注册来源<br />From
                                </th>
                                <th>
                                    最近退回时间<br />Last Back Time
                                </th>
                                <th>
                                    最近提交时间<br />Last Submit Time
                                </th>
                                <th>
                                    注册号状态<br />Registration Status
                                </th>
                                <th>
                                    征募观察对象时间<br />Recruiting time
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Items)
                            {
                                <tr>
                                    <td>
                                        <a href="@Url.Action("SingleFinalAudit", "Manager",new { Pid=item.Pid} )">审核/Review</a>
                                        <a href="@Url.Action("ProjectView", "Manager",new { Pid=item.Pid} )">查看/View</a>

                                    </td>
                                    <td>
                                        @item.regNumber
                                    </td>
                                    <td>
                                        @Html.Raw(item.filloutLanguage == "1009001" ? item.publicTitleCN + "<br />" : "") @item.publicTitleEN
                                    </td>
                                    <td>
                                        @(item.regTime.HasValue?item.regTime.Value.ToString("yyyy-MM-dd HH:mm:ss"):"")
                                    </td>
                                    <td>
                                        @item.ProjectOriginCn
                                    </td>
                                    <td>
                                        @(item.flowRecordBackTime.HasValue?item.flowRecordBackTime.Value.ToString("yyyy-MM-dd HH:mm:ss"):"")
                                    </td>
                                    <td>
                                        @(item.flowLastSubmitTime.HasValue?item.flowLastSubmitTime.Value.ToString("yyyy-MM-dd HH:mm:ss"):"")
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrWhiteSpace(item.registrationStatus))
                                        {
                                            if (item.registrationStatus == "1008001")
                                            {
                                                @Html.Raw("预注册/Prospective registration");
                                            }
                                            else if (item.registrationStatus == "1008002")
                                            {
                                                @Html.Raw("补注册/Retrospective registration");
                                            }
                                        }
                                    </td>
                                    <td>
                                        @(item.recruitingTimeStart.HasValue?item.recruitingTimeStart.Value.ToString("yyyy-MM-dd"):"")
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                    <div class="row">
                        <div class="col-md-5 col-sm-12">
                        </div>
                        <div class="col-md-7 col-sm-12">
                            <div class="dataTables_paginate paging_bootstrap_number">
                                @Html.PageLinks(Model, x => Url.Action("ProFinalReview", new { pageNo = x }))
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>

    <script>
        $(function () {
            $('.ajaxModal').on('click', function () {
                if ($("tbody").find('input:checked').length == 0) {
                    alert("请选择记录");
                    return false;
                }
                var $modal = $('#secondary-modal');
                // create the backdrop and wait for next modal to be triggered
                $('body').modalmanager('loading');
                var el = $(this);
                var ids = '';
                var url = el.attr('data-url') + "?ids=";
                $("tbody").find('input:checked').each(function (i, item) {
                    ids += $(item).attr("id") + ',';
                });
                url += ids;
                setTimeout(function () {
                    $modal.load(url, '', function () {
                        $modal.modal();
                    });
                }, 1000);
            });
        });
    </script>
}
<!-- END PAGE CONTENT INNER -->
