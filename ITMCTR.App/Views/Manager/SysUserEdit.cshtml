@model ITMCTR.App.Models.SysUserModel
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
<script src="~/Scripts/jquery-3.4.1.min.js"></script>
<script src="~/Scripts/jquery.validate.min.js"></script>
@using (Html.BeginForm("SysUserEdit", "Manager", FormMethod.Post, new { id = "from1", @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(x => x.Uid)
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">编辑系统用户/Edit user</span>
                </div>
            </div>
            @Html.Hidden("hidPatient", Model.ParentId)
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
            <div class="form-body">
                <div class="form-group">
                    <label class="col-md-3 control-label">登录账户/Account<span class='required'>*</span></label>
                    <div class="col-md-9">
                        @Model.Username
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">姓名/Name<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.TextBoxFor(x => x.Name, new { @class = "form-control", maxlength = "100", required = "required", placeholder = "请输入姓名" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">角色/Role<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.DropDownListFor(x => x.SysRoleId, ViewBag.RoleList as IEnumerable<SelectListItem>, "请选择角色", new { @class = "bs-select form-control", required = "required" })
                    </div>
                </div>

                <div class="form-group ParentId">
                    <label class="col-md-3 control-label">上级/Parent</label>
                    <div class="col-md-9 fromValid">
                        @Html.DropDownListFor(x => x.ParentId, ViewBag.UserList as IEnumerable<SelectListItem>, "请选择上级用户", new { @class = "bs-select form-control" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">邮件/E-mail<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.TextBoxFor(x => x.Email, new { @class = "form-control", maxlength = "100", required = "required", placeholder = "请输入邮件" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">性别/Sex<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.DropDownListFor(x => x.Sex, ViewBag.SexList as IEnumerable<SelectListItem>, "请选择性别", new { @class = "bs-select form-control", required = "required" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">座机/Landline<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.TextBoxFor(x => x.Phone, new { @class = "form-control", maxlength = "100", required = "required", placeholder = "请输入座机" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">所属国家/Country<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.DropDownListFor(x => x.Country, ViewBag.CountryList as IEnumerable<SelectListItem>, "请选择所属国家", new { @class = "bs-select form-control", required = "required" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">移动电话/Mobile Phone<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.TextBoxFor(x => x.CellPhone, new { @class = "form-control", maxlength = "20", required = "required", placeholder = "请输入移动电话" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">单位名称/Institution Name<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.TextBoxFor(x => x.RegUnit, new { @class = "form-control", maxlength = "200", required = "required", placeholder = "请输入单位名称" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-3 control-label">单位地址/Institution addr<span class='required'>*</span></label>
                    <div class="col-md-9 fromValid">
                        @Html.TextBoxFor(x => x.RegAddress, new { @class = "form-control", maxlength = "400", required = "required", placeholder = "请输入单位地址" })
                    </div>
                </div>

                <div class="form-group ">
                    <label class="control-label col-md-3">头像/Head portrait</label>
                    <div class="col-md-9">
                        <input type="file" id="postPhotograph" name="postPhotograph">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">特殊权限</label>
                    <div class="col-md-9" style="top: 7px;">
                        @Html.CheckBoxFor(x => x.IsSysUser)
                    </div>
                </div>
            </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-3 col-md-9">
                            <button type="submit" class="btn green">保存/save</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("SysUserList")'">返回/Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    if (UiFun.IsLang("zh-CN"))
    {
        <script src="~/Content/formValid.js?v=@UiFun.AppVersion"></script>
        <script src="~/Content/assets/global/plugins/jquery-validation/js/localization/messages_zh.min.js?v=@UiFun.AppVersion"></script>
    }
    else
    {
        <script src="~/Content/formValid_en.js?v=@UiFun.AppVersion"></script>
    }


<script>
        formVaild.init("from1");
        $(".ParentId").hide();

    $(function () {
        debugger;
        if ($("#SysRoleId").val() == "49bb2491-2ba5-492d-9f54-6317bcc05baa") {//二级
                ajaxLoadParent("49bb2491-2ba5-492d-9f54-6317bcc05baa", $("#hidPatient").val());
            }
        else if ($("#SysRoleId").val() == "87977f61-df64-41dd-8517-bda64116cc38") {//三级
                ajaxLoadParent("87977f61-df64-41dd-8517-bda64116cc38", $("#hidPatient").val());
            }
        else if ($("#SysRoleId").val() == "3d64080d-042f-45da-84be-570b360236a6") {//四级
                ajaxLoadParent("3d64080d-042f-45da-84be-570b360236a6", $("#hidPatient").val());
            }
            $("#SysRoleId").change(function () {
                var data = [];
                var roleId = "";
                if ($(this).val() == "49bb2491-2ba5-492d-9f54-6317bcc05baa") {//二级
                    ajaxGetParent("49bb2491-2ba5-492d-9f54-6317bcc05baa");
                }
                else if ($(this).val() == "87977f61-df64-41dd-8517-bda64116cc38") {//三级
                    ajaxGetParent("87977f61-df64-41dd-8517-bda64116cc38");
                }
                else if ($(this).val() == "3d64080d-042f-45da-84be-570b360236a6") {//四级
                    ajaxGetParent("3d64080d-042f-45da-84be-570b360236a6");
                }
                else {
                    $(".ParentId").hide();
                }
            })
        });

        function ajaxGetParent(roleid) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("GetParentUser", "Manager")",
                data: { RoleId: roleid },
                success: function (res) {
                    if (res.Success) {
                        $("#ParentId").empty();
                        debugger;
                        res.data.forEach(function (option) {
                            $('#ParentId').append($('<option>', {
                                value: option.Value,
                                text: option.Text
                            }));

                        });
                        $(".ParentId").show();
                    }
                }
            });
        }

        function ajaxLoadParent(roleid,parentId) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("GetParentUser", "Manager")",
                data: { RoleId: roleid },
                success: function (res) {
                    if (res.Success) {
                        $("#ParentId").empty();
                        debugger;
                        res.data.forEach(function (option) {
                            $('#ParentId').append($('<option>', {
                                value: option.Value,
                                text: option.Text
                            }));
                            $('#ParentId').val(parentId);
                        });
                        $(".ParentId").show();
                    }
                }
            });
        }
</script>
}