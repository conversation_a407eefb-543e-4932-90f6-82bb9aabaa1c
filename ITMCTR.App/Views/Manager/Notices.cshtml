@model ITMCTR.Core.Models.SA_Notice
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}

@using (Html.BeginForm("Notices", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>我的消息/Messages</span>
        </li>
    </ul>
    <div class="page-head">
        <div class="container" style="width: 100%;">
            <div class="page-title">
                <h1>
                    @Model.Title
                </h1>
                <h1>
                    <small style="margin-left:10px;">发布时间/CreateTime： @Model.CreateTime.Value.ToString("yyyy-MM-dd")</small>
                </h1>
            </div>
        </div>
    </div>
    <div class="page-content-inner">
        <div class="portlet light portlet-fit ">
            <div class="portlet-body">
                @Html.Raw(Model.Content)
            </div>
        </div>
    </div>
}
