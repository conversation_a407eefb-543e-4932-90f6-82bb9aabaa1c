@model ITMCTR.App.Models.MessageInfoModel
@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}

<!-- [D] CONTENTS -->
<div id="content">
    <div class="inner">
        <div class="member_ct">
            <div class="board_result @(Model.IsError ? "error" : "done")">
                <strong>@Model.MessageTitle</strong>
                <p>
                    @Html.Raw(Model.MessageBody)
                </p>
                <div class="btn_wrap">
                    <a id="warning" class="btn_default btn_outline" href="@Model.TargetUrl">返回/Back</a>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- // [D] CONTENTS -->

@section Scripts{
    <script type="text/javascript">
        var InterValObj; //timer变量，控制时间
        var count = 5; //间隔函数，1秒执行
        var curCount;//当前剩余秒数
        $(document).ready(function () {
            curCount = count;
            var dealType; //验证方式
            InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次
        });
        //timer处理函数
        function SetRemainTime() {
            if (curCount == 0) {
                window.clearInterval(InterValObj);//停止计时器
                window.location = "@Model.TargetUrl";
            }
            else {
                curCount--;
                $("#warning").text(curCount + "秒后自动跳转,will be jump");
            }
        }
    </script>
}
