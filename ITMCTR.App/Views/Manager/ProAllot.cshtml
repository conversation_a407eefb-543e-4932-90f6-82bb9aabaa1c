@model PetaPoco.Page<ITMCTR.App.Models.NewProjectListModel>

@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@using (Html.BeginForm("ProAllot", "Manager", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>已分配项目信息/DistProjects</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- B<PERSON>IN PAGE CONTENT INNER -->
<div class="page-content-inner">
    <div class="m-heading-1 border-green m-bordered">
        <h3 class="form-section">
            筛选条件/Filter Options
        </h3>
        <div class="horizontal-form">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>
                            注册题目/Public title
                        </label>
                        <input type="text" id="txtTitle" name="txtTitle" value="@ViewBag.txtTitle" class="form-control">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>
                            注册号/Registration number
                        </label>
                        <input type="text" id="txtRegNo" name="txtRegNo" value="@ViewBag.txtRegNo" class="form-control">
                    </div>
                </div>
            </div>
            <div class="form-actions right">
                <button type="submit" class="btn blue">
                    <i class="fa fa-check"></i> 查询/Query
                </button>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <!-- BEGIN EXAMPLE TABLE PORTLET-->
            <div class="portlet light ">
                <div class="portlet-title">
                    <div class="caption font-dark">
                        <i class="icon-settings font-dark"></i>
                        <span class="caption-subject bold uppercase">已分配项目信息/DistedProjects</span>
                    </div>
                    <div class="actions">
                    </div>
                </div>
                <div class="portlet-body">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>操作<br />Operate</th>
                                <th>
                                    注册号<br />Registration number
                                </th>
                                <th>
                                    项目名称<br />Public title
                                </th>
                                <th>
                                    审核人<br />Reviewer
                                </th>
                                <th>
                                    首次提交时间<br />First Submission
                                </th>
                                <th>
                                    注册来源<br />From
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Items)
                            {
                                <tr>
                                    <td><a href="@Url.Action("SingleDistributionChange", "Manager",new { Pid = item.Pid} )">变更审核员/ResetUser</a></td>
                                    <td>
                                        @item.regNumber
                                    </td>
                                    <td>
                                        @Html.Raw(item.IsChinese ? item.publicTitleCN + "<br />" : "")  @item.publicTitleEN
                                    </td>
                                    <td>
                                        @item.executeTaskSysUserName
                                    </td>
                                    <td>
                                        @Html.FormatValue(item.regTime, "{0:yyyy/MM/dd}")
                                    </td>
                                    <td>
                                        @item.ProjectOriginCn
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                    <div class="row">
                        <div class="col-md-5 col-sm-12">
                        </div>
                        <div class="col-md-7 col-sm-12">
                            <div class="dataTables_paginate paging_bootstrap_number">
                                @Html.PageLinks(Model, x => Url.Action("ProAllot", new { pageNo = x }))
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>

    <script>
        $(function () {

        });
    </script>
}

