@model ITMCTR.Core.Models.SA_WebStatus
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/CssProject.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />
}

@section Scripts{
    <script src="~/Content/assets/global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datetimepicker/js/locales/bootstrap-datetimepicker.zh-CN.js"></script>
    <script>
        $(function () {
            $(".form_datetime").datetimepicker({
                autoclose: true,
                isRTL: App.isRTL(),
                format: "yyyy-mm-dd hh:ii:ss",
                dateFormat: "yyyy-mm-dd",
                timeFormat:"HH:mm:ss",
                initialDate:new Date(),
                language: "zh-CN",
                pickerPosition: "bottom-left"
            });
        });
    </script>
}
@using (Html.BeginForm("SetWebStatus", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(x => x.WebStatusId)
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">系统关闭/System shutdown</span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
            <div class="form-body">
                <div class="form-group">
                    <label class="col-md-4 control-label">关闭时间/Closing time</label>
                    <div class="col-md-8">
                        <div class="input-group">
                            @Html.TextBoxFor(x => x.EndTime, "{0:yyyy-MM-dd HH:mm:ss}", new { @class = "form-control input-large date form_datetime", maxlength = "100" })
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-4 control-label">中文内容/Chinese content</label>
                    <div class="col-md-8">
                        @Html.TextBoxFor(x => x.Content, new { @class = "form-control", maxlength = "100", required = "required", placeholder = "请输入邮件 input please" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-4 control-label">英文内容/English content</label>
                    <div class="col-md-8">
                        @Html.TextBoxFor(x => x.ContentEn, new { @class = "form-control", maxlength = "100", required = "required", placeholder = "请输入邮件 input please" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-4 control-label">是否开启/Is Disabled</label>
                    <div class="col-md-8">
                        @Html.CheckBoxFor(x => x.IsOpen, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-4 control-label">是否登录页显示/Is Disabled</label>
                    <div class="col-md-8">
                        @Html.CheckBoxFor(x => x.IsShowLogin, new { @class = "make-switch", data_on_color = "success", data_off_color = "danger" })
                    </div>
                </div>
            </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-4 col-md-8">
                            <button type="submit" class="btn green">保存/Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

}