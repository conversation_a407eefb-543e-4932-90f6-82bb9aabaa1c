@model List<ITMCTR.Core.Models.MessageModel>
@{
    Layout = null;
}

<script src="~/Content/assets/global/plugins/jquery.min.js" type="text/javascript"></script>
<script src="~/Scripts/jquery.tmpl.js"></script>

<script>
        function showalert(btn, title, conent, url) {
            $("#divAlertModal").modal();
            var $modal = $('#divAlertModal');
            $modal.find(".modal-title").text(title);//标题
            $modal.find("#pcontent").text(conent);//内容
            $modal.find("#btnConfirm").data("url", url);//Url
            $modal.find("#btnConfirm").text(btn);//按钮文字

            $modal.on('click', '.update', function () {
                if (url && url.length > 0) {
                    window.location.href = url;
                }
            });
            $modal.modal();
        }
        $(function () {
            $("#btnInsertMsg").click(function () {
                var txtMsg = $("#txtMsg").val();
                if (txtMsg == "") {
                    showalert("确定", "提示", "请输入内容", "")
                    return false;
                }
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    url: "@Url.Action("InsertMsg", "Manager")",
                    data: { Pid: $("#pid").val(), Message: txtMsg },
                    success: function (data) {
                        if (data.success) {
                            var msgData = eval("(" + data.data + ")");
                            var html = "";
                            for (var i = 0; i < msgData.length; i++) {
                                if (msgData[i].isIn == 1) {
                                    html += "<li class=\"in\">";
                                    html += "<div class=\"message\">";
                                    html += "<span class=\"arrow\"> </span>";
                                    html += "<a href=\"javascript:;\" class=\"name\">" + msgData[i].FromUserName + "</a>";
                                    html += "<span class=\"datetime\">" + msgData[i].CreateTime + "</span>";
                                    html += "<span class=\"body\">" + msgData[i].Content + "</span>";
                                    html += "</div>";
                                }
                                else {
                                    html += "<li class=\"out\">";
                                    html += "<div class=\"message\">";
                                    html += "<span class=\"arrow\"> </span>";
                                    html += "<a href=\"javascript:;\" class=\"name\">" + msgData[i].ToUserName + "</a>";
                                    html += "<span class=\"datetime\">" + msgData[i].CreateTime + "</span>";
                                    html += "<span class=\"body\">" + msgData[i].Content + "</span>";
                                    html += "</div>";
                                }
                            }
                            $('#ulchats').append(html);
                            showalert("确定", "提示", data.message, '');
                        }
                        else {
                            if (data.IsRedirect) {
                                showalert("确定", "提示", data.message, '');
                            }
                            else {
                                showalert("确定", "提示", data.message, '');
                            }

                        }
                    }
                });
            });
        });
</script>
<script id="MessageTemplate" type="text/x-jquery-tmpl">
    {{if isIn == 1 }}
    <li class="in">
        <div class="message">
            <span class="arrow"> </span>
            <a href="javascript:;" class="name"> ${FromUserName} </a>
            <span class="datetime"> ${CreateTime} </span>
            <span class="body">${Content}</span>
        </div>
    </li>
    {{else}}
    <li class="out">
        <div class="message">
            <span class="arrow"> </span>
            <a href="javascript:;" class="name"> ${ToUserName} </a>
            <span class="datetime"> ${CreateTime} </span>
            <span class="body">${Content}</span>
        </div>
    </li>
    {{/if}}
</script>
<div id="divAlertModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"></h4>
            </div>
            <div class="modal-body">
                <p id="pcontent"></p>
            </div>
            <div class="modal-footer">
                <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
            </div>
        </div>
    </div>
</div>
<div class="form-body">
    <div class="ProjetInfo_ms form-group" style="border-top: 2px solid #666666;">
        <div class="col-md-12 col-sm-12">
            <!-- BEGIN PORTLET-->
            <div class="portlet light bordered">
                <div class="portlet-title">
                    <div class="caption">
                        <i class="icon-bubble font-hide hide"></i>
                        <span class="caption-subject font-hide bold uppercase">站内信</span>
                    </div>
                    <div class="actions">

                    </div>
                </div>
                <div class="portlet-body" id="chats">
                    <div class="scroller" style="height: 525px;" data-always-visible="1" data-rail-visible1="1">
                        <ul class="chats" id="ulchats">
                            @foreach (var item in Model)
                            {
                                if (item.isIn == 1)
                                {
                                    <li class="in">
                                        <div class="message">
                                            <span class="arrow"> </span>
                                            <a href="javascript:;" class="name"> @Html.Raw(item.FromUserName) </a>
                                            <span class="datetime"> @Html.Raw(item.CreateTime) </span>
                                            <span class="body"> @Html.Raw(item.Content)</span>
                                        </div>
                                    </li>
                                }
                                else
                                {
                                    <li class="out">
                                        <div class="message">
                                            <span class="arrow"> </span>
                                            <a href="javascript:;" class="name"> @Html.Raw(item.FromUserName) </a>
                                            <span class="datetime"> @Html.Raw(item.CreateTime) </span>
                                            <span class="body">@Html.Raw(item.Content)</span>
                                        </div>
                                    </li>
                                }
                            }
                        </ul>
                    </div>
                    @if (ITMCTR.App.Models.SysUserProfile.CurrentProfile.UserModel.SysRoleId.Value.ToString() != "49bb2491-2ba5-492d-9f54-6317bcc05baa") { 
                    <div class="chat-form">
                        <div class="input-cont">
                            <input class="form-control" id="txtMsg" type="text" placeholder="请输入内容..." />
                        </div>
                        <div class="btn-cont">
                            <span class="arrow"> </span>
                            <a href="javascript:void(0)" id="btnInsertMsg" class="btn blue icn-only">
                                <i class="fa fa-check icon-white"></i>
                            </a>
                        </div>
                    </div>
                    }
                    
                </div>
            </div>
            <!-- END PORTLET-->
        </div>
    </div>
</div>