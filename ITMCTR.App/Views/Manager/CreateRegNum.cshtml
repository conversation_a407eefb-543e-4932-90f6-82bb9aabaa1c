@{
    ViewBag.ModalTitle = "生成自定义注册号";
    Layout = "~/Views/Shared/_M_ModalLayout.cshtml";
}
@using (Html.BeginForm("CreateRegNum", "Manager", FormMethod.Post))
{
    <div class="modal-body">
        <div class="form-body form-horizontal">
            <div class="alert alert-danger display-hide" id="divdanger">
                <button class="close" data-close="alert"></button>
                输入内容错误，请检查输入内容。input valid
            </div>
            <div class="alert alert-success display-hide" id="divsuccess">
                <button class="close" data-close="alert"></button>
                表单验证成功！ vaildtion success
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label">选择项目/select please</label>
                <div class="col-md-9">
                    
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    数据名称/DataName
                </label>
                <div class="col-md-8">
                    
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    数据备注/DataDesc
                </label>
                <div class="col-md-8">
                    
                </div>
            </div>
            
        </div>
    </div>
    <div class="modal-footer">
        
        <input type="submit" id="btnSave" disabled="disabled" name="btnSave" class="btn green" data-success-dismiss="true" value="确定保存" />
        <button type="button" class="btn default" data-dismiss="modal">关闭/Close</button>
    </div>
}
