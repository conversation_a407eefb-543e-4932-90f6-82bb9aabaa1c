@model ITMCTR.App.Models.DistributionProjectModel

@{
    Layout = null;
}
@section Css{

}
@using (Html.BeginForm("DistributionProject", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
@Html.HiddenFor(x=> x.Pids)
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
        <h4 class="modal-title">分配人员/Distriution</h4>
    </div>
    <div class="modal-body">

        <div class="row">
            <div class="col-md-12">

                <div class="portlet-body">


                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>
                                    项目名称/ProjectName
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.PList)
                            {
                                <tr>
                                    <td>
                                        @item.publicTitleCN <br /> @item.publicTitleEN
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>



                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">分配人员/DistUser</label>
                    <div class="col-md-9">
                        @Html.DropDownListFor(x => x.Uid, ViewBag.UserList as IEnumerable<SelectListItem>, "请选择分配人员 select please", new { @class = "bs-select form-control", required = "required" })
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="modal-footer">
        <button type="button" data-dismiss="modal" class="btn btn-outline dark">关闭/Close</button>
        <button type="submit" class="btn green">保存/Save</button>
    </div>
}