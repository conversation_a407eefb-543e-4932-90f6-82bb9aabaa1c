@model ITMCTR.App.Models.ModifyPwdModel
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@using (Html.BeginForm("ModifyPwd", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(x => x.Uid)
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">
                        密码修改/Change the Password
                    </span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                <div class="form-body">
                    <div class="form-group">
                        <label class="col-md-3 control-label">用户名/Username</label>
                        <div class="col-md-9">
                            @Model.Username
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">原始密码/Original Password </label>
                        <div class="col-md-9">
                            @Html.PasswordFor(x => x.OldPassword, new { @class = "form-control", maxlength = "20", placeholder = "请输入原始密码 old password" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">新密码/New password</label>
                        <div class="col-md-9">
                            @Html.PasswordFor(x => x.NewPassword, new { @class = "form-control", maxlength = "20", placeholder = "请输入新密码 new password" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 control-label">确认密码/Confirm password</label>
                        <div class="col-md-9">
                            @Html.PasswordFor(x => x.ReNewPassword, new { @class = "form-control", maxlength = "20", placeholder = "请输入确认密码 confirm password" })
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-3 col-md-9">
                            <button type="submit" class="btn green">保存/Save</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("SysRolesList")'">返回/Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

}