@model ITMCTR.Core.Models.SA_Users
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}

@using (Html.BeginForm("RegUserCheck", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{

    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">注册用户审核</span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                <div class="form-body">
                    @Html.HiddenFor(x => x.Uid)
                    <div class="form-group">
                        <label class="col-md-3 control-label">登录账户/Account</label>
                        <div class="col-md-9">
                            @Model.Username
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">姓名/Name</label>
                        <div class="col-md-9">
                            @Model.Name
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">邮件/E-mail</label>
                        <div class="col-md-9">
                            @Model.Email
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">注册日期/Date of registration</label>
                        <div class="col-md-9">
                            @Model.RegDate.Value.ToString("yyyy-MM-dd HH:mm:ss")
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">注册IP/Register IP</label>
                        <div class="col-md-9">
                            @Model.RegIP
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">性别/Sex</label>
                        <div class="col-md-9">
                            @Model.Sex
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">座机/Landline</label>
                        <div class="col-md-9">
                            @Model.Phone
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">所属国家/country</label>
                        <div class="col-md-9">
                            @Model.Country
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">移动电话/Mobile Phone</label>
                        <div class="col-md-9">
                            @Model.CellPhone
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">注册单位名称/Institution Name</label>
                        <div class="col-md-9">
                            @Model.RegUnit
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-3 control-label">注册单位地址/Institution</label>
                        <div class="col-md-9">
                            @Model.RegAddress
                        </div>
                    </div>

                </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-3 col-md-9">
                            <button type="button" class="btn green" onclick="CheckUser(1)">同意/agree</button>
                            <button type="button" class="btn green" onclick="CheckUser(2)">拒绝/refuse</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("RegUserCheckList")'">返回/Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="divAlertModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <p id="pcontent"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
                </div>
            </div>
        </div>
    </div>
    <script>
        function showalert(btn, title, conent, url) {
            $("#divAlertModal").modal();
            var $modal = $('#divAlertModal');
            $modal.find(".modal-title").text(title);//标题
            $modal.find("#pcontent").text(conent);//内容
            $modal.find("#btnConfirm").data("url", url);//Url
            $modal.find("#btnConfirm").text(btn);//按钮文字

            $modal.on('click', '.update', function () {
                if (url && url.length > 0) {
                    window.location.href = url;
                }
            });
            $modal.modal();
        }
        function CheckUser(CheckType) {
            var userId = $("#Uid").val();
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("ajaxCheckRegUser", "Manager")",
                data: { Uid: userId, CheckType: CheckType },
                success: function (data) {
                    if (data.success) {
                        showalert("确定", "提示", "操作成功/Operated Successfully", '')
                    }
                    else {
                        showalert("确定", "提示", data.message, '@Url.Action("RegUserCheckList")')
                    }
                }
            });
        }
    </script>
}
