@model ITMCTR.App.Models.BatchAuditModel

@{
    Layout = null;
}
@section Css{

}
@using (Html.BeginForm("BatchAudit", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(x => x.Pids)
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true"></button>
        <h4 class="modal-title">批量审核/Review</h4>
    </div>
    <div class="modal-body">

        <div class="row">
            <div class="col-md-12">
                <div class="portlet-body">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>
                                    项目名称/ProjectName
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.PList)
                            {
                                <tr>
                                    <td>
                                        @item.publicTitleCN <br /> @item.publicTitleEN
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">未通过原因/Reason</label>
                    <div class="col-md-9">
                        @Html.TextAreaFor(x => x.reverifyFailReason, 5, 5, new { @class = "form-control", maxlength = "1000", required = "required", placeholder = "请输入未通过原因" })
                    </div>
                </div>
            </div>

        </div>
    </div>
    <div class="modal-footer">
        <button type="button" data-dismiss="modal" class="btn btn-outline dark">关闭/Close</button>
        <button type="button" id="btnAgree" class="btn green">同意/Agree</button>
        <button type="button" id="btnRefuse" class="btn blue">拒绝/Refuse</button>
    </div>
    <div id="divAlertModal" class="modal fade hide" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <p id="pcontent"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
                </div>
            </div>
        </div>
    </div>
}
<script>
    $(function () {
        $("#btnAgree").click(function () {
            if (confirm("确认是否同意审批记录 confirm to process?")) {
                examinePro(1);
            }
        });
        $("#btnRefuse").click(function () {
            if (confirm("确认是否拒绝此批记录 confirm to process?")) {
                examinePro(2);
            }
        });
    });
    function showalert(btn,title,conent,url) {
        $("#divAlertModal").modal();
        var $modal = $('#divAlertModal');
        $modal.find(".modal-title").text(title);//标题
        $modal.find("#pcontent").text(conent);//内容
        $modal.find("#btnConfirm").data("url", url);//Url
        $modal.find("#btnConfirm").text(btn);//按钮文字

        $modal.on('click', '.update', function () {
            var url = $(this).attr('data-url');
            if (url && url.length > 0) {
                window.location.href = url;
            }
        });
        $modal.modal();
    }
        function examinePro(type) {
            var reverifyFailReason = $("#reverifyFailReason").val();
            if (type == 2 && reverifyFailReason == "") {
                showalert("确定", "提醒", "请输入未通过原因 Reason Please ", "")
                return false;
            }
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("BatchAudit")",
                data: { ids: $("#Pids").val(), type: type, reverifyFailReason:reverifyFailReason},
                success: function (data) {
                    if (data.success) {
                        showalert("确定", "提示", data.message, '@Url.Action("ProFirReview","Manager")')
                    }
                    else {
                       showalert("确定", "提示", data.message, '')
                    }
                }
            });
        }
</script>