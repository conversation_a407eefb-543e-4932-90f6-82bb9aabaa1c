@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/jstree-style.min.css" rel="stylesheet" />
    <link href="~/Content/bootstrap-treeview.css" rel="stylesheet" />
}

@using (Html.BeginForm("Set<PERSON><PERSON><PERSON>", "Manager", FormMethod.Post))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>设置权限</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="row">
            <div class="col-md-12">
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-bubble font-green-sharp"></i>
                            <span class="caption-subject font-green-sharp bold uppercase">设置权限</span>
                        </div>
                        <div class="actions">
                            <div class="btn-group">
                            </div>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <div id="selectTree" class="tree-demo"></div>
                        <div class="form-actions" style="margin-top:20px;">
                            <div class="row">
                                <div class="col-md-offset-3 col-md-9">
                                    <button type="button" id="btnSave" class="btn green">保存</button>
                                    <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("Index")'">返回</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="divAlertModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <p id="pcontent"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
                </div>
            </div>
        </div>
    </div>
}
@section Scripts{
    <script src="~/Scripts/jstree.js"></script>
    <script src="~/Scripts/ui-tree.js"></script>
    <script src="~/Scripts/bootstrap-treeview.js"></script>

    <script>
        function showalert(btn, title, conent, url) {
            $("#divAlertModal").modal();
            var $modal = $('#divAlertModal');
            $modal.find(".modal-title").text(title);//标题
            $modal.find("#pcontent").text(conent);//内容
            $modal.find("#btnConfirm").data("url", url);//Url
            $modal.find("#btnConfirm").text(btn);//按钮文字

            $modal.on('click', '.update', function () {
                if (url && url.length > 0) {
                    window.location.href = url;
                }
            });
            $modal.modal();
        }
        $(function () {
            var data = eval('(' + '@Html.Raw(ViewData["TreeData"])'+')');
            $('#selectTree').jstree({
                multiple: false,
                'plugins': ["wholerow", "checkbox2", "types"],
                'core': {
                    "themes": {
                        "responsive": false
                    },
                    'data': data
                },
                "checkbox": { "visible": true },
                "types": {
                    "default": {
                        "icon": "fa fa-folder icon-state-warning icon-lg"
                    },
                    "file": {
                        "icon": "fa fa-file icon-state-warning icon-lg"
                    }
                }
            });

            $("#btnSave").click(function () {
                var tree = $('#selectTree').jstree(true);
                var se = tree.get_selected();
                var json = tree.get_json();
                var RoleId = '@ViewData["RoleId"]'
                $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("PostSelectFun", "Manager")",
                    data: { strTreeData: JSON.stringify(json), RoleId: RoleId },
                success: function (data) {
                    if (data.success) {
                        showalert("确定", "提示", "保存成功", "")
                    }
                    else {
                        showalert("确定", "提示", data.message, "")
                    }
                }
            });
            });
        });
    </script>
}
