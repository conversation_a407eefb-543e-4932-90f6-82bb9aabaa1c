@model PetaPoco.Page<ITMCTR.Core.Models.SA_NewsType>
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
    <script>
    function DelRecord(MainKey) {
        if (confirm("是否要删除词条记录,delete?")) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("NewsTypeDelete", "Manager")",
                data: { NtId: MainKey },
                success: function (data) {
                    if (data.success) {
                        window.location.href = window.location.href;
                    }
                    else {
                        alert(data.message);
                    }
                }
            });
        }
    }
    </script>
}
@using (Html.BeginForm("NewsTypeList", "Manager", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>新闻分类管理/New Class</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="m-heading-1 border-green m-bordered">
            <h3 class="form-section">
                筛选条件/Filter Options
            </h3>
            <div class="horizontal-form">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>分类名称</label>
                            <input type="text" id="txtRoleName" name="txtRoleName" value="@ViewBag.txtTitle" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="submit" class="btn blue">
                        <i class="fa fa-check"></i> 查询/Query
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">新闻分类管理/New Class</span>

                        </div>
                        <div class="actions">
                            <a href="@Url.Action("NewsTypeAdd", "Manager" )" data-toggle="modal" class="btn default yellow-stripe">
                                <i class="fa fa-plus text"></i>
                                <span class="text">新建/Add</span>
                            </a>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>操作<br />Operate</th>
                                    <th>
                                        分类名称<br />Name
                                    </th>
                                    <th>
                                        创建时间<br />CreateTime
                                    </th>
                                    <th>
                                        显示<br />Used
                                    </th>
                                    <th>
                                        首页显示<br />HomeDisplay
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                    <tr>
                                        <td>
                                            <a href="@Url.Action("NewsTypeEdit", "Manager", new { NtId = item.NtId })">编辑/Edit</a>
                                            <a href="javascript:DelRecord('@item.NtId')">删除/Delete</a>
                                        </td>
                                        <td>
                                            @item.Name
                                        </td>
                                        <td>
                                            @item.CreateTime
                                        </td>
                                        <td>
                                            @(item.IsView == 1 ? "是Yes" : "否No" )
                                        </td>
                                        <td>
                                            @(item.IsIndexView == 1 ? "是Yes" : "否No" )
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("NewsTypeList", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
}