@model PetaPoco.Page<ITMCTR.Core.Models.SA_SysRoles>
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
}
@using (Html.BeginForm("SysRolesList", "Manager", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>用户角色/Roles</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="m-heading-1 border-green m-bordered">
            <h3 class="form-section">
                筛选条件/Filter Options
            </h3>
            <div class="horizontal-form">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>角色名称/RoleName</label>
                            <input type="text" id="txtRoleName" name="txtRoleName" value="@ViewBag.txtRoleName" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>角色描述/RoleDesc</label>
                            <input type="text" id="txtRoleDesc" name="txtRoleDesc" value="@ViewBag.txtRoleDesc" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="submit" class="btn blue">
                        <i class="fa fa-check"></i> 查询/Query
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">用户角色/Roles</span>

                        </div>
                        <div class="actions">
                            <a href="@Url.Action("SysRolesAdd", "Manager" )" data-toggle="modal" class="btn default yellow-stripe">
                                <i class="fa fa-plus text"></i>
                                <span class="text">新建/Add</span>
                            </a>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>操作<br />Operate</th>
                                    <th>
                                        角色名称<br />RoleName
                                    </th>
                                    <th>
                                        角色描述<br />RoleDesc
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                    <tr>
                                        <td>
                                            <a href="@Url.Action("SysRolesEdit", "Manager",new { RoleId=item.RoleId} )">编辑/Edit</a>
                                            @if (!item.IsSysRole.HasValue || (item.IsSysRole.HasValue && item.IsSysRole.Value == 0))
                                            {
                                                <a href="javascript:DelRecord('@item.RoleId')">删除/Delete</a>
                                            }
                                            <a href="@Url.Action("SettingFun", "Manager",new { RoleId=item.RoleId} )">设置权限/Setup</a>
                                        </td>
                                        <td>
                                            @item.RoleName
                                        </td>
                                        <td>
                                            @item.RoleDesc
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("SysRolesList", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
    <div id="divAlertModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <p id="pcontent"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
                </div>
            </div>
        </div>
    </div>
}
<script>
    function showalert(btn, title, conent, url) {
        $("#divAlertModal").modal();
        var $modal = $('#divAlertModal');
        $modal.find(".modal-title").text(title);//标题
        $modal.find("#pcontent").text(conent);//内容
        $modal.find("#btnConfirm").data("url", url);//Url
        $modal.find("#btnConfirm").text(btn);//按钮文字
        $modal.on('click', '.update', function () {
            if (url && url.length > 0) {
                window.location.href = url;
            }
        });
        $modal.modal();
    }
    function DelRecord(MainKey) {
        if (confirm("是否要删除词条记录,delete?")) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("SysRolesDel", "Manager")",
                data: { RoleId: MainKey },
                success: function (data) {
                    if (data.success) {
                        showalert("确定", "提示", '删除成功', window.location.href)
                    }
                    else {
                        showalert("确定", "提示", data.message,'')
                    }
                }
            });
        }
    }
</script>