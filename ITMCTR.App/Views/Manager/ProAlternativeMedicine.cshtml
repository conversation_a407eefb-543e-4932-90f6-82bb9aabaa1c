@model PetaPoco.Page<ITMCTR.App.Models.UserProjectViewModel>

@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@using (Html.BeginForm("ProAlternativeMedicine", "Manager", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>非传统医学/Alternative Medicine</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">

        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">非传统医学</span>
                        </div>
                        <div class="actions">

                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>操作<br />Operate</th>
                                    <th>
                                        注册号<br />Registration number
                                    </th>
                                    <th>
                                        项目名称<br />Public title
                                    </th>
                                    <th>
                                        首次提交时间<br />First Submission
                                    </th>
                                    <th>
                                        审核状态<br />Review Status
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                    <tr>
                                        <td>
                                            <a href="@Url.Action("ProjectView", "Manager",new { Pid=item.pid} )">查看/View</a>
                                            @if (ViewData["SysRoleId"].ToString() == "49bb2491-2ba5-492d-9f54-6317bcc05baa")
                                            {
                                                <a href="@Url.Action("ModifyAlternativeMedicineState", "Manager",new { Pid=item.pid} )">修改状态/Modify State</a>
                                            }
                                        </td>
                                        <td>
                                            @item.regNumber
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.publicTitleCN) && item.IsChinese)
                                            {
                                                @Html.ActionLink(item.publicTitleCN, "ProjectView", new { pid = item.pid })
                                                <br />
                                            }
                                            @if (!string.IsNullOrEmpty(item.publicTitleEN))
                                            {
                                                @Html.ActionLink(item.publicTitleEN, "ProjectView", new { pid = item.pid })
                                                <br />
                                            }
                                            @if (!string.IsNullOrEmpty(item.applierCompanyEN) && item.IsEnglist)
                                            {
                                                @item.applierCompanyEN
                                            }
                                            else if (!string.IsNullOrEmpty(item.applierCompanyCN) && item.IsChinese)
                                            {
                                                @item.applierCompanyCN
                                            }
                                        </td>
                                        <td>
                                            @(item.regTime.HasValue?item.regTime.Value.ToString("yyyy-MM-dd HH:mm:ss"):"")
                                        </td>
                                        <td>
                                            @switch (item.status)
                                            {
                                                case 0:
                                                    <span class="label label-sm label-warning">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                    break;
                                                case 1:
                                                    <span class="label label-sm label-info">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                    break;
                                                case 3:
                                                    <span class="label label-sm label-success">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                    break;
                                                case 2:
                                                    <span class="label label-sm label-danger">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                    break;
                                                default:
                                                    break;
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("ProAlternativeMedicine", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>

    <script>
        $(function () {
            $('.ajaxModal').on('click', function () {
                if ($("tbody").find('input:checked').length == 0) {
                    alert("请选择记录");
                    return false;
                }
                var $modal = $('#secondary-modal');
                // create the backdrop and wait for next modal to be triggered
                $('body').modalmanager('loading');
                var el = $(this);
                var ids = '';
                var url = el.attr('data-url') + "?ids=";
                $("tbody").find('input:checked').each(function (i, item) {
                    ids += $(item).attr("id") + ',';
                });
                url += ids;
                setTimeout(function () {
                    $modal.load(url, '', function () {
                        $modal.modal();
                    });
                }, 1000);
            });
        });
    </script>
}
<!-- END PAGE CONTENT INNER -->
