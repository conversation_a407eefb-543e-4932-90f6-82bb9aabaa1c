@model PetaPoco.Page<ITMCTR.Core.Models.SA_Users>
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
    <script src="~/Scripts/clipboard/clipboard.js"></script>
}
@using (Html.BeginForm("RegUserResetList", "Manager", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>用户注册管理/UserManagement</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="m-heading-1 border-green m-bordered">
            <h3 class="form-section">
                筛选条件/Filter Optionss
            </h3>
            <div class="horizontal-form">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>登录账户/Account</label>
                            <input type="text" id="txtUsername" name="txtUsername" value="@ViewBag.txtUsername" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>姓名/Username</label>
                            <input type="text" id="txtName" name="txtName" value="@ViewBag.txtName" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="submit" class="btn blue">
                        <i class="fa fa-check"></i> 查询/Query
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">用户注册管理/UserManagement</span>

                        </div>
                        <div class="actions">

                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>操作<br />Operate</th>
                                    <th>登录账户<br />Account</th>
                                    <th>姓名<br />UserName</th>
                                    <th>邮箱<br />Email</th>
                                    <th>所属国家<br />Country</th>
                                    <th>联系电话<br />Phone</th>
                                    <th>注册单位名称<br />RegUnit</th>
                                    <th>注册单位地址<br />RegAddress</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                    <tr>
                                        <td>
                                            <a href="javascript:ResetPwd('@item.Uid')">重置密码/reset password</a>
                                        </td>
                                        <td>@item.Username</td>
                                        <td>@item.Name</td>
                                        <td>@item.Email</td>
                                        <td>@item.Country</td>
                                        <td>@item.CellPhone</td>
                                        <td>@item.RegUnit</td>
                                        <td>@item.RegAddress</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("RegUserResetList", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="divAlertModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <p id="pcontent"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
}
<script>
    function showalert(btn, title, conent, url) {
        $("#divAlertModal").modal();
        var $modal = $('#divAlertModal');
        $modal.find(".modal-title").text(title);//标题
        $modal.find("#pcontent").text(conent);//内容
        $modal.find("#btnConfirm").data("url", url);//Url
        $modal.find("#btnConfirm").text(btn);//按钮文字

        $modal.on('click', '.update', function () {
            if (url && url.length > 0) {
                window.location.href = url;
            }
        });
        $modal.modal();
    }
    function ResetPwd(MainKey) {
        if (confirm("是否要重置此账号的密码,delete?")) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("ResetPwd", "Manager")",
                data: { Uid: MainKey },
                success: function (data) {
                    if (data.Success) {
                        try {
                            ClipboardJS.copy(data.Pw);
                        } catch (e) {

                        }
                        showalert("确定", "提示", "密码重置成功!\r\n新密码为" + data.Pw + ",已复制到剪切板中。reset is success", "")
                    }
                    else {
                        showalert("确定", "提示", data.message, "")
                    }
                }
            });
        }
    }
    function copyText(text) {

        try {
            var flag = document.execCommand("copy"); //执行复制
        } catch (eo) {
            var flag = false;
        }
        return flag;
    }
</script>
