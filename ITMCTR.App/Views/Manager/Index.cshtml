@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <style>
        .slh1 {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box !important;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }
    </style>
}

<!-- BEGIN PAGE BREADCRUMBS -->
<ul class="page-breadcrumb breadcrumb">
    <li>
        <a href="@Url.Action("Index")">首页/Home</a>
        <i class="fa fa-circle"></i>
    </li>
    <li>
        <span>Home</span>
    </li>
</ul>
<!-- END PAGE BREADCRUMBS -->
<!-- BEGIN PAGE CONTENT INNER -->
<div class="page-content-inner">
    @if (ViewBag.RoleId == "49bb2491-2ba5-492d-9f54-6317bcc05baa")//一级审核员
    {
        @Html.Partial("Index_Partial_FirstReview");
    }
    @if (ViewBag.RoleId == "3d64080d-042f-45da-84be-570b360236a6")//二级审核员
    {
        @Html.Partial("Index_Partial_SecReview");
    }
    @if (ViewBag.RoleId == "f707cae7-17c3-49e3-b70c-a1d9fb6e5afe")//管理员
    {
        @Html.Partial("Index_Partial_Admin");
    }


    <div class="row">
        <div class="col-md-6 col-sm-6">
            <!-- BEGIN PORTLET-->
            <div class="portlet light ">
                <div class="portlet-title tabbable-line">
                    <div class="caption">
                        <i class="icon-globe font-dark hide"></i>
                        <span class="caption-subject font-dark bold uppercase">
                            站内消息/Messages
                        </span>
                    </div>
                    <ul class="nav nav-tabs">
                        <li class="active">
                            <a href="#tab_1_1" class="active" data-toggle="tab">未读/Unread</a>
                        </li>
                        <li>
                            <a href="#tab_1_2" data-toggle="tab">已读/Read</a>
                        </li>
                    </ul>
                </div>
                <div class="portlet-body">
                    <!--BEGIN TABS-->
                    <div class="tab-content">
                        <div class="tab-pane active" id="tab_1_1">
                            <div class="scroller" style="height: 339px;" data-always-visible="1" data-rail-visible="0">
                                <ul class="feeds">
                                    @foreach (var item in ViewBag.NoReadList)
                                    {
                                        <li>
                                            <a href="@Url.Action("Notices","Manager",new {Nid=item.Nid })" title="@item.Title" target="_blank">
                                                <div class="col1" style="padding-right:5px;">
                                                    <div class="cont">
                                                        <div class="cont-col1">
                                                            <div class="label label-sm label-danger">
                                                                <i class="fa fa-bell-o"></i>
                                                            </div>
                                                        </div>
                                                        <div class="cont-col2">
                                                            <div class="desc slh1" style="padding-bottom: 0px;">@item.Title</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col2" style="width:100px; margin-left:-100px;">
                                                    <div class="date" style="padding: 4px 4px 4px 0;"><span>@Html.Raw(item.CreateTime.ToString("yyyy-MM-dd"))</span></div>
                                                </div>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </div>
                        </div>
                        <div class="tab-pane" id="tab_1_2">
                            <div class="scroller" style="height: 290px;" data-always-visible="1" data-rail-visible1="1">
                                <ul class="feeds">
                                    @foreach (var item in ViewBag.IsReadList)
                                    {
                                        <li>
                                            <a href="@Url.Action("Notices","Manager",new {Nid=item.Nid })" title="@item.Title" target="_blank">
                                                <div class="col1" style="padding-right:5px;">
                                                    <div class="cont">
                                                        <div class="cont-col1">
                                                            <div class="label label-sm label-success">
                                                                <i class="fa fa-bell-o"></i>
                                                            </div>
                                                        </div>
                                                        <div class="cont-col2">
                                                            <div class="desc slh1" style="padding-bottom: 0px;">@item.Title</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col2" style="width:100px; margin-left:-100px;">
                                                    <div class="date" style="padding: 4px 4px 4px 0;"><span>@Html.Raw(item.CreateTime.ToString("yyyy-MM-dd"))</span></div>
                                                </div>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </div>
                        </div>
                    </div>
                    <!--END TABS-->
                </div>
            </div>
            <!-- END PORTLET-->
        </div>
        <div class="col-md-6 col-sm-6">
            <!-- BEGIN PORTLET-->
            <div class="portlet light ">
                <div class="portlet-title tabbable-line">
                    <div class="caption">
                        <i class="icon-globe font-dark hide"></i>
                        <span class="caption-subject font-dark bold uppercase">
                            新闻/News
                        </span>
                    </div>
                    <ul class="nav nav-tabs">
                        <li class="active">
                            <a href="#tab_1_1" class="active" data-toggle="tab">
                                平台发布/Published
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="portlet-body">
                    <!--BEGIN TABS-->
                    <div class="tab-content">
                        <div class="tab-pane active" id="tab_1_1">
                            <div class="scroller" style="height: 339px;" data-always-visible="1" data-rail-visible="0">
                                <ul class="feeds">
                                    @foreach (var item in ViewBag.News)
                                    {
                                        <li>
                                            <a href="javascript:void(0)" title="@item.Title">
                                                <div class="col1" style="padding-right:5px;">
                                                    <div class="cont">
                                                        <div class="cont-col1">
                                                            <div class="label label-sm label-success">
                                                                <i class="fa fa-bullhorn"></i>
                                                            </div>
                                                        </div>
                                                        <div class="cont-col2">
                                                            <div class="desc slh1" style="padding-bottom: 0px;">@item.Title</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col2" style="width:100px; margin-left:-100px;">
                                                    <div class="date" style="padding: 4px 4px 4px 0;"><span>@Html.FormatValue(item.CreateTime, "{0:yyyy-MM-dd}")</span></div>
                                                </div>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </div>
                        </div>
                        <div class="tab-pane" id="tab_1_2">
                            <div class="scroller" style="height: 290px;" data-always-visible="1" data-rail-visible1="1">
                                <ul class="feeds">
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <!--END TABS-->
                </div>
            </div>
            <!-- END PORTLET-->
        </div>
    </div>

</div>
<!-- END PAGE CONTENT INNER -->
