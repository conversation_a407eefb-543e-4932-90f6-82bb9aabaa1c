@model List<ITMCTR.Core.Models.SA_ProjectVerifyTaskFlowRecord>
@{
    Layout = null;
}
@if (Model.Count() > 0)
{
    <table width="100%" cellspacing="0" cellpadding="0" border="0">
        <tbody>
            
            <tr>
                <td class="left_title control-label col-lg-2">
                    <p class="cn">
                        历史修改时间：
                    </p>
                    <p class="en">
                        Historical modification time：
                    </p>
                </td>
                <td>
                    @foreach (var item in Model)
                    {
                        <p> @item.TaskFlowCreateSysUserId 于 @(item.CreateTime.HasValue?item.CreateTime.Value.ToString("yyyy-MM-dd HH:mm:ss"):"") 提交 </p>
                    }
                </td>
            </tr>
        </tbody>
    </table>
}
