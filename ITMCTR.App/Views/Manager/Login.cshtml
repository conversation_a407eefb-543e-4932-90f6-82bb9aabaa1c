@model ITMCTR.App.Models.ManagerLoginModel
@{
    Layout = null;
}

<!DOCTYPE html>
<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
    <title>国际传统医学临床注册平台ITMCTR</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <link href="http://fonts.googleapis.com/css?family=Open+Sans:400,300,600,700&subset=all" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/select2/css/select2.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/select2/css/select2-bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/css/components.min.css?v=@UiFun.AppVersion" rel="stylesheet" id="style_components" type="text/css" />
    <link href="~/Content/assets/global/css/plugins.min.css?v=@UiFun.AppVersion" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/pages/css/login-3.min.css?v=@UiFun.AppVersion" rel="stylesheet" type="text/css" />
</head>
<body class=" login">
    <div class="logo">
        <a href="javascript:void(0)">
            <img src="~/img/Loginlogo.png" />
        </a>
    </div>
    <div class="content">
        <!-- BEGIN LOGIN FORM -->
        @using (Html.BeginForm("Login", "Manager", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
        {
            var validationSummary = Html.ValidationSummary(true);
            if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
            {
                <div class="alert alert-danger">
                    <button class="close" data-close="alert"></button>
                    <span>
                        @validationSummary
                    </span>
                </div>
            }
            <h3 class="form-title">
                系统登录/System Login
            </h3>
            <div class="form-group">
                <!--ie8, ie9 does not support html5 placeholder, so we just show field title for that-->
                <label class="control-label visible-ie8 visible-ie9">
                    用户名/Username
                </label>
                <div class="input-icon">
                    <i class="fa fa-user"></i>
                    @Html.TextBoxFor(x => x.Username, new { @class = "form-control placeholder-no-fix", maxlength = "30", required = "required", placeholder = "请输入用户名 UserName", autocomplete = "off" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label visible-ie8 visible-ie9">密码/Password</label>
                <div class="input-icon">
                    <i class="fa fa-lock"></i>
                    @Html.PasswordFor(x => x.Password, new { @class = "form-control placeholder-no-fix", maxlength = "20", required = "required", placeholder = "请输入密码 Password", autocomplete = "off" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label visible-ie8 visible-ie9">图形验证码/Captcha</label>
                <div class="input-group input-icon">
                    <i class="fa fa-image">
                    </i>
                    @Html.TextBoxFor(x => x.Captcha, new { @class = "form-control placeholder-no-fix", maxlength = "20", required = "required", placeholder = "输入图形验证码 Captcha", autocomplete = "off" })
                    <span class="input-group-addon">
                        <img id="valiCode" style="cursor: pointer;" src="@Url.Action("GetValidateCode")" alt="验证码" />
                    </span>
                </div>
            </div>
            <div class="form-actions">
                @Html.HiddenFor(x => x.Encrypt)
                <label class="rememberme mt-checkbox-outline">
                    <span></span>
                </label>
                <button type="submit" id="btnLogin" class="btn green pull-right">
                    登录/Log In
                </button>
            </div>
        }
        <!-- END LOGIN FORM -->
    </div>

    <!--[if lt IE 9]>
    <script src="../assets/global/plugins/respond.min.js"></script>
    <script src="../assets/global/plugins/excanvas.min.js"></script>
    <![endif]-->
    <script src="~/Content/assets/global/plugins/jquery.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/js.cookie.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-hover-dropdown/bootstrap-hover-dropdown.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-slimscroll/jquery.slimscroll.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery.blockui.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-switch/js/bootstrap-switch.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/additional-methods.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/select2/js/select2.full.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/scripts/app.min.js?v=@UiFun.AppVersion" type="text/javascript"></script>
    <script src="~/Content/assets/global/scripts/login.min.js?v=@UiFun.AppVersion"></script>
    <script src="~/Content/JsEncryptHelper.js"></script>
    <script>
        jQuery(document).ready(function () {
            $("#btnLogin").click(function () {
                var val = $("#Password").val();
                $("#Password").val($.encryptVal(val));
                return true;
            });
        });
    </script>
</body>
</html>
