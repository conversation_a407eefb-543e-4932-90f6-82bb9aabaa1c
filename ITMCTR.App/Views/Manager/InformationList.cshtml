@model PetaPoco.Page<ITMCTR.App.Models.WebsiteInformationViewModel>
@{
    Layout = "~/Views/Shared/_M_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
    <script>
    function DelRecord(MainKey) {
        if (confirm("是否要删除词条记录,delete?")) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("InformationDelete", "Manager")",
                data: { WiId: MainKey },
                success: function (data) {
                    if (data.success) {
                        window.location.href = window.location.href;
                    }
                    else {
                        alert(data.message);
                    }
                }
            });
        }
    }
    </script>
}
@using (Html.BeginForm("InformationList", "Manager", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>内容管理/Content Manager</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="m-heading-1 border-green m-bordered">
            <h3 class="form-section">
                筛选条件/Filter Options
            </h3>
            <div class="horizontal-form">
                <div class="row">
                    <div class="col-md-4">
                        http://localhost:56374/zh-CN/Manager/InformationAdd
                        <div class="form-group">
                            <label>内容标题</label>
                            <input type="text" id="txtRoleName" name="txtRoleName" value="@ViewBag.txtTitle" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="submit" class="btn blue">
                        <i class="fa fa-check"></i> 查询/Query
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">内容管理/Content Manager</span>
                        </div>
                        <div class="actions">
                            <a href="@Url.Action("InformationAdd", "Manager" )" data-toggle="modal" class="btn default yellow-stripe">
                                <i class="fa fa-plus text"></i>
                                <span class="text">新建/Add</span>
                            </a>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>操作<br />Operate</th>
                                    <th>
                                        标题<br />Title
                                    </th>
                                    <th>
                                        代码<br />InfoCode
                                    </th>
                                    <th>
                                        创建时间<br />CreateTime
                                    </th>
                                    <th>
                                        置顶<br />Top
                                    </th>
                                    <th>
                                        首页显示<br />HomeDisplay
                                    </th>
                                    <th>
                                        发布<br />Used
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                    <tr>
                                        <td>
                                            <a href="@Url.Action("InformationEdit", "Manager", new { WiId = item.WiId })">编辑Edit</a>
                                            <a href="javascript:DelRecord('@item.WiId')">删除/Delete</a>
                                        </td>
                                        <td>
                                            @item.Title<br />@item.TitleEN
                                        </td>
                                        <td>
                                            @item.InfoCode
                                        </td>
                                        <td>
                                            @item.CreateTime
                                        </td>
                                        <td>
                                            @(item.IsTop == 1 ? "是Yes" : "否No" )
                                        </td>
                                        <td>
                                            @(item.IsViewIndex == 1 ? "是Yes" : "否No" )
                                        </td>
                                        <td>
                                            @(item.IsRelease == 1 ? "是Yes" : "否No" )
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("InformationList", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
}