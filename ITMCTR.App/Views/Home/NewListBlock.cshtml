@model PetaPoco.Page<ITMCTR.Core.Models.SA_News>
@{

    Layout = null;
}
<div class="nr_center3">
    @foreach (var item in Model.Items)
    {
        <div class="nr_center3_new">
            <div class="slh1"><a target="_blank" href="@Url.Action("Details","home",new {Nid=item.Nid })">@UiFun.Lang(item.Title, item.TitleEN)</a></div>
            <div style=" color: #777777; font-size: 14px; display: flex;">
                <div style="margin-top:2px;">
                    <img src="~/Content/images/new.png" width="18" height="19" />
                </div>
                <div>
                    @Html.FormatValue(item.ReleaseTime, UiFun.Lang("{0:「于yyyy年MM月dd日更新」}", "{0:「yyyy/MM/dd」}"))
                </div>
            </div>
        </div>
    }
</div>