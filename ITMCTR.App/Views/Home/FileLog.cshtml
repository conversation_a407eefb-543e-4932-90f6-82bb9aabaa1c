
@{
    Layout = null;
}
@using ITMCTR.Core.Models
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>FileLog</title>
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
</head>
<body>
    @using (Html.BeginForm())
    {
        <div>
            <input id="trialid" name="trialid" /><input id="btnQuery" type="submit" value="submit" />
        </div>
        if (ViewBag.files != null)
        {
            <ul>
                @foreach (var item in ViewBag.files)
                {
                    <li><a href="/@item">@item</a></li>
                }
            </ul>
        }
        if (ViewBag.list != null)
        {
            <table class="table">
                <tr>
                    <td>RequestTime</td>
                    <td>ApplyTime</td>
                    <td>EditRequestResult</td>
                    <td>EditRequestReason</td>
                </tr>

                @foreach (SA_ProjectRequestEditFlow item in ViewBag.list)
                {
                    <tr>
                        <td>@item.RequestTime</td>
                        <td>@item.ApplyTime</td>
                        <td>@item.EditRequestResult</td>
                        <td>@item.EditRequestReason</td>
                    </tr>
                }

            </table>
        }
    }
</body>
</html>
