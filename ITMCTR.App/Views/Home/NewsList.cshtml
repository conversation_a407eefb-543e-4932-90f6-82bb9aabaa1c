@model PetaPoco.Page<ITMCTR.Core.Models.SA_News>
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="zhuce_nr">
    <div class="zhuce_nr_title2" style="display:flex;"><font style="font-size:20px; font-weight:700; padding-right:20px; padding-top:20px;">@Resources.Global.View_新闻列表</font></div>
    <div class="jiansuo_content1">
        @foreach (var item in Model.Items)
        {
            if (UiFun.IsLang("zh-CN"))
            {
                <div class="jiansuo_center3_new">
                    <div class="slh1"><A href="@Url.Action("Details","Home",new {Nid=item.Nid})">@item.Title</A></div>
                    <div class="jiansuo_center3_new1">
                        <div class="jiansuo_center3_new2 slh1" style="width:80%;">@item.Subtitle</div>
                        <div class="jiansuo_center3_new3" style="width:80%;">@Resources.Global.View_发布时间：@string.Format("{0:yyyy-MM-dd}", item.ReleaseTime)</div>
                    </div>
                </div>
            }
            else
            {
                <div class="jiansuo_center3_new">
                    <div class="slh1"><A href="@Url.Action("Details","Home",new {Nid=item.Nid})">@item.TitleEN</A></div>
                    <div class="jiansuo_center3_new1">
                        <div class="jiansuo_center3_new2 slh1" style="width:80%;">@item.SubtitleEN</div>
                        <div class="jiansuo_center3_new3" style="width:20%;">@Resources.Global.View_发布时间：@string.Format("{0:yyyy-MM-dd}", item.ReleaseTime)</div>
                    </div>
                </div>
            }

        }


    </div>

    <div class="jiansuo_fl">
        <ul class="pagination">
            @Html.PageLinks(Model, x => Url.Action("NewsList", new { pageNo = x }), false)
            @*<li><a href="#"><</a></li>
            <li><a href="#">1</a></li>
            <li><a class="active" href="#">2</a></li>
            <li><a href="#">3</a></li>
            <li><a href="#">4</a></li>
            <li><a href="#">5</a></li>
            <li><a href="#">6</a></li>
            <li><a href="#">7</a></li>
            <li><a href="#">下一页&nbsp;></a></li>*@
        </ul>
    </div>

</div>