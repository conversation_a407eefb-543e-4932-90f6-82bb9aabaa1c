@model PetaPoco.Page<ITMCTR.App.Models.UserProjectViewModel>
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@section Css{

    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script>
        $(function () {
            if (jQuery().datepicker) {
                $('.date-picker').datepicker({
                    language: '@UiFun.Applang',
                    orientation: "left",
                    autoclose: true,
                    format: "yyyy-mm-dd",
                    todayBtn: "linked"
                });
            }
            $("#filter > div:not(.normal)").hide();
            $("#btnMoreHide").hide();
            $("#btnMore,#btnMoreHide").click(function () {
                var show = $("#btnMoreHide").is(":visible");
                if (show) {
                    $("#filter > div:not(.normal)").hide();
                    $("#btnMoreHide").hide();
                    $("#btnMore").show();
                } else {
                    $("#filter > div:not(.normal)").show();
                    $("#btnMoreHide").show();
                    $("#btnMore").hide();
                }
            });
            $(".pagination-panel-input").change(function () {
                var page = Number($(this).val());
                if (page !== undefined && page > 0) {
                    $("#pageNo").val(page - 1);
                    $("#TrialSearchForm").submit();
                }
            });
        })
    </script>
}
<div class="zhuce_nr">
    @using (Html.BeginForm("TrialSearch", "Home", FormMethod.Get, new { id = "TrialSearchForm" }))
    {
        <div class="page-container">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption font-green sbold">
                        @UiFun.Lang("项目筛选条件", "Filter Options")
                    </div>
                </div>
                <div class="portlet-body form">
                    <div class="form-horizontal">
                        <div class="form-body" id="filter">
                            <div class="row normal">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("注册题目", "Public title")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtTitle" name="txtTitle" value="@ViewBag.txtTitle" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("正式科学名", "Scientific title")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtOfficialName" name="txtOfficialName" value="@ViewBag.txtOfficialName" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究课题代号(代码)", "Subject ID")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtSubjectID" name="txtSubjectID" value="@ViewBag.txtSubjectID" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row normal">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("注册状态", "Registration Status")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listRegStatus", ViewBag.listRegStatusDict as IEnumerable<SelectListItem>, "", new { @class = "form-control input-medium" })
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("注册号", "Registration number")</label>
                                        <div class="col-md-8">
                                            <input type="text" d="txtRegNo" name="txtRegNo" value="@ViewBag.txtRegNo" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("在其它机构的注册号", "Secondary ID")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtSecondaryID" name="txtSecondaryID" value="@ViewBag.txtSecondaryID" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("申请注册联系人", "Applicant")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtApplier" name="txtApplier" value="@ViewBag.txtApplier" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究负责人", "Study leader")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtStudyLeader" name="txtStudyLeader" value="@ViewBag.txtStudyLeader" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("年份", "Year")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listCreateYear", ViewBag.listCreateYearDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究实施负责（组长）单位", "Primary sponsor")</label>
                                        <div class="col-md-8">
                                            <input type="text" class="form-control x-lager" id="txtSponsor" name="txtSponsor" value="@ViewBag.txtSponsor">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("试验主办单位", "Secondary sponsor")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtSecSponsor" name="txtSecSponsor" value="@ViewBag.txtSecSponsor" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("经费或物资来源", "Source(s) of funding")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtSourceOfSpends" name="txtSourceOfSpends" value="@ViewBag.txtSourceOfSpends" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究疾病名称", "Target disease")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtStudyAilment" name="txtStudyAilment" value="@ViewBag.txtStudyAilment" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究疾病代码", "Target disease code")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtStudyAilmentCode" name="txtStudyAilmentCode" value="@ViewBag.txtStudyAilment" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究类型", "Study type")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listStudyType", ViewBag.listStudyTypeDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究所处阶段", "Study phase")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listStudyStage", ViewBag.listStudyStageDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究设计", "Study design")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listStudyDesign", ViewBag.listStudyDesignDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("研究实施时间", "Study execute time")</label>
                                        <div class="col-md-8">
                                            <div class="input-group input-large input-daterange">
                                                <input type="text" id="txtMinStudyExecuteTime" name="txtMinStudyExecuteTime" value="@ViewBag.txtMinStudyExecuteTime" class="form-control date-picker">
                                                <span class="input-group-addon"> to </span>
                                                <input type="text" id="txtMaxStudyExecuteTime" name="txtMaxStudyExecuteTime" value="@ViewBag.txtMaxStudyExecuteTime" class="form-control date-picker">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("征募研究对象情况", "Recruiting status")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listRecruitmentStatus", ViewBag.listRecruitmentStatusDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("性别", "Gender")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listGender", ViewBag.listGenderDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("签署知情同意书", "Sign the informed consent")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listAgreeToSign", ViewBag.listAgreeToSignDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <h6 class="form-section">@UiFun.Lang("实施地点", "Countries of recruitment and research settings")</h6>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("国家(地区)", "Nation(Area)")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtCountry" name="txtCountry" value="@ViewBag.txtCountry" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("省(直辖市)", "Province")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtProvince" name="txtProvince" value="@ViewBag.txtProvince" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("市(区县)", "City")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtCity" name="txtCity" value="@ViewBag.txtCity" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("单位(医院)", "Institution")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtInstitution" name="txtInstitution" value="@ViewBag.txtInstitution" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("单位级别", "Level of the institution")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtInstitutionLevel" name="txtInstitutionLevel" value="@ViewBag.txtInstitutionLevel" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <h6 class="form-section"></h6>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("干预措施", "Intervention")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtMeasure" name="txtMeasure" value="" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("干预措施代码", "Intervention code")</label>
                                        <div class="col-md-8">
                                            <input type="text" id="txtIntercode" name="txtIntercode" value="" class="form-control x-lager">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("获伦理委员会批准", "Approved by ethic committee")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listEthicalCommitteeSanction", ViewBag.listEthicalCommitteeSanctionDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row ">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("公开试验结果文件", "Calculated Results after the Study Completed public access")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listWhetherPublic", ViewBag.listWhetherPublicDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4">@UiFun.Lang("上传试验结果文件", "Statistical results after completion of the test file upload")</label>
                                        <div class="col-md-8">
                                            @Html.DropDownList("listIsUploadRF", ViewBag.listIsUploadRFDict as IEnumerable<SelectListItem>, "", new { @class = "input-medium form-control" })
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="control-label col-md-4"></label>
                                        <div class="col-md-8">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-md-offset-3 col-md-9">
                                            <input type="hidden" name="pageNo" id="pageNo" />
                                            <button type="submit" class="btn green">@UiFun.Lang("筛选结果", "Search Result")</button>
                                            <button id="btnMoreHide" type="button" class="btn yellow-crusta">@UiFun.Lang("隐藏高级查询", "Hide Options")</button>
                                            <button id="btnMore" type="button" class="btn yellow-crusta">@UiFun.Lang("高级查询", "More Options")</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6"> </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="portlet box">
                <div class="portlet-title">
                    <div class="caption font-green sbold">
                        @UiFun.Lang("数据检索", "Trial search")
                    </div>
                    <div class="actions">
                        @if (UiFun.IsLang("zh-CN"))
                        {
                            <span class="label label-sm label-info">共 @Model.TotalPages 页, @Model.TotalItems 条记录</span>
                        }
                        else
                        {
                            <span class="label label-sm label-info">Total @Model.TotalPages Pages, @Model.TotalItems Records</span>
                        }
                    </div>
                </div>
                <div class="portlet-body">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>
                                    @UiFun.Lang("历史版本", "Historical Versions")
                                </th>
                                <th>
                                    @UiFun.Lang("注册号", "Registration number")
                                </th>
                                <th>
                                    @UiFun.Lang("注册题目", "Public title")
                                </th>
                                <th>
                                    @UiFun.Lang("所在单位", "Affiliation")
                                </th>
                                <th>
                                    @UiFun.Lang("研究类型", "Type")
                                </th>
                                <th>
                                    @UiFun.Lang("注册时间", "RegDate")
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Items)
                            {

                                <tr>
                                    <td>
                                        @if (string.IsNullOrEmpty(item.secondaryID))
                                        {
                                            @Html.ActionLink(UiFun.Lang("历史版本", "Historical Versions"), "History", new { pid = item.pid }, new { target = "_blank" })
                                        }
                                        else
                                        {
                                            <a href="https://www.chictr.org.cn/historyversionpub.html?regno=@item.secondaryID" target="_blank">@UiFun.Lang("历史版本", "Historical Versions")</a>
                                        }
                                    </td>
                                    <td>
                                        @item.regNumber
                                    </td>
                                    <td>
                                        @if (item.IsEnglist)
                                        {
                                            @Html.ActionLink(item.publicTitleEN, "ProjectView", new { pid = item.pid })
                                        }
                                        else
                                        {
                                            if (!string.IsNullOrEmpty(UiFun.Lang(item.publicTitleCN, item.publicTitleEN)))
                                            {
                                                @Html.ActionLink(UiFun.Lang(item.publicTitleCN, item.publicTitleEN), "ProjectView", new { pid = item.pid })
                                            }
                                        }
                                    </td>
                                    <td>
                                        @if (item.IsEnglist)
                                        {
                                            @item.applierCompanyEN
                                        }
                                        else
                                        {
                                            @UiFun.Lang(item.applierCompanyCN, item.applierCompanyEN)
                                        }
                                    </td>
                                    <td>
                                        @if (item.IsEnglist)
                                        {
                                            @item.studyTypeNameEN
                                        }
                                        else
                                        {
                                            @UiFun.Lang(item.studyTypeNameCN, item.studyTypeNameEN)
                                        }
                                    </td>
                                    <td>
                                        @Html.FormatValue(item.regNumberTime, "{0:yyyy/MM/dd}")
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="dataTables_paginate paging_bootstrap_extended" id="sample_2_paginate">
                                <div class="pagination-panel">
                                    Page
                                    <input type="text" class="pagination-panel-input form-control input-sm input-inline input-mini" value="@(Model.CurrentPage + 1)" maxlenght="5" style="text-align:center; margin: 0 5px;">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-10 col-sm-12">
                            <div class="dataTables_paginate paging_bootstrap_number">
                                @Html.PageLinks(Model, x => Url.Action("TrialSearch", new { pageNo = x }), false)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
