@model ITMCTR.Core.Models.SA_News
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
    <div class="zhuce_nr">
        <div class="new_xq">
            @if (UiFun.IsLang("zh-CN"))
            {
			@*
			<div class="new_xq1">@Model.Title</div>
            <div class="new_xq2">@Model.Author  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;@Html.FormatValue(Model.ReleaseTime, "{0:yyyy年MM月dd日}")</div>
			*@
                <div class="new_xq3">@Html.Raw(Model.Content)</div>
            }
            else if (UiFun.IsLang("en-US"))
            {
				@*
                <div class="new_xq1">@Model.TitleEN</div>
                <div class="new_xq2">@Model.AuthorEN  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;@Html.FormatValue(Model.ReleaseTime, "{0:MM/dd/yyyy}")</div>
				*@
                <div class="new_xq3">@Html.Raw(Model.ContentEN)</div>
            }
        </div>
    </div>
