@model ITMCTR.App.Models.WebsiteInformationViewModel
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="zhuce_nr">
    @if (UiFun.IsLang("zh-CN"))
    {
        @*<div class="zhuce_nr_title"><font style="font-size:20px; font-weight:700; padding-right:20px;">内容详情</font> </div>*@
        <div class="new_xq">
            <div class="new_xq1">@Model.Title</div>
            <div class="new_xq1">@Model.Subtitle</div>
            <div class="new_xq2">@Model.Author  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;@Html.FormatValue(Model.ReleaseTime, "{0:yyyy年MM月dd日}")</div>
            <div class="new_xq3">@Html.Raw(Model.Content)</div>
        </div>
    }
    else if (UiFun.IsLang("en-US"))
    {
        @*<div class="zhuce_nr_title"><font style="font-size:20px; font-weight:700; padding-right:20px;">Detail</font> </div>*@
        <div class="new_xq">
            <div class="new_xq1">@Model.TitleEN</div>
            <div class="new_xq1">@Model.SubtitleEN</div>
            <div class="new_xq2">@Model.AuthorEN  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;@Html.FormatValue(Model.ReleaseTime, "{0:MM/dd/yyyy}")</div>
            <div class="new_xq3">@Html.Raw(Model.ContentEN)</div>
        </div>
    }
</div>
