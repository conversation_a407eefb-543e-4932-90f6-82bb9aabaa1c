@model ITMCTR.Core.Models.SA_News
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<div class="zhuce_nr">
    <div class="zhuce_nr_title"><font style="font-size:20px; font-weight:700; padding-right:20px;">@Resources.Global.View_新闻详情 </font> </div>
    <div class="new_xq">
        @if (UiFun.IsLang("zh-CN"))
        {
            <div class="new_xq1">@Model.Title</div>
            <div class="new_xq2">@Model.Subtitle  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; @string.Format("{0:yyyy-MM-dd}", Model.ReleaseTime)</div>
            <div class="new_xq3">@Html.Raw(Model.Content)</div>
        }
        else
        {
            <div class="new_xq1">@Model.TitleEN</div>
            <div class="new_xq2">@Model.SubtitleEN  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; @string.Format("{0:yyyy-MM-dd}", Model.ReleaseTime)</div>
            <div class="new_xq3">@Html.Raw(Model.ContentEN)</div>
        }
    </div>
</div>