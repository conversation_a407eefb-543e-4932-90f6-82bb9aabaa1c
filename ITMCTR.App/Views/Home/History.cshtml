@model List<ITMCTR.Core.Models.SA_NewProjectHistory>
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script>
        $(function () {
            if (jQuery().datepicker) {
                $('.date-picker').datepicker({
                    language: '@UiFun.Applang',
                    orientation: "left",
                    autoclose: true,
                    format: "yyyy-mm-dd",
                    todayBtn: "linked"
                });
            }
        })
    </script>
}
<div class="zhuce_nr">
    @using (Html.BeginForm("History", "Home", FormMethod.Get))
    {
        <div class="page-container">
            <div class="portlet box">
                <div class="portlet-title">
                    <div class="caption font-green sbold">
                        @UiFun.Lang("初始版本", "Original version")
                    </div>
                </div>
                <div class="portlet-body">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>
                                    @UiFun.Lang("操作", "Operate")
                                </th>
                                <th>
                                    @UiFun.Lang("版本", "Version")
                                </th>
                                <th>
                                    @UiFun.Lang("注册号", "Registration number")
                                </th>
                                <th>
                                    @UiFun.Lang("版本创建时间", "Create Time")
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td>
                                        @Html.ActionLink(UiFun.Lang("打开查看", "Open View"), "ProjectHView", new { Phid = item.Phid }, new { target = "_blank" })
                                    </td>
                                    <td>
                                        @item.version
                                    </td>
                                    <td>
                                        @item.regNumber
                                    </td>
                                    <td>
                                        @Html.FormatValue(item.operateTime, "{0:yyyy-MM-dd}")
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>
