@model ITMCTR.App.Models.UserProjectModel
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}
@section Css{
    <link href="~/Content/CssProject.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/jquery.validate.js"></script>
    <script>
        $(function () {
            var isEn ='@UiFun.IsLang("en-US")';
            $(".en").show();
            if (isEn=='True') {
                $(".heigthLef").height(0);
                $(".cn").hide();
            }
            else {
                $(".heigthLef").height(48);
                $(".cn").show();
            }
            if ($("#listLang").val() == "1009002") {
                $(".cn").empty();
            }
        })
    </script>
}
<div class="zhuce_nr">
    @using (Html.BeginForm("ProjectView", "Home", FormMethod.Post))
    {
        @Html.HiddenFor(x => x.listLang)
        <div class="page-container">
            <div class="portlet light">
                <div class="portlet-title">
                    <div class="caption font-green sbold">
                        @UiFun.Lang(Model.txtTitle, Model.txtTitleEn)
                    </div>
                </div>
                <div class="portlet-body form">
                    <div class="form-horizontal">
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                注册号：
                                            </p>
                                            <p class="en">
                                                Registration number：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtRegNumber
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                最近更新日期：
                                            </p>
                                            <p class="en">
                                                Date of Last Refreshed on：
                                            </p>
                                        </td>
                                        <td>
                                            @Html.FormatValue(Model.txtModifyTime, "{0:yyyy-MM-dd}")
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                注册时间：
                                            </p>
                                            <p class="en">
                                                Date of Registration：
                                            </p>
                                        </td>
                                        <td>
                                            @Html.FormatValue(Model.txtRegNumberTime, "{0:yyyy-MM-dd}")
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                注册号状态：
                                            </p>
                                            <p class="en">
                                                Registration Status：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.RegStatusCn
                                            </p>
                                            <p class="en">
                                                @Model.RegStatusEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                注册题目：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtTitle
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Public title：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtTitleEn
                                            </p>
                                        </td>
                                    </tr>

                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                注册题目简写：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtTitleAcronym
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                English Acronym：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtTitleAcronymEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究课题的正式科学名称：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtOfficialName
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Scientific title：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtOfficialNameEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究课题的正式科学名称简写：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtOfficialNameAcronym
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Scientific title acronym：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtOfficialNameAcronymEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究课题代号(代码)：
                                            </p>
                                            <p class="en">
                                                Study subject ID：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtSubjectID
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                在二级注册机构或其它机构的注册号：
                                            </p>
                                            <p class="en">
                                                The registration number of the Partner Registry or other register：
                                            </p>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(Model.txtSecondaryID))
                                            {
                                                @Html.ActionLink(Model.txtSecondaryID, "ProjectViewTo", new { pid = Model.pid }, new { target = "_blank" }); @Html.Raw(" ; ") @Model.ReleaseNumber
                                            }
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                申请注册联系人：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtApplier
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究负责人：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtStudyLeader
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Applicant：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtApplierEn
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Study leader：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtStudyLeaderEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                申请注册联系人电话：
                                            </p>
                                            <p class="en">
                                                Applicant telephone：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtApplierPhone
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究负责人电话：
                                            </p>
                                            <p class="en">
                                                Study leader's telephone：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtStudyLeaderPhone
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                申请注册联系人传真 ：
                                            </p>
                                            <p class="en">
                                                Applicant Fax：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtApplierFax
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究负责人传真：
                                            </p>
                                            <p class="en">
                                                Study leader's fax：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtStudyLeaderFax
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                申请注册联系人电子邮件：
                                            </p>
                                            <p class="en">
                                                Applicant E-mail：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtApplierEmail
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究负责人电子邮件：
                                            </p>
                                            <p class="en">
                                                Study leader's E-mail：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtStudyLeaderEmail
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                申请单位网址(自愿提供)：
                                            </p>
                                            <p class="en">
                                                Study leader's website(voluntary supply)：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtApplierWebsite
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究负责人网址(自愿提供)：
                                            </p>
                                            <p class="en">
                                                Study leader's website<br />
                                                (voluntary supply)：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtStudyLeaderWebsite
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                申请注册联系人通讯地址：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtApplierAddress
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究负责人通讯地址：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtStudyLeaderAddress
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Applicant address：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtApplierAddressEn
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Study leader's address：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtStudyLeaderAddressEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                申请注册联系人邮政编码：
                                            </p>
                                            <p class="en">
                                                Applicant postcode：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtApplierPostcode
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究负责人邮政编码：
                                            </p>
                                            <p class="en">
                                                Study leader's postcode：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtStudyLeaderPostcode
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                申请人所在单位：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtApplierCompany
                                            </p>
                                        </td>
                                        @*<td class="left_title control-label col-lg-3">
                                                <p class="cn">
                                                    研究负责人所在单位：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtStudyLeaderCompany
                                                </p>
                                            </td>*@
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Applicant's institution：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtApplierCompanyEn
                                            </p>
                                        </td>
                                        @*<td class="left_title control-label col-lg-3">
                                                <p class="en">
                                                    Affiliation of the Leader：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtStudyLeaderCompanyEn
                                                </p>
                                            </td>*@
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                是否获伦理委员会批准：
                                            </p>
                                            <p class="en">
                                                Approved by ethic committee：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <span id="listEthicalCommitteeSanction">
                                                @if (Model.listEthicalCommitteeSanction == 1)
                                                {
                                                    <p class="cn">
                                                        <label for="listEthicalCommitteeSanction_0">
                                                            是
                                                        </label>
                                                    </p>
                                                    <p class="en">
                                                        <label for="listEthicalCommitteeSanction_0">
                                                            Yes
                                                        </label>
                                                    </p>
                                                }
                                                else
                                                {
                                                    <p class="cn">
                                                        <label for="listEthicalCommitteeSanction_1">
                                                            否
                                                        </label>
                                                    </p>
                                                    <p class="en">
                                                        <label for="listEthicalCommitteeSanction_1">
                                                            No
                                                        </label>
                                                    </p>
                                                }
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                                @*@if (Model.listEthicalCommitteeSanction == 1){*@
                                <tbody id="tbodyEcs">
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                伦理委员会批件文号：
                                            </p>
                                            <p class="en">
                                                Approved No. of ethic committee：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtEthicalCommitteeFileID
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                伦理委员会批件附件：
                                            </p>
                                            <p class="en">
                                                Approved file of Ethical Committee：
                                            </p>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(Model.fileEthicalCommittee))
                                            {
                                                <p>
                                                    @UiFun.Lang("查看附件", "View")
                                                    @*@Html.ActionLink(UiFun.Lang("查看附件", "View"), "DownFile", new { path = Model.fileEthicalCommittee, pid = Model.pid }, new { target = "_blank", @class = "download" })*@
                                                </p>
                                            }
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                批准本研究的伦理委员会名称：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtEthicalCommitteeName
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Name of the ethic committee：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtEthicalCommitteeNameEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                伦理委员会批准日期：
                                            </p>
                                            <p class="en">
                                                Date of approved by ethic committee：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @Model.txtEthicalCommitteeSanctionDate
                                        </td>
                                    </tr>
                                    <!--联系人 -->
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                伦理委员会联系人：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtEthicalCommitteeCName
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Contact Name of the ethic committee：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtEthicalCommitteeCNameEN
                                            </p>
                                        </td>
                                    </tr>
                                    <!--联系地址 -->
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                伦理委员会联系地址：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtEthicalCommitteeCAddress
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Contact Address of the ethic committee：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtEthicalCommitteeCAddressEN
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <!-- 联系人电话  -->
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                伦理委员会联系人电话：
                                            </p>
                                            <p class="en">
                                                Contact phone of the ethic committee：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtEthicalCommitteeCPhone
                                        </td>

                                        <!-- 联系人邮箱  -->
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                伦理委员会联系人邮箱：
                                            </p>
                                            <p class="en">
                                                Contact email of the ethic committee：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtEthicalCommitteeCEmail
                                        </td>
                                    </tr>
                                </tbody>
                                @* } *@
                                <tbody class="hidden">
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                国家药监局批准文号：
                                            </p>
                                            <p class="en">
                                                Approved No. of MPA：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtNationalFDASanctionNO
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                国家药监局批准附件：
                                            </p>
                                            <p class="en">
                                                Approved file of MPA：
                                            </p>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(Model.fileNationalFDASanction))
                                            {
                                                <p>
                                                    @UiFun.Lang("查看附件", "View")
                                                    @*@Html.ActionLink(UiFun.Lang("查看附件", "View"), "DownFile", new { path = Model.fileNationalFDASanction, pid = Model.pid }, new { target = "_blank", @class = "download" })*@
                                                </p>
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                国家药监局批准日期：
                                            </p>
                                            <p class="en">
                                                Date of approved by MPA：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @Html.FormatValue(Model.txtNationalFDASanctionDate, "{0:yyyy-MM-dd}")
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究方案：
                                            </p>
                                            <p class="en">
                                                Study protocol：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @*@if (!string.IsNullOrEmpty(Model.fileStudyPlan))
                                                {
                                                    <p>
                                                        @Html.ActionLink(UiFun.Lang("查看附件", "View"), "DownFile", new { path = Model.fileStudyPlan, pid = Model.pid }, new { target = "_blank", @class = "download" })
                                                    </p>
                                                }*@
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                知情同意书：
                                            </p>
                                            <p class="en">
                                                Informed consent file：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @*@if (!string.IsNullOrEmpty(Model.fileInformedConsent))
                                                {
                                                    <p>
                                                        @Html.ActionLink(UiFun.Lang("查看附件", "View"), "DownFile", new { path = Model.fileInformedConsent, pid = Model.pid }, new { target = "_blank", @class = "download" })

                                                    </p>
                                                }*@
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究实施负责（组长）单位：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtSponsor
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Primary sponsor：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtSponsorEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究实施负责（组长）单位地址：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtSponsorAddress
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Primary sponsor's address：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtSponsorAddressEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                试验主办单位(项目批准或申办者)：
                                            </p>
                                            <p class="en">
                                                Secondary sponsor：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @Html.HiddenFor(x => x.hdnSecSponsorCount)
                                            <div class="tblist">
                                                @for (int i = 0; i < Model.SecondarySponsor.Count; i++)
                                                {
                                                    <table id="table_ss@(Model.SecondarySponsor[i].ssId)" class="col-md-12" cellspacing="0" cellpadding="0" border="0">
                                                        <tbody>
                                                            <tr>
                                                                <td class="t col-md-1">
                                                                    <p class="cn">
                                                                        国家：
                                                                    </p>
                                                                </td>
                                                                <td class="t col-md-2">
                                                                    <p id="pcn_country@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                        @Model.SecondarySponsor[i].countryCN
                                                                    </p>
                                                                </td>
                                                                <td class="t col-md-1">
                                                                    <p class="cn">
                                                                        省(直辖市)：
                                                                    </p>
                                                                </td>
                                                                <td class="t col-md-2">
                                                                    <p id="pcn_province@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                        @Model.SecondarySponsor[i].provinceCN
                                                                    </p>
                                                                </td>
                                                                <td class="t col-md-1">
                                                                    <p class="cn">
                                                                        市(区县)：
                                                                    </p>
                                                                </td>
                                                                <td class="t col-md-2">
                                                                    <p id="pcn_city@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                        @Model.SecondarySponsor[i].cityCN
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="t">
                                                                    <p class="en">
                                                                        Country：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p id="pen_country@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                        @Model.SecondarySponsor[i].countryEN
                                                                    </p>
                                                                </td>
                                                                <td class="t">
                                                                    <p class="en">
                                                                        Province：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p id="pen_province@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                        @Model.SecondarySponsor[i].provinceEN
                                                                    </p>
                                                                </td>
                                                                <td class="t">
                                                                    <p class="en">
                                                                        City：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p id="pen_city@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                        @Model.SecondarySponsor[i].cityEN
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="t">
                                                                    <p class="cn">
                                                                        单位(医院)：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p id="pcn_Institution@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                        @Model.SecondarySponsor[i].institutionCN
                                                                    </p>
                                                                </td>
                                                                <td class="t">
                                                                    <p class="cn">
                                                                        具体地址：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p id="pcn_Address@(Model.SecondarySponsor[i].ssId)" class="cn">
                                                                        @Model.SecondarySponsor[i].specificAddressCN
                                                                    </p>
                                                                </td>
                                                                <td colspan="2">
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="t">
                                                                    <p class="en">
                                                                        Institution<br>
                                                                        hospital：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p id="pen_Institution@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                        @Model.SecondarySponsor[i].institutionEN
                                                                    </p>
                                                                </td>
                                                                <td class="t">
                                                                    <p class="en">
                                                                        Address：
                                                                    </p>
                                                                </td>
                                                                <td colspan="2">
                                                                    <p id="pen_Address@(Model.SecondarySponsor[i].ssId)" class="en">
                                                                        @Model.SecondarySponsor[i].specificAddressEN
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                经费或物资来源：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtSourceOfSpends
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Source(s) of funding：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtSourceOfSpendsEn
                                            </p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究疾病：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtStudyAilment
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究疾病代码：
                                            </p>
                                        </td>
                                        <td rowspan="2">
                                            @Model.txtStudyAilmentCode
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Target disease：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtStudyAilmentEn
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Target disease code：
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究类型：
                                            </p>
                                            <p class="en">
                                                Study type：
                                            </p>
                                        </td>
                                        <!--id="listStudyType"-->
                                        <td>
                                            <p class="cn">
                                                @Model.StudyTypeCn
                                            </p>
                                            <p class="en">
                                                @Model.StudyTypeEn
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究设计：
                                            </p>
                                            <p class="en">
                                                Study design：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.StudyDesignCn
                                            </p>
                                            <p class="en">
                                                @Model.StudyDesignEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究所处阶段：
                                            </p>
                                            <p class="en">
                                                Study phase：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.StudyPhaseCn
                                            </p>
                                            <p class="en">
                                                @Model.StudyPhaseEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究目的：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtStudyAim
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Objectives of Study：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtStudyAimEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                药物成份或治疗方案详述：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtDrugsComposition
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Description for medicine or protocol of treatment in detail：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtDrugsCompositionEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                纳入标准：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtSelectionCriteria
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Inclusion criteria
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtSelectionCriteriaEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                排除标准：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtEliminateCriteria
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Exclusion criteria：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="en">
                                                @Model.txtEliminateCriteriaEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究实施时间：
                                            </p>
                                            <p class="en">
                                                Study execute time：
                                            </p>
                                        </td>
                                        <td>
                                            <p>
                                                <span class="cn">从</span><span class="en">From</span>
                                                @Html.FormatValue(Model.txtStudyExecuteTime, "{0:yyyy-MM-dd}")
                                            </p>
                                            <p>
                                                <span class="cn">至</span><span class="en">To</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                @Html.FormatValue(Model.txtStudyEndTime, "{0:yyyy-MM-dd}")
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                征募观察对象时间：
                                            </p>
                                            <p class="en">
                                                Recruiting time：
                                            </p>
                                        </td>
                                        <td>
                                            <p>
                                                <span class="cn">从</span><span class="en">From</span>
                                                @Html.FormatValue(Model.txtEnlistBeginTime, "{0:yyyy-MM-dd}")
                                            </p>
                                            <p>
                                                <span class="cn">至</span><span class="en">To</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                @Html.FormatValue(Model.txtEnlistEndTime, "{0:yyyy-MM-dd}")
                                            </p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        @if (Model.listStudyType != "1001003")
                        {
                            <div class="ProjetInfo_ms form-group" id="divInter">
                                <div class="tblist">
                                    <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                        <tbody>
                                            <tr>
                                                <td rowspan="2" class="left_title  col-lg-3">
                                                    <p class="cn">
                                                        干预措施：
                                                    </p>
                                                    <p class="en">
                                                        Interventions：
                                                    </p>
                                                </td>
                                                <td>
                                                    <div class="tblist" id="tabInter">
                                                        @for (int i = 0; i < Model.Interventions.Count; i++)
                                                        {

                                                            <table id="table_inter@(Model.Interventions[i].inId)" class="col-md-12" cellspacing="0" cellpadding="0" border="0">
                                                                <tbody>
                                                                    <tr>
                                                                        <td class="t col-md-2">
                                                                            <p class="cn">
                                                                                组别：
                                                                            </p>
                                                                        </td>
                                                                        <td class="col-md-4">
                                                                            <p class="cn">
                                                                                @Model.Interventions[i].groupsCN
                                                                            </p>
                                                                        </td>
                                                                        <td class="t col-md-2">
                                                                            <p class="cn">
                                                                                样本量：
                                                                            </p>
                                                                        </td>
                                                                        <td rowspan="2" class="col-md-4">
                                                                            @Model.Interventions[i].sampleSize
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <p class="en">
                                                                                Group：
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <p class="en">
                                                                                @Model.Interventions[i].groupsEN
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <p class="en">
                                                                                Sample size：
                                                                            </p>
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>
                                                                            <p class="cn">
                                                                                干预措施：
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <p class="cn">
                                                                                @Model.Interventions[i].interventionCN
                                                                            </p>
                                                                        </td>
                                                                        <td>
                                                                            <p class="cn">
                                                                                干预措施代码：
                                                                            </p>
                                                                        </td>
                                                                        <td rowspan="2">
                                                                            @Model.Interventions[i].interventionCode
                                                                        </td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td class="sub">
                                                                            <p class="en">
                                                                                Intervention：
                                                                            </p>
                                                                        </td>
                                                                        <td class="sub">
                                                                            <p class="en">
                                                                                @Model.Interventions[i].interventionEN
                                                                            </p>
                                                                        </td>
                                                                        <td class="sub">
                                                                            <p class="en">
                                                                                Intervention code：
                                                                            </p>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        }
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td colspan="3">
                                                    <span class="cn">样本总量</span><span class="en">
                                                        Total sample
                                                        size
                                                    </span>：
                                                    @Model.txtTotalSampleSize
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        }
                        @if (Model.listStudyType == "1001003")
                        {
                            <div class="ProjetInfo_ms form-group" id="divDiagnostic">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td rowspan="2" class="left_title control-label col-lg-3">
                                                <p class="cn">
                                                    诊断试验：
                                                </p>
                                                <p class="en">
                                                    Diagnostic Tests：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <table class="col-md-12" cellspacing="0" cellpadding="0" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                <p class="cn">
                                                                    金标准或参考标准（即可准确诊断某疾病的单项方法或多项联合方法，在本研究中用于诊断是否有该病的临床参考标准）：
                                                                </p>
                                                            </td>
                                                            <td colspan="3">
                                                                <p class="cn">
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().standard
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="en">
                                                                    Gold Standard or Reference Standard (The clinical reference standards required to establish the presence or absence of the target condition in the tested population in present study):
                                                                </p>
                                                            </td>
                                                            <td colspan="3">
                                                                <p class="en">
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().standardEn
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="cn">
                                                                    指标试验（即本研究的待评估诊断试验，无论为方法、生物标志物或设备，均请列出名称）：
                                                                </p>
                                                            </td>
                                                            <td colspan="3">
                                                                <p class="cn">
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().indexTest
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="en">
                                                                    Index test:
                                                                </p>
                                                            </td>
                                                            <td colspan="3">
                                                                <p class="en">
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().indexTestEn
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="cn">
                                                                    目标人群（可以是某种疾病患者或正常人群，详细描述其疾病特征，注意应纳入符合分布特点的全序列病例，具有良好的代表性）
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="cn">
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().targetCondition
                                                                    }
                                                                </p>
                                                            </td>
                                                            <td rowspan="2" class="col-md-3">
                                                                <p class="cn">
                                                                    例数:
                                                                </p>
                                                                <p class="en">
                                                                    Sample size:
                                                                </p>

                                                                <p>
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().sampleSizeT
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="en">
                                                                    Target condition (The target condition is a particular disease or disease stage that the index test will be intended to identify. Please specify the characteristics in detail; the population should has a complete spectrum and good representative):
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="en">
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().targetConditionEn
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="cn">
                                                                    容易混淆的疾病人群（即与目标疾病不易区分的一种或多种不同疾病，应避免采用正常人群对照的病例-对照设计）：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="cn">
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().difficultCondition
                                                                    }
                                                                </p>
                                                            </td>
                                                            <td rowspan="2" class="col-md-3">
                                                                <p class="cn">
                                                                    例数:
                                                                </p>
                                                                <p class="en">
                                                                    Sample size:
                                                                </p>

                                                                <p>
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().sampleSizeD
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="en">
                                                                    Population with condition difficult to distinguish from the target condition, the normal population in a case-control study design should be avoid:
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="en">
                                                                    @if (Model.Diagnostic.Count > 0)
                                                                    {
                                                                        @Model.Diagnostic.FirstOrDefault().difficultConditionEn
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        }
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究实施地点：
                                            </p>
                                            <p class="en">
                                                Countries of recruitment
                                                <br />
                                                and research settings：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @Html.HiddenFor(x => x.hdnPlaceCount)
                                            @for (int i = 0; i < Model.ResearchAddress.Count; i++)
                                            {
                                                <table id="table_ra@(Model.ResearchAddress[i].raId)" class="col-md-12" cellspacing="0" cellpadding="0" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="t col-md-1">
                                                                <p class="cn">
                                                                    国家：
                                                                </p>
                                                            </td>
                                                            <td class="col-md-3">
                                                                <p id="pcn_country_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                    @Model.ResearchAddress[i].countryCN
                                                                </p>
                                                            </td>
                                                            <td class="t col-md-1">
                                                                <p class="cn">
                                                                    省(直辖市)：
                                                                </p>
                                                            </td>
                                                            <td class="col-md-3">
                                                                <p id="pcn_province_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                    @Model.ResearchAddress[i].provinceCN
                                                                </p>
                                                            </td>
                                                            <td class="t col-md-1">
                                                                <p class="cn">
                                                                    市(区县)：
                                                                </p>
                                                            </td>
                                                            <td class="col-md-3">
                                                                <p id="pcn_city_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                    @Model.ResearchAddress[i].cityCN
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="t">
                                                                <p class="en">
                                                                    Country：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_country_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                    @Model.ResearchAddress[i].countryEN
                                                                </p>
                                                            </td>
                                                            <td class="t">
                                                                <p class="en">
                                                                    Province：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_province_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                    @Model.ResearchAddress[i].provinceEN
                                                                </p>
                                                            </td>
                                                            <td class="t">
                                                                <p class="en">
                                                                    City：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_city_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                    @Model.ResearchAddress[i].cityEN
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="t">
                                                                <p class="cn">
                                                                    单位(医院)：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pcn_hospital_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                    @Model.ResearchAddress[i].hospitalCN
                                                                </p>
                                                            </td>
                                                            <td class="t">
                                                                <p class="cn">
                                                                    单位级别：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pcn_institution_ra@(Model.ResearchAddress[i].raId)" class="cn">
                                                                    @Model.ResearchAddress[i].levelInstitutionCN
                                                                </p>
                                                            </td>
                                                            <td colspan="2">
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="t">
                                                                <p class="en">
                                                                    Institution/hospital：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_hospital_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                    @Model.ResearchAddress[i].hospitalEN
                                                                </p>
                                                            </td>
                                                            <td class="t">
                                                                <p class="en">
                                                                    Level of the institution：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_institution_ra@(Model.ResearchAddress[i].raId)" class="en">
                                                                    @Model.ResearchAddress[i].levelInstitutionEN
                                                                </p>
                                                            </td>
                                                            <td colspan="2">
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            }
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                测量指标：
                                            </p>
                                            <p class="en">
                                                Outcomes：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @Html.HiddenFor(x => x.hdnIndexCount)
                                            @for (int i = 0; i < Model.Outcomes.Count; i++)
                                            {
                                                <table id="table_out@(Model.Outcomes[i].ouId)" class="col-md-12" cellspacing="0" cellpadding="0" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="t col-md-2">
                                                                <input type="hidden" value="@(Model.Outcomes[i].ouId)" id="txtOuid@(i)" name="txtOuid">
                                                                <p class="cn ">
                                                                    指标中文名：
                                                                </p>
                                                            </td>
                                                            <td class="t col-md-4">
                                                                <p class="cn" id="pcn_name@(Model.Outcomes[i].ouId)">
                                                                    @Model.Outcomes[i].outcomeNameCN
                                                                </p>
                                                            </td>
                                                            <td class="t col-md-2">
                                                                <p class="cn">
                                                                    指标类型：
                                                                </p>
                                                            </td>
                                                            <td class="t col-md-4">
                                                                <p class="cn" id="pcn_type@(Model.Outcomes[i].ouId)">
                                                                    @switch (Model.Outcomes[i].pointerType)
                                                                    {
                                                                        case "4002001":@("主要指标"); break;
                                                                        case "4002002": @("次要指标"); break;
                                                                        case "4002003": @("附加指标"); break;
                                                                        case "4002004": @("副作用指标"); break;
                                                                        default:
                                                                            break;
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="t">
                                                                <p class="en">
                                                                    Outcome：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="en" id="pen_name@(Model.Outcomes[i].ouId)">
                                                                    @Model.Outcomes[i].outcomeNameEN
                                                                </p>
                                                            </td>
                                                            <td class="t">
                                                                <p class="en">
                                                                    Type：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="en" id="pen_type@(Model.Outcomes[i].ouId)">
                                                                    @switch (Model.Outcomes[i].pointerType)
                                                                    {
                                                                        case "4002001":@("Primary indicator"); break;
                                                                        case "4002002": @("Secondary indicator"); break;
                                                                        case "4002003": @("Additional indicator"); break;
                                                                        case "4002004": @("Adverse events"); break;
                                                                        default:
                                                                            break;
                                                                    }
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="t">
                                                                <p class="cn">
                                                                    测量时间点：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="cn" id="pcn_time@(Model.Outcomes[i].ouId)">
                                                                    @Model.Outcomes[i].measureTimeCN
                                                                </p>
                                                            </td>
                                                            <td class="t">
                                                                <p class="cn">
                                                                    测量方法：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="cn" id="pcn_method@(Model.Outcomes[i].ouId)">
                                                                    @Model.Outcomes[i].measureMethodCN
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td class="t">
                                                                <p class="en">
                                                                    Measure time point of outcome：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="en" id="pen_time@(Model.Outcomes[i].ouId)">
                                                                    @Model.Outcomes[i].measureTimeEN
                                                                </p>
                                                            </td>
                                                            <td class="t">
                                                                <p class="en">
                                                                    Measure method：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="en" id="pen_method@(Model.Outcomes[i].ouId)">
                                                                    @Model.Outcomes[i].measureMethodEN
                                                                </p>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            }
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                采集人体标本：
                                            </p>
                                            <p class="en">
                                                Collecting sample(s)
                                                <br />
                                                from participants：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @Html.HiddenFor(x => x.hdnSpecimenCount)
                                            @for (int i = 0; i < Model.CollectingSample.Count; i++)
                                            {
                                                <table id="table_cs@(Model.CollectingSample[i].csId)" class="col-md-12" cellspacing="0" cellpadding="0" border="0">
                                                    <tbody>
                                                        <tr>
                                                            <td class="t col-md-2">
                                                                <input type="hidden" style="display: none" value="@(Model.CollectingSample[i].csId)" id="txtCSid_@(i)" name="txtcsid">
                                                                <p class="cn">
                                                                    标本中文名：
                                                                </p>
                                                            </td>
                                                            <td class="col-md-4">
                                                                <p id="pcn_SaName@(Model.CollectingSample[i].csId)" class="cn">
                                                                    @Model.CollectingSample[i].sampleNameCN
                                                                </p>
                                                            </td>
                                                            <td class="t col-md-2">
                                                                <p class="cn">
                                                                    组织：
                                                                </p>
                                                            </td>
                                                            <td class="col-md-4">
                                                                <p id="pcn_tissue@(Model.CollectingSample[i].csId)" class="cn">
                                                                    @Model.CollectingSample[i].tissueCN
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="en">
                                                                    Sample Name：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_SaName@(Model.CollectingSample[i].csId)" class="en">
                                                                    @Model.CollectingSample[i].sampleNameEN
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="en">
                                                                    Tissue：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_tissue@(Model.CollectingSample[i].csId)" class="en">
                                                                    @Model.CollectingSample[i].tissueEN
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="cn">
                                                                    人体标本去向
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <input type="hidden" id="fate@(Model.CollectingSample[i].csId)" value="@Model.CollectingSample[i].fateSample">
                                                                <p id="pcn_fate@(Model.CollectingSample[i].csId)" class="cn">
                                                                    @switch (Model.CollectingSample[i].fateSample)
                                                                    {
                                                                        case "1012001":@("使用后销毁"); break;
                                                                        case "1012002":@("使用后保存"); break;
                                                                        case "1012003":@("其它"); break;
                                                                        default:
                                                                            break;
                                                                    }

                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="cn">
                                                                    说明
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pcn_note@(Model.CollectingSample[i].csId)" class="cn">
                                                                    @Model.CollectingSample[i].noteCN
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <p class="en">
                                                                    Fate of sample&nbsp;
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_fate@(Model.CollectingSample[i].csId)" class="en">
                                                                    @switch (Model.CollectingSample[i].fateSample)
                                                                    {
                                                                        case "1012001":@("Destruction after use"); break;
                                                                        case "1012002":@("Preservation after use"); break;
                                                                        case "1012003":@("Others"); break;
                                                                        default:
                                                                            break;
                                                                    }
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p class="en">
                                                                    Note：
                                                                </p>
                                                            </td>
                                                            <td>
                                                                <p id="pen_note@(Model.CollectingSample[i].csId)" class="en">
                                                                    @Model.CollectingSample[i].noteEN
                                                                </p>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            }
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr>
                                        <td class="left_title control-label heigthLef">
                                            <p class="cn">
                                                征募研究对象情况：
                                            </p>
                                        </td>
                                        <td rowspan="2">
                                            <p class="cn">
                                                @Model.RecruitmentStatusCn
                                            </p>
                                            <p class="en">
                                                @Model.RecruitmentStatusEn
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                年龄范围：
                                            </p>
                                        </td>
                                        <td rowspan="2">
                                            <table>
                                                <tbody>
                                                    <tr>
                                                        <td>
                                                            <span class="cn">最小</span>
                                                        </td>
                                                        <td rowspan="2">
                                                            @Model.txtMinAge
                                                        </td>
                                                        <td>
                                                            <span class="cn">岁</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <span class="en">Min age</span>
                                                        </td>
                                                        <td>
                                                            <span class="en">years</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <span class="cn">最大</span>
                                                        </td>
                                                        <td rowspan="2">
                                                            @Model.txtMaxAge
                                                        </td>
                                                        <td>
                                                            <span class="cn">岁</span>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <span class="en">Max age</span>
                                                        </td>
                                                        <td>
                                                            <span class="en">years</span>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Recruiting status：
                                            </p>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Participant age：
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                性别：
                                            </p>
                                            <p class="en">
                                                Gender：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.GenderCn
                                            </p>
                                            <p class="en">
                                                @Model.GenderEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                随机方法（请说明由何人用什么方法产生随机序列）：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            <p class="cn">
                                                @Model.txtGenerafionMethod
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Randomization Procedure (please state who generates the random number sequence and
                                                by what method)：
                                            </p>
                                        </td>
                                        <td colspan="3">
                                            @Model.txtGenerafionMethodEn
                                        </td>
                                    </tr>
                                    <tr class="hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究对象是否签署知情同意书：
                                            </p>
                                            <p class="en">
                                                Sign the informed consent：
                                            </p>
                                        </td>
                                        <td>
                                            <span id="listAgreeToSign">
                                                @if (Model.listAgreeToSign == 1)
                                                {
                                                    <p class="cn">
                                                        <label for="listAgreeToSign_0">
                                                            是
                                                        </label>
                                                    </p>
                                                    <p class="en">
                                                        <label for="listAgreeToSign_0">
                                                            Yes
                                                        </label>
                                                    </p>
                                                }
                                                else
                                                {
                                                    <p class="cn" for="listAgreeToSign_1">
                                                        否：
                                                    </p>
                                                    <p class="en" for="listAgreeToSign_1">
                                                        No
                                                    </p>
                                                }
                                            </span>
                                        </td>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                随访时间：
                                            </p>
                                            <p class="en">
                                                Length of follow-up (include time point of outcome measure)：
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtFollowUpFrequency
                                            <p class="cn">
                                                @Model.FollowUpTimeUnitCn
                                            </p>
                                            <p class="en">
                                                @Model.FollowUpTimeUnitEn
                                            </p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr class="cn hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                隐蔽分组方法和过程：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtConcealment
                                            </p>
                                            <p class="cn">
                                                @*<span class="help-block">请描述您采用的隐蔽分组方法和过程</span>*@
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Process of allocation
                                                <br />
                                                concealment：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtConcealmentEn
                                            </p>
                                            <p class="en">
                                                @*<span class="help-block">Please describe the process of allocation concealment you will use.</span>*@
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                盲法：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtBlinding
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Blinding：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtBlindingEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                揭盲或破盲原则和方法：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtUncoverPrinciple
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Rules of uncover or
                                                <br />
                                                ceasing blinding：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtUncoverPrincipleEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                统计方法名称：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtStatisticalMethod
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Statistical method：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtStatisticalMethodEn
                                            </p>
                                        </td>
                                    </tr>
                                    @if (Model.listStatisticalEffectChiCTRPublic == 1)
                                    {
                                        <tr class="cn publicshow hidden">
                                            <td class="left_title control-label col-lg-3">
                                                <p class="cn">
                                                    试验完成后的统计结果（上传文件）：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Model.txtStatisticalEffect
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en publicshow hidden">
                                            <td class="left_title control-label col-lg-3">
                                                <p class="en">
                                                    Calculated Results ater
                                                    <br />
                                                    the Study Completed：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Model.txtStatisticalEffectEn
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="publicshow hidden">
                                            <td class="left_title control-label col-lg-3">
                                                <p class="cn">
                                                    上传试验完成后的统计结果：
                                                </p>
                                                <p class="en">
                                                    Statistical results after completion of the test file upload
                                                </p>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.fileExperimentalresults))
                                                {
                                                    <p>
                                                        @UiFun.Lang("查看附件", "View")
                                                        @*@Html.ActionLink(UiFun.Lang("查看附件", "View"), "DownFile", new { path = Model.fileExperimentalresults, pid = Model.pid }, new { target = "_blank", @class = "download" })*@

                                                    </p>
                                                }
                                            </td>
                                        </tr>
                                    }
                                    <tr class="hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                是否公开试验完成后的统计结果：
                                            </p>
                                            <p class="en">
                                                Calculated Results after
                                                <br>
                                                the Study Completed(upload file)：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.StatisticalEffectChiCTRPublicCn
                                            </p>
                                            <p class="en">
                                                @Model.StatisticalEffectChiCTRPublicEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="hidden">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                全球唯一识别码：
                                            </p>
                                            <p class="en">
                                                UTN
                                            </p>
                                        </td>
                                        <td>
                                            @Model.txtUTN
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="ProjetInfo_ms form-group">
                            <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                是否共享原始数据：
                                            </p>
                                            <p class="en">
                                                IPD sharing：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.DataCollectionUnitCn
                                            </p>
                                            <p class="en">
                                                @Model.DataCollectionUnitEn
                                            </p>
                                        </td>
                                    </tr>

                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                共享原始数据的方式（说明：请填入公开原始数据日期和方式，如采用网络平台，需填该网络平台名称和网址）：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtDataChargeUnit
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                The way of sharing IPD”(include metadata and protocol, If use web-based public database, please provide the url)：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtDataChargeUnitEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                数据采集和管理（说明：数据采集和管理由两部分组成，一为病例记录表(Case Record Form, CRF)，二为电子采集和管理系统(Electronic Data Capture, EDC)，如ResMan即为一种基于互联网的EDC：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtDataAnalysisUnit
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Data collection and Management (A standard data collection and management system include a CRF and an electronic data capture：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtDataAnalysisUnitEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                数据管理委员会：
                                            </p>
                                            <p class="en">
                                                Data Managemen Committee：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.DataManagemenBoardCn
                                            </p>
                                            <p class="en">
                                                @Model.DataManagemenBoardEn
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="cn">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="cn">
                                                研究计划书或研究结果报告发表信息<br />（杂志名称、期、卷、页，时间；或网址）：
                                            </p>
                                        </td>
                                        <td>
                                            <p class="cn">
                                                @Model.txtStudyReport
                                            </p>
                                        </td>
                                    </tr>
                                    <tr class="en">
                                        <td class="left_title control-label col-lg-3">
                                            <p class="en">
                                                Publication information of the protocol/research results report<br />(name of the journal, volume, issue, pages, time; or website):
                                            </p>
                                        </td>
                                        <td>
                                            <p class="en">
                                                @Model.txtStudyReportEN
                                            </p>
                                        </td>
                                    </tr>
                                    <!-- end replace -->
                                </tbody>
                            </table>
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-md-offset-3 col-md-9">
                                            <a class="btn green" href="@Url.Action("TrialSearch","Home")">@UiFun.Lang("返回", "back")</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6"> </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>