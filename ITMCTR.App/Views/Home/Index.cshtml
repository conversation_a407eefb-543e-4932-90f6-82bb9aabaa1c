@{
    ViewBag.Title = "Home Page 中国中医药临床注册平台";
    Layout = null;
}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>@Resources.Global.APP_NAME_网站名称</title>
    <link href="~/Content/css/style.css?v=@UiFun.AppVersion" rel="stylesheet" type="text/css" />
</head>

<body>
    <div class="top">
        <div class="top1">
            <div class="top2">
                <div class="top3"><img src="~/Content/images/logo.png" width="165" height="54" /></div>
                <div class="top4">
                    <a href="@Url.Action("Register", "UserPlatform")">@Resources.Global.QT_INDEX_我要注册</a><div class="top5"><img src="~/Content/images/top02.png" width="30" height="30" /></div>

                    <a href="@Url.Action("Login", "UserPlatform")">@Resources.Global.QT_INDEX_立即登录</a> <div class="top5"><img src="~/Content/images/top01.png" width="26" height="30" /></div>
                </div>
            </div>
            <div class="dh">
                <div class="dh_left"><img src="~/Content/images/title.png" width="243" height="44" style="padding-top:10px;" /></div>
                <div class="dh_right">
                    <div class="index_en">
                        <a href="@Url.Action("Index",  new { lang="zh-CN"})" style=" padding:0 10px; box-sizing:border-box;font-size:18px;">中文</a>/<a href="@Url.Action("Index",  new { lang="en-US"})" style=" padding: 0 10px; box-sizing: border-box; font-size: 18px;">English</a>
                    </div>
                    <div class="dh_right_a">
                        <a href="@Url.Action("InformationCode",new { code="one"})" class="dh_right_aa">@Resources.Global.QT_INDEX_平台介绍</a>|
                        <a href="@Url.Action("InformationCode",new { code="two"})" class="dh_right_aa">@Resources.Global.QT_INDEX_关于我们</a>|
                        <a href="@Url.Action("NewsList")" class="dh_right_aa">@Resources.Global.QT_INDEX_新闻</a>
                    </div>
                </div>
            </div>

            <div style="padding:25px 0; padding-top:60px;">
                <font style="font-size:31px;">@Resources.Global.APP_NAME_网站名称</font><br />
                <font style="font-size:13px; color:#d3d5ff;"></font>
            </div>
            <div>
                @Html.Raw(Resources.Global.QT_INDEX_平台详介)
            </div>
            <div style=" width:550px; display:flex; margin:0px auto; text-align:center;">
                <div style="margin-right:30px; margin-top:70px;">
                    <a href="@Url.Action("TrialSearch","Home")" style="width: 250px; text-decoration: none;">
                        <input type="button" value="@Resources.Global.QT_INDEX_我要检索" class="top_but1" style="cursor:pointer; margin:0;" />
                    </a>
                </div>
                <div style="margin-top:70px;">
                    <a href="@Url.Action("Login","UserPlatform")" style="text-decoration: none;">
                        <input type="button" value="@Resources.Global.QT_INDEX_试验注册" class="top_but2" style="cursor:pointer; margin:0;" />
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="nr_center">
        <div class="nr_center1">
            <div class="nr_center2 slh2">
                @Resources.Global.QT_INDEX_中医特色1<br />
                @Resources.Global.QT_INDEX_中医特色2<br />
                @Resources.Global.QT_INDEX_中医特色3
            </div>
            @Html.Action("NewListBlock")
        </div>
    </div>
    <div style="width:100%; height:1px; border-bottom:1px #d2d2d2 solid;"></div>

    <div class="lj">
        <li><a href="https://www.who.int/" target="_blank"> <img src="~/Content/images/lj.png" width="245" height="79" /></a></li>
        <li><a href="https://g-i-n.net/" target="_blank"><img src="~/Content/images/lj1.png" width="245" height="79" /></a></li>
        <li><a href="https://www.cochrane.org/" target="_blank"><img src="~/Content/images/lj2.png" width="245" height="79" /></a></li>

    </div>
    @Html.Partial("_Footer")
	<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?a47045d4bb7d6a85b755ca6d9a384c50";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();
</script>

</body>
</html>
