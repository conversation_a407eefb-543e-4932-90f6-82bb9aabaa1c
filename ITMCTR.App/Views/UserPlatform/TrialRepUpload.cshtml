@model PetaPoco.Page<ITMCTR.App.Models.ProjectRepUploadViewModel>
@{
    Layout = "~/Views/Shared/_U_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
    <script src="~/Content/assets/pages/scripts/form-validation.js"></script>
    <script>

    </script>
}

@using (Html.BeginForm("TrialRepUpload", "UserPlatform", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>
                报告上传/Upload Reports
            </span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">报告上传UploadReport</span>
                        </div>
                        <div class="actions">
                            <a href="@Url.Action("TrialRepUploadAdd", "UserPlatform" )" data-target="#modal-ajax" data-toggle="modal" class="btn default yellow-stripe">
                                <i class="fa fa-plus text"></i>
                                <span class="text">上传报告Upload</span>
                            </a>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        操作
                                        <br />
                                        Operate
                                    </th>
                                    <th>注册号<br />Registration number</th>
                                    <th>项目简介<br />Project Abstract</th>
                                    <th>文件标题<br />Filename</th>
                                    <th>创建时间<br />Time Created</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                    <tr>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.FilePath))
                                            {
                                                <a target="_blank" href="@item.FilePath" class="btn download">查看附件/View</a>
                                            }
                                            <a class="btn" href="@Url.Action("TrialRepUploadUpdate", "UserPlatform" ,new { PruId = item.PruId})" data-target="#modal-ajax" data-toggle="modal">
                                                重新上传/Upload
                                            </a>
                                        </td>
                                        <td>
                                            @item.regNumber
                                        </td>
                                        <td>
                                            @item.publicTitleCN<br />
                                            @item.publicTitleEN
                                        </td>
                                        <td>@item.ReportTitle</td>
                                        <td>@item.CreateTime</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("TrialRepUpload", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->

}