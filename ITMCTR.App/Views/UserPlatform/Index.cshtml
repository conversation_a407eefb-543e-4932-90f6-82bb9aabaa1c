
@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_U_Layout.cshtml";
}
@section Css{
    <style>
        .slh1 {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box !important;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
        }
    </style>
}

<!-- BEGIN PAGE BREADCRUMBS -->
<ul class="page-breadcrumb breadcrumb">
    <li>
        <a href="@Url.Action("Index")">首页/Home</a>
    </li>
</ul>
<!-- END PAGE BREADCRUMBS -->
<!-- B<PERSON>IN PAGE CONTENT INNER -->
<div class="page-content-inner">
    <div class="row widget-row">
        <div class="col-md-4">
            <!-- BEGIN WIDGET THUMB -->
            <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 ">
                <h4 class="widget-thumb-heading">
                    待审核/To be Reviewed
                </h4>
                <div class="widget-thumb-wrap">
                    <i class="widget-thumb-icon bg-green icon-bulb"></i>
                    <div class="widget-thumb-body">
                        <span class="widget-thumb-subtitle">项目数/Number of Projects</span>
                        <span class="widget-thumb-body-stat" data-counter="counterup" data-value="7,644">@ViewBag.proToBeReview</span>
                    </div>
                </div>
            </div>
            <!-- END WIDGET THUMB -->
        </div>

        <div class="col-md-4">
            <!-- BEGIN WIDGET THUMB -->
            <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 ">
                <h4 class="widget-thumb-heading">已通过审核/Approved</h4>
                <div class="widget-thumb-wrap">
                    <i class="widget-thumb-icon bg-purple icon-screen-desktop"></i>
                    <div class="widget-thumb-body">
                        <span class="widget-thumb-subtitle">项目数/Number of Projects</span>
                        <span class="widget-thumb-body-stat" data-counter="counterup" data-value="815">@ViewBag.proSuccess</span>
                    </div>
                </div>
            </div>
            <!-- END WIDGET THUMB -->
        </div>
        <div class="col-md-4">
            <!-- BEGIN WIDGET THUMB -->
            <div class="widget-thumb widget-bg-color-white text-uppercase margin-bottom-20 ">
                <h4 class="widget-thumb-heading">项目总计/Toal Number</h4>
                <div class="widget-thumb-wrap">
                    <i class="widget-thumb-icon bg-blue icon-bar-chart"></i>
                    <div class="widget-thumb-body">
                        <span class="widget-thumb-subtitle">项目数/Number of Projects</span>
                        <span class="widget-thumb-body-stat" data-counter="counterup" data-value="5,071">@ViewBag.proSum</span>
                    </div>
                </div>
            </div>
            <!-- END WIDGET THUMB -->
        </div>
    </div>
    <div class="row">
        <div class="col-md-6 col-sm-6">
            <!-- BEGIN PORTLET-->
            <div class="portlet light ">
                <div class="portlet-title tabbable-line">
                    <div class="caption">
                        <i class="icon-globe font-dark hide"></i>
                        <span class="caption-subject font-dark bold uppercase">站内消息/Messages</span>
                    </div>
                    <ul class="nav nav-tabs">
                        <li class="active">
                            <a href="#tab_1_1" class="active" data-toggle="tab">未读/Unread</a>
                        </li>
                        <li>
                            <a href="#tab_1_2" data-toggle="tab">已读/Read</a>
                        </li>
                    </ul>
                </div>
                <div class="portlet-body">
                    <!--BEGIN TABS-->
                    <div class="tab-content">
                        <div class="tab-pane active" id="tab_1_1">
                            <div class="scroller" style="height: 339px;" data-always-visible="1" data-rail-visible="0">
                                <ul class="feeds NoReadList">
                                    @foreach (var item in ViewBag.NoReadList)
                                    {
                                        <li>
                                            <a href="@Url.Action("MessageAction","UserPlatform",new {Nid=item.Nid })" target="_blank">
                                                <div title="@item.Title" style="cursor: pointer; color: #23527C;">
                                                    <div class="col1" style="padding-right:5px;">
                                                        <div class="cont">
                                                            <div class="cont-col1">
                                                                <div class="label label-sm label-danger">
                                                                    <i class="fa fa-bell-o"></i>
                                                                </div>
                                                            </div>
                                                            <div class="cont-col2">
                                                                <div class="desc slh1" style="padding-bottom: 0px;">@(System.Threading.Thread.CurrentThread.CurrentUICulture.Name== "en-US"? item.TitleEN : item.Title)</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col2" style="width:100px; margin-left:-100px;">
                                                        <div class="date" style="padding: 4px 4px 4px 0;"><span>@Html.Raw(item.CreateTime.ToString("yyyy-MM-dd"))</span></div>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    }

                                </ul>
                            </div>
                        </div>
                        <div class="tab-pane" id="tab_1_2">
                            <div class="scroller" style="height: 290px;" data-always-visible="1" data-rail-visible1="1">
                                <ul class="feeds IsReadList">
                                    @foreach (var item in ViewBag.IsReadList)
                                    {
                                        <li>
                                            <a href="@Url.Action("MessageAction","UserPlatform",new {Nid=item.Nid })" target="_blank">
                                                <div title="@item.Title" style="cursor: pointer; color: #23527C;">
                                                    <div class="col1" style="padding-right:5px;">
                                                        <div class="cont">
                                                            <div class="cont-col1">
                                                                <div class="label label-sm label-success">
                                                                    <i class="fa fa-bell-o"></i>
                                                                </div>
                                                            </div>
                                                            <div class="cont-col2">
                                                                <div class="desc slh1" style="padding-bottom: 0px;">@(System.Threading.Thread.CurrentThread.CurrentUICulture.Name== "en-US"? item.TitleEN : item.Title)</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col2" style="width:100px; margin-left:-100px;">
                                                        <div class="date" style="padding: 4px 4px 4px 0;"><span>@Html.Raw(item.CreateTime.ToString("yyyy-MM-dd"))</span></div>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </div>
                        </div>
                    </div>
                    <!--END TABS-->
                </div>
            </div>
            <!-- END PORTLET-->
        </div>
        <div class="col-md-6 col-sm-6">
            <!-- BEGIN PORTLET-->
            <div class="portlet light ">
                <div class="portlet-title tabbable-line">
                    <div class="caption">
                        <i class="icon-globe font-dark hide"></i>
                        <span class="caption-subject font-dark bold uppercase">新闻/News</span>
                    </div>
                    <ul class="nav nav-tabs">
                        <li class="active">
                            <a href="#tab_1_1" class="active" data-toggle="tab">平台发布/System</a>
                        </li>
                    </ul>
                </div>
                <div class="portlet-body">
                    <!--BEGIN TABS-->
                    <div class="tab-content">
                        <div class="tab-pane active" id="tab_1_1">
                            <div class="scroller" style="height: 339px;" data-always-visible="1" data-rail-visible="0">
                                <ul class="feeds">
                                    @foreach (var item in ViewBag.News)
                                    {
                                        <li>
                                            <a href="@Url.Action("NewsInfo","UserPlatform",new { Nid=item.Nid })" title="@item.Title">
                                                <div class="col1" style="padding-right:5px;">
                                                    <div class="cont">
                                                        <div class="cont-col1">
                                                            <div class="label label-sm label-success">
                                                                <i class="fa fa-bullhorn"></i>
                                                            </div>
                                                        </div>
                                                        <div class="cont-col2">
                                                            <div class="desc slh1" style="padding-bottom: 0px;">@item.Title</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col2" style="width:100px; margin-left:-100px;">
                                                    <div class="date" style="padding: 4px 4px 4px 0;"><span>
    @Html.FormatValue(item.ReleaseTime, "{0:yyyy/MM/dd}"))
</span></div>
                                                </div>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </div>
                        </div>
                        <div class="tab-pane" id="tab_1_2">
                            <div class="scroller" style="height: 290px;" data-always-visible="1" data-rail-visible1="1">
                                <ul class="feeds">
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New order received </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> 10 mins </div>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <div class="col1">
                                            <div class="cont">
                                                <div class="cont-col1">
                                                    <div class="label label-sm label-danger">
                                                        <i class="fa fa-bolt"></i>
                                                    </div>
                                                </div>
                                                <div class="cont-col2">
                                                    <div class="desc">
                                                        Order #24DOP4 has been rejected.
                                                        <span class="label label-sm label-danger ">
                                                            Take action
                                                            <i class="fa fa-share"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col2">
                                            <div class="date"> 24 mins </div>
                                        </div>
                                    </li>
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                    <li>
                                        <a href="javascript:;">
                                            <div class="col1">
                                                <div class="cont">
                                                    <div class="cont-col1">
                                                        <div class="label label-sm label-success">
                                                            <i class="fa fa-bell-o"></i>
                                                        </div>
                                                    </div>
                                                    <div class="cont-col2">
                                                        <div class="desc"> New user registered </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col2">
                                                <div class="date"> Just now </div>
                                            </div>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <!--END TABS-->
                </div>
            </div>
            <!-- END PORTLET-->
        </div>
    </div>
</div>
<!-- END PAGE CONTENT INNER -->
@section Scripts{
    <script>
        LoadNotice()
        window.setInterval(function () {
            LoadNotice();
        }, 5000)
    function SetNoticeRead(Nids) {
        $.ajax({
            type: "POST",
            dataType: "json",
            url: "@Url.Action("SetNoticeRead")",
            data: { Nid: Nids},
            success: function (data) {
                //Success=true, url = model.AccessUrl
                if (data.Success) {
                    if (data.url != "")
                        window.location.href = data.url;
                    else
                        window.location.href = '@Url.Action("Notices","UserPlatform")?Nid=' + Nids;
                }
                else {
                    alert("站内信跳转失败");
                }
            }
        });
        }
        function LoadNotice() {
            var name = "";
            if (document.cookie.length > 0) {
                var cookies = document.cookie.split(";");
                for (var i = 0; i < cookies.length; i++) {
                    var items = cookies[i].trim().split("=");
                    if (items[0] == "Localization.CurrentUICulture" || items[0] == " Localization.CurrentUICulture") {
                        name = items[1];
                        break;
                    }
                }
            }
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("ajaxLoadNotice")",
                success: function (data) {
                    if (data.Success) {
                        //未读
                        $(".NoReadList").empty();
                        var strInner = "";
                        for (var i = 0; i < data.NoReadList.length; i++) {
                            strInner += "<li>";
                            strInner += "<a href=\"/UserPlatform/MessageAction?Nid=" + data.NoReadList[i].Nid + "&Type=" + data.NoReadList[i].Type +"\" target=\"_blank\">";
                            if (name == "en-US") {
                                strInner += "<div title=\"" + data.NoReadList[i].TitleEN + "\" style=\"cursor: pointer; color: #23527C;\">";
                            }
                            else {
                                strInner += "<div title=\"" + data.NoReadList[i].Title + "\" style=\"cursor: pointer; color: #23527C;\">";
                            }
                            strInner += "<div class=\"col1\" style=\"padding-right: 5px;\">";
                            strInner += "<div class=\"cont\">";
                            strInner += "<div class=\"cont-col1\">";
                            strInner += "<div class=\"label label-sm label-danger\">";
                            strInner += "<i class=\"fa fa-bell-o\"></i>";
                            strInner += "</div>";
                            strInner += "</div>";
                            strInner += "<div class=\"cont-col2\">";
                            if (name == "en-US") {
                                strInner += "<div class=\"desc slh1\" style=\"padding-bottom: 0px;\">" + data.NoReadList[i].TitleEN + "</div>";
                            }
                            else {
                                strInner += "<div class=\"desc slh1\" style=\"padding-bottom: 0px;\">" + data.NoReadList[i].Title + "</div>";
                            }
                            strInner += "</div></div></div>";
                            strInner += "<div class=\"col2\" style=\"width:100px; margin-left:-100px;\">";
                            strInner += "<div class=\"date\" style=\"padding: 4px 4px 4px 0;\"><span>"+data.NoReadList[i].CreateTime+"</span></div>";
                            strInner += "</div></div></a></li>";
                        }
                        $(".NoReadList").append(strInner);

                        $(".IsReadList").empty();
                        strInner = "";
                        for (var i = 0; i < data.IsReadList.length; i++) {
                            strInner += "<li>";
                            strInner += "<a href=\"/UserPlatform/MessageAction?Nid=" + data.IsReadList[i].Nid + "&Type=" + data.IsReadList[i].Type +"\" target=\"_blank\">";
                            if (name == "en-US") {
                                strInner += "<div title=\"" + data.IsReadList[i].TitleEN + "\" style=\"cursor: pointer; color: #23527C;\">";
                            }
                            else {
                                strInner += "<div title=\"" + data.IsReadList[i].Title + "\" style=\"cursor: pointer; color: #23527C;\">";
                            }
                            strInner += "<div class=\"col1\" style=\"padding-right: 5px;\">";
                            strInner += "<div class=\"cont\">";
                            strInner += "<div class=\"cont-col1\">";
                            strInner += "<div class=\"label label-sm label-success\">";
                            strInner += "<i class=\"fa fa-bell-o\"></i>";
                            strInner += "</div>";
                            strInner += "</div>";
                            strInner += "<div class=\"cont-col2\">";
                            if (name == "en-US") {
                                strInner += "<div class=\"desc slh1\" style=\"padding-bottom: 0px;\">" + data.IsReadList[i].TitleEN + "</div>";
                            }
                            else {
                                strInner += "<div class=\"desc slh1\" style=\"padding-bottom: 0px;\">" + data.IsReadList[i].Title + "</div>";
                            }
                            strInner += "</div></div></div>";
                            strInner += "<div class=\"col2\" style=\"width:100px; margin-left:-100px;\">";
                            strInner += "<div class=\"date\" style=\"padding: 4px 4px 4px 0;\"><span>" + data.IsReadList[i].CreateTime + "</span></div>";
                            strInner += "</div></div></a></li>";
                        }
                        $(".IsReadList").append(strInner);
                    }
                }
            });
        }
    </script>
}

