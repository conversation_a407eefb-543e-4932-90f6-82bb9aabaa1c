@model PetaPoco.Page<ITMCTR.App.Models.UserProjectViewModel>
@{
    ViewBag.Title = "ProjectList";
    Layout = "~/Views/Shared/_U_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
    <script src="~/Content/assets/pages/scripts/form-validation.js"></script>
}
@using (Html.BeginForm("ModifiedReviewed", "UserPlatform", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>待审核项目/To be Reviewed</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="m-heading-1 border-green m-bordered">
            <h3 class="form-section">
                筛选条件/Filter Options
            </h3>
            <div class="horizontal-form">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>
                                注册题目/Public title
                            </label>
                            <input type="text" id="txtTitle" name="txtTitle" value="@ViewBag.txtTitle" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>
                                正式科学名/Scientific title
                            </label>
                            <input type="text" id="txtOfficialName" name="txtOfficialName" value="@ViewBag.txtOfficialName" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>
                                研究课题代号(代码)/Subject ID
                            </label>
                            <input type="text" id="txtSubjectID" name="txtSubjectID" value="@ViewBag.txtSubjectID" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>
                                注册号/Registration number
                            </label>
                            <input type="text" id="txtRegNo" name="txtRegNo" value="@ViewBag.txtRegNo" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">

                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">

                        </div>
                    </div>
                </div>
                <div class="form-actions right">
                    <button type="submit" class="btn blue">
                        <i class="fa fa-check"></i> 查询
                    </button>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">待审核项目/To be Reviewed</span>

                        </div>
                        <div class="actions">
                            <div class="btn-group btn-group-devided" data-toggle="buttons">
                            </div>
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>
                                        操作
                                        <br />
                                        Operate
                                    </th>
                                    <th>
                                        注册号<br />Registration number
                                    </th>
                                    <th>
                                        注册题目<br />Public title
                                    </th>
                                    @*<th>
            研究阶段<br />Phase
        </th>
        <th>
            完成情况<br />Level
        </th>*@
                                    <th>
                                        审核状态<br />Review Status
                                    </th>
                                    <th>
                                        首次提交时间<br />First Submission
                                    </th>
                                    <th>
                                        一审人员<br />Send User
                                    </th>
                                    <th>
                                        二审人员<br />Execute User
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {

                                    <tr>
                                        <td>
                                            @if (UserFun.IsEdit(item))
                                            {
                                                <a class="btn default btn-xs purple-seance" href="@Url.Action("ProjectEdit", new { pid = item.pid })">
                                                    <i class="fa fa-edit"></i> 编辑
                                                </a>
                                            }
                                            @if (UserFun.IsReEdit(item))
                                            {
                                                <a class="btn default btn-xs purple-plum" href="@Url.Action("ProjectEdit", new { pid = item.pid })">
                                                    <i class="fa fa-edit"></i> 重新编辑
                                                </a>
                                            }
                                            @if (UserFun.IsRequestEdit(item))
                                            {
                                                <a href="@Url.Action("RequestEditFlow", new { pid = item.pid })" data-target="#modal-ajax" data-toggle="modal" class="btn default btn-xs green-haze">
                                                    <i class="fa fa-reply text"></i>
                                                    <span class="text">申请修改</span>
                                                </a>
                                            }
                                        </td>
                                        <td>
                                            @item.regNumber
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.publicTitleCN) && item.IsChinese)
                                            {
                                                @Html.ActionLink(item.publicTitleCN, "ProjectView", new { pid = item.pid })
                                                <br />
                                            }
                                            @if (!string.IsNullOrEmpty(item.publicTitleEN))
                                            {
                                                @Html.ActionLink(item.publicTitleEN, "ProjectView", new { pid = item.pid })
                                                <br />
                                            }
                                            @if (!string.IsNullOrEmpty(item.applierCompanyEN) && item.IsEnglist)
                                            {
                                                @item.applierCompanyEN
                                            }
                                            else if (!string.IsNullOrEmpty(item.applierCompanyCN) && item.IsChinese)
                                            {
                                                @item.applierCompanyCN
                                            }
                                        </td>
                                        @*<td>
            @item.studyPhaseNameCN
            <br />
            @item.studyPhaseNameEN
        </td>
        <td>
            @item.recruitingStatusNameCN
            <br />
            @item.recruitingStatusNameEN
        </td>*@
                                    <td>
                                        @switch (item.status)
                                        {
                                            case 0:
                                                <span class="label label-sm label-warning">
                                                    @item.statusCn
                                                    <br />
                                                    @item.statusEn
                                                </span>
                                                break;
                                            case 1:
                                                <span class="label label-sm label-info">
                                                    @item.statusCn
                                                    <br />
                                                    @item.statusEn
                                                </span>
                                                break;
                                            case 3:
                                                if (string.IsNullOrWhiteSpace(item.regNumber))
                                                {
                                                    <span class="label label-sm label-info">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="label label-sm label-success">
                                                        @item.statusCn
                                                        <br />
                                                        @item.statusEn
                                                    </span>
                                                }
                                                break;
                                            case 2:
                                                <span class="label label-sm label-danger">
                                                    @item.statusCn
                                                    <br />
                                                    @item.statusEn
                                                </span>
                                                break;
                                            default:
                                                break;
                                        }
                                    </td>
                                        <td>
                                            @Html.FormatValue(item.regTime, "{0:yyyy/MM/dd}")
                                        </td>
                                        <td>
                                            @item.sendTaskUser
                                        </td>
                                        <td>
                                            @item.executeTaskUser
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("ModifiedReviewed", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
}