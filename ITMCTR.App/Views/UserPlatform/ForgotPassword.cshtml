
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="http://fonts.googleapis.com/css?family=Open+Sans:400,300,600,700&subset=all" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->
    <!-- BEGIN THEME GLOBAL STYLES -->
    <link href="~/Content/assets/global/css/components.min.css?v=@UiFun.AppVersion" rel="stylesheet" id="style_components" type="text/css" />
    <link href="~/Content/assets/global/css/plugins.min.css?v=@UiFun.AppVersion" rel="stylesheet" type="text/css" />
    <!-- END THEME GLOBAL STYLES -->
    <!-- BEGIN THEME LAYOUT STYLES -->
    <link href="~/Content/assets/layouts/layout3/css/layout.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/layouts/layout3/css/themes/default.min.css" rel="stylesheet" type="text/css" id="style_color" />
    <link href="~/Content/assets/layouts/layout3/css/custom.min.css" rel="stylesheet" type="text/css" />

    <link href="~/Content/css/style.css?v=@UiFun.AppVersion" rel="stylesheet" />
    <script src="~/Content/assets/global/plugins/jquery.min.js" type="text/javascript"></script>
    <script src="~/Scripts/jquery.validate.min.js"></script>
    <link href="~/Scripts/plug/link.css" rel="stylesheet" />
    <link href="~/Scripts/plug/layui.css" rel="stylesheet" />
    <script src="~/Content/assets/global/plugins/bootstrap-modal/js/bootstrap-modal.js"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-modal/js/bootstrap-modalmanager.js"></script>
    <script src="~/Content/assets/pages/scripts/ui-extended-modals.js?v=@UiFun.AppVersion" type="text/javascript"></script>
</head>
<body>
    @using (Html.BeginForm("ForgotPassword", "UserPlatform", FormMethod.Post, new { id = "ForgotPasswordForm" }))
    {
        <input type="hidden" id="hidEmail" />
        <input type="hidden" id="hidUid" />
        <div id="divAlertModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false" style="z-index:10052;">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title"></h4>
                    </div>
                    <div class="modal-body">
                        <p id="pcontent"></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
                    </div>
                </div>
            </div>
        </div>
        @Html.Partial("_Header")
        <div class="zhuce_nr">
            <div class="zhuce_nr_title"><font style="font-size:20px; font-weight:700; padding-right:20px;">找回密码</font></div>
            <div class="zhuce_content" style="height:350px;">
                <div style="height:100%;">
                    <div class="rebinding-box">
                        <div class="box-timeline">
                            <ul class="text-center" style="width: 816px;">
                                <li>
                                    输入账号
                                    <div class="box-num1">
                                        1
                                    </div>
                                </li>
                                <li class="ml45">
                                    邮箱验证
                                    <div class="box-outside1 outside1ab" id="outside1abs">
                                        <div class="box-num2 num2ab">
                                            2
                                        </div>
                                    </div>
                                </li>
                                <li class="ml45">
                                    填写新密码
                                    <div class="box-outside2 outside2ab" id="outside2as">
                                        <div class="box-num3 num3ab">
                                            3
                                        </div>
                                    </div>
                                </li>
                                <li class="ml45">
                                    完成
                                    <div class="box-outside3 outside3a" id="outside3as">
                                        <div class="box-num4 num4a">
                                            4
                                        </div>
                                    </div>
                                </li>
                            </ul>


                        </div>
                        <!--第一步-->
                        <div class="onebox-form" id="oneform">
                            <div class="oneform">
                                <div class="oneform-box">
                                    <label class="oneform-label">登录账号</label>
                                    <div class="oneform-input">
                                        <input id="userName" autocomplete="off" placeholder="请填写要找回密码的帐号">
                                    </div>
                                </div>
                                <div class="onebtn-box">
                                    <button class="onebtn" type="button" id="onebtn" onclick="fun()">下一步</button>
                                </div>
                            </div>
                        </div>
                        <!--第二步-->
                        <div class="twobox-form" id="twoform">
                            <div class="twoform">
                                <div class="twoform-box">
                                    <div class="newtel">
                                        <label class="twoform-label">邮箱</label>
                                        <div class="twoform-input">
                                            <input type="text" id="email" readonly="readonly" placeholder="请输入邮箱地址">
                                        </div>
                                    </div>
                                    <div class="validatecode">
                                        <label class="twoform-label2">验证码</label>
                                        <div class="twoform-input2">
                                            <input type="text" autocomplete="off" id="rndNum" maxlength="6" placeholder="请输入验证码">
                                        </div>
                                        <button class="sendcode" type="button" id="sendRndnum">发送验证码</button>
                                        <div class="sendtimer">
                                            &nbsp;&nbsp;&nbsp;重新获取<span id="timer1"></span>s
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="twobtn-box">
                                <button class="twobtn" type="button" id="twobtn" onclick="fun1()">下一步</button>
                            </div>
                        </div>
                        <!--第三步-->
                        <div class="threebox-form twobox-form" id="threeform">
                            <div class="threeform">
                                <div class="threeform-box">
                                    <div class="newtel">
                                        <label class="threeform-label">新密码</label>
                                        <div class="threeform-input3">
                                            <input type="password" id="pwd" autocomplete="off" placeholder="请输入密码">
                                        </div>
                                    </div>
                                    <div class="validatecode">
                                        <label class="threeform-label3">确认密码</label>
                                        <div class="threeform-input3">
                                            <input type="password" autocomplete="off" id="repwd" placeholder="请确认输入密码">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="threebtn-box">
                                <button class="threebtn" type="button" id="threebtn" onclick="fun2()">下一步</button>
                            </div>
                        </div>
                        <!--第四步-->
                        <div class="fourbox-form" id="fourform">
                            <div class="successr">
                                <div class="symbol">

                                </div>
                                <p>恭喜您，修改密码成功！</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="login_footer1">@Resources.Global.APP_TIP_浏览器兼容</div>
    }

    <script src="~/Scripts/plug/layui.js"></script>
    <script>
        var onebtns = document.getElementById("onebtn");
        var twobtns = document.getElementById("twobtn");
        var soutside1ab = document.getElementById("outside1abs");
        var soutside2as = document.getElementById("outside2as");
        var soutside3as = document.getElementById("outside3as");
        var oneforms = document.getElementById("oneform");
        var twoforms = document.getElementById("twoform");
        var threeforms = document.getElementById("threeform");
        var fourforms = document.getElementById("fourform");

        function showalert(btn, title, conent, url) {
            $("#divAlertModal").modal();
            var $modal = $('#divAlertModal');
            $modal.find(".modal-title").text(title);//标题
            $modal.find("#pcontent").text(conent);//内容
            $modal.find("#btnConfirm").data("url", url);//Url
            $modal.find("#btnConfirm").text(btn);//按钮文字

            $modal.on('click', '.update', function () {
                if (url && url.length > 0) {
                    window.location.href = url;
                }
            });
            $modal.modal();
        }

        var timer1 = 60;
        //倒计时
        function TimeDown() {
            $("#timer1").html(timer1);

            if (timer1 > 0) {
                setTimeout(function () { TimeDown(); }, 1000);
            } else if (timer1 <= 0) {
                timer1 = 61;
                $("#sendRndnum").css("display", "block");
                $(".sendtimer").css("display", "none");
            }
            --timer1;
        };


        $("#sendRndnum").click(function () {
            var Uid = $("#hidUid").val();
            var email = $("#hidEmail").val();
            if (email == "" || email == null) {
                showalert("确定", "提示", "未绑定邮箱", "")
                return false;
            }
            $.ajax({
                type: "GET",
                dataType: "json",
                url: "@Url.Action("SendForgotPwdMail", "UserPlatform")",
                data: { Uid: Uid, Email: email},
                success: function (data) {
                    if (!data.succ) {
                        showalert("确定", "提示", "邮件发送失败", "");
                    }
                    else {
                        $("#sendRndnum").css("display", "none");
                        $(".sendtimer").css("display", "block");
                        TimeDown();
                    }
                }
            });
        });

        ///验证账号
        function fun() {
            var userName = $("#userName").val();
            if (userName == "") {
                showalert("确定", "提示", "请输入要找回密码的账户", "")
                return false;
            }
            $.ajax({
                type: "GET",
                dataType: "json",
                url: "@Url.Action("CheckUserName", "UserPlatform")",
                data: { Account: userName},
                success: function (data) {
                    if (!data.succ) {
                        showalert("确定", "提示", "账号不存在", "")
                        return false;
                    }
                    else {
                        $("#email").val(data.asteriskEmail);
                        $("#hidEmail").val(data.Email);
                        $("#hidUid").val(data.Uid);
                        soutside1ab.classList.remove("outside1ab");
                        oneforms.style.display = "none";
                        twoforms.style.display = "block";
                    }
                }
            });

        }
        //验证邮箱验证码
        function fun1() {
            var Uid = $("#hidUid").val();
            var rndNum = $("#rndNum").val();
            if (rndNum == "") {
                showalert("确定", "提示", "请输入验证码", "");
                return false;
            }
            $.ajax({
                type: "GET",
                dataType: "json",
                url: "@Url.Action("CheckVerificationCode", "UserPlatform")",
                data: { Uid: Uid, Code: rndNum},
                success: function (data) {
                    if (!data.succ) {
                        showalert("确定", "提示", data.message, "");
                    }
                    else {
                        threeforms.style.display = "block";
                        twoforms.style.display = "none";
                        soutside2as.classList.remove("outside2ab");
                    }
                }
            });
        }
        //修改密码
        function fun2() {
            var Uid = $("#hidUid").val();
            var pwd = $("#pwd").val();
            var repwd = $("#repwd").val();
            if (pwd != repwd && pwd != "" && repwd != "") {
                alert("2次密码不一致", { icon: 5 });
                return false;
            }
            $.ajax({
                type: "GET",
                dataType: "json",
                url: "@Url.Action("ModifyUserPassword", "UserPlatform")",
                data: { Uid: Uid, Pwd: pwd, RePwd: repwd},
                success: function (data) {
                    if (!data.succ) {
                        showalert("确定", "提示", data.message, "");
                    }
                    else {
                        fourforms.style.display = "block";
                        threeforms.style.display = "none";
                        soutside3as.classList.remove("outside3a");
                    }
                }
            });
            

        }
    </script>
</body>
</html>
