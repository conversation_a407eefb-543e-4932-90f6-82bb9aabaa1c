@model ITMCTR.App.Models.RegisterModel
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title></title>
    <!-- BEGIN GLOBAL MANDATORY STYLES -->
    <link href="http://fonts.googleapis.com/css?family=Open+Sans:400,300,600,700&subset=all" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-switch/css/bootstrap-switch.min.css" rel="stylesheet" type="text/css" />
    <!-- END GLOBAL MANDATORY STYLES -->
    <!-- BEGIN THEME GLOBAL STYLES -->
    <link href="~/Content/assets/global/css/components.min.css?v=@UiFun.AppVersion" rel="stylesheet" id="style_components" type="text/css" />
    <link href="~/Content/assets/global/css/plugins.min.css?v=@UiFun.AppVersion" rel="stylesheet" type="text/css" />
    <!-- END THEME GLOBAL STYLES -->
    <!-- BEGIN THEME LAYOUT STYLES -->
    <link href="~/Content/assets/layouts/layout3/css/layout.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/layouts/layout3/css/themes/default.min.css" rel="stylesheet" type="text/css" id="style_color" />
    <link href="~/Content/assets/layouts/layout3/css/custom.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/jquery-notific8/jquery.notific8.min.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/bootstrap-toastr/toastr.css" rel="stylesheet" />

    <link href="~/Content/css/style.css?v=@UiFun.AppVersion" rel="stylesheet" />
    <script src="~/Scripts/jquery-3.4.1.min.js"></script>
    <script src="~/Scripts/jquery.validate.min.js"></script>

</head>
<body>
    @using (Html.BeginForm("Register", "UserPlatform", FormMethod.Post, new { id = "registerForm" }))
    {
        @Html.Partial("_Header")
        <div class="zhuce_nr">
            <div class="zhuce_nr_title"><font style="font-size:20px; font-weight:700; padding-right:20px;">@Resources.Global.UR_REG_我要注册</font> @Resources.Global.UR_REG_已有帐号， <font class="zhuce_nr_title1"><a href="@Url.Action("Login", "UserPlatform")">@Resources.Global.QT_INDEX_立即登录</a></font></div>
            <div class="zhuce_content">
                @{
                    var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_用户名</div>
                    <div class="zhuce_content1_right">
                        @Html.TextBoxFor(x => x.Username, new { @class = "form-control help-inline", maxlength = "30", required = "required", placeholder = Resources.Global.UR_REG_用户名 })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_用户密码</div>
                    <div class="zhuce_content1_right">
                        @Html.PasswordFor(x => x.Password, new { @class = "form-control help-inline", maxlength = "20", required = "required", placeholder = Resources.Global.UR_REG_用户密码 })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_确认密码</div>
                    <div class="zhuce_content1_right">
                        @Html.PasswordFor(x => x.RePassword, new { @class = "form-control help-inline", maxlength = "20", equalTo = "#Password", required = "required", placeholder = Resources.Global.UR_REG_确认密码 })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_电子邮件</div>
                    <div class="zhuce_content1_right">
                        @Html.TextBoxFor(x => x.Email, new { @class = "form-control help-inline", maxlength = "100", required = "required", email = "true", placeholder = @Resources.Global.UR_REG_电子邮件 })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_您的姓名</div>
                    <div class="zhuce_content1_right">
                        @Html.TextBoxFor(x => x.Name, new { @class = "form-control help-inline", maxlength = "200", required = "required", placeholder = Resources.Global.UR_REG_您的姓名 })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_性别</div>
                    <div class="zhuce_content1_right">
                        @Html.DropDownListFor(m => m.Sex, ViewBag.SexList as IEnumerable<SelectListItem>, Resources.Global.UR_REG_请选择, new { @class = "input-large form-control help-inline", required = "required" })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_国家</div>
                    <div class="zhuce_content1_right">
                        @Html.DropDownListFor(m => m.Country, ViewBag.CountryList as IEnumerable<SelectListItem>, Resources.Global.UR_REG_请选择, new { @class = "input-large form-control help-inline", required = "required" })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_注册单位名称</div>
                    <div class="zhuce_content1_right">
                        @Html.TextBoxFor(x => x.RegUnit, new { @class = "form-control help-inline", maxlength = "200", required = "required", placeholder = Resources.Global.UR_REG_请输入 })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_联系地址</div>
                    <div class="zhuce_content1_right">
                        @Html.TextBoxFor(x => x.RegAddress, new { @class = "form-control help-inline", maxlength = "400", required = "required", placeholder = Resources.Global.UR_REG_请输入 })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_手机号码</div>
                    <div class="zhuce_content1_right">
                        @Html.TextBoxFor(x => x.CellPhone, new { @class = "form-control help-inline", maxlength = "20", required = "required", placeholder = Resources.Global.UR_REG_请输入 })
                    </div>
                </div>
                <div class="zhuce_content1">
                    <div class="zhuce_content1_left">@Resources.Global.UR_REG_固定电话</div>
                    <div class="zhuce_content1_right">
                        @Html.TextBoxFor(x => x.Phone, new { @class = "form-control help-inline", maxlength = "20", required = "required", placeholder = Resources.Global.UR_REG_请输入 })
                    </div>
                </div>
                <div style="width:260px; margin:0px auto; padding:50px 0;">
                    <input type="submit" name="ButtonSave" class="zhuce_content_but" value="@Resources.Global.UR_REG_立即注册" />
                </div>
            </div>
        </div>
        
        <div class="login_footer1">@Resources.Global.APP_TIP_浏览器兼容</div>
        if (UiFun.IsLang("zh-CN"))
        {
            <script src="~/Content/register.js"></script>
            <script src="~/Content/assets/global/plugins/jquery-validation/js/localization/messages_zh.min.js"></script>
        }
        else
        {
            <script src="~/Content/register_en.js"></script>
        }
    }
</body>
</html>
