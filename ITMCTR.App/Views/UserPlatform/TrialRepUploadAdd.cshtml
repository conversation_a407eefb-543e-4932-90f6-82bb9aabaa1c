@model ITMCTR.Core.Models.SA_ProjectReportUpload
@{
    Layout = "~/Views/Shared/_M_ModalLayout.cshtml";
    ViewBag.ModalTitle = "报告上传/Upload Reports";
}
@using (Html.BeginForm("TrialRepUploadAdd", "UserPlatform", FormMethod.Post, new { @id = "UploadForm" }))
{
    <div class="modal-body">
        <div class="form-body form-horizontal">
            <div class="alert alert-danger display-hide" id="divdanger">
                <button class="close" data-close="alert"></button>
                输入内容错误，input error。
            </div>
            <div class="alert alert-success display-hide" id="divsuccess">
                <button class="close" data-close="alert"></button>
                表单验证成功！Form Validation Succ!
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label">选择项目/Select the Project</label>
                <div class="col-md-9">
                    @Html.DropDownListFor(x => x.Pid, ViewBag.projectlist as SelectList, new { @class = "form-control input-large" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    报告标题/Report Title
                </label>
                <div class="col-md-8">
                    @Html.TextBoxFor(x => x.ReportTitle, new { @class = "form-control input-xlarge", maxlength = "100", required = "required" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    报告备注/Report Notes
                </label>
                <div class="col-md-8">
                    @Html.TextAreaFor(x => x.ReportDesc, 10, 5, new { @class = "form-control input-xlarge", maxlength = "500", required = "required" })
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">上传附件/Upload File</label>
                <div class="col-md-9">
                    <div id="container">
                        <a id="selectfiles" href="javascript:void(0);" class='btn green'>选择文件/Select the File</a>
                        <input type="button" id="btnUpload" name="btnUpload" class="btn yellow" value="上传文件/Upload File" />
                        <br />
                        <div class="progress progress-striped active">
                            <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">
                                <span class="sr-only">
                                    40% Complete (success)
                                </span>
                            </div>
                        </div>
                    </div>
                    <div id="ossfile">你的浏览器不支持flash,Silverlight或者HTML5！Must Supply Flash,Silverligt or HTML5</div>
                </div>
            </div>
            <div class="note note-danger note-bordered">
                <p>
                    每个试验只能上传一个报告文件，如有多个报告文件，请打包后上传。
                    <br />
                    Upload a single report file for each study. If there are multiple reports, please generate a zip file and upload it.
                </p>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        @Html.HiddenFor(x => x.FilePath)
        <input type="submit" id="btnSave" disabled="disabled" name="btnSave" class="btn green" data-success-dismiss="true" value="确定保存/Save" />
        <button type="button" class="btn default" data-dismiss="modal">关闭/Exit</button>
    </div>
}
@section Css{
    <link href="~/Content/assets/global/plugins/select2/select2.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/select2/select2-bootstrap.css" rel="stylesheet" />
}
@section Scripts{
    <script src="~/Content/assets/global/plugins/select2/select2.js"></script>
    @*<script src="~/Content/assets/global/plugins/select2/select2.min.js"></script>*@

    <script src="~/Content/assets/global/plugins/plupload/js/plupload.full.min.js"></script>
    <script>
        $(function () {
            FormValidation.init();

            var new_multipart_params = { types:"trial"};
            function set_upload_param(up, filename, ret) {
               up.setOption({
                   'url': '@Url.Action("UploaderFile")',
                   'multipart_params': new_multipart_params
               });
               up.start();
            }
            var uploader = new plupload.Uploader({
                runtimes: 'html5,flash,silverlight,html4',
                browse_button: 'selectfiles',
                multi_selection: false,
                container: document.getElementById('container'),
                flash_swf_url: '~/Content/assets/global/plugins/plupload/js/Moxie.swf',
                silverlight_xap_url: '~/Content/assets/global/plugins/plupload/js/Moxie.xap',
                url: '@Url.Action("UploaderFile")',
                filters: {
                    mime_types: [
                        { title: "准许的文件类型/file type supply", extensions: "rar,zip,7z" }
                    ],
                    max_file_size: '200mb', //最大只能上传10mb的文件
                    prevent_duplicates: false //不允许选取重复文件
                },
                init: {
                    PostInit: function () {
                        $("#ossfile").html("");
                        $("#btnUpload").click(function () {
                            set_upload_param(uploader, "", true);
                            return false;
                        });
                        $("#btnUpload").hide();
                    },
                    FilesAdded: function (up, files) {
                        if (uploader.files.length > 1) {
                            uploader.removeFile(files[0]);
                        }
                        plupload.each(files, function (file) {
                            $("#ossfile").html('<div id="file_' + file.id + '">' + file.name + ' (' + plupload.formatSize(file.size) + ')<b></b>'
                            + '</div>')
                        });
                        $("#btnUpload").show();
                    },
                    BeforeUpload: function (up, file) {
                        //set_upload_param(up, file.name, true);
                    },
                    UploadProgress: function (up, file) {
                        var d = $("#file_" + file.id);
                        d.children('b').html("<span>" + file.percent + "%</span>");
                        var prog = d.children('div');
                        var progBar = $(".progress-bar");
                        progBar.css("width", file.percent + '%');
                        progBar.attr('aria-valuenow', file.percent);
                    },
                    FileUploaded: function (up, file, info) {
                        if (info.status == 200 || info.status == 203) {
                            if (info.response && info.response.length > 0) {
                                AddSuccess('文件上传成功/upload success');
                                $("#FilePath").val(info.response);
                                $("#container").hide();
                                $("#btnSave").removeAttr("disabled");
                            } else
                                AddError("文件上传失败/upload faild");
                        } else {
                            AddError("文件上传失败/upload faild");
                        }
                    },
                    Error: function (up, err) {
                        if (err.code == -600) {
                            AddError("选择的文件太大了/Size out of");
                        }
                        else if (err.code == -601) {
                            AddError("所选的文件类型不对/file type vaild (rar,zip,7z)");
                        }
                        else if (err.code == -602) {
                            AddError("这个文件已经上传过一遍了/file is exists");
                        }
                        else {
                            AddError("Error xml:" + err.response);
                        }
                    }
                }
            });
            uploader.init();
            function AddSuccess(msg) {
                $("#divsuccess").empty();
                $("#divsuccess").append('<button class="close" data-close="alert"></button>');
                $("#divsuccess").append(msg);
                $("#divsuccess").removeClass("display-hide");

            }
            function AddError(msg) {
                $("#divdanger").empty();
                $("#divdanger").append('<button class="close" data-close="alert"></button>');
                $("#divdanger").append(msg);
                $("#divdanger").removeClass("display-hide");
            }
        });
    </script>
}