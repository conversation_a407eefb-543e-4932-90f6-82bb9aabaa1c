@model ITMCTR.App.Models.MessageInfoModel
@{
    ViewBag.Title = "提示信息";
    Layout = null;
}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>提示信息</title>
    <link href="~/Content/css/style.css?v=@DateTime.Now.Ticks.ToString()" rel="stylesheet" type="text/css" />
    <script src="~/Scripts/jquery-3.4.1.min.js"></script>
</head>
<body>
    @Html.Partial("_Header")
    <div class="zhuce_nr">
        <div class="zhuce_nr_title">

        </div>
        <div class="zhuce_content">
            <div class="zhuce_tz">
                @if (Model.IsError)
                {
                    <div><img src="~/Content/images/zhuce02.png" width="87" height="87" /></div>
                    <div class="zhuce_tz1">@Model.MessageTitle</div>
                }
                else
                {
                    <div><img src="~/Content/images/zhuce01.png" width="86" height="78" /></div>
                    <div class="zhuce_tz11">@Model.MessageTitle</div>
                }
                <div class="zhuce_tz2">@Model.MessageBody</div>
                <div class="zhuce_tz3" style="width:500px;">
                    <a id="warning" class="zhuce_content_but_aa" href="@Model.TargetUrl">@Resources.Global.UR_LOGIN_返回</a>
                    <a class="zhuce_content_but_aa" style="margin: 0 0 0 20px;" href="@Url.Action("Login")" />@Resources.Global.UR_LOGIN_立即登录</a>
                </div>
            </div>
        </div>
    </div>
    <div class="login_footer1">@Resources.Global.APP_TIP_浏览器兼容</div>
    <script type="text/javascript">
        var InterValObj; //timer变量，控制时间
        var count = 5; //间隔函数，1秒执行
        var curCount;//当前剩余秒数
        $(document).ready(function () {
            curCount = count;
            var dealType; //验证方式
            InterValObj = window.setInterval(SetRemainTime, 1000); //启动计时器，1秒执行一次
        });
        //timer处理函数
        function SetRemainTime() {
            if (curCount == 0) {
                window.clearInterval(InterValObj);//停止计时器
                window.location = "@Model.TargetUrl";
            }
            else {
                curCount--;
                $("#warning").text(curCount + @Resources.Global.UR_LOGIN_秒后自动跳转);
            }
        }
    </script>
</body>
</html>

