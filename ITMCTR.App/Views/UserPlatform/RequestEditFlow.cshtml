@model ITMCTR.Core.Models.SA_ProjectRequestEditFlow
@{
    Layout = "~/Views/Shared/_M_ModalLayout.cshtml";
    ViewBag.ModalTitle = "注册项目修改申请";
}
@using (Html.BeginForm("RequestEditFlow", "UserPlatform", FormMethod.Post, new { @enctype = "multipart/form-data" }))
{
    <div class="modal-body">
        <div class="form-body form-horizontal">
            <div class="alert alert-danger display-hide" id="divdanger">
                <button class="close" data-close="alert"></button>
                输入内容错误，请检查输入内容。
            </div>
            <div class="alert alert-success display-hide" id="divsuccess">
                <button class="close" data-close="alert"></button>
                表单验证成功！
            </div>
            <div class="note note-danger alert alert-danger">
                <h4 class="block"><strong>请务必在提交再修改申请后，与平台工作人员取得联系</strong></h4>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    注册号
                </label>
                <div class="col-md-8">
                    @ViewBag.regNumber
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    注册题目
                </label>
                <div class="col-md-8">
                    @ViewBag.publicTitle
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    请上传支持试验修改的相应试验方案
                    <br />
                    Please upload the corresponding update trial protocol file supporting your update registration
                </label>
                <div class="col-md-8">
                    <input type="file" id="editRequestAttachment" name="editRequestAttachment" required="required">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    请上传支持试验修改的相应伦理批件
                    <br />
                    Please upload the corresponding update ethical approval file supporting your update registration
                </label>
                <div class="col-md-8">
                    <input type="file" id="editRequestAttachment2" name="editRequestAttachment2" required="required">
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-md-3">
                    修改原因与计划修改内容
                    <br />
                    Reason and content of the update registration
                </label>
                <div class="col-md-8">
                    @Html.TextAreaFor(x => x.EditRequestReason, 10, 5, new { @class = "form-control input-xlarge", maxlength = "500", required = "required" })
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        @Html.HiddenFor(x => x.Pid)
        <input type="submit" id="btnSave" name="btnSave" class="btn green" data-success-dismiss="true" value="确定" />
        <button type="button" class="btn default" data-dismiss="modal">关闭</button>
    </div>
}
@section Css{
}
@section Scripts{
    <script>
        $(function () {
            FormValidation.init();
        });
    </script>
}