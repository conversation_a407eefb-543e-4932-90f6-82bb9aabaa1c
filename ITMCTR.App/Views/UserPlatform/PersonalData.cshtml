@model ITMCTR.App.Models.SysUserModel
@{
    Layout = "~/Views/Shared/_U_Layout.cshtml";
}

@using (Html.BeginForm("PersonalData", "UserPlatform", FormMethod.Post, new { @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    @Html.HiddenFor(x => x.Uid)
    <div class="col-md-12">
        <div class="portlet light ">
            <div class="portlet-title">
                <div class="caption">
                    <i class="icon-settings font-dark"></i>
                    <span class="caption-subject font-dark sbold uppercase">
                        个人信息/Personal Informaiton
                    </span>
                </div>
            </div>
            <div class="portlet-body form">
                @{var validationSummary = Html.ValidationSummary(true);
                    if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                    {
                        <div class="alert alert-danger">
                            <button class="close" data-close="alert"></button>
                            <span>
                                @validationSummary
                            </span>
                        </div>
                    }
                }
                @Html.HiddenFor(x => x.Username)
                <div class="form-body">
                    <div class="form-group">
                        <label class="col-md-2 control-label">用户名/Username</label>
                        <div class="col-md-4">
                            @Model.Username
                        </div>
                        <label class="col-md-2 control-label">姓名/RealName</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.Name, new { @class = "form-control", maxlength = "100", required = "required", placeholder = "请输入姓名,name please" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label">性别/Gender</label>
                        <div class="col-md-4">
                            @Html.DropDownListFor(x => x.Sex, ViewBag.SexList as IEnumerable<SelectListItem>, "请选择性别 select sex", new { @class = "bs-select form-control", required = "required" })
                        </div>
                        <label class="col-md-2 control-label">邮件/Email</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.Email, new { @class = "form-control", maxlength = "100", required = "required", placeholder = "请输入邮件 email please" })
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-2 control-label">固定电话/Landline</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.Phone, new { @class = "form-control", maxlength = "100", required = "required", placeholder = "请输入座机 phone please" })
                        </div>
                        <label class="col-md-2 control-label">手机号码/Mobile Phone</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.CellPhone, new { @class = "form-control", maxlength = "20", required = "required", placeholder = "请输入移动电话Mobile please" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-2 control-label">所属国家/Country</label>
                        <div class="col-md-4">
                            @Html.DropDownListFor(x => x.Country, ViewBag.CountryList as IEnumerable<SelectListItem>, "请选择所属国家 country please", new { @class = "bs-select form-control", required = "required" })
                        </div>
                        <label class="col-md-2 control-label">注册单位名称/Applicant's Institution</label>
                        <div class="col-md-4">
                            @Html.TextBoxFor(x => x.RegUnit, new { @class = "form-control", maxlength = "200", required = "required", placeholder = "请输入单位名称 reg unit please" })
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-2 control-label">注册单位地址/Applicant's Institution Address</label>
                        <div class="col-md-10">
                            @Html.TextBoxFor(x => x.RegAddress, new { @class = "form-control", maxlength = "400", required = "required", placeholder = "请输入单位地址Address" })
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <div class="row">
                        <div class="col-md-offset-4 col-md-8">
                            <button type="submit" class="btn green">保存Save</button>
                            <button type="button" class="btn default" onclick="javascript: window.location.href = '@Url.Action("Index","UserPlatform")'">返回Back</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

}