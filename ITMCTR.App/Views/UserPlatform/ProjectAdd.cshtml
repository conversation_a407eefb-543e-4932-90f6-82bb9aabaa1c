@model ITMCTR.App.Models.UserProjectModel

@{
    ViewBag.Title = "ProjectAdd";
    Layout = "~/Views/Shared/_U_Layout.cshtml";
}
@section Css{
    <link href="~/Content/CssProject.css" rel="stylesheet" />
    <link href="~/Content/assets/global/plugins/bootstrap-daterangepicker/daterangepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css" />

}
@section Scripts{
    <script src="~/Content/assets/global/plugins/bootbox/bootbox.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/bootstrap-datepicker/locales/bootstrap-datepicker.zh-CN.min.js"></script>
    <script src="~/Content/addprojJScript.js?v=@UiFun.AppVersion"></script>

}
@using (Html.BeginForm("ProjectAdd", "UserPlatform", FormMethod.Post, new { id = "fromProjectAdd", @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>新项目注册Create a new project</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">
        <div class="row">
            <div class="col-md-12">
                <div class="portlet light bordered">
                    <div class="portlet-title">
                        <div class="caption">
                            <span class="caption-subject font-red-sunglo bold">新项目注册Create a new project</span>
                        </div>
                        <div class="tools">
                        </div>
                    </div>
                    <div class="portlet-body form">
                        <div class="note note-danger note-bordered">
                            <h4 class="block">注意事项 notice</h4>
                            <p>
                                本页面20分钟无操作自动退出，请您及时保存注册信息，以免丢失！
                                <br />
                                There will be automatic logout after 20 minutes of inactivity. Please save the work in time to avoid loss.
                            </p>
                        </div>
                        <div class="form-body">
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    填写语言：
                                                </p>
                                                <p class="en">
                                                    Language：
                                                </p>
                                            </td>
                                            <td>
                                                <span>
                                                    @Html.RadioButtonFor(model => model.listLang, "1009001", new { @Checked = "Checked" })
                                                    <label for="listLang_0">
                                                        中文和英文/Chinese And English
                                                    </label>
                                                    @Html.RadioButtonFor(model => model.listLang, "1009002", new { })
                                                    <label for="listLang_1">
                                                        仅英文/English Only
                                                    </label>
                                                </span>

                                                <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    注册号状态：
                                                </p>
                                                <p class="en">
                                                    Registration Status：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.listRegStatus, ViewBag.listRegStatusDict as SelectList,new { @class = "form-control input-large input-inline" })
                                                <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    注册题目：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtTitle, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Public title：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtTitleEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    注册题目简写：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtTitleAcronym, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    English Acronym：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtTitleAcronymEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究课题的正式科学名称：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtOfficialName, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Scientific title：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtOfficialNameEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究课题的正式科学名称简写：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtOfficialNameAcronym, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Scientific title acronym：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtOfficialNameAcronymEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究课题代号(代码)：
                                                </p>
                                                <p class="en">
                                                    Study subject ID：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtSubjectID, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <!--
                                                <p class="cn">
                                                    在其它机构的注册号：</p>
                                                <p class="en">
                                                    Secondary ID：
                                                </p>-->
                                                <p class="cn">
                                                    在二级注册机构或其它机构的注册号：
                                                </p>
                                                <p class="en">
                                                    The registration number of the Partner Registry or other register：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtSecondaryID, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    申请注册联系人：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtApplier, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究负责人：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtStudyLeader, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Applicant：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtApplierEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-medium input-inline", maxlength = "100" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Study leader：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtStudyLeaderEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-medium input-inline", maxlength = "100" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    申请注册联系人电话：
                                                </p>
                                                <p class="en">
                                                    Applicant's telephone：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Html.TextBoxFor(x => x.txtApplierPhone, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                                <span class="required">*</span>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究负责人电话：
                                                </p>
                                                <p class="en">
                                                    Study leader's telephone：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtStudyLeaderPhone, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                                <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    申请注册联系人传真 ：
                                                </p>
                                                <p class="en">
                                                    Applicant's Fax：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Html.TextBoxFor(x => x.txtApplierFax, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究负责人传真：
                                                </p>
                                                <p class="en">
                                                    Study leader's fax：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Html.TextBoxFor(x => x.txtStudyLeaderFax, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    申请注册联系人电子邮件：
                                                </p>
                                                <p class="en">
                                                    Applicant's E-mail：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Html.TextBoxFor(x => x.txtApplierEmail, new { @class = "form-control input-medium input-inline", email = "true", maxlength = "256" })
                                                <span class="required">*</span>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究负责人电子邮件：
                                                </p>
                                                <p class="en">
                                                    Study leader's E-mail：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtStudyLeaderEmail, new { @class = "form-control input-medium input-inline", email = "true", maxlength = "256" })
                                                <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    申请单位网址(自愿提供)：
                                                </p>
                                                <p class="en">
                                                    Applicant's website<br />
                                                    (voluntary supply)：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Html.TextBoxFor(x => x.txtApplierWebsite, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "100" })
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究负责人网址(自愿提供)：
                                                </p>
                                                <p class="en">
                                                    Study leader's website<br />
                                                    (voluntary supply)：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtStudyLeaderWebsite, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "100" })
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    申请注册联系人通讯地址：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtApplierAddress, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究负责人通讯地址：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtStudyLeaderAddress, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Applicant's address：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtApplierAddressEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Study leader's address：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtStudyLeaderAddressEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    申请注册联系人邮政编码：
                                                </p>
                                                <p class="en">
                                                    Applicant's postcode：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtApplierPostcode, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究负责人邮政编码：
                                                </p>
                                                <p class="en">
                                                    Study leader's postcode：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtStudyLeaderPostcode, new { @class = "form-control input-medium input-inline", maxlength = "100" })
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    申请人所在单位：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtApplierCompany, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究负责人所在单位：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtStudyLeaderCompany, new { @class = "form-control input-large input-inline", maxlength = "450" })<span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Affiliation of the Registrant：<!--Applicant's institution：-->
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtApplierCompanyEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Affiliation of the Leader：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtStudyLeaderCompanyEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })<span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    是否获伦理委员会批准：
                                                </p>
                                                <p class="en">
                                                    Approved by ethic committee：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <span id="listEthicalCommitteeSanction">
                                                    @Html.RadioButtonFor(model => model.listEthicalCommitteeSanction, 1, new { @name = "listEthicalCommitteeSanction", @Checked = "Checked" })
                                                    <label for="listEthicalCommitteeSanction_0">
                                                        是/Yes
                                                    </label>
                                                    @Html.RadioButtonFor(model => model.listEthicalCommitteeSanction, 0, new { @name = "listEthicalCommitteeSanction" })
                                                    <label for="listEthicalCommitteeSanction_1">
                                                        否/No
                                                    </label>
                                                </span><span class="required">*</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody id="tbodyEcs" style="display: none;">
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    伦理委员会批件文号：
                                                </p>
                                                <p class="en">
                                                    Approved No. of ethic committee：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Html.TextBoxFor(x => x.txtEthicalCommitteeFileID, new { @class = "form-control input-medium input-inline", maxlength = "100" })<span class="required">*</span>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    伦理委员会批件附件：
                                                </p>
                                                <p class="en">
                                                    Approved file of Ethical Committee：
                                                </p>
                                            </td>
                                            <td>
                                                <input type="file" style="width: 300px;" class="form-control input-large input-inline" id="fileEthicalCommittee"
                                                       name="fileEthicalCommittee" />
                                                <a id="valueRemove1" href="javascript:void(0)" class="bt_operation">清除Clear</a>
                                                <span class="en">
                                                    (If the language of the approved document is not Chinese or English, please also attach a Chinese or English translation.)
                                                </span>
                                                <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 20px;" class="left_title control-label">
                                                <p class="cn">
                                                    提示：
                                                </p>
                                                <p class="en">
                                                    Tip：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn" style="color: #FF0066;">
                                                    上传文件最大不超过3MB。
                                                </p>
                                                <p class="en" style="color: #FF0066;">
                                                    Upload file does not exceed the maximum 3MB.
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    批准本研究的伦理委员会名称：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtEthicalCommitteeName, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Name of the ethic committee：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtEthicalCommitteeNameEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    伦理委员会批准日期：
                                                </p>
                                                <p class="en">
                                                    Date of approved by ethic committee：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.TextBoxFor(x => x.txtEthicalCommitteeSanctionDate, "{0:yyyy-MM-dd}", new { @class = "form-control input-lager input-inline date-picker", maxlength = "100" })  <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <!--联系人 -->
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    伦理委员会联系人：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtEthicalCommitteeCName, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Contact Name of the ethic committee：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtEthicalCommitteeCNameEN, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <!--联系地址 -->
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    伦理委员会联系地址：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtEthicalCommitteeCAddress, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Contact Address of the ethic committee：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtEthicalCommitteeCAddressEN, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>

                                        <tr>
                                            <!-- 联系人电话  -->
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    伦理委员会联系人电话：
                                                </p>
                                                <p class="en">
                                                    Contact phone of the ethic committee：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtEthicalCommitteeCPhone, new { @class = "form-control input-medium input-inline", maxlength = "100" })   <span class="required">*</span>
                                            </td>

                                            <!-- 联系人邮箱  -->
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    伦理委员会联系人邮箱：
                                                </p>
                                                <p class="en">
                                                    Contact email of the ethic committee：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtEthicalCommitteeCEmail, new { @class = "form-control input-medium input-inline", email = "true", maxlength = "100" }) <span class="required">*</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    国家药监局批准文号：
                                                </p>
                                                <p class="en">
                                                    Approved No. of MPA：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                @Html.TextBoxFor(x => x.txtNationalFDASanctionNO, new { @class = "form-control input-small input-inline", maxlength = "100" })
                                                (<span class="cn">选填</span><span class="en">Optional </span>)
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    国家药监局批准附件：
                                                </p>
                                                <p class="en">
                                                    Approved file of MPA：
                                                </p>
                                            </td>
                                            <td>
                                                <input type="file" style="width: 300px;" class="form-control input-large input-inline" id="fileNationalFDASanction"
                                                       name="fileNationalFDASanction" />
                                                <a id="valueRemove2" href="javascript:void(0)" class="bt_operation">清除Clear</a> (<span class="cn">选填</span><span class="en">Optional</span>)
                                            </td>
                                        </tr>

                                        <tr>
                                            <td style="width: 20px;" class="left_title control-label">
                                                <p class="cn">
                                                    提示：
                                                </p>
                                                <p class="en">
                                                    Tip：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn" style="color: #FF0066;">
                                                    上传文件最大不超过3MB。
                                                </p>
                                                <p class="en" style="color: #FF0066;">
                                                    Upload file does not exceed the maximum 3MB.
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    国家药监局批准日期：
                                                </p>
                                                <p class="en">
                                                    Date of approved by MPA：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.TextBoxFor(x => x.txtNationalFDASanctionDate, "{0:yyyy-MM-dd}", new { @class = "form-control input-large input-inline date-picker", maxlength = "100" })
                                                (<span class="cn">选填</span><span class="en">Optional </span>)
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究方案：
                                                </p>
                                                <p class="en">
                                                    Study protocol：
                                                </p>
                                            </td>
                                            <td colspan="2">
                                                <input type="file" style="width: 300px;" class="form-control input-large input-inline" id="fileStudyPlan" name="fileStudyPlan" />
                                                <a id="valueRemove3" href="javascript:void(0)" class="bt_operation">
                                                    清除Clear
                                                </a>
                                                (
                                                <span class="cn" style="color: #FF0066;">仅用于审核，不公开</span>
                                                <span class="en" style="color: #FF0066;">For prejudication of registration only, will not be published. Please upload the file in Chinese or English language.</span>
                                                )
                                                <span class="required">*</span>
                                            </td>
                                            <td>
                                                <p class="cn" style="color: #FF0066;">
                                                    请上传与伦理委员会批件中版本号一致的研究方案
                                                </p>
                                                <p class="en" style="color: #FF0066;">
                                                    Please upload the study protocol with the same version number as the one approved by the ethics committee
                                                </p>
                                            </td>
                                        </tr>

                                        <tr>
                                            <td style="width: 20px;" class="left_title control-label">
                                                <p class="cn">
                                                    提示：
                                                </p>
                                                <p class="en">
                                                    Tip：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn" style="color: #FF0066;">
                                                    上传文件最大不超过3MB。
                                                </p>
                                                <p class="en" style="color: #FF0066;">
                                                    Upload file does not exceed the maximum 3MB.
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    知情同意书：
                                                </p>
                                                <p class="en">
                                                    Informed consent file：
                                                </p>
                                            </td>
                                            <td colspan="2">
                                                <input type="file" style="width: 300px;" class="form-control input-large input-inline" id="fileInformedConsent"
                                                       name="fileInformedConsent" />
                                                <a id="valueRemove4" href="javascript:void(0)" class="bt_operation">
                                                    清除Clear
                                                </a>
                                                (
                                                <span class="cn" style="color: #FF0066;">仅用于审核，不公开</span>
                                                <span class="en" style="color: #FF0066;">For prejudication of registration only, will not be published. Please upload the file in Chinese or English language.</span>)
                                                <span class="required">*</span>
                                            </td>
                                            <td>
                                                <p class="cn" style="color: #FF0066;">
                                                    请上传与伦理委员会批件中版本号一致的知情同意书
                                                </p>
                                                <p class="en" style="color: #FF0066;">
                                                    Please upload the informed consent file with the same version number as the one approved by the ethics committee
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 20px;" class="left_title control-label">
                                                <p class="cn">
                                                    提示：
                                                </p>
                                                <p class="en">
                                                    Tip：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn" style="color: #FF0066;">
                                                    上传文件最大不超过3MB。
                                                </p>
                                                <p class="en" style="color: #FF0066;">
                                                    Upload file does not exceed the maximum 3MB.
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究实施负责（组长）单位：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtSponsor, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Primary sponsor：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtSponsorEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究实施负责（组长）单位地址：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtSponsorAddress, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Primary sponsor's address：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtSponsorAddressEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    试验主办单位(即项目批准或申办者)：
                                                </p>
                                                <p class="en">
                                                    Secondary sponsor：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.HiddenFor(x => x.hdnSecSponsorCount)
                                                <div id="tbSecSponsor" class="noComma subitem">
                                                    <table width="850" cellspacing="0" cellpadding="0" border="0">
                                                        <tbody>
                                                            <tr>
                                                                <td width="80">
                                                                    <p class="cn">
                                                                        国家：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="200">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline SecSponsorCountry" maxlength="450" value="" name="SecSponsorCountry0" />
                                                                    </p>
                                                                </td>
                                                                <td width="80">
                                                                    <p class="cn">
                                                                        省(直辖市)：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="200">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline SecSponsorProvince" maxlength="450" name="SecSponsorProvince0" value="" />
                                                                    </p>
                                                                </td>
                                                                <td width="80">
                                                                    <p class="cn">
                                                                        市(区县)：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline SecSponsorCity" maxlength="450" name="SecSponsorCity0" value="" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <p class="en">
                                                                        Country：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline SecSponsorCountryEn" maxlength="450" name="SecSponsorCountryEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        Province：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline SecSponsorProvinceEn" maxlength="450" name="SecSponsorProvinceEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        City：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline SecSponsorCityEn" maxlength="450" name="SecSponsorCityEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <p class="cn">
                                                                        单位：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline SecSponsorInstitution" maxlength="450" name="SecSponsorInstitution0" value="" />
                                                                    </p>
                                                                    <p class="cn help-block">
                                                                        每格只填写一个单位(医院)
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        具体地址：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td colspan="2">
                                                                    <p class="cn">
                                                                        <input type="text" size="30" class="form-control input-large input-inline SecSponsorAddress" maxlength="450" name="SecSponsorAddress0" value="" />
                                                                    </p>
                                                                </td>
                                                                <td rowspan="2">
                                                                    <p class="cn help-block">
                                                                        点击下面按钮添加更多
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Click below add more one
                                                                    </p>
                                                                    <p>
                                                                        <input type="button" class="btn btn-default" title="点击这里添加更多。Click here to add more." value="增加一个More" id="btnAddSecSponsor" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <p class="en">
                                                                        Institution：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline SecSponsorInstitutionEn" maxlength="450" name="SecSponsorInstitutionEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />

                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Input only one institution
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        Address：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td colspan="2">
                                                                    <p class="en">
                                                                        <input type="text" size="30" class="form-control input-large input-inline SecSponsorAddressEn" maxlength="450" name="SecSponsorAddressEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    @for (int i = 1; i < Model.SecondarySponsor.Count; i++)
                                                    {
                                                        <table border="0" cellpadding="0" cellspacing="0" width="850">
                                                            <tbody>
                                                                <tr>
                                                                    <td width="80">
                                                                        <p class="cn">
                                                                            国家：<span class="required">*</span>
                                                                        </p>
                                                                    </td>
                                                                    <td width="200">
                                                                        <p class="cn">
                                                                            <input name="SecSponsorCountry@(i)" value="@Model.SecondarySponsor[i].countryCN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorCountry">
                                                                        </p>

                                                                    </td>
                                                                    <td width="80">

                                                                        <p class="cn">省(直辖市)：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="200">
                                                                        <p class="cn">
                                                                            <input name="SecSponsorProvince@(i)" value="@Model.SecondarySponsor[i].provinceCN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorProvince">
                                                                        </p>
                                                                    </td>
                                                                    <td width="80"><p class="cn">市(区县)：</p></td>
                                                                    <td>
                                                                        <p class="cn">
                                                                            <input name="SecSponsorCity@(i)" value="@Model.SecondarySponsor[i].cityCN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorCity">
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td><p class="en">Country：<span class="required">*</span></p></td>
                                                                    <td>
                                                                        <p class="en">
                                                                            <input name="SecSponsorCountryEn@(i)" value="@Model.SecondarySponsor[i].countryEN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorCountryEn" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td><p class="en">Province：<span class="required">*</span></p></td>
                                                                    <td>
                                                                        <p class="en">
                                                                            <input name="SecSponsorProvinceEn@(i)" value="@Model.SecondarySponsor[i].provinceEN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorProvinceEn" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td><p class="en">City：</p></td>
                                                                    <td>
                                                                        <p class="en">
                                                                            <input name="SecSponsorCityEn@(i)" value="@Model.SecondarySponsor[i].cityEN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorCityEn" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>
                                                                        <p class="cn">单位：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="cn">
                                                                            <input name="SecSponsorInstitution@(i)" value="@Model.SecondarySponsor[i].institutionCN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorInstitution">
                                                                        </p>
                                                                    </td>
                                                                    <td><p class="cn">具体地址：<span class="required">*</span></p></td>
                                                                    <td colspan="2">
                                                                        <p class="cn">
                                                                            <input name="SecSponsorAddress@(i)" value="@Model.SecondarySponsor[i].specificAddressCN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorAddress" size="30">
                                                                        </p>
                                                                    </td>
                                                                    <td rowspan="2">
                                                                        <p><input type="button" class="btn btn-default" value="删除该单位Delete" title="删除该单位。Click here to delete the institution."></p>
                                                                        <p><input type="button" class="btn btn-default" value="增加一个单位More" title="点击这里添加更多。Click here to add more."></p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td><p class="en">Institution：<span class="required">*</span></p></td>
                                                                    <td>
                                                                        <p class="en">
                                                                            <input name="SecSponsorInstitutionEn@(i)" value="@Model.SecondarySponsor[i].institutionEN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorInstitutionEn" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td><p class="en">Address：<span class="required">*</span></p></td>
                                                                    <td colspan="2">
                                                                        <p class="en">
                                                                            <input name="SecSponsorAddressEn@(i)" value="@Model.SecondarySponsor[i].specificAddressEN" type="text" maxlength="450" class="form-control input-large input-inline SecSponsorAddressEn" size="30" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    经费或物资来源：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtSourceOfSpends, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Source(s) of funding：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtSourceOfSpendsEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究疾病：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtStudyAilment, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究疾病代码：
                                                </p>
                                            </td>
                                            <td rowspan="2">
                                                @Html.TextBoxFor(x => x.txtStudyAilmentCode, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Target disease：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtStudyAilmentEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Target disease code：
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究类型：
                                                </p>
                                                <p class="en">
                                                    Study type：
                                                </p>
                                            </td>
                                            <!--id="listStudyType"-->
                                            <td style="width: 303px;">
                                                @Html.DropDownList("listStudyType", ViewBag.PagelistStudyType as IEnumerable<SelectListItem>, "", new { @class = "form-control form-control input-large input-inline", required = "required" })
                                                <span class="required">*</span>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究设计：
                                                </p>
                                                <p class="en">
                                                    Study design：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownList("listStudyDesign", ViewBag.PagelistStudyDesign as IEnumerable<SelectListItem>, "", new { @class = "form-control form-control input-large input-inline", required = "required" })
                                                <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究所处阶段：
                                                </p>
                                                <p class="en">
                                                    Study phase：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.DropDownList("listStudyStage", ViewBag.PagelistStudyStage as IEnumerable<SelectListItem>, "", new { @class = "form-control form-control input-large input-inline", required = "required" })
                                                <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究目的：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtStudyAim" cols="20" rows="5"
                                                              name="txtStudyAim">@Model.txtStudyAim</textarea><span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Objectives of Study：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtStudyAimEn" cols="20" rows="5"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')"
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              name="txtStudyAimEn">@Model.txtStudyAimEn</textarea><span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    药物成份或治疗方案详述：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtDrugsComposition" cols="20"
                                                              rows="5" name="txtDrugsComposition">@Model.txtDrugsComposition</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Description for medicine or protocol of treatment in detail：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtDrugsCompositionEn" cols="20"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')" onbeforepaste="clipboardData.setData
('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              rows="5" name="txtDrugsCompositionEn">@Model.txtDrugsCompositionEn</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    纳入标准：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtSelectionCriteria" cols="20"
                                                              rows="5" name="txtSelectionCriteria">@Model.txtSelectionCriteria</textarea><span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Inclusion criteria
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtSelectionCriteriaEn" cols="20"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')"
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              rows="5" name="txtSelectionCriteriaEn">@Model.txtSelectionCriteriaEn</textarea><span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    排除标准：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtEliminateCriteria" cols="20"
                                                              rows="5" name="txtEliminateCriteria">@Model.txtEliminateCriteria</textarea><span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Exclusion criteria：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtEliminateCriteriaEn" cols="20"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')"
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              rows="5" name="txtEliminateCriteriaEn">@Model.txtEliminateCriteriaEn</textarea><span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究实施时间：
                                                </p>
                                                <p class="en">
                                                    Study execute time：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <p>
                                                    <span class="cn">从</span><span class="en">From</span>
                                                    @Html.TextBoxFor(x => x.txtStudyExecuteTime, "{0:yyyy-MM-dd}", new { @class = "form-control input-medium input-inline date-picker", maxlength = "100" })<span class="required">*</span>
                                                </p>
                                                <p>
                                                    <span class="cn">至</span><span class="en">To</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    @Html.TextBoxFor(x => x.txtStudyEndTime, "{0:yyyy-MM-dd}", new { @class = "form-control input-medium input-inline date-picker", maxlength = "100" })<span class="required">*</span>
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    征募观察对象时间：
                                                </p>
                                                <p class="en">
                                                    Recruiting time：
                                                </p>
                                            </td>
                                            <td>
                                                <p>
                                                    <span class="cn">从</span><span class="en">From</span>
                                                    @Html.TextBoxFor(x => x.txtEnlistBeginTime, "{0:yyyy-MM-dd}", new { @class = "form-control input-medium input-inline date-picker", maxlength = "100" })<span class="required">*</span>
                                                </p>
                                                <p>
                                                    <span class="cn">至</span><span class="en">To</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    @Html.TextBoxFor(x => x.txtEnlistEndTime, "{0:yyyy-MM-dd}", new { @class = "form-control input-medium input-inline date-picker", maxlength = "100" })<span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group" id="divInter">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td rowspan="2" style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    干预措施：
                                                </p>
                                                <p class="en">
                                                    Interventions：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.HiddenFor(x => x.hdnInterCount)
                                                <div id="tbInter" class="noComma subitem">
                                                    <table width="850" cellspacing="0" cellpadding="0" border="0">
                                                        <tbody>
                                                            <tr>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        组别：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline GroupName" maxlength="450" name="GroupName0" value="" />
                                                                    </p>
                                                                </td>
                                                                <td width="110">
                                                                    <p class="cn">
                                                                        样本量：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180" rowspan="2">
                                                                    <p>
                                                                        <input type="text" class="form-control input-large input-inline SampleSize" maxlength="450" name="SampleSize0" value="" />
                                                                    </p>
                                                                </td>
                                                                <td rowspan="4">
                                                                    <p class="cn help-block">
                                                                        点击下面按钮添加更多
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Click below add more one
                                                                    </p>
                                                                    <p>
                                                                        <input type="button" class="btn btn-default" title="点击这里添加更多干预措施。Click here to add more." value="增加一项干预措施AddMore"
                                                                               id="btnAddInter" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="90">
                                                                    <p class="en">
                                                                        Group：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline GroupNameEn" maxlength="450" name="GroupNameEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                                <td width="110">
                                                                    <p class="en">
                                                                        Sample size：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        干预措施：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline Measure" maxlength="450" name="Measure0" value="" />
                                                                    </p>
                                                                    <p class="cn help-block">
                                                                        每格只填写一个干预措施
                                                                    </p>
                                                                </td>
                                                                <td width="110">
                                                                    <p class="cn">
                                                                        干预措施代码：
                                                                    </p>
                                                                </td>
                                                                <td width="180" rowspan="2">
                                                                    <p class="cn help-block">
                                                                        每格只填写一个代码
                                                                    </p>
                                                                    <p>
                                                                        <input type="text" class="form-control input-large input-inline" maxlength="450" name="InterCode0" value="" />
                                                                    </p>
                                                                    <p class="cn help-block">
                                                                        Please input only one code
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="90">
                                                                    <p title="trade name and common name, dosage, usage, TCM formulation name" class="en">
                                                                        Intervention：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline MeasureEn" maxlength="450" name="MeasureEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Please input only one Measure
                                                                    </p>
                                                                </td>
                                                                <td width="110">
                                                                    <p class="en">
                                                                        Intervention code：
                                                                    </p>
                                                                </td>
                                                            </tr>

                                                            <tr>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        提示：
                                                                    </p>
                                                                    <p class="en">
                                                                        Tip：
                                                                    </p>
                                                                </td>
                                                                <td colspan="4">
                                                                    <p class="cn" style="color: #FF0066;">
                                                                        请确保本次注册研究的所有分组的信息填写完整
                                                                    </p>
                                                                    <p class="en" style="color: #FF0066;">
                                                                        Please ensure that information for all groups involved in this registered study is filled in completely
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    @for (int i = 1; i < Model.Interventions.Count; i++)
                                                    {
                                                        <table border="0" cellpadding="0" cellspacing="0" width="850">
                                                            <tbody>
                                                                <tr>
                                                                    <td width="90">
                                                                        <p class="cn">组别：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="180">
                                                                        <p class="cn"><input name="GroupName@(i)" type="text" maxlength="450" class="form-control input-large input-inline GroupName" value="@Model.Interventions[i].groupsCN"></p>
                                                                    </td>
                                                                    <td width="110">
                                                                        <p class="cn">样本量：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td rowspan="2" width="180"><p><input name="SampleSize@(i)" maxlength="30" type="text" class="form-control input-large input-inline SampleSize" value="@Model.Interventions[i].sampleSize"></p></td>
                                                                    <td rowspan="4">
                                                                        <p><input type="button" class="btn btn-default" value="删除该项干预措施Delete" title="删除该干预措施。Click here to delete the intervention."></p><p><input type="button" class="btn btn-default" value="增加一项干预措施AddMore" title="点击这里添加更多干预措施。Click here to add more."></p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="90">
                                                                        <p class="en">Group：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="180">
                                                                        <p class="en">
                                                                            <input name="GroupNameEn@(i)" maxlength="450" type="text" class="form-control input-large input-inline GroupNameEn" value="@Model.Interventions[i].groupsEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td width="110"><p class="en">Sample size：<span class="required">*</span></p></td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="90">
                                                                        <p class="cn">干预措施：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="180"><p class="cn"><input name="Measure@(i)" type="text" maxlength="450" class="form-control input-large input-inline Measure" value="@Model.Interventions[i].interventionCN"></p></td>
                                                                    <td width="110"><p class="cn">干预措施代码：</p></td>
                                                                    <td rowspan="2"><p><input name="InterCode@(i)" type="text" class="form-control input-large input-inline" value=""></p></td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="90">
                                                                        <p class="en" title="trade name and common name, dosage, usage, TCM formulation name">Measure：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="180">
                                                                        <p class="en">
                                                                            <input name="MeasureEn@(i)" type="text" maxlength="450" class="form-control input-large input-inline MeasureEn" value="@Model.Interventions[i].interventionEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td width="110"><p class="en">Intervention code：</p></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="3">
                                                <span class="required">*</span><span class="cn">样本总量</span> <span class="en">
                                                    Total sample
                                                    size
                                                </span>：
                                                @Html.TextBoxFor(x => x.txtTotalSampleSize, new { @class = "form-control input-large input-inline", maxlength = "30" })
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group" id="divDiagnostic">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td rowspan="2" style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    诊断试验：
                                                </p>
                                                <p class="en">
                                                    Diagnostic Tests：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <input type="hidden" id="hdnDiagnosticId" name="hdnDiagnosticId" value="" />
                                                <div id="Div2" class="noComma subitem">
                                                    <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                                        <tbody>
                                                            <tr>
                                                                <td style="width: 250px;padding:10px">
                                                                    <p class="cn">
                                                                        金标准或参考标准（即可准确诊断某疾病的单项方法或多项联合方法，在本研究中用于诊断是否有该病的临床参考标准）：
                                                                    </p>

                                                                </td>
                                                                <td colspan="3">
                                                                    <p class="cn">
                                                                        <textarea style="width:80%" class="form-control input-inline" id="txtStandard" cols="20" rows="4" name="txtStandard"></textarea>
                                                                        <span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 250px;padding:10px">
                                                                    <p class="en">
                                                                        Gold Standard or Reference Standard (The clinical reference standards required to establish the presence or absence of the target condition in the tested population in present study):
                                                                    </p>

                                                                </td>
                                                                <td colspan="3">
                                                                    <p class="en">
                                                                        <textarea style="width:80%" class="form-control input-inline" id="txtStandardEn" cols="20" rows="4" name="txtStandardEn" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"></textarea>
                                                                        <span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 250px;padding:10px">
                                                                    <p class="cn">
                                                                        指标试验（即本研究的待评估诊断试验，无论为方法、生物标志物或设备，均请列出名称）：
                                                                    </p>
                                                                </td>
                                                                <td colspan="3">
                                                                    <p class="cn">
                                                                        <textarea style="width:80%" class="form-control input-inline" id="txtIndexTest" cols="20" rows="4" name="txtIndexTest"></textarea>
                                                                        <span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 250px;padding:10px">
                                                                    <p class="en">
                                                                        Index test:
                                                                    </p>
                                                                </td>
                                                                <td colspan="3">
                                                                    <p class="en">
                                                                        <textarea style="width:80%" class="form-control input-inline" id="txtIndexTestEn" cols="20" rows="4" name="txtIndexTestEn" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"></textarea>
                                                                        <span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding:10px">
                                                                    <p class="cn">
                                                                        目标人群（可以是某种疾病患者或正常人群，详细描述其疾病特征，注意应纳入符合分布特点的全序列病例，具有良好的代表性）
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        <textarea style="width:80%" class="form-control input-inline" id="txtTargetCondition" cols="20" rows="4" name="txtTargetCondition"></textarea><span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td style="width: 80px;padding-left:10px" rowspan="2">
                                                                    <p class="cn">
                                                                        例数:
                                                                    </p>
                                                                    <p class="en">
                                                                        Sample size:
                                                                    </p>
                                                                    <span class="required">*</span>
                                                                    <p>
                                                                        <input type="text" class="form-control input-large input-inline SampleSize" maxlength="30" name="txtSampleSizeT" value="" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding:10px">
                                                                    <p class="en">
                                                                        Target condition (The target condition is a particular disease or disease stage that the index test will be intended to identify. Please specify the characteristics in detail; the population should has a complete spectrum and good representative):
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <textarea style="width:80%" class="form-control input-inline" id="txtTargetConditionEn" cols="20" rows="4" name="txtTargetConditionEn" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"></textarea><span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding:10px">
                                                                    <p class="cn">
                                                                        容易混淆的疾病人群（即与目标疾病不易区分的一种或多种不同疾病，应避免采用正常人群对照的病例-对照设计）：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        <textarea style="width:80%" class="form-control input-inline" id="txtDifficultCondition" cols="20" rows="4" name="txtDifficultCondition"></textarea>   <span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td style="width: 80px;padding-left:10px" rowspan="2">
                                                                    <p class="cn">
                                                                        例数:
                                                                    </p>
                                                                    <p class="en">
                                                                        Sample size:
                                                                    </p>
                                                                    <span class="required">*</span>
                                                                    <p>
                                                                        <input type="text" style="width: 80%;" class="form-control input-large input-inline " maxlength="30" name="txtSampleSizeD" value="" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="padding:10px">
                                                                    <p class="en">
                                                                        Population with condition difficult to distinguish from the target condition, the normal population in a case-control study design should be avoid:
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <textarea style="width:80%" class="form-control input-inline"
                                                                                  id="txtDifficultConditionEn" cols="20" rows="4" name="txtDifficultConditionEn"
                                                                                  onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') "
                                                                                  onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"></textarea>
                                                                        <span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究实施地点：
                                                </p>
                                                <p class="en">
                                                    Countries of recruitment
                                                    <br />
                                                    and research settings：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.HiddenFor(x => x.hdnPlaceCount)
                                                <div id="tbPlace" class="noComma subitem">
                                                    <table width="850" cellspacing="0" cellpadding="0" border="0">
                                                        <tbody>
                                                            <tr>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        国家：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline Country" maxlength="450" name="Country0" value="" />
                                                                    </p>
                                                                </td>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        省(直辖市)：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline Province" maxlength="450" name="Province0" value="" />
                                                                    </p>
                                                                </td>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        市(区县)：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline City" maxlength="450" name="City0" value="" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="90">
                                                                    <p class="en">
                                                                        Country：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline CountryEn" maxlength="450" name="CountryEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                                <td width="90">
                                                                    <p class="en">
                                                                        Province：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline ProvinceEn" maxlength="450" name="ProvinceEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                                <td width="90">
                                                                    <p class="en">
                                                                        City：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline CityEn" maxlength="450" name="CityEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        单位(医院)：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline Institution" maxlength="450" name="Institution0" value="" />
                                                                    </p>
                                                                    <p class="cn help-block">
                                                                        每格只填写一个单位(医院)
                                                                    </p>
                                                                </td>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        单位级别：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline InstitutionLevel" maxlength="450" name="InstitutionLevel0" value="" />
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                </td>
                                                                <td rowspan="2">
                                                                    <p class="cn help-block">
                                                                        点击下面按钮添加更多
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Click below add more one
                                                                    </p>
                                                                    <p>
                                                                        <input type="button" class="btn btn-default" title="点击这里添加更多地点。Click here to add more." value="增加一个地点More"
                                                                               id="btnAddPlace" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="90">
                                                                    <p class="en">
                                                                        Institution<br />
                                                                        /hospital：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline InstitutionEn" maxlength="450" name="InstitutionEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Input only one institution
                                                                    </p>
                                                                </td>
                                                                <td width="90">
                                                                    <p class="en">
                                                                        Level of the
                                                                        <br />
                                                                        institution：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline InstitutionLevelEn" maxlength="450" name="InstitutionLevelEn0" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    @for (int i = 1; i < Model.ResearchAddress.Count; i++)
                                                    {
                                                        <table border="0" cellpadding="0" cellspacing="0" width="850">
                                                            <tbody>
                                                                <tr>
                                                                    <td width="90"><p class="cn">国家：<span class="required">*</span></p></td>
                                                                    <td width="180"><p class="cn"><input name="Country@(i)" type="text" maxlength="450" class="form-control input-large input-inline Country" value="@Model.ResearchAddress[i].countryCN"></p></td>
                                                                    <td width="90"><p class="cn">省(直辖市)：<span class="required">*</span></p></td>
                                                                    <td width="180"><p class="cn"><input name="Province@(i)" type="text" maxlength="450" class="form-control input-large input-inline Province" value="@Model.ResearchAddress[i].provinceCN"></p></td>
                                                                    <td width="90"><p class="cn">市(区县)：</p></td>
                                                                    <td><p class="cn"><input name="City@(i)" type="text" maxlength="450" class="form-control input-large input-inline City" value="@Model.ResearchAddress[i].cityCN"></p></td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="90">
                                                                        <p class="en">Country：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="180">
                                                                        <p class="en">
                                                                            <input name="CountryEn@(i)" type="text" maxlength="450" class="form-control input-large input-inline CountryEn" value="@Model.ResearchAddress[i].countryEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td width="90"><p class="en">Province：<span class="required">*</span></p></td>
                                                                    <td width="180">
                                                                        <p class="en">
                                                                            <input name="ProvinceEn@(i)" type="text" maxlength="450" class="form-control input-large input-inline ProvinceEn" value="@Model.ResearchAddress[i].provinceEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td width="90"><p class="en">City：</p></td>
                                                                    <td>
                                                                        <p class="en">
                                                                            <input name="CityEn@(i)" type="text" maxlength="450" class="form-control input-large input-inline CityEn" value="@Model.ResearchAddress[i].cityEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="90"><p class="cn">单位(医院)：<span class="required">*</span></p></td>
                                                                    <td width="180"><p class="cn"><input name="Institution@(i)" type="text" maxlength="450" class="form-control input-large input-inline Institution" value="@Model.ResearchAddress[i].hospitalCN"></p></td>
                                                                    <td width="90"><p class="cn">单位级别：<span class="required">*</span></p></td>
                                                                    <td width="180"><p class="cn"><input name="InstitutionLevel@(i)" type="text" maxlength="450" class="form-control input-large input-inline InstitutionLevel" value="@Model.ResearchAddress[i].levelInstitutionCN"></p></td>
                                                                    <td></td>
                                                                    <td colspan="2"><p><input type="button" class="btn btn-default" value="删除该地点Delete" title="删除该地点。Click here to delete the place."></p><p><input type="button" class="btn btn-default" value="增加一个地点More" title="点击这里添加更多地点。Click here to add more."></p></td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="90"><p class="en">Institution<br>/hospital：<span class="required">*</span></p></td>
                                                                    <td width="180">
                                                                        <p class="en">
                                                                            <input name="InstitutionEn@(i)" type="text" maxlength="450" class="form-control input-large input-inline InstitutionEn" value="@Model.ResearchAddress[i].hospitalEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td width="90"><p class="en">Level of the <br>institution：<span class="required">*</span></p></td>
                                                                    <td width="180">
                                                                        <p class="en">
                                                                            <input name="InstitutionLevelEn@(i)" type="text" maxlength="450" class="form-control input-large input-inline InstitutionLevelEn" value="@Model.ResearchAddress[i].levelInstitutionEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td></td>
                                                                    <td></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    测量指标：
                                                </p>
                                                <p class="en">
                                                    Outcomes：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                @Html.HiddenFor(x => x.hdnIndexCount)
                                                <div id="tbIndex" class="noComma subitem">
                                                    <table width="850" cellspacing="0" cellpadding="0" border="0">
                                                        <tbody>
                                                            <tr>
                                                                <td width="110">
                                                                    <p class="cn">
                                                                        指标中文名：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline IndexName0" name="IndexName0" maxlength="450" value="" />
                                                                    </p>
                                                                    <p class="cn help-block">
                                                                        每格只填写一个指标
                                                                    </p>
                                                                </td>
                                                                <td width="90">
                                                                    <p class="cn">
                                                                        指标类型：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="230" rowspan="2">
                                                                    <select class="IndexTypeId form-control" name="IndexTypeId0">
                                                                        <option value="4002001">主要指标/Primary indicator</option>
                                                                        <option value="4002002">次要指标/Secondary indicator</option>
                                                                        <option value="4002003">附加指标/Additional indicator</option>
                                                                        <option value="4002004">副作用指标/Adverse events</option>
                                                                    </select>
                                                                </td>
                                                                <td rowspan="4">
                                                                    <p class="cn help-block">
                                                                        点击下面按钮添加更多
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Click below add more one
                                                                    </p>
                                                                    <p>
                                                                        <input type="button" class="btn btn-default" title="点击这里添加更多指标。Click here to add more." value="增加一项指标More"
                                                                               id="btnAddIndex" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="110">
                                                                    <p class="en">
                                                                        Outcome Name：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="180">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline IndexNameEn" name="IndexNameEn0" maxlength="450" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Input only one outcomes name
                                                                    </p>
                                                                </td>
                                                                <td width="90">
                                                                    <p class="en">
                                                                        Type：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="t">
                                                                    <p class="cn">
                                                                        测量时间点：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline" name="txtIndexNamePointTime0" maxlength="450" />
                                                                    </p>
                                                                </td>
                                                                <td class="t">
                                                                    <p class="cn">
                                                                        测量方法：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline" name="txtIndexNameMeasureMethod0" maxlength="450" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="t">
                                                                    <p class="en">
                                                                        Measure time point of outcome：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline" name="txtIndexNamePointTimeEn0" maxlength="450" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                                <td class="t">
                                                                    <p class="en">
                                                                        Measure method：
                                                                    </p>
                                                                </td>
                                                                <td>
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline" name="txtIndexNameMeasureMethodEn0" maxlength="450" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    @for (int i = 1; i < Model.Outcomes.Count; i++)
                                                    {
                                                        <table border="0" cellpadding="0" cellspacing="0" width="850">
                                                            <tbody>
                                                                <tr>
                                                                    <td width="110">
                                                                        <p class="cn">指标中文名：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="180">
                                                                        <p class="cn"><input name="IndexName@(i)" type="text" class="form-control input-large input-inline IndexName" maxlength="450" value="@Model.Outcomes[i].outcomeNameCN"></p>
                                                                    </td>
                                                                    <td width="90">
                                                                        <p class="cn">指标类型：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="230" rowspan="2">
                                                                        <select name="IndexTypeId@(i)" class="IndexTypeId form-control">
                                                                            <option value="4002001" @(Model.Outcomes[i].pointerType == "4002001" ? "selected" : "")>主要指标/Primary indicator</option>
                                                                            <option value="4002002" @(Model.Outcomes[i].pointerType == "4002002" ? "selected" : "")>次要指标/Secondary indicator</option>
                                                                            <option value="4002003" @(Model.Outcomes[i].pointerType == "4002003" ? "selected" : "")>附加指标/Additional indicator</option>
                                                                            <option value="4002004" @(Model.Outcomes[i].pointerType == "4002004" ? "selected" : "")>副作用指标/Adverse events</option>
                                                                        </select>
                                                                    </td>
                                                                    <td rowspan="4">
                                                                        <p><input type="button" class="btn btn-default" value="删除该指标Delete" title="删除该指标。Click here to delete the index."></p><p><input type="button" class="btn btn-default" value="增加一项指标More" title="点击这里添加更多指标。Click here to add more."></p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="110">
                                                                        <p class="en">Outcome Name：<span class="required">*</span></p>
                                                                    </td>
                                                                    <td width="180">
                                                                        <p class="en">
                                                                            <input name="IndexNameEn@(i)" type="text" class="form-control input-large input-inline IndexNameEn" maxlength="450" value="@Model.Outcomes[i].outcomeNameEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td width="90"><p class="en">Type：</p></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="t">
                                                                        <p class="cn">测量时间点：</p>
                                                                    </td>
                                                                    <td><p class="cn"><input type="text" class="form-control input-large input-inline" name="txtIndexNamePointTime@(i)" maxlength="450" value="@Model.Outcomes[i].measureTimeCN"></p></td>
                                                                    <td class="t"><p class="cn">测量方法：</p></td>
                                                                    <td><p class="cn"><input type="text" class="form-control input-large input-inline" name="txtIndexNameMeasureMethod@(i)" maxlength="450" value="@Model.Outcomes[i].measureMethodCN"></p></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="t">
                                                                        <p class="en">Measure time point of outcome：</p>
                                                                    </td>
                                                                    <td>
                                                                        <p class="en">
                                                                            <input type="text" class="form-control input-large input-inline" name="txtIndexNamePointTimeEn@(i)" maxlength="450" value="@Model.Outcomes[i].measureTimeEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td class="t"><p class="en">Measure method：</p></td>
                                                                    <td>
                                                                        <p class="en">
                                                                            <input type="text" class="form-control input-large input-inline" name="txtIndexNameMeasureMethodEn@(i)" maxlength="450" value="@Model.Outcomes[i].measureMethodEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    采集人体标本：
                                                </p>
                                                <p class="en">
                                                    Collecting sample(s)
                                                    <br />
                                                    from participants：
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <div id="tbSpecimen" class="noComma subitem">
                                                    @Html.HiddenFor(x => x.hdnSpecimenCount)
                                                    <table width="850" cellspacing="0" cellpadding="0" border="0">
                                                        <tbody>
                                                            <tr>
                                                                <td width="110">
                                                                    <p class="cn">
                                                                        标本中文名：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="240">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline SpecimenName" name="SpecimenName0" maxlength="450" value="" />
                                                                    </p>
                                                                    <p class="cn help-block">
                                                                        每格只填写一个，如"血液"、"唾液"等
                                                                    </p>
                                                                </td>
                                                                <td width="60">
                                                                    <p class="cn">
                                                                        组织：
                                                                    </p>
                                                                </td>
                                                                <td width="270">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline" name="SpecimenTakeFrom0" maxlength="450" value="" />
                                                                    </p>
                                                                    <p class="cn help-block">
                                                                        请说明取自何组织或器官
                                                                    </p>
                                                                </td>
                                                                <td rowspan="4">
                                                                    <p class="cn help-block">
                                                                        点击下面按钮添加更多
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Click below add more
                                                                    </p>
                                                                    <p>
                                                                        <input type="button" class="btn btn-default" title="点击这里添加更多标本。Click here to add more." value="增加一项More"
                                                                               id="btnAddSpecimen" />
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="110">
                                                                    <p class="en">
                                                                        Sample Name：<span class="required">*</span>
                                                                    </p>
                                                                </td>
                                                                <td width="240">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline SpecimenNameEn" name="SpecimenNameEn0" maxlength="450" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Input only one,such as "Blood","Saliva" etc.
                                                                    </p>
                                                                </td>
                                                                <td width="60">
                                                                    <p class="en">
                                                                        Tissue：
                                                                    </p>
                                                                </td>
                                                                <td width="270">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline" name="SpecimenTakeFromEn0" maxlength="450" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Specify take from which tissue or organ
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="110">
                                                                    <p class="cn">
                                                                        人体标本去向
                                                                    </p>
                                                                </td>
                                                                <td width="240" rowspan="2">
                                                                    <select class="SpecimenAfterUse form-control" name="SpecimenAfterUse0">
                                                                        <option value="1012001">使用后销毁/Destruction after use</option>
                                                                        <option value="1012002">使用后保存/Preservation after use</option>
                                                                        <option value="1012003">其它/Others</option>
                                                                    </select>
                                                                    <span class="required">*</span>
                                                                </td>
                                                                <td width="60">
                                                                    <p class="cn">
                                                                        说明
                                                                    </p>
                                                                </td>
                                                                <td width="270">
                                                                    <p class="cn">
                                                                        <input type="text" class="form-control input-large input-inline" name="SpecimenNote0" maxlength="450" value="" />
                                                                    </p>
                                                                    <p class="cn help-block">
                                                                        说明保存年限或其他去向等
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td width="110">
                                                                    <p class="en">
                                                                        Fate of sample：
                                                                    </p>
                                                                </td>
                                                                <td width="60">
                                                                    <p class="en">
                                                                        Note：
                                                                    </p>
                                                                </td>
                                                                <td width="270">
                                                                    <p class="en">
                                                                        <input type="text" class="form-control input-large input-inline" name="SpecimenNoteEn0" maxlength="450" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))" />
                                                                    </p>
                                                                    <p class="en help-block">
                                                                        Specify expected time of preservation or other fate
                                                                    </p>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                    @for (int i = 1; i < Model.CollectingSample.Count; i++)
                                                    {
                                                        <table border="0" cellpadding="0" cellspacing="0" width="850">
                                                            <tbody>
                                                                <tr>
                                                                    <td width="110">
                                                                        <p class="cn">标本中文名：</p>
                                                                    </td>
                                                                    <td width="240"><p class="cn"><input name="SpecimenName@(i)" type="text" class="form-control input-large input-inline SpecimenName" maxlength="450" value="@Model.CollectingSample[i].sampleNameCN"></p></td>
                                                                    <td width="60"><p class="cn">组织：</p></td>
                                                                    <td width="270"><p class="cn"><input name="SpecimenTakeFrom@(i)" type="text" class="form-control input-large input-inline" maxlength="450" value="@Model.CollectingSample[i].tissueCN"></p></td>
                                                                    <td rowspan="4">
                                                                        <p><input type="button" class="btn btn-default" value="删除该标本Delete" title="删除该标本。Click here to delete the sample."></p>
                                                                        <p><input type="button" class="btn btn-default" value="增加一项AddMore" title="点击这里添加更多标本。Click here to add more."></p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="110"><p class="en">Sample Name：</p></td>
                                                                    <td width="240">
                                                                        <p class="en">
                                                                            <input name="SpecimenNameEn@(i)" type="text" class="form-control input-large input-inline SpecimenNameEn" maxlength="450" value="@Model.CollectingSample[i].sampleNameEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                    <td width="60"><p class="en">Tissue：</p></td>
                                                                    <td width="270">
                                                                        <p class="en">
                                                                            <input name="SpecimenTakeFromEn@(i)" type="text" class="form-control input-large input-inline" maxlength="450" value="@Model.CollectingSample[i].tissueEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="110"><p class="cn">人体标本去向：</p></td>
                                                                    <td rowspan="2" width="240">
                                                                        <select name="SpecimenAfterUse@(i)" class="SpecimenAfterUse form-control">
                                                                            <option value="1012001" @(Model.CollectingSample[i].fateSample == "1012001" ? "selected" : "")>使用后销毁/Destruction after use</option>
                                                                            <option value="1012002" @(Model.CollectingSample[i].fateSample == "1012002" ? "selected" : "")>使用后保存/Preservation after use</option>
                                                                            <option value="1012003" @(Model.CollectingSample[i].fateSample == "1012003" ? "selected" : "")>其它/Others</option>
                                                                        </select>
                                                                    </td>
                                                                    <td width="60"><p class="cn">说明：</p></td>
                                                                    <td width="270">
                                                                        <p class="cn">
                                                                            <input name="SpecimenNote@(i)" type="text" class="form-control input-large input-inline" maxlength="450" value="@Model.CollectingSample[i].noteCN">
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td width="110"><p class="en">Fate of sample：</p></td>
                                                                    <td width="60">
                                                                        <p class="en">Note：</p>
                                                                    </td>
                                                                    <td width="270">
                                                                        <p class="en">
                                                                            <input name="SpecimenNoteEn@(i)" type="text" class="form-control input-large input-inline" maxlength="450" value="@Model.CollectingSample[i].noteEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">
                                                                        </p>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label heigthLef">
                                                <p class="cn">
                                                    征募研究对象情况：
                                                </p>
                                            </td>
                                            <td rowspan="2" style="width: 300px;">
                                                @Html.DropDownListFor(x => x.listRecruitmentStatus, ViewBag.listRecruitmentStatusDict as SelectList, "", new { @class = "form-control input-large input-inline" })
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    年龄范围：<span class="required">*</span>
                                                </p>
                                            </td>
                                            <td rowspan="2">
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                <span class="cn">最小</span>
                                                            </td>
                                                            <td rowspan="2">
                                                                @Html.TextBoxFor(x => x.txtMinAge, new { @class = "form-control input-medium input-inline", maxlength = "3" })
                                                            </td>
                                                            <td>
                                                                <span class="cn">岁</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <span class="en">Min age</span>
                                                            </td>
                                                            <td>
                                                                <span class="en">years</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <span class="cn">最大</span>
                                                            </td>
                                                            <td rowspan="2">
                                                                @Html.TextBoxFor(x => x.txtMaxAge, new { @class = "form-control input-medium input-inline", maxlength = "3" })
                                                            </td>
                                                            <td>
                                                                <span class="cn">岁</span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <span class="en">Max age</span>
                                                            </td>
                                                            <td>
                                                                <span class="en">years</span>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Recruiting status：
                                                </p>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Participant age：<span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    性别：
                                                </p>
                                            </td>
                                            <td rowspan="2" colspan="2">
                                                @Html.DropDownListFor(x => x.listGender, ViewBag.listGenderDict as SelectList, "", new { @class = "form-control input-large input-inline" })
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Gender：
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    随机方法（请说明由何人用什么方法产生随机序列）： <span class="required">*</span>
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtGenerafionMethod" cols="20"
                                                              rows="5" name="txtGenerafionMethod">@Model.txtGenerafionMethod</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Randomization Procedure (please state who generates the random number sequence and
                                                    by what method)：<span class="required">*</span>
                                                </p>
                                            </td>
                                            <td colspan="3">
                                                <textarea style="width:80%" class="form-control input-inline" id="txtGenerafionMethodEn" cols="20"
                                                          rows="5" name="txtGenerafionMethodEn" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">@Model.txtGenerafionMethodEn</textarea>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究对象是否签署知情同意书：
                                                </p>
                                                <p class="en">
                                                    Sign the informed consent：
                                                </p>
                                            </td>
                                            <td style="width: 303px;">
                                                <span id="listAgreeToSign">
                                                    @Html.RadioButtonFor(model => model.listAgreeToSign, 1, new { @name = "listAgreeToSign" })
                                                    <label for="listAgreeToSign_0">
                                                        是/Yes
                                                    </label>
                                                    @Html.RadioButtonFor(model => model.listAgreeToSign, 0, new { @name = "listAgreeToSign" })
                                                    <label for="listAgreeToSign_1">
                                                        否/No
                                                    </label>
                                                </span>
                                            </td>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    随访时间：
                                                </p>
                                                <p class="en">
                                                    Length of follow-up (include time point of outcome measure)：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtFollowUpFrequency, new { @class = "form-control input-medium input-inline", maxlength = "50" })
                                                @Html.DropDownListFor(x => x.listFollowUpTimeUnit, ViewBag.listFollowUpTimeUnitDict as SelectList, new { @class = "form-control input-medium input-inline" })
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    隐蔽分组方法和过程：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtConcealment" cols="20"
                                                              rows="5" name="txtConcealment">@Model.txtConcealment</textarea>
                                                </p>
                                                <p class="cn">
                                                    <span class="help-block">请描述您采用的隐蔽分组方法和过程</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Process of allocation
                                                    <br />
                                                    concealment：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtConcealmentEn" cols="20"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')"
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              rows="5" name="txtConcealmentEn">@Model.txtConcealmentEn</textarea>
                                                </p>
                                                <p class="en">
                                                    <span class="help-block">Please describe the process of allocation concealment you will use.</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    盲法：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtBlinding" cols="20" rows="5"
                                                              name="txtBlinding">@Model.txtBlinding</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Blinding：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtBlindingEn" cols="20" rows="5"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')"
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              name="txtBlindingEn">@Model.txtBlindingEn</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    揭盲或破盲原则和方法：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtUncoverPrinciple" cols="20"
                                                              rows="5" name="txtUncoverPrinciple">@Model.txtUncoverPrinciple</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Rules of uncover or
                                                    <br />
                                                    ceasing blinding：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtUncoverPrincipleEn" cols="20"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')"
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              rows="5" name="txtUncoverPrincipleEn">@Model.txtUncoverPrincipleEn</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    统计方法名称：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtStatisticalMethod" cols="20"
                                                              rows="5" name="txtStatisticalMethod">@Model.txtStatisticalMethod</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Statistical method：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtStatisticalMethodEn" cols="20"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')"
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              rows="5" name="txtStatisticalMethodEn">@Model.txtStatisticalMethodEn</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn publicshow">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    试验完成后的统计结果：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtStatisticalEffect" cols="20"
                                                              rows="5" name="txtStatisticalEffect">@Model.txtStatisticalEffect</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en publicshow">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Calculated Results ater
                                                    <br />
                                                    the Study Completed：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtStatisticalEffectEn" cols="20"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'')"
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))"
                                                              rows="5" name="txtStatisticalEffectEn">@Model.txtStatisticalEffectEn</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="publicshow">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    上传试验完成后的统计结果：
                                                </p>
                                                <p class="en">
                                                    Statistical results after completion of the test file upload
                                                </p>
                                            </td>
                                            <td>
                                                <input type="file" style="width: 260px;" class="form-control input-inline" id="fileExperimentalresults" name="fileExperimentalresults" />
                                                <a id="valueRemove5" href="javascript:void(0)" class="bt_operation">清除Clear</a>
                                                &nbsp;&nbsp;
                                                <span style="color:#ff0000" class="cn">注册时无需上传，可在试验完成之后再上传</span>
                                                <span style="color:#ff0000" class="en">There is no need to upload the data during registration, and you can upload it after the completion of trial.</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    是否公开试验完成后的统计结果：
                                                </p>
                                                <p class="en">
                                                    Calculated Results after the Study
                                                    <br />
                                                    Completed public access：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.listStatisticalEffectChiCTRPublic, ViewBag.listStatisticalEffectChiCTRPublicDict as SelectList, "", new { @class = "form-control input-large input-inline" })
                                                &nbsp;&nbsp;
                                                <span style="color:#ff0000" class="cn">注册时无需上传，可在试验完成之后再选择是否公开</span>
                                                <span style="color:#ff0000" class="en">There is no need to upload the data during registration, and you can share data after the completion of trial. </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    全球唯一识别码：
                                                </p>
                                                <p class="en">
                                                    UTN：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.TextBoxFor(x => x.txtUTN, new { @class = "form-control input-large input-inline", maxlength = "50" })
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="ProjetInfo_ms form-group">
                                <table width="100%" cellspacing="0" cellpadding="0" border="0">
                                    <tbody>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    是否共享原始数据：
                                                </p>
                                                <p class="en">
                                                    IPD sharing：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.txtDataCollectionUnit, ViewBag.txtDataCollectionUnitDict as SelectList, "", new { @class = "form-control input-large input-inline" })
                                                <span class="required">*</span>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    <!-- replace by follow -liubo 2016-06-14 -->
                                                    <!--公开原始数据计划（说明：请填入公开原始数据日期和方式，如何让公众查询）：-->
                                                    共享原始数据的方式（说明：请填入公开原始数据日期和方式，如采用网络平台，需填该网络平台名称和网址）：
                                                    <!-- end replace -->
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    @Html.TextBoxFor(x => x.txtDataChargeUnit, new { @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    <!-- replace by follow -liubo 2016-06-14 -->
                                                    <!--Individual Participant Data sharing plan(include metadata and protocol, and the way to allow public to be able to access the data)：-->
                                                    The way of sharing IPD”(include metadata and protocol, If use web-based public database, please provide the url)：
                                                    <!-- end replace -->
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    @Html.TextBoxFor(x => x.txtDataChargeUnitEn, new { onkeyup = "value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') ", onbeforepaste = "clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))", @class = "form-control input-large input-inline", maxlength = "450" })
                                                    <span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    数据采集和管理（说明：数据采集和管理由两部分组成，一为病例记录表(Case Record Form, CRF)，二为电子采集和管理系统(Electronic Data Capture, EDC)，如ResMan即为一种基于互联网的EDC：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtDataAnalysisUnit"
                                                              cols="20" rows="5" name="txtDataAnalysisUnit">@Model.txtDataAnalysisUnit</textarea><span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Data collection and Management (A standard data collection and management system include a CRF and an electronic data capture：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtDataAnalysisUnitEn"
                                                              cols="20" rows="5" name="txtDataAnalysisUnitEn"
                                                              onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') "
                                                              onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">@Model.txtDataAnalysisUnitEn</textarea><span class="required">*</span>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    数据与安全监察委员会：
                                                </p>
                                                <p class="en">
                                                    Data and Safety Monitoring Committee：
                                                </p>
                                            </td>
                                            <td>
                                                @Html.DropDownListFor(x => x.txtDataManagemenBoard, ViewBag.txtDataManagemenBoardDict as SelectList, "", new { @class = "form-control input-large input-inline" })
                                            </td>
                                        </tr>
                                        <tr class="cn">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="cn">
                                                    研究计划书或研究结果报告发表信息<br />（杂志名称、期、卷、页，时间；或网址）：
                                                </p>
                                            </td>
                                            <td>
                                                <p class="cn">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtStudyReport"
                                                              cols="20" rows="5" name="txtStudyReport">@Model.txtStudyReport</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <tr class="en">
                                            <td style="width: 200px;" class="left_title control-label">
                                                <p class="en">
                                                    Publication information of the protocol/research results report<br />(name of the journal, volume, issue, pages, time; or website):
                                                </p>
                                            </td>
                                            <td>
                                                <p class="en">
                                                    <textarea style="width:80%" class="form-control input-inline" id="txtStudyReportEN"
                                                              cols="20" rows="5" name="txtStudyReportEN" onkeyup="value=value.replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,'') " onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[\u4e00-\u9fa5,\u3002\uff0c\u3001\uff1a\uff1f\u02c9\u02c7\u2016\u2236\u0060\u007c\u2026\u2014\uff5e\u3003\u2018\u2019\u201c\u201d\u301d\u301e\u3014\u3015\u3008\u3009\u300a\u300b\u3010\u3011\uff1b]/gi,''))">@Model.txtStudyReportEN</textarea>
                                                </p>
                                            </td>
                                        </tr>
                                        <!-- end replace -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="form-actions">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="row">
                                        <div class="col-md-offset-3 col-md-9">
                                            @Html.Hidden("btn")
                                            <input type="button" class="btn green" id="btnSave" title="保存save" value="保存save" name="btnSave" />
                                            &nbsp;&nbsp;&nbsp;&nbsp;
                                            <input type="button" class="btn green" id="btnAdd" title="提交submit" value="提交submit" name="btnAdd" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6"> </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
}