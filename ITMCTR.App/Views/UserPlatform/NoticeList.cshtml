@model PetaPoco.Page<ITMCTR.App.Models.NoticeListModel>
@{
    Layout = "~/Views/Shared/_U_Layout.cshtml";
}
@section Css{
    <link href="~/Content/assets/global/plugins/datatables/datatables.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.css" rel="stylesheet" type="text/css" />
}
@section Scripts{
    <script src="~/Content/assets/global/scripts/datatable.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/datatables.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/datatables/plugins/bootstrap/datatables.bootstrap.js" type="text/javascript"></script>
}
@using (Html.BeginForm("NoticeList", "UserPlatform", FormMethod.Get))
{
    <!-- BEGIN PAGE BREADCRUMBS -->
    <ul class="page-breadcrumb breadcrumb">
        <li>
            <a href="@Url.Action("Index")">首页/Home</a>
            <i class="fa fa-circle"></i>
        </li>
        <li>
            <span>站内信/Messages</span>
        </li>
    </ul>
    <!-- END PAGE BREADCRUMBS -->
    <!-- BEGIN PAGE CONTENT INNER -->
    <div class="page-content-inner">

        <div class="row">
            <div class="col-md-12">
                <!-- BEGIN EXAMPLE TABLE PORTLET-->
                <div class="portlet light ">
                    <div class="portlet-title">
                        <div class="caption font-dark">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject bold uppercase">站内信/Messages</span>

                        </div>
                        <div class="actions">
                        </div>
                    </div>
                    <div class="portlet-body">
                        <table class="table table-striped table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>操作<br />Operate</th>
                                    <th>标题<br />Title</th>
                                    <th>发送人<br />Sended by</th>
                                    <th>创建时间<br />Time Created</th>
                                    <th>是否已读<br />Read Receipts</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.Items)
                                {
                                    <tr>
                                        <td>
                                            <a href="javascript:DelRecord('@item.Nid')">删除/Delete</a>
                                            <a href="@Url.Action("MessageAction", "UserPlatform",new { Nid=item.Nid} )">查看/Views</a>
                                        </td>
                                        <td>@(System.Threading.Thread.CurrentThread.CurrentUICulture.Name== "en-US"? item.TitleEn : item.Title)</td>
                                        <td>@item.SendUserId</td>
                                        <td>@item.CreateTime</td>
                                        <td>@item.IsRead</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-md-5 col-sm-12">
                            </div>
                            <div class="col-md-7 col-sm-12">
                                <div class="dataTables_paginate paging_bootstrap_number">
                                    @Html.PageLinks(Model, x => Url.Action("NoticeList", new { pageNo = x }))
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END PAGE CONTENT INNER -->
    <div id="divAlertModal" class="modal fade" tabindex="-1" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <p id="pcontent"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" id="btnConfirm" data-dismiss="modal" data-url="" class="btn green update"></button>
                </div>
            </div>
        </div>
    </div>
}
<script>
    function showalert(btn, title, conent, url) {
        $("#divAlertModal").modal();
        var $modal = $('#divAlertModal');
        $modal.find(".modal-title").text(title);//标题
        $modal.find("#pcontent").text(conent);//内容
        $modal.find("#btnConfirm").data("url", url);//Url
        $modal.find("#btnConfirm").text(btn);//按钮文字
        $modal.on('click', '.update', function () {
            if (url && url.length > 0) {
                window.location.href = url;
            }
        });
        $modal.modal();
    }
    function DelRecord(MainKey) {
        if (confirm("是否要删除词条记录/Are you sure to delete the views??")) {
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "@Url.Action("NoticeDel", "UserPlatform")",
                data: { Nid: MainKey },
                success: function (data) {
                    if (data.success) {
                        showalert("确定", "提示", '删除成功', window.location.href)
                    }
                    else {
                        showalert("确定", "提示", data.message, '')
                    }
                }
            });
        }
    }
</script>