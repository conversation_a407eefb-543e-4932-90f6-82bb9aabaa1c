@model ITMCTR.App.Models.LoginModel
@{
    Layout = null;
}

<!DOCTYPE html>
<!--[if IE 8]> <html lang="en" class="ie8 no-js"> <![endif]-->
<!--[if IE 9]> <html lang="en" class="ie9 no-js"> <![endif]-->
<!--[if !IE]><!-->
<html lang="en">
<!--<![endif]-->
<head>
    <title>@Resources.Global.APP_NAME_网站名称</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <link href="http://fonts.googleapis.com/css?family=Open+Sans:400,300,600,700&subset=all" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/simple-line-icons/simple-line-icons.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/assets/global/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="~/Content/css/style.css?v=@UiFun.AppVersion" rel="stylesheet" />

</head>
<body>
    @using (Html.BeginForm("Login", "UserPlatform", FormMethod.Post, new { id = "LoginForm", @class = "form-horizontal", role = "form", @enctype = "multipart/form-data" }))
    {
        <div style="width:100%; height:150px; text-align:center; margin-top:8%; box-sizing:border-box; font-size:28px; color:#ff0000; font-weight:700; letter-spacing:5px;">@ViewData["Content"]</div>

        <div class="login_bg" style="margin-top: 0;">
            <div class="login_box">
                <div class="login_box1"><a href="@Url.Action("Index", "Home")"><img src="~/Content/images/login_logo.png" width="359" height="165" /></a></div>
                <div class="login_box2">
                    <div class="login_cw">
                        @{
                            var validationSummary = Html.ValidationSummary(true);
                            if (!MvcHtmlString.IsNullOrEmpty(validationSummary))
                            {
                                @validationSummary
                            }
                        }
                    </div>
                    <div class="login_cw message">
                    </div>
                    <div style="height: 50px; line-height: 50px; font-size: 20px; font-weight: 700; display: flex;">
                        <div style="width:30%; text-align:left;">@Resources.Global.UR_LOGIN_用户登录</div>
                        <div class="index_en1" style="width:70%; text-align:right;"><a href="@Url.Action("Login","UserPlatform",  new { lang="zh-CN"})" style=" padding:0 10px;">中文</a>/<a href="@Url.Action("Login","UserPlatform",  new { lang="en-US"})" style=" padding:0 10px;">English</a></div>
                    </div>
                    <div class="login_but">
                        <div class="login_but1"><img src="~/Content/images/login1.png" width="16" height="20" /></div>
                        <div>
                            @Html.TextBoxFor(x => x.Username, new { @class = "login_but2", maxlength = "30", required = "required", placeholder = Resources.Global.UR_LOGIN_请输入用户名, autocomplete = "off" })
                        </div>
                    </div>
                    <div class="login_but">
                        <div class="login_but1"><img src="~/Content/images/login2.png" width="16" height="20" /></div>
                        <div>
                            @Html.PasswordFor(x => x.Password, new { @class = "login_but2", maxlength = "20", required = "required", placeholder = Resources.Global.UR_LOGIN_请输入密码, autocomplete = "off" })
                        </div>
                    </div>
                    <div style="display:flex; ">
                        <div class="login_but" style=" width:70%; margin:0;">
                            <div class="login_but1"><img src="~/Content/images/login2.png" width="16" height="20" /></div>
                            <div>
                                @Html.TextBoxFor(x => x.Captcha, new { @class = "login_but2", style = "width:210px;", maxlength = "30", required = "required", placeholder = Resources.Global.UR_LOGIN_请输验证码, autocomplete = "off" })
                            </div>
                        </div>
                        <div style="padding-left:10px; box-sizing:border-box;">
                            <img id="valiCode" style="cursor: pointer;" src="@Url.Action("GetValidateCode")" alt="验证码" width="110" height="50" />
                        </div>
                    </div>
                    <div><input type="submit" class="login_but3" value="@Resources.Global.UR_LOGIN_立即登录" style="cursor:pointer" /></div>
                    <div class="login_but4">
                        <div style="width:50%; text-align:left;"><a href="@Url.Action("Register","UserPlatform")">@Resources.Global.UR_LOGIN_新用户注册</a></div>
                        <div style="width:50%; text-align:right;"><a href="@Url.Action("ForgotPassword","UserPlatform")">@Resources.Global.UR_LOGIN_忘记密码</a></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="login_footer">@Resources.Global.APP_TIP_浏览器兼容</div>
    }
    <script src="~/Content/assets/global/plugins/jquery.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/jquery.validate.min.js" type="text/javascript"></script>
    <script src="~/Content/assets/global/plugins/jquery-validation/js/additional-methods.min.js" type="text/javascript"></script>
    @if (UiFun.IsLang("zh-CN"))
    {
        <script src="~/Content/assets/global/plugins/jquery-validation/js/localization/messages_zh.min.js"></script>
    }
    <script>
        $(function () {
            $('#LoginForm').validate({
                errorElement: 'span', //default input error message container
                errorClass: 'help-block', // default input error message class
                highlight: function (element) { // hightlight error inputs
                    $(element)
                        .closest('div').addClass('has-error'); // set error class to the control group
                },
                success: function (label) {
                    label.closest('div').removeClass('has-error');
                    label.remove();
                },
                errorPlacement: function (error, element) {
                    $(".login_cw").text(error.text());
                },
                submitHandler: function (form) {
                    form.submit(); // form validation success, call ajax form submit
                }
            });
        })
    </script>
</body>
</html>
