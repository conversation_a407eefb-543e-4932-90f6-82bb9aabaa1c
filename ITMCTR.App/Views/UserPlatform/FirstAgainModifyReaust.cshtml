@model List<ITMCTR.Core.Models.SA_ProjectRequestEditFlow>
@{
    Layout = null;
}
@if (Model.Count() > 0)
{
    <table width="100%" cellspacing="0" cellpadding="0" border="0">
        <tbody>
            @{
                foreach (var item in Model)
                {
                    <tr>
                        @if (ViewData["ViewName"] == "Edit")
                        {
                            <td style="width: 200px;" class="left_title control-label">
                                <p class="cn">
                                    修改建议 @Html.Raw(item.RequestTime.Value.ToString("yyyy-MM-dd"))：
                                </p>
                                <p class="en">
                                    Review comment：
                                </p>
                            </td>
                        }
                        else
                        {
                            <td class="left_title control-label col-lg-2">
                                <p class="cn">
                                    修改建议 @Html.Raw(item.RequestTime.Value.ToString("yyyy-MM-dd"))：
                                </p>
                                <p class="en">
                                    Review comment：
                                </p>
                            </td>
                        }
                        <td>
                            @item.EditRequestResult
                        </td>
                    </tr>
                }
            }
        </tbody>
    </table>
}
