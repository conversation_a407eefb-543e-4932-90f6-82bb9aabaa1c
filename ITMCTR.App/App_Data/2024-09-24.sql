alter table SA_NewProject add  isTraditionalMedicine int  
go

ALTER VIEW [dbo].[V_NewProject]
AS
SELECT P.*,
(SELECT MAX(R.CreateTime)  FROM SA_ProjectVerifyTaskFlowRecord AS R WHERE R.Pid = P.Pid) AS flowRecordTime ,
(SELECT MAX(R.CreateTime)  FROM SA_ProjectVerifyTaskFlowRecord AS R WHERE R.Pid = P.Pid AND R.ProjectStatus =2) AS flowRecordBackTime 
FROM SA_NewProject AS P
GO

INSERT [dbo].[SA_SysFuns] ([FunId], [FunName], [FunFloor], [FunKind], [FunURL], [FunNameEN]) VALUES (N'22fe6541-eecd-4a54-934a-3ae1211b463f', N'�Ǵ�ͳҽѧ/Alternative Medicine', N'100100103110', 1, N'/Manager/ProAlternativeMedicine', N'Alternative Medicine')
GO

INSERT INTO [dbo].[SA_RoleFuns] ([RoleFunId] ,[RoleId] ,[FunId]) VALUES ('06fd3b0e-ee99-4419-acac-234ecacf4344','3d64080d-042f-45da-84be-570b360236a6','22fe6541-eecd-4a54-934a-3ae1211b463f')
GO
INSERT INTO [dbo].[SA_RoleFuns] ([RoleFunId] ,[RoleId] ,[FunId]) VALUES ('481340a0-7cad-4eb8-9992-9c4056e352e6','49bb2491-2ba5-492d-9f54-6317bcc05baa','22fe6541-eecd-4a54-934a-3ae1211b463f')
GO