
alter table SA_NewProject add  isTraditionalMedicine int  
go

alter table SA_NewProject add  firstTaskSysUserId uniqueidentifier  
go
ALTER VIEW [dbo].[V_NewProject]
AS
SELECT P.*,
(SELECT MAX(R.CreateTime)  FROM SA_ProjectVerifyTaskFlowRecord AS R WHERE R.Pid = P.Pid) AS flowRecordTime ,
(SELECT MAX(R.CreateTime)  FROM SA_ProjectVerifyTaskFlowRecord AS R WHERE R.Pid = P.Pid AND R.ProjectStatus =2) AS flowRecordBackTime,
(SELECT   MAX(CreateTime) FROM dbo.SA_ProjectVerifyTaskFlowRecord AS R WHERE   (Pid = P.Pid) AND (ProjectStatus =1) and VerifyTaskStatus = 5) AS flowLastSubmitTime ,
(SELECT u.Name  FROM SA_SysUser AS u WHERE u.Uid = P.sendTaskSysUserId) AS sendTaskUser ,
(SELECT u.Name  FROM SA_SysUser AS u WHERE u.Uid = P.executeTaskSysUserId) AS executeTaskUser ,
(SELECT u.Name  FROM SA_SysUser AS u WHERE u.Uid = P.firstTaskSysUserId) AS firstTaskUser
FROM SA_NewProject AS P
GO

INSERT [dbo].[SA_SysFuns] ([FunId], [FunName], [FunFloor], [FunKind], [FunURL], [FunNameEN]) VALUES (N'b873e211-6219-473a-8272-26ee7fab4b16', N'��������Ŀ/To be Send Number', N'100100103103', 1, N'/Manager/ProToBeSendNumber', N'To be Send Number')
GO
INSERT [dbo].[SA_SysFuns] ([FunId], [FunName], [FunFloor], [FunKind], [FunURL], [FunNameEN]) VALUES (N'b873e211-6219-473a-8272-26ee7fab4b78', N'���ж���Ŀ/To be Judged', N'100100103101', 1, N'/Manager/ProTobeJudged', N'To be Judged')
GO
INSERT [dbo].[SA_SysFuns] ([FunId], [FunName], [FunFloor], [FunKind], [FunURL], [FunNameEN]) VALUES (N'b873e211-6219-473a-8272-26ee7fab4b17', N'�������Ŀ/To be Reviewed', N'100100103104', 1, N'/Manager/ProFinalReview', N'To be Reviewed')
GO
INSERT [dbo].[SA_SysRoles] ([RoleId], [RoleName], [RoleDesc], [IsSysRole]) VALUES (N'f2c6f6a5-069a-4f8e-8d11-6d336045529b', N'0���Ա', N'0���Ա', 1)
GO

alter table SA_SysUser add  ParentId nvarchar(50)  
go

