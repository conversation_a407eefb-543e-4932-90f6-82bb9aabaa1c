ALTER VIEW [dbo].[V_NewProject]
AS
SELECT P.*,
(SELECT MAX(R.CreateTime)  FROM SA_ProjectVerifyTaskFlowRecord AS R WHERE R.Pid = P.Pid) AS flowRecordTime ,
(SELECT MAX(R.CreateTime)  FROM SA_ProjectVerifyTaskFlowRecord AS R WHERE R.Pid = P.Pid AND R.ProjectStatus =2) AS flowRecordBackTime 
FROM SA_NewProject AS P
GO


INSERT [dbo].[SA_SysFuns] ([FunId], [FunName], [FunFloor], [FunKind], [FunURL], [FunNameEN]) VALUES (N'3708cb08-0bfb-4c81-bef7-58b64ad709b2', N'��������/Export', N'100100102103', 1, N'/Manager/AllProjectsExport', N'Export')
GO
INSERT [dbo].[SA_SysFuns] ([FunId], [FunName], [Fun<PERSON>loor], [<PERSON><PERSON><PERSON>], [FunURL], [<PERSON>NameEN]) VALUES (N'5cec3e0e-1b78-4a5b-aec6-bb17ba8eb353', N'��Ŀ��Ϣ����/Project Search', N'100100102104', 1, N'/Manager/AllProjectsQuery', N'Project Search')
GO