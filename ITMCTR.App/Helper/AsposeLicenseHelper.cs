using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;

namespace ITMCTR.App.Helper
{
    public sealed class AsposeLicenseHelper
    {
        public static void SetCellLicense()
        {
            var txtLicense = @"<?xml version=""1.0"" encoding=""utf-8""?>
<License>
  <Data>
    <LicensedTo>Shanghai Hudun Information Technology Co., Ltd</LicensedTo>
    <EmailTo><EMAIL></EmailTo>
    <LicenseType>Developer OEM</LicenseType>
    <LicenseNote>Limited to 1 developer.</LicenseNote>
    <OrderID>140714065432</OrderID>
    <UserID>266166</UserID>
    <OEM>This is a redistributable license</OEM>
    <Products>
      <Product>Aspose.Total for .NET</Product>
    </Products>
    <EditionType>Enterprise</EditionType>
    <SerialNumber>ea712420-fbb2-41e7-9ede-39f7b3a43e4a</SerialNumber>
    <SubscriptionExpiry>20150714</SubscriptionExpiry>
    <LicenseVersion>3.0</LicenseVersion>
    <LicenseInstructions>http://www.aspose.com/corporate/purchase/license-instructions.aspx</LicenseInstructions>
  </Data>
  <Signature>Jv8ksvWRYLPzqyQX6J5pAUk4VNc1sogN5xq3ep7Piiy0iGQXg3p8yiGILjPpyCn7G2fkpcnCWPPffoWKqELUc40CiQWsuSmvAuLpvdJCVArt41CpaHNPAUKhrpOzrruzMPWgOB/ByzBVyvUkXl5g2OYHS29gVFlCJ2Ps+p0LnIQ=</Signature>
</License>";
            using (var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(txtLicense)))
            {
                new Aspose.Cells.License().SetLicense(memoryStream);
            }
        }
    }
}