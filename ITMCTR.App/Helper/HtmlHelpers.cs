using Newtonsoft.Json;
using PetaPoco;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace ITMCTR.App
{
    public static class HtmlHelpers
    {
        public static MvcHtmlString PageLinks<T>(this HtmlHelper html, Page<T> pagingInfo, Func<int, string> pageUrl, bool showNum = true)
        {
            StringBuilder htmlstrsb = new StringBuilder();
            int start = (((int)pagingInfo.CurrentPage) / 10 * 10);
            int end = Math.Min((start + 10), (int)pagingInfo.TotalPages);
            var UrlParams = "";
            if (pagingInfo.Context != null && pagingInfo.Context is NameValueCollection)
            {
                var data = ((NameValueCollection)pagingInfo.Context);
                foreach (string item in data.Keys)
                {
                    if (!item.Equals("pageNo"))
                        UrlParams += $"&{item}={data[item]}";
                }
            }

            htmlstrsb.Append("<ul class=\"pagination\">");
            if (pagingInfo.Items.Count > 0)
            {

                TagBuilder tag_li = new TagBuilder("li");
                tag_li.AddCssClass("prev");

                TagBuilder tag_li2 = new TagBuilder("i");
                tag_li2.AddCssClass("fa");
                tag_li2.AddCssClass("fa-angle-left");

                TagBuilder tag_href = new TagBuilder("a");
                tag_href.MergeAttribute("title", "Prev");
                if ((int)pagingInfo.CurrentPage > 0)
                {
                    tag_href.MergeAttribute("href", pageUrl((int)pagingInfo.CurrentPage - 1) + UrlParams);
                }
                tag_href.InnerHtml = tag_li2.ToString();

                tag_li.InnerHtml = tag_href.ToString();
                htmlstrsb.Append(tag_li.ToString());

                if (start < end)
                {
                    for (int i = start; i < end; i++)
                    {
                        if (i != (int)pagingInfo.CurrentPage)
                        {
                            htmlstrsb.Append("<li>");
                            TagBuilder tag = new TagBuilder("a");
                            tag.MergeAttribute("href", pageUrl(i) + UrlParams);
                            tag.InnerHtml = (i + 1).ToString();
                            htmlstrsb.Append(tag.ToString());
                            htmlstrsb.Append("</li>");
                        }
                        else
                        {
                            htmlstrsb.Append("<li class=\"active\">");
                            TagBuilder tag = new TagBuilder("a");
                            tag.MergeAttribute("href", pageUrl(i) + UrlParams);
                            tag.InnerHtml = (i + 1).ToString();
                            tag.AddCssClass("on");
                            htmlstrsb.Append(tag.ToString());
                            htmlstrsb.Append("</li>");
                        }
                    }
                }

                TagBuilder tag_li_next = new TagBuilder("li");
                tag_li_next.AddCssClass("next");

                TagBuilder tag_li2_next = new TagBuilder("i");
                tag_li2_next.AddCssClass("fa");
                tag_li2_next.AddCssClass("fa-angle-right");

                TagBuilder tag_href_next = new TagBuilder("a");
                tag_href_next.MergeAttribute("title", "Next");
                if ((int)pagingInfo.CurrentPage < (int)pagingInfo.TotalPages - 1)
                {
                    tag_href_next.MergeAttribute("href", pageUrl((int)pagingInfo.CurrentPage + 1) + UrlParams);
                }
                tag_href_next.InnerHtml = tag_li2_next.ToString();

                tag_li_next.InnerHtml = tag_href_next.ToString();
                htmlstrsb.Append(tag_li_next.ToString());

                htmlstrsb.Append("</ul>");
                if (showNum)
                {
                    TagBuilder tag_total = new TagBuilder("span");
                    if (CultureInfo.CurrentUICulture.Name.Equals("zh-CN"))
                    {
                        tag_total.InnerHtml = $"共{pagingInfo.TotalPages}页,{pagingInfo.TotalItems}条记录";
                    }
                    else
                    {
                        tag_total.InnerHtml = $"Total{pagingInfo.TotalPages}Pages,{pagingInfo.TotalItems}Records";
                    }
                    tag_total.AddCssClass("label label-sm label-info");
                    htmlstrsb.Append(tag_total.ToString());
                }
            }

            return MvcHtmlString.Create(htmlstrsb.ToString());
        }
        public static string ToJson(this HtmlHelper html, object Obj)
        {
            return JsonConvert.SerializeObject(Obj);
        }
    }
}