using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    public class SA_SysUserListModel
    {
		 public string Uid { get; set; }
		 public string Username { get; set; }
		 public string Password { get; set; }
		 public string Name { get; set; }
		 public string Email { get; set; }
		 public string RegDate { get; set; }
		 public string RegIP { get; set; }
		 public string LastLoginDate { get; set; }
		 public string LastLoginIp { get; set; }
		 public string Sex { get; set; }
		 public string Phone { get; set; }
		 public string Country { get; set; }
		 public string CellPhone { get; set; }
		 public string RegUnit { get; set; }
		 public string RegAddress { get; set; }
		 public string SysRoleId { get; set; }
		 public string Photograph { get; set; }
		public int? IsSysUser { get; set; }
	}
}