using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    public class NewsTypeEditModel : Core.Models.SA_NewsType
    {
        public bool IsViewOption
        {
            get
            {
                return IsView == 1;
            }
            set
            {
                if (value)
                    IsView = 1;
                else
                    IsView = 0;
            }
        }
        public bool IsIndexViewOption
        {
            get
            {
                return IsIndexView == 1;
            }
            set
            {
                if (value)
                    IsIndexView = 1;
                else
                    IsIndexView = 0;
            }
        }
    }
}