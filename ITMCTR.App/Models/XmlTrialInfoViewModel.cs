using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Xml.Serialization;

namespace ITMCTR.App.Models
{
    [XmlRoot(ElementName = "trials")]
    public class ExportXmlInfo
    {
        [XmlAttribute(AttributeName = "subjects")]
        public int Subjects
        {
            get; set;
        }

        [XmlElement("Trial")]
        public List<TrialInfo> Triall { get; set; }
    }
    public class TrialInfo
    {
        private MainStrt main = new MainStrt();

        private ContactsStr _contacts = new ContactsStr();

        private CountriesStr _countries = new CountriesStr();

        private CriteriaStr _criteria = new CriteriaStr();

        private Health_condition_codeStr _health_condition_code = new Health_condition_codeStr();

        private Health_condition_keywordStr _health_condition_keyword = new Health_condition_keywordStr();

        private Intervention_codeStr _intervention_code = new Intervention_codeStr();

        private Intervention_keywordStr _intervention_keyword = new Intervention_keywordStr();

        private Primary_outcomeStr _primary_outcome = new Primary_outcomeStr();

        private Secondary_outcomeStr _secondary_outcome = new Secondary_outcomeStr();

        private Secondary_sponsorStr _secondary_sponsor = new Secondary_sponsorStr();

        private Secondary_idsStr _secondary_ids = new Secondary_idsStr();

        private Source_supportStr _source_support = new Source_supportStr();

        private Ethics_Reviews _ethics_reviews = new Ethics_Reviews();

        [XmlElement("main")]
        public MainStrt Main
        {
            get
            {
                return main;
            }
            set
            {
                main = value;
            }
        }

        [XmlElement("contacts")]
        public ContactsStr Contacts
        {
            get
            {
                return _contacts;
            }
            set
            {
                _contacts = value;
            }
        }

        [XmlElement("countries")]
        public CountriesStr Countries
        {
            get
            {
                return _countries;
            }
            set
            {
                _countries = value;
            }
        }

        [XmlElement("criteria")]
        public CriteriaStr Criteria
        {
            get
            {
                return _criteria;
            }
            set
            {
                _criteria = value;
            }
        }

        [XmlElement("health_condition_code")]
        public Health_condition_codeStr Health_condition_code
        {
            get
            {
                return _health_condition_code;
            }
            set
            {
                _health_condition_code = value;
            }
        }

        [XmlElement("health_condition_keyword")]
        public Health_condition_keywordStr Health_condition_keyword
        {
            get
            {
                return _health_condition_keyword;
            }
            set
            {
                _health_condition_keyword = value;
            }
        }

        [XmlElement("intervention_code")]
        public Intervention_codeStr Intervention_code
        {
            get
            {
                return _intervention_code;
            }
            set
            {
                _intervention_code = value;
            }
        }

        [XmlElement("intervention_keyword")]
        public Intervention_keywordStr Intervention_keyword
        {
            get
            {
                return _intervention_keyword;
            }
            set
            {
                _intervention_keyword = value;
            }
        }

        [XmlElement("primary_outcome")]
        public Primary_outcomeStr Primary_outcome
        {
            get
            {
                return _primary_outcome;
            }
            set
            {
                _primary_outcome = value;
            }
        }

        [XmlElement("secondary_outcome")]
        public Secondary_outcomeStr Secondary_outcome
        {
            get
            {
                return _secondary_outcome;
            }
            set
            {
                _secondary_outcome = value;
            }
        }

        [XmlElement("secondary_sponsor")]
        public Secondary_sponsorStr Secondary_sponsor
        {
            get
            {
                return _secondary_sponsor;
            }
            set
            {
                _secondary_sponsor = value;
            }
        }

        [XmlElement("secondary_ids")]
        public Secondary_idsStr Secondary_ids
        {
            get
            {
                return _secondary_ids;
            }
            set
            {
                _secondary_ids = value;
            }
        }

        [XmlElement("source_support")]
        public Source_supportStr Source_support
        {
            get
            {
                return _source_support;
            }
            set
            {
                _source_support = value;
            }
        }

        [XmlElement("ethics_reviews")]
        public Ethics_Reviews Ethics_Reviews
        {
            get
            {
                return _ethics_reviews;
            }
            set
            {
                _ethics_reviews = value;
            }
        }
    }
    public class MainStrt
    {
        private string trial_id = "";

        private string utrn = "";

        private string reg_name = "";

        private string date_registration = "";

        private string primary_sponsor = "";

        private string public_title = "";

        private string acronym = "";

        private string scientific_title = "";

        private string scientific_acronym = "";

        private string date_enrolment = "";

        private string type_enrolment = "";

        private string target_size = "";

        private string recruitment_status = "";

        private string _url = "";

        private string study_type = "";

        private string study_design = "";

        private string phase = "";

        private string hc_freetext = "";

        private string i_freetext = "";

        private string results_actual_enrolment = "";

        private string results_date_completed = "";

        private string results_url_link = "";

        private string results_summary = "";

        private string results_date_posted = "";

        private string results_date_first_publication = "";

        private string results_baseline_char = "";

        private string results_participant_flow = "";

        private string results_adverse_events = "";

        private string results_outcome_measures = "";

        private string results_url_protocol = "";

        private string results_IPD_plan = "";

        private string results_IPD_description = "";


        [XmlElement("trial_id")]
        public string Trial_id
        {
            get
            {
                return trial_id;
            }
            set
            {
                trial_id = value;
            }
        }

        [XmlElement("utrn")]
        public string Utrn
        {
            get
            {
                return utrn;
            }
            set
            {
                utrn = value;
            }
        }

        [XmlElement("reg_name")]
        public string Reg_name
        {
            get
            {
                return reg_name;
            }
            set
            {
                reg_name = value;
            }
        }

        [XmlElement("date_registration")]
        public string Date_registration
        {
            get
            {
                return date_registration;
            }
            set
            {
                date_registration = value;
            }
        }

        [XmlElement("primary_sponsor")]
        public string Primary_sponsor
        {
            get
            {
                return primary_sponsor;
            }
            set
            {
                primary_sponsor = value;
            }
        }

        [XmlElement("public_title")]
        public string Public_title
        {
            get
            {
                return public_title;
            }
            set
            {
                public_title = value;
            }
        }

        [XmlElement("acronym")]
        public string Acronym
        {
            get
            {
                return acronym;
            }
            set
            {
                acronym = value;
            }
        }

        [XmlElement("scientific_title")]
        public string Scientific_title
        {
            get
            {
                return scientific_title;
            }
            set
            {
                scientific_title = value;
            }
        }

        [XmlElement("Scientific_acronym")]
        public string Scientific_acronym
        {
            get
            {
                return scientific_acronym;
            }
            set
            {
                scientific_acronym = value;
            }
        }

        [XmlElement("date_enrolment")]
        public string Date_enrolment
        {
            get
            {
                return date_enrolment;
            }
            set
            {
                date_enrolment = value;
            }
        }

        [XmlElement("type_enrolment")]
        public string Type_enrolment
        {
            get
            {
                return type_enrolment;
            }
            set
            {
                type_enrolment = value;
            }
        }

        [XmlElement("target_size")]
        public string Target_size
        {
            get
            {
                return target_size;
            }
            set
            {
                target_size = value;
            }
        }

        [XmlElement("recruitment_status")]
        public string Recruitment_status
        {
            get
            {
                return recruitment_status;
            }
            set
            {
                recruitment_status = value;
            }
        }

        [XmlElement("url")]
        public string Url
        {
            get
            {
                return _url;
            }
            set
            {
                _url = value;
            }
        }

        [XmlElement("study_type")]
        public string Study_type
        {
            get
            {
                return study_type;
            }
            set
            {
                study_type = value;
            }
        }

        [XmlElement("study_design")]
        public string Study_design
        {
            get
            {
                return study_design;
            }
            set
            {
                study_design = value;
            }
        }

        [XmlElement("phase")]
        public string Phase
        {
            get
            {
                return phase;
            }
            set
            {
                phase = value;
            }
        }

        [XmlElement("hc_freetext")]
        public string Hc_freetext
        {
            get
            {
                return hc_freetext;
            }
            set
            {
                hc_freetext = value;
            }
        }

        [XmlElement("i_freetext")]
        public string I_freetext
        {
            get
            {
                return i_freetext;
            }
            set
            {
                i_freetext = value;
            }
        }

        [XmlElement("results_actual_enrolment")]
        public string Results_Actual_Enrolment
        {
            get
            {
                return results_actual_enrolment;
            }
            set
            {
                results_actual_enrolment = value;
            }
        }

        [XmlElement("results_date_completed")]
        public string Results_date_completed
        {
            get
            {
                return results_date_completed;
            }
            set
            {
                results_date_completed = value;
            }
        }

        [XmlElement("results_url_link")]
        public string Results_url_link
        {
            get
            {
                return results_url_link;
            }
            set
            {
                results_url_link = value;
            }
        }

        [XmlElement("results_summary")]
        public string Results_summary
        {
            get
            {
                return results_summary;
            }
            set
            {
                results_summary = value;
            }
        }

        [XmlElement("results_date_posted")]
        public string Results_date_posted
        {
            get
            {
                return results_date_posted;
            }
            set
            {
                results_date_posted = value;
            }
        }

        [XmlElement("results_date_first_publication")]
        public string Results_date_first_publication
        {
            get
            {
                return results_date_first_publication;
            }
            set
            {
                results_date_first_publication = value;
            }
        }

        [XmlElement("results_baseline_char")]
        public string Results_baseline_char
        {
            get
            {
                return results_baseline_char;
            }
            set
            {
                results_baseline_char = value;
            }
        }

        [XmlElement("results_participant_flow")]
        public string Results_participant_flow
        {
            get
            {
                return results_participant_flow;
            }
            set
            {
                results_participant_flow = value;
            }
        }

        [XmlElement("results_adverse_events")]
        public string Results_adverse_events
        {
            get
            {
                return results_adverse_events;
            }
            set
            {
                results_adverse_events = value;
            }
        }

        [XmlElement("results_outcome_measures")]
        public string Results_outcome_measures
        {
            get
            {
                return results_outcome_measures;
            }
            set
            {
                results_outcome_measures = value;
            }
        }

        [XmlElement("results_url_protocol")]
        public string Results_url_protocol
        {
            get
            {
                return results_url_protocol;
            }
            set
            {
                results_url_protocol = value;
            }
        }

        [XmlElement("results_IPD_plan")]
        public string Results_IPD_plan
        {
            get
            {
                return results_IPD_plan;
            }
            set
            {
                results_IPD_plan = value;
            }
        }

        [XmlElement("results_IPD_description")]
        public string Results_IPD_description
        {
            get
            {
                return results_IPD_description;
            }
            set
            {
                results_IPD_description = value;
            }
        }
    }
    public class ContactStr
    {
        private string type = "";

        private string firstname = "";

        private string middlename = "";

        private string lastname = "";

        private string address = "";

        private string city = "";

        private string country1 = "";

        private string zip = "";

        private string telephone = "";

        private string email = "";

        private string affiliation = "";

        [XmlElement("type")]
        public string Type
        {
            get
            {
                return type;
            }
            set
            {
                type = value;
            }
        }

        [XmlElement("firstname")]
        public string Firstname
        {
            get
            {
                return firstname;
            }
            set
            {
                firstname = value;
            }
        }

        [XmlElement("middlename")]
        public string Middlename
        {
            get
            {
                return middlename;
            }
            set
            {
                middlename = value;
            }
        }

        [XmlElement("lastname")]
        public string Lastname
        {
            get
            {
                return lastname;
            }
            set
            {
                lastname = value;
            }
        }

        [XmlElement("address")]
        public string Address
        {
            get
            {
                return address;
            }
            set
            {
                address = value;
            }
        }

        [XmlElement("city")]
        public string City
        {
            get
            {
                return city;
            }
            set
            {
                city = value;
            }
        }

        [XmlElement("country1")]
        public string Country1
        {
            get
            {
                return country1;
            }
            set
            {
                country1 = value;
            }
        }

        [XmlElement("zip")]
        public string Zip
        {
            get
            {
                return zip;
            }
            set
            {
                zip = value;
            }
        }

        [XmlElement("telephone")]
        public string Telephone
        {
            get
            {
                return telephone;
            }
            set
            {
                telephone = value;
            }
        }

        [XmlElement("email")]
        public string Email
        {
            get
            {
                return email;
            }
            set
            {
                email = value;
            }
        }

        [XmlElement("affiliation")]
        public string Affiliation
        {
            get
            {
                return affiliation;
            }
            set
            {
                affiliation = value;
            }
        }
    }
    public class ContactsStr
    {
        private ContactStr[] contact = new ContactStr[0];

        [XmlElement("contact")]
        public ContactStr[] Contact
        {
            get
            {
                return contact;
            }
            set
            {
                contact = value;
            }
        }
    }
    public class CountriesStr
    {
        private string country2 = "";

        [XmlElement("country2")]
        public string Country2
        {
            get
            {
                return country2;
            }
            set
            {
                country2 = value;
            }
        }
    }
    public class CriteriaStr
    {
        private string inclusion_criteria = "";

        private string agemin = "";

        private string agemax = "";

        private string gender = "";

        private string exclusion_criteria = "";

        [XmlElement("inclusion_criteria")]
        public string Inclusion_criteria
        {
            get
            {
                return inclusion_criteria;
            }
            set
            {
                inclusion_criteria = value;
            }
        }

        [XmlElement("agemin")]
        public string Agemin
        {
            get
            {
                return agemin;
            }
            set
            {
                agemin = value;
            }
        }

        [XmlElement("agemax")]
        public string Agemax
        {
            get
            {
                return agemax;
            }
            set
            {
                agemax = value;
            }
        }

        [XmlElement("gender")]
        public string Gender
        {
            get
            {
                return gender;
            }
            set
            {
                gender = value;
            }
        }

        [XmlElement("exclusion_criteria")]
        public string Exclusion_criteria
        {
            get
            {
                return exclusion_criteria;
            }
            set
            {
                exclusion_criteria = value;
            }
        }
    }
    public class Health_condition_codeStr
    {
        private string hc_code = "";

        [XmlElement("hc_code")]
        public string Hc_code
        {
            get
            {
                return hc_code;
            }
            set
            {
                hc_code = value;
            }
        }
    }
    public class Health_condition_keywordStr
    {
        private string hc_code = "";

        [XmlElement("hc_keyword")]
        public string Hc_code
        {
            get
            {
                return hc_code;
            }
            set
            {
                hc_code = value;
            }
        }
    }
    public class Intervention_codeStr
    {
        private string i_code = "";

        [XmlElement("i_code")]
        public string I_code
        {
            get
            {
                return i_code;
            }
            set
            {
                i_code = value;
            }
        }
    }
    public class Intervention_keywordStr
    {
        private string i_keyword = "";

        [XmlElement("i_keyword")]
        public string I_keyword
        {
            get
            {
                return i_keyword;
            }
            set
            {
                i_keyword = value;
            }
        }
    }
    public class Primary_outcomeStr
    {
        private string prim_outcome = "";

        [XmlElement("prim_outcome")]
        public string Prim_outcome
        {
            get
            {
                return prim_outcome;
            }
            set
            {
                prim_outcome = value;
            }
        }
    }
    public class Secondary_idsStr
    {
        private List<Secondary_idStr> secondary_id = new List<Secondary_idStr>();
        [XmlElement("secondary_id")]
        public List<Secondary_idStr> Secondary_id
        {
            get
            {
                return secondary_id;
            }
            set
            {
                secondary_id = value;
            }
        }
    }
    public class Secondary_idStr
    {
        private string sec_id = "";

        private string issuing_authority = "";

        [XmlElement("sec_id")]
        public string Sec_id
        {
            get
            {
                return sec_id;
            }
            set
            {
                sec_id = value;
            }
        }

        [XmlElement("issuing_authority")]
        public string Issuing_authority
        {
            get
            {
                return issuing_authority;
            }
            set
            {
                issuing_authority = value;
            }
        }
    }
    public class Secondary_outcomeStr
    {
        private string sec_outcome = "";

        [XmlElement("sec_outcome")]
        public string Sec_outcome
        {
            get
            {
                return sec_outcome;
            }
            set
            {
                sec_outcome = value;
            }
        }
    }
    public class Secondary_sponsorStr
    {
        private string sponsor_name = "";

        [XmlElement("sponsor_name")]
        public string Sponsor_name
        {
            get
            {
                return sponsor_name;
            }
            set
            {
                sponsor_name = value;
            }
        }
    }
    public class Source_supportStr
    {
        private string source_name = "";

        [XmlElement("source_name")]
        public string Source_name
        {
            get
            {
                return source_name;
            }
            set
            {
                source_name = value;
            }
        }
    }
    public class Ethics_Review
    {
        private string status = "";

        private string approval_date = "";

        private string contact_name = "";

        private string contact_address = "";

        private string contact_phone = "";

        private string contact_email = "";

        [XmlElement("status")]
        public string Status
        {
            get
            {
                return status;
            }
            set
            {
                status = value;
            }
        }

        [XmlElement("approval_date")]
        public string Approval_date
        {
            get
            {
                return approval_date;
            }
            set
            {
                approval_date = value;
            }
        }

        [XmlElement("contact_name")]
        public string Contact_name
        {
            get
            {
                return contact_name;
            }
            set
            {
                contact_name = value;
            }
        }

        [XmlElement("contact_address")]
        public string Contact_address
        {
            get
            {
                return contact_address;
            }
            set
            {
                contact_address = value;
            }
        }

        [XmlElement("contact_phone")]
        public string Contact_phone
        {
            get
            {
                return contact_phone;
            }
            set
            {
                contact_phone = value;
            }
        }

        [XmlElement("contact_email")]
        public string Contact_email
        {
            get
            {
                return contact_email;
            }
            set
            {
                contact_email = value;
            }
        }
    }
    public class Ethics_Reviews
    {
        private Ethics_Review ethics_review = new Ethics_Review();

        [XmlElement("ethics_review")]
        public Ethics_Review Ethics_review
        {
            get
            {
                return ethics_review;
            }
            set
            {
                ethics_review = value;
            }
        }
    }

    public class AllProjectsExportModel
    {
        public Guid Pid { get; set; }
        public string 注册号 { get; set; }
        public string 注册时间 { get; set; }
        public string 注册状态 { get; set; }
        public string 注册题目 { get; set; }
        public string 注册题目EN { get; set; }
        public string 正式科学名 { get; set; }
        public string 正式科学名EN { get; set; }
        public string 申请注册联系人 { get; set; }
        public string 申请注册联系人EN { get; set; }
        public string 研究负责人 { get; set; }
        public string 研究负责人EN { get; set; }
        public string 注册联系人电话 { get; set; }
        public string 试验负责人电话 { get; set; }
        public string 申请注册联系人传真 { get; set; }
        public string 研究负责人传真 { get; set; }
        public string 申请注册联系人电子邮件 { get; set; }
        public string 研究负责人电子邮件 { get; set; }
        public string 申请单位网址 { get; set; }
        public string 研究负责人网址 { get; set; }
        public string 申请注册联系人通讯地址 { get; set; }
        public string 申请注册联系人通讯地址EN { get; set; }
        public string 研究负责人通讯地址 { get; set; }
        public string 研究负责人通讯地址EN { get; set; }
        public string 申请注册联系人邮政编码 { get; set; }
        public string 研究负责人邮政编码 { get; set; }
        public string 申请人所在单位 { get; set; }
        public string 研究负责人所在单位 { get; set; }
        public string 研究负责人所在单位EN { get; set; }
        public string 是否获伦理委员会批准 { get; set; }
        public string 伦理委员会批件文号 { get; set; }
        public string 批准本研究的伦理委员会名称 { get; set; }
        public string 批准本研究的伦理委员会名称EN { get; set; }
        public string 伦理委员会批准日期 { get; set; }
        public string 伦理委员会联系人 { get; set; }
        public string 伦理委员会联系人EN { get; set; }
        public string 伦理委员会联系地址 { get; set; }
        public string 伦理委员会联系地址EN { get; set; }
        public string 伦理委员会联系人电话 { get; set; }
        public string 伦理委员会联系人邮箱 { get; set; }
        public string 国家药监局批准文号 { get; set; }
        public string 国家药监局批准附件 { get; set; }
        public string 国家药监局批准日期 { get; set; }
        public string 研究计划书 { get; set; }
        public string 研究实施负责单位 { get; set; }
        public string 研究实施负责单位EN { get; set; }
        public string 研究实施负责单位地址 { get; set; }
        public string 研究实施负责单位地址EN { get; set; }
        public string 经费或物资来源 { get; set; }
        public string 经费或物资来源EN { get; set; }
        public string 研究疾病 { get; set; }
        public string 研究疾病EN { get; set; }
        public string 研究疾病代码 { get; set; }
        public string 研究类型 { get; set; }
        public string 研究设计 { get; set; }
        public string 研究所处阶段 { get; set; }
        public string 研究目的 { get; set; }
        public string 研究目的EN { get; set; }
        public string 药物成份或治疗方案详述 { get; set; }
        public string 药物成份或治疗方案详述EN { get; set; }
        public string 纳入标准 { get; set; }
        public string 纳入标准EN { get; set; }
        public string 排除标准 { get; set; }
        public string 排除标准EN { get; set; }
        public string 研究实施时间开始 { get; set; }
        public string 研究实施时间结束 { get; set; }
        public string 征募观察对象时间开始 { get; set; }
        public string 征募观察对象时间结束 { get; set; }
        public string 样本总量 { get; set; }
        public string 征募研究对象情况 { get; set; }
        public string 年龄范围最小 { get; set; }
        public string 年龄范围最大 { get; set; }
        public string 随机方法 { get; set; }
        public string 随机方法EN { get; set; }
        public string 研究对象是否签署知情同意书 { get; set; }
        public string 随访时间 { get; set; }
        public string 随访时间单位 { get; set; }
        public string 隐蔽分组方法和过程 { get; set; }
        public string 隐蔽分组方法和过程EN { get; set; }
        public string 盲法 { get; set; }
        public string 盲法EN { get; set; }
        public string 揭盲或破盲原则和方法 { get; set; }
        public string 揭盲或破盲原则和方法EN { get; set; }
        public string 统计方法名称 { get; set; }
        public string 统计方法名称EN { get; set; }
        public string 试验完成后的统计结果 { get; set; }
        public string 试验完成后的统计结果EN { get; set; }
        public string 是否公开试验完成后的统计结果 { get; set; }
        public string 共享原始数据的方式 { get; set; }
        public string 共享原始数据的方式EN { get; set; }
        public string 数据采集和管理 { get; set; }
        public string 数据采集和管理EN { get; set; }
        public string 研究计划书或研究结果报告发表信息 { get; set; }
        public string 研究计划书或研究结果报告发表信息EN { get; set; }
        public string 全球唯一识别码 { get; set; }
        public string 数据与安全监察委员会 { get; set; }
        public string 是否共享原始数据 { get; set; }
        public string 二级注册机注册号 { get; set; }
        public string 其它机构的注册号 { get; set; }
    }

    public class AllProjectsExport1Model
    {
        public Guid Pid { get; set; }
        public string 注册号 { get; set; }
        public string 标本中文名 { get; set; }
        public string 标本中文名EN { get; set; }
        public string 组织 { get; set; }
        public string 组织EN { get; set; }
        public string 人体标本去向 { get; set; }
        public string 说明 { get; set; }
        public string 说明EN { get; set; }
    }
    public class AllProjectsExport2Model
    {
        public Guid Pid { get; set; }
        public string 注册号 { get; set; }
        public string 指标中文名 { get; set; }
        public string 指标英文名 { get; set; }
        public string 指标类型 { get; set; }
        public string 测量时间点 { get; set; }
        public string 测量时间点EN { get; set; }
        public string 测量方法 { get; set; }
        public string 测量方法EN { get; set; }
    }
    public class AllProjectsExport3Model
    {
        public Guid Pid { get; set; }
        public string 注册号 { get; set; }
        public string 组别 { get; set; }
        public string 组别EN { get; set; }
        public string 样本量 { get; set; }
        public string 干预措施 { get; set; }
        public string 干预措施EN { get; set; }
        public string 干预措施代码 { get; set; }
    }
    public class AllProjectsExport4Model
    {
        public Guid Pid { get; set; }
        public string 注册号 { get; set; }
        public string 国家 { get; set; }
        public string 国家EN { get; set; }
        public string 省 { get; set; }
        public string 省EN { get; set; }
        public string 市 { get; set; }
        public string 市EN { get; set; }
        public string 单位 { get; set; }
        public string 单位EN { get; set; }
        public string 具体地址 { get; set; }
        public string 具体地址EN { get; set; }
    }
    public class AllProjectsExport5Model
    {
        public Guid Pid { get; set; }
        public string 注册号 { get; set; }
        public string 国家 { get; set; }
        public string 国家EN { get; set; }
        public string 省 { get; set; }
        public string 省EN { get; set; }
        public string 市 { get; set; }
        public string 市EN { get; set; }
        public string 单位 { get; set; }
        public string 单位EN { get; set; }
        public string 单位级别 { get; set; }
        public string 单位级别EN { get; set; }
    }
    public class AllProjectsExport6Model
    {
        public Guid Pid { get; set; }
        public string 注册号 { get; set; }
        public string 金标准或参考标准 { get; set; }
        public string 金标准或参考标准EN { get; set; }
        public string 指标试验 { get; set; }
        public string 指标试验EN { get; set; }
        public string 目标人群 { get; set; }
        public string 目标人群EN { get; set; }
        public string 例数 { get; set; }
        public string 容易混淆的疾病人群 { get; set; }
        public string 容易混淆的疾病人群EN { get; set; }
        public string 例数2 { get; set; }
    }
}