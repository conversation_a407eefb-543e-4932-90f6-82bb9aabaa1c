using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    [Serializable]
    public class UserProfile
    {
        public UserProfile()
        {

        }
        public Guid UserID { get; set; }
        public bool IsLogin { get { return UserModel != null; } }
        public Core.Models.SA_Users UserModel { get; set; }
        public bool InitUser(Guid User_id)
        {
            Core.BLL.SA_UsersBLL bll = new Core.BLL.SA_UsersBLL();
            UserModel = bll.GetById(User_id);
            if (UserModel != null)
            {
                this.UserID = User_id;
                SaveProfile();
            }
            else
                return false;
            return true;
        }
        public static UserProfile CurrentProfile
        {
            get
            {
                UserProfile profile = new UserProfile();
                if (HttpContext.Current.Session["CurrentProfile"] != null)
                {
                    profile = (UserProfile)HttpContext.Current.Session["CurrentProfile"];
                }
                return profile;
            }
        }
        public void SaveProfile()
        {
            HttpContext.Current.Session["CurrentProfile"] = this;
        }
    }
}