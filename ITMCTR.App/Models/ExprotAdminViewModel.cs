using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    public class ExprotAdminViewModel
    {
        public Guid? Pid { get; set; }
        public string 注册题目 { get; set; }
        public string 注册联系人 { get; set; }
        public string 注册联系人电话 { get; set; }
        public string 试验负责人 { get; set; }
        public string 试验负责人电话 { get; set; }
        public string 试验负责人所在单位 { get; set; }
        public string 已发号的注册号码 { get; set; }
        public DateTime? 已发号时间 { get; set; }
        public DateTime? 征募研究对象开始时间 { get; set; }
        public DateTime? 首次提交时间 { get; set; }
        public string 流程状态 { get; set; }
        public string 审核状态 { get; set; }
        public string 目前审核人 { get; set; }
        public DateTime? 一审分配时间 { get; set; }
        public DateTime? 二审最后一次审核时间 { get; set; }
        public DateTime? 用户最后一次提交时间 { get; set; }
        public DateTime? 再修改申请记录时间 { get; set; }
        public string 再修改申请记录原因 { get; set; }
    }
}