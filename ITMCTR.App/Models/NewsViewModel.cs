using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    public class NewsViewModel
    {
        public Guid Nid { get; set; }
        public string Title { get; set; }
        public string Subtitle { get; set; }
        public string Content { get; set; }
        public DateTime? ReleaseTime { get; set; }
        public string Author { get; set; }
        public string AuthorUnit { get; set; }
        public int? IsRelease { get; set; }
        public int? IsDeleted { get; set; }
        public int? IsViewIndex { get; set; }
        public string TypeName { get; set; }
    }
}