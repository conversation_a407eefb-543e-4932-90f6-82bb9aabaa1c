using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    public class WebsiteInformationViewModel : Core.Models.SA_WebsiteInformation
    {
        public bool IsReleaseOption
        {
            get
            {
                return IsRelease == 1;
            }
            set
            {
                if (value)
                    IsRelease = 1;
                else
                    IsRelease = 0;
            }
        }
        public bool IsViewIndexOption
        {
            get
            {
                return IsViewIndex == 1;
            }
            set
            {
                if (value)
                    IsViewIndex = 1;
                else
                    IsViewIndex = 0;
            }
        }
        public bool IsTopOption
        {
            get
            {
                return IsTop == 1;
            }
            set
            {
                if (value)
                    IsTop = 1;
                else
                    IsTop = 0;
            }
        }
    }
}