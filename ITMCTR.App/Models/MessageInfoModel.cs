using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    public class MessageInfoModel
    {
        public MessageInfoModel()
        {

        }
        public MessageInfoModel(bool error,
            string title,
            string content,
            string redictUrl = "",
            string buttonText = "确定")
        {
            this.IsError = error;
            this.MessageBody = content;
            this.MessageTitle = title;
            this.TargetUrl = redictUrl;
            this.ButtonTitle = buttonText;
        }
        public string MessageTitle { set; get; }
        public string MessageBody { set; get; }
        public string ButtonTitle { set; get; }
        public bool IsError { get; set; }
        public string TargetUrl { get; set; } = "/";
    }
}