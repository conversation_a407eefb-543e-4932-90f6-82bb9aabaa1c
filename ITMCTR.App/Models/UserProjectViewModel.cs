using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    public class UserProjectViewModel
    {
        public Guid pid { get; set; }
        public string regNumber { get; set; }
        public long? regSerialNumber { get; set; }
        public string publicTitleCN { get; set; }
        public string publicTitleEN { get; set; }
        public string studyPhaseNameCN { get; set; }
        public string studyPhaseNameEN { get; set; }
        public string recruitingStatusNameCN { get; set; }
        public string recruitingStatusNameEN { get; set; }
        public int? status { get; set; }
        public int? taskstatus { get; set; }
        public string statusCn { get; set; }
        public string statusEn { get; set; }
        public DateTime? regTime { get; set; }
        public string applierCompanyCN { get; set; }
        public string applierCompanyEN { get; set; }
        public string studyTypeNameCN { get; set; }
        public string studyTypeNameEN { get; set; }
        public string ReleaseNumber { get; set; }
        public DateTime? regNumberTime { get; set; }
        public DateTime? modifyTime { get; set; }
        public DateTime? flowRecordTime { get; set; }
        public string secondaryID { get; set; }
        public bool IsEnglist { get; set; }
        public bool IsChinese { get; set; }

        public string sendTaskUser { get; set; }

        public string executeTaskUser { get; set; }
    }
}