using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Models
{
    public class UserProjectHistoryModel
    {
        public Guid? pid { get; set; }
        public Core.Models.SA_NewProjectHistory Project { get; set; }
        public List<Core.Models.SA_SecondarySponsorHistory> SecondarySponsor { get; set; } = new List<Core.Models.SA_SecondarySponsorHistory>();
        public List<Core.Models.SA_InterventionsHistory> Interventions { get; set; } = new List<Core.Models.SA_InterventionsHistory>();
        public List<Core.Models.SA_DiagnosticHistory> Diagnostic { get; set; } = new List<Core.Models.SA_DiagnosticHistory>();
        public List<Core.Models.SA_ResearchAddressHistory> ResearchAddress { get; set; } = new List<Core.Models.SA_ResearchAddressHistory>();
        public List<Core.Models.SA_OutcomesHistory> Outcomes { get; set; } = new List<Core.Models.SA_OutcomesHistory>();
        public List<Core.Models.SA_CollectingSampleHistory> CollectingSample { get; set; } = new List<Core.Models.SA_CollectingSampleHistory>();
        public string SecSponsorInstitution0 { get; set; }
        public string SecSponsorAddress0 { get; set; }
        public string SecSponsorInstitutionEn0 { get; set; }
        public string SecSponsorAddressEn0 { get; set; }
        public string listStudyType { get; set; }
        public string listStudyDesign { get; set; }
        public string listStudyStage { get; set; }
        public string listLang { get; set; }
        public string listRegStatus { get; set; }
        public string txtSubjectID { get; set; }
        public string txtSecondaryID { get; set; }
        public string txtApplierPhone { get; set; }
        public string txtStudyLeaderPhone { get; set; }
        public string txtApplierFax { get; set; }
        public string txtStudyLeaderFax { get; set; }
        public string txtApplierEmail { get; set; }
        public string txtStudyLeaderEmail { get; set; }
        public string txtApplierWebsite { get; set; }
        public string txtStudyLeaderWebsite { get; set; }
        public string txtApplierPostcode { get; set; }
        public string txtStudyLeaderPostcode { get; set; }
        public string txtStudyAilmentCode { get; set; }
        public DateTime? txtNationalFDASanctionDate { get; set; }
        public DateTime? txtStudyExecuteTime { get; set; }
        public DateTime? txtStudyEndTime { get; set; }
        public DateTime? txtEnlistBeginTime { get; set; }
        public DateTime? txtEnlistEndTime { get; set; }
        public string txtTotalSampleSize { get; set; }
        public string listRecruitmentStatus { get; set; }
        public string txtMinAge { get; set; }
        public string txtMaxAge { get; set; }
        public string listGender { get; set; }
        public int? listAgreeToSign { get; set; } = 1;
        public string txtFollowUpFrequency { get; set; }
        public string listFollowUpTimeUnit { get; set; }
        public int? listStatisticalEffectChiCTRPublic { get; set; }
        public string txtNationalFDASanctionNO { get; set; }
        public string fileNationalFDASanction { get; set; }
        public string fileStudyPlan { get; set; }
        public string fileInformedConsent { get; set; }
        public string fileExperimentalresults { get; set; }
        public string txtTitleEn { get; set; }
        public string txtTitleAcronymEn { get; set; }
        public string txtOfficialNameEn { get; set; }
        public string txtOfficialNameAcronymEn { get; set; }
        public string txtApplierEn { get; set; }
        public string txtStudyLeaderEn { get; set; }
        public string txtApplierAddressEn { get; set; }
        public string txtStudyLeaderAddressEn { get; set; }
        public string txtApplierCompanyEn { get; set; }
        public string txtSponsorEn { get; set; }
        public string txtSponsorAddressEn { get; set; }
        public string txtSourceOfSpendsEn { get; set; }
        public string txtStudyAilmentEn { get; set; }
        public string txtStudyAimEn { get; set; }
        public string txtDrugsCompositionEn { get; set; }
        public string txtSelectionCriteriaEn { get; set; }
        public string txtEliminateCriteriaEn { get; set; }
        public string txtGenerafionMethodEn { get; set; }
        public string txtConcealmentEn { get; set; }
        public string txtBlindingEn { get; set; }
        public string txtUncoverPrincipleEn { get; set; }
        public string txtStatisticalMethodEn { get; set; }
        public string txtStatisticalEffectEn { get; set; }
        //public string txtDataCollectionUnitEn { get; set; }
        public string txtDataCollectionUnit { get; set; }
        public string txtDataChargeUnitEn { get; set; }
        public string txtDataAnalysisUnitEn { get; set; }
        public int? listEthicalCommitteeSanction { get; set; }
        public string txtEthicalCommitteeFileID { get; set; }
        public string fileEthicalCommittee { get; set; }
        public string txtEthicalCommitteeName { get; set; }
        public string txtEthicalCommitteeNameEn { get; set; }
        public DateTime? txtEthicalCommitteeSanctionDate { get; set; }
        public string txtTitle { get; set; }
        public string txtTitleAcronym { get; set; }
        public string txtOfficialName { get; set; }
        public string txtOfficialNameAcronym { get; set; }
        public string txtApplier { get; set; }
        public string txtStudyLeader { get; set; }
        public string txtApplierAddress { get; set; }
        public string txtStudyLeaderAddress { get; set; }
        public string txtApplierCompany { get; set; }
        public string txtSponsor { get; set; }
        public string txtSponsorAddress { get; set; }
        public string txtSourceOfSpends { get; set; }
        public string txtStudyAilment { get; set; }
        public string txtStudyAim { get; set; }
        public string txtDrugsComposition { get; set; }
        public string txtSelectionCriteria { get; set; }
        public string txtEliminateCriteria { get; set; }
        public string txtGenerafionMethod { get; set; }
        public string txtConcealment { get; set; }
        public string txtBlinding { get; set; }
        public string txtUncoverPrinciple { get; set; }
        public string txtStatisticalMethod { get; set; }
        public string txtStatisticalEffect { get; set; }
        public string txtDataChargeUnit { get; set; }
        public string txtDataAnalysisUnit { get; set; }
        public string txtUTN { get; set; }
        public string txtStudyLeaderCompany { get; set; }
        public string txtStudyLeaderCompanyEn { get; set; }
        public string txtDataManagemenBoard { get; set; }
        public string txtStudyReport { get; set; }
        public string txtStudyReportEN { get; set; }
        public string txtEthicalCommitteeCName { get; set; }
        public string txtEthicalCommitteeCNameEN { get; set; }
        public string txtEthicalCommitteeCAddress { get; set; }
        public string txtEthicalCommitteeCAddressEN { get; set; }
        public string txtEthicalCommitteeCPhone { get; set; }
        public string txtEthicalCommitteeCEmail { get; set; }
        public int? hdnSecSponsorCount { get; set; } = 0;
        public int? hdnInterCount { get; set; } = 0;
        public int? hdnPlaceCount { get; set; } = 0;
        public int? hdnIndexCount { get; set; } = 0;
        public int? hdnSpecimenCount { get; set; } = 0;
        public string btn { get; set; }
        public string StudyTypeCn { get; set; }
        public string StudyDesignCn { get; set; }
        public string StudyPhaseCn { get; set; }
        public string LangCn { get; set; }
        public string RegStatusCn { get; set; }
        public string RecruitmentStatusCn { get; set; }
        public string GenderCn { get; set; }
        public string FollowUpTimeUnitCn { get; set; }
        public string StatisticalEffectChiCTRPublicCn { get; set; }
        public string DataCollectionUnitCn { get; set; }
        public string DataManagemenBoardCn { get; set; }
        public string StudyTypeEn { get; set; }
        public string StudyDesignEn { get; set; }
        public string StudyPhaseEn { get; set; }
        public string LangEn { get; set; }
        public string RegStatusEn { get; set; }
        public string RecruitmentStatusEn { get; set; }
        public string GenderEn { get; set; }
        public string FollowUpTimeUnitEn { get; set; }
        public string StatisticalEffectChiCTRPublicEn { get; set; }
        public string DataCollectionUnitEn { get; set; }
        public string DataManagemenBoardEn { get; set; }
        public string txtRegNumber { get; set; }
        public DateTime? txtModifyTime { get; set; }
        public DateTime? txtRegTime { get; set; }
        public DateTime? txtRegNumberTime { get; set; }

        public Guid? TaskFlowCreateSysUserId { get; set; }
        /// <summary>
        /// 审核状态
        /// </summary>
        public int AuditState { get; set; }
        public string strRegNumber { get; set; }
        public string regNumber { get; set; }
        public string regSerialNumber { get; set; }
        /// <summary>
        /// 未通过原因
        /// </summary>
        public string Reason { get; set; }
        /// <summary>
        /// 申请再次修改原因
        /// </summary>
        public string EditRequestReason { get; set; }
        public string reverifyFailReason { get; set; }
        public int Status { get; set; }
        public int TaskStatus { get; set; }
        public string editRequestAttachment { get; set; }
        public string executeTaskSysUserName { get; set; }
        public string ReleaseNumber { get; set; }

        
    }
}