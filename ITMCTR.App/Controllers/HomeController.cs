using Antlr.Runtime;
using AutoMapper;
using ITMCTR.App.Common;
using ITMCTR.App.Filter;
using ITMCTR.App.Models;
using ITMCTR.Core.BLL;
using ITMCTR.Core.Models;
using NPOI.POIFS.Crypt.Dsig;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using PetaPoco;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Policy;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;

namespace ITMCTR.App.Controllers
{
    [Localization]
    public class HomeController : BaseController
    {
        public ActionResult Index()
        {
            return View();
        }
        [ChildActionOnly]
        public ActionResult NewListBlock()
        {
            var bll = new Core.BLL.SA_NewsBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_News").Where("IsViewIndex =1 AND IsRelease =1 AND IsDeleted=0 ").OrderBy("ReleaseTime desc");
            var list = bll.GetPageList(0, 4, sql);
            return PartialView(list);
        }
        public ActionResult Information(Guid? WiId)
        {
            Models.WebsiteInformationViewModel viewModel = new WebsiteInformationViewModel();
            Core.BLL.SA_WebsiteInformationBLL bll = new Core.BLL.SA_WebsiteInformationBLL();
            if (WiId.HasValue)
            {
                var model = bll.GetById(WiId.Value);
                if (model != null)
                {
                    viewModel = Mapper.Map<Models.WebsiteInformationViewModel>(model);
                    return View(viewModel);
                }
            }
            return View();
        }
        public ActionResult NewsInfo(Guid? Nid)
        {
            Core.BLL.SA_NewsBLL bll = new Core.BLL.SA_NewsBLL();
            if (Nid.HasValue)
            {
                var model = bll.GetById(Nid.Value);
                return View(model);
            }
            return View();
        }
        public ActionResult InformationCode(string code)
        {
            Models.WebsiteInformationViewModel viewModel = new WebsiteInformationViewModel();
            Core.BLL.SA_WebsiteInformationBLL bll = new Core.BLL.SA_WebsiteInformationBLL();
            var model = bll.GetByInfoCode(code);
            if (model != null)
            {
                viewModel = Mapper.Map<Models.WebsiteInformationViewModel>(model);
            }
            return View(viewModel);
        }
        #region 数据检索
        public ActionResult TrialSearch(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listRegStatus = Request["listRegStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            ViewBag.txtSecondaryID = Request["txtSecondaryID"];
            ViewBag.txtApplier = Request["txtApplier"];
            ViewBag.txtStudyLeader = Request["txtStudyLeader"];
            ViewBag.listCreateYear = Request["listCreateYear"];
            ViewBag.txtSponsor = Request["txtSponsor"];
            ViewBag.txtSecSponsor = Request["txtSecSponsor"];
            ViewBag.txtSourceOfSpends = Request["txtSourceOfSpends"];
            ViewBag.txtStudyAilment = Request["txtStudyAilment"];
            ViewBag.txtStudyAilmentCode = Request["txtStudyAilmentCode"];
            ViewBag.listStudyType = Request["listStudyType"];
            ViewBag.listStudyStage = Request["listStudyStage"];
            ViewBag.listStudyDesign = Request["listStudyDesign"];
            ViewBag.txtMinStudyExecuteTime = Request["txtMinStudyExecuteTime"];
            ViewBag.txtMaxStudyExecuteTime = Request["txtMaxStudyExecuteTime"];
            ViewBag.listRecruitmentStatus = Request["listRecruitmentStatus"];
            ViewBag.listGender = Request["listGender"];
            ViewBag.listAgreeToSign = Request["listAgreeToSign"];
            ViewBag.txtMeasure = Request["txtMeasure"];
            ViewBag.txtIntercode = Request["txtIntercode"];
            ViewBag.listEthicalCommitteeSanction = Request["listEthicalCommitteeSanction"];
            ViewBag.listWhetherPublic = Request["listWhetherPublic"];
            ViewBag.listIsUploadRF = Request["listIsUploadRF"];

            ViewBag.txtProvince = Request["txtProvince"];
            ViewBag.txtCountry = Request["txtCountry"];
            ViewBag.txtCity = Request["txtCity"];
            ViewBag.txtInstitution = Request["txtInstitution"];
            ViewBag.txtInstitutionLevel = Request["txtInstitutionLevel"];

            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.listRegStatusDict = new SelectList(CommonList.getRegStatuslist(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listRegStatus);
            ViewBag.listCreateYearDict = new SelectList(CommonList.getCreateYearlist(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listCreateYear);
            ViewBag.listRecruitmentStatusDict = new SelectList(CommonList.getRecruitmentStatuslist(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(CommonList.getGenderlist(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listGender);
            ViewBag.listEthicalCommitteeSanctionDict = new SelectList(CommonList.getTrueOrFalse(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listEthicalCommitteeSanction);
            ViewBag.listWhetherPublicDict = new SelectList(CommonList.getTrueOrFalse(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listWhetherPublic);
            ViewBag.listIsUploadRFDict = new SelectList(CommonList.getTrueOrFalse(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listIsUploadRF);
            ViewBag.listAgreeToSignDict = new SelectList(CommonList.getTrueOrFalse(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listAgreeToSign);
            ViewBag.listStudyTypeDict = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = IsLang("en-US") ? x.DescriptionEn : x.DescriptionCn }), "Key", "Text", ViewBag.listStudyType);
            ViewBag.listStudyStageDict = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = IsLang("en-US") ? x.DescriptionEn : x.DescriptionCn }), "Key", "Text", ViewBag.listStudyStage);
            ViewBag.listStudyDesignDict = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = IsLang("en-US") ? x.DescriptionEn : x.DescriptionCn }), "Key", "Text", ViewBag.listStudyDesign);

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");

            sql = sql.Where(" status = 3 and isDeleted <> 1 and (regNumber is not null or regNumber <>'')");

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");

            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");

            if (!string.IsNullOrEmpty(ViewBag.txtSubjectID))
                sql = sql.Where(" studyID=@0 ", $"%{ViewBag.txtSubjectID}%");

            if (!string.IsNullOrEmpty(ViewBag.listRegStatus))
                sql = sql.Where(" registrationStatus = @0 ", ViewBag.listRegStatus);

            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);

            if (!string.IsNullOrEmpty(ViewBag.txtSecondaryID))
                sql = sql.Where("( secondaryID = @0 or ReleaseNumber = @0 ) ", ViewBag.txtSecondaryID);

            if (!string.IsNullOrEmpty(ViewBag.txtApplier))
                sql = sql.Where(" (applicantCN like @0 or applicantEN like @0) ", $"%{ViewBag.txtApplier}%");

            if (!string.IsNullOrEmpty(ViewBag.txtStudyLeader))
                sql = sql.Where(" (studyLeaderCN = @0 or studyLeaderEN = @0) ", ViewBag.txtStudyLeader);

            if (!string.IsNullOrEmpty(ViewBag.listCreateYear))
                sql = sql.Where("year(RegNumberTime)= @0 ", ViewBag.listCreateYear);

            if (!string.IsNullOrEmpty(ViewBag.txtSponsor))
                sql = sql.Where(" (primarySponsorCN = @0 or primarySponsorEN = @0) ", ViewBag.txtSponsor);

            //从表 SA_SecondarySponsor
            if (!string.IsNullOrEmpty(ViewBag.txtSecSponsor))
                sql = sql.Where(" pid in(select pid from SA_SecondarySponsor AS SS where (SS.institutionCN  = @0 or SS.institutionEN = @0)) ", ViewBag.txtSecSponsor);

            if (!string.IsNullOrEmpty(ViewBag.txtSourceOfSpends))
                sql = sql.Where(" (sourceFundingCN = @0 or sourceFundingEN = @0) ", ViewBag.txtSourceOfSpends);

            if (!string.IsNullOrEmpty(ViewBag.txtStudyAilment))
                sql = sql.Where("  (targetDiseaseCN = @0 or targetDiseaseEN = @0) ", ViewBag.txtStudyAilment);

            if (!string.IsNullOrEmpty(ViewBag.txtStudyAilmentCode))
                sql = sql.Where(" (targetCode = @0) ", ViewBag.txtStudyAilmentCode);

            if (!string.IsNullOrEmpty(ViewBag.listStudyType))
                sql = sql.Where(" (studyTypeID = @0) ", ViewBag.listStudyType);

            if (!string.IsNullOrEmpty(ViewBag.listStudyStage))
                sql = sql.Where(" (studyPhaseID = @0) ", ViewBag.listStudyStage);

            if (!string.IsNullOrEmpty(ViewBag.listStudyDesign))
                sql = sql.Where(" (studyDesignID = @0) ", ViewBag.listStudyDesign);

            if (!string.IsNullOrEmpty(ViewBag.txtMinStudyExecuteTime))
                sql = sql.Where(" (studyTimeStart >= @0) ", ViewBag.txtMinStudyExecuteTime);

            if (!string.IsNullOrEmpty(ViewBag.txtMaxStudyExecuteTime))
                sql = sql.Where(" (studyTimeEnd <= @0) ", ViewBag.txtMaxStudyExecuteTime);

            if (!string.IsNullOrEmpty(ViewBag.listRecruitmentStatus))
                sql = sql.Where(" (recruitingStatus = @0) ", ViewBag.listRecruitmentStatus);

            if (!string.IsNullOrEmpty(ViewBag.listGender))
                sql = sql.Where(" (sex = @0) ", ViewBag.listGender);

            if (!string.IsNullOrEmpty(ViewBag.listAgreeToSign))
                sql = sql.Where(" (signConsent = @0) ", ViewBag.listAgreeToSign);

            //从表 SA_Interventions
            if (!string.IsNullOrEmpty(ViewBag.txtMeasure))
                sql = sql.Where(" pid in(select pid from SA_Interventions AS SI where (SI.interventionCN  = @0 or SI.interventionEN = @0)) ", ViewBag.txtMeasure);

            //从表 SA_Interventions
            if (!string.IsNullOrEmpty(ViewBag.txtIntercode))
                sql = sql.Where(" pid in(select pid from SA_Interventions AS SI where (SI.interventionCode  = @0 or SI.interventionCode = @0)) ", ViewBag.txtIntercode);

            if (!string.IsNullOrEmpty(ViewBag.listEthicalCommitteeSanction))
                sql = sql.Where(" (approvedCommittee = @0) ", ViewBag.listEthicalCommitteeSanction);

            if (!string.IsNullOrEmpty(ViewBag.listWhetherPublic))
                sql = sql.Where(" (whetherPublic = @0) ", ViewBag.listWhetherPublic);

            if (!string.IsNullOrEmpty(ViewBag.listIsUploadRF))
            {
                if (ViewBag.listIsUpload == 1)
                {
                    sql = sql.Where("( isnull(fileExperimentalresults,'') <> '' )", ViewBag.listIsUploadRF);
                }
                else
                {
                    sql = sql.Where("( isnull(FileExperimentalresults,'') = '' )", ViewBag.listIsUploadRF);
                }
            }
            if (!string.IsNullOrEmpty(ViewBag.txtProvince))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.provinceCN  = @0 or SS.provinceEN = @0)) ", ViewBag.txtProvince);
            if (!string.IsNullOrEmpty(ViewBag.txtCountry))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.countryCN  = @0 or SS.countryEN = @0)) ", ViewBag.txtCountry);
            if (!string.IsNullOrEmpty(ViewBag.txtCity))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.cityCN  = @0 or SS.cityEN = @0)) ", ViewBag.txtCity);
            if (!string.IsNullOrEmpty(ViewBag.txtInstitution))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.hospitalCN  = @0 or SS.hospitalEN = @0)) ", ViewBag.txtInstitution);
            if (!string.IsNullOrEmpty(ViewBag.txtInstitutionLevel))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.levelInstitutionCN  = @0 or SS.levelInstitutionEN = @0)) ", ViewBag.txtInstitutionLevel);


            sql = sql.OrderBy("sourcefrom desc,regnumber desc");
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        public ActionResult ProjectView(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    model = ConvertUserProject(proInfo);
                }
            }
            return View(model);
        }
        public ActionResult ProjectHView(Guid? Phid)
        {
            UserProjectHistoryModel model = new UserProjectHistoryModel();
            var bllProj = new Core.BLL.SA_NewProjectHistoryBLL();
            if (Phid.HasValue)
            {
                var proInfo = bllProj.GetById(Phid.Value);
                if (proInfo != null)
                {
                    model = ConvertUserProjectHistory(proInfo);
                }
            }
            return View(model);
        }
        public ActionResult DownFile(Guid pid, string path)
        {
            var proj = new Core.BLL.SA_NewProjectBLL().GetById(pid);
            if (proj != null && proj.sourcefrom == null)
            {
                return Redirect("https://www.chictr.org.cn" + path);
            }
            var file = new FileInfo(path);
            string filePath = Server.MapPath(path);
            //if (System.IO.File.Exists(filePath))
            //{
            //    return File(new FileStream(filePath, FileMode.Open), "text/plain", file.Name);
            //}
            //else
            //{
            return Content("您无权使用所提供的凭据查看此目录或页面");
            //}
        }
        public ActionResult ProjectViewTo(Guid? pid)
        {
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {

                    var url = $"https://www.chictr.org.cn/searchproj.html?title=&officialname=&subjectid=&regstatus=&regno={proInfo.secondaryID}&secondaryid=&applier=&studyleader=&createyear=&sponsor=&secsponsor=&sourceofspends=&studyailment=&studyailmentcode=&studytype=&studystage=&studydesign=&recruitmentstatus=&gender=&agreetosign=&measure=&country=&province=&city=&institution=&institutionlevel=&intercode=&ethicalcommitteesanction=&whetherpublic=&minstudyexecutetime=&maxstudyexecutetime=&btngo=btn";

                    if (proInfo.Pid.Equals(Guid.Parse("ef3d9121-360e-478d-8750-021341ba9ebe")))
                    {
                        url = $"https://www.chictr.org.cn/searchproj.html?title=&officialname=&subjectid=&regstatus=&regno=&secondaryid={proInfo.secondaryID}&applier=&studyleader=&createyear=&sponsor=&secsponsor=&sourceofspends=&studyailment=&studyailmentcode=&studytype=&studystage=&studydesign=&recruitmentstatus=&gender=&agreetosign=&measure=&country=&province=&city=&institution=&institutionlevel=&intercode=&ethicalcommitteesanction=&whetherpublic=&minstudyexecutetime=&maxstudyexecutetime=&btngo=btn";
                    }
                    return Redirect(url);
                    var httpContent = GetHtmlStr(url);
                    Regex reg = new Regex(@"(?is)<a[^>]*?href=(['""\s]?)(?<href>[^'""\s]*)\1[^>]*?>");
                    MatchCollection match = reg.Matches(httpContent);
                    foreach (Match m in match)
                    {
                        var find = m.Groups["href"].Value;
                        if (!string.IsNullOrEmpty(find) && find.StartsWith("showproj.aspx"))
                        {
                            var urlto = $"https://www.chictr.org.cn/{find}";
                            return Redirect(urlto);
                        }
                    }
                }
            }
            return Content("无法查询到数据");
        }
        [NonAction]
        public static string GetHtmlStr(string url)
        {
            string htmlStr = "";
            try
            {
                if (!String.IsNullOrEmpty(url))
                {
                    WebRequest request = WebRequest.Create(url);
                    WebResponse response = request.GetResponse();
                    Stream datastream = response.GetResponseStream();
                    Encoding ec = Encoding.UTF8;
                    StreamReader reader = new StreamReader(datastream, ec);
                    htmlStr = reader.ReadToEnd();
                    reader.Close();
                    datastream.Close();
                    response.Close();
                }
            }
            catch { }
            return htmlStr;
        }

        #endregion
        public ActionResult History(Guid? pid)
        {
            if (pid.HasValue)
            {
                var bll = new Core.BLL.SA_NewProjectHistoryBLL();
                var list = bll.GetProjectHistory(pid.Value);
                return View(list);
            }
            return RedirectToAction("TrialSearch");
        }
        public ActionResult FileLog(Guid? trialid)
        {
            if (trialid.HasValue)
            {
                var path = $"/uploads/file/{trialid.Value.ToString("N")}/";
                var p = Server.MapPath(path);
                var p2 = Server.MapPath("/");
                if (Directory.Exists(p))
                {
                    var fs = Directory.GetFiles(p, "*.*", SearchOption.AllDirectories)
                        .ToList()
                        .ConvertAll(x => x.Replace(p2, ""));
                    ViewBag.files = fs;
                }
                SA_ProjectRequestEditFlowBLL bll = new SA_ProjectRequestEditFlowBLL();
                var list = bll.GetByListByPid(trialid.Value);
                ViewBag.list = list;
            }
            return View();
        }
        public ActionResult QueryAdmin()
        {
            return View();
        }
        [HttpPost]
        public ActionResult QueryAdmin(DateTime? dt1, DateTime? dt2, string pwd)
        {
            if (pwd != "1qaz@WSX3edc")
            {
                return Content("密码错误");
            }
            using (MemoryStream fs = new MemoryStream())
            {
                var sql = @"SELECT Pid,
                               isnull(publicTitleCN ,publicTitleEN)注册题目,
                               isnull(applicantCN,applicantEN) 注册联系人,
	                           applicantTelephone 注册联系人电话,
	                           isnull(studyLeaderCN,studyLeaderEN) 试验负责人,
	                           studyTelephone 试验负责人电话,
	                           isnull(studyLeaderCompanyCN,studyLeaderCompanyEN) 试验负责人所在单位,
	                           regNumber 已发号的注册号码,
	                           regNumberTime 已发号时间,
	                           recruitingTimeStart as 征募研究对象开始时间,
	                           regTime 首次提交时间,
	                           (case taskStatus 
	                           when 0 then '无' 
	                           when 1 then '待分配' 
	                           when 2 then '已分配' 
	                           when 3 then '待复核' 
	                           when 4 then '复核未通过' 
	                           when 5 then '复核通过' 
	                           when 6 then '再修改申请' 
	                           when 7 then '申请通过' 
	                           when 8 then '申请未通过' 
	                           end) 
	                           as 流程状态,
	                           (case status 
	                           when 0 then '未填完' 
	                           when 1 then '待审核' 
	                           when 2 then '未通过审核' 
	                           when 3 then '通过审核' 
	                           end) 
	                           as 审核状态,
	                           (case 
	                           when taskStatus > 1 and taskStatus < 5 
	                           then (select top 1 [Username] from SA_SysUser as su where su.Uid   =  [SA_NewProject].executeTaskSysUserId) 
	                           when taskStatus > 4 
	                           then (select top 1 [Username] from SA_SysUser as su where su.Uid   =  [SA_NewProject].sendTaskSysUserId) 
	                           end) 
	                           as 目前审核人 ,
	                           (select top 1 r.CreateTime from SA_ProjectVerifyTaskFlowRecord as r where r.Pid  =  [SA_NewProject].Pid and r.VerifyTaskStatus  = 1 order by CreateTime desc) as 一审分配时间,
	                           (select top 1 r.CreateTime from SA_ProjectVerifyTaskFlowRecord as r where r.Pid  =  [SA_NewProject].Pid and r.VerifyTaskStatus  = 2 order by CreateTime desc) as 二审最后一次审核时间,
	                           (select top 1 r.CreateTime from SA_ProjectVerifyTaskFlowRecord as r where r.Pid  =  [SA_NewProject].Pid and r.VerifyTaskStatus  = 5 order by CreateTime desc) as 用户最后一次提交时间,
	                           (select top 1 r.RequestTime from SA_ProjectRequestEditFlow as r where r.Pid  =  [SA_NewProject].Pid and r.RequestStatus = 6  order by RequestTime desc) as 再修改申请记录时间,
	                           (select top 1 r.EditRequestReason from SA_ProjectRequestEditFlow as r where r.Pid  =  [SA_NewProject].Pid and r.RequestStatus =  6  order by RequestTime desc) as 再修改申请记录原因
                          FROM [SA_NewProject]
                          where regTime is not null and isDeleted <> 1";
                if (dt1.HasValue)
                {
                    sql += $" and regTime>='{dt1:yyyy-MM-dd}'";
                }
                if (dt2.HasValue)
                {
                    sql += $" and regTime<='{dt2:yyyy-MM-dd}'";
                }
                using (var db = new ITMCTRDatabase())
                {
                    var list = db.Query<ExprotAdminViewModel>(sql);
                    IWorkbook book = new XSSFWorkbook();
                    var sheet = book.CreateSheet();
                    var row = sheet.CreateRow(0);
                    row.CreateCell(0).SetCellValue("Pid");
                    row.CreateCell(1).SetCellValue("注册题目");
                    row.CreateCell(2).SetCellValue("注册联系人");
                    row.CreateCell(3).SetCellValue("注册联系人电话");
                    row.CreateCell(4).SetCellValue("试验负责人");
                    row.CreateCell(5).SetCellValue("试验负责人电话");
                    row.CreateCell(6).SetCellValue("试验负责人所在单位");
                    row.CreateCell(7).SetCellValue("已发号的注册号码");
                    row.CreateCell(8).SetCellValue("已发号时间");
                    row.CreateCell(9).SetCellValue("征募研究对象开始时间");
                    row.CreateCell(10).SetCellValue("首次提交时间");
                    row.CreateCell(11).SetCellValue("流程状态");
                    row.CreateCell(12).SetCellValue("审核状态");
                    row.CreateCell(13).SetCellValue("目前审核人");
                    row.CreateCell(14).SetCellValue("一审分配时间");
                    row.CreateCell(15).SetCellValue("二审最后一次审核时间");
                    row.CreateCell(16).SetCellValue("用户最后一次提交时间");
                    row.CreateCell(17).SetCellValue("再修改申请记录时间");
                    row.CreateCell(18).SetCellValue("再修改申请记录原因");
                    int Index = 1;
                    foreach (var item in list)
                    {
                        var rowNew = sheet.CreateRow(Index++);
                        if (item.Pid.HasValue)
                            rowNew.CreateCell(0).SetCellValue(item.Pid.ToString());
                        rowNew.CreateCell(1).SetCellValue(item.注册题目);
                        rowNew.CreateCell(2).SetCellValue(item.注册联系人);
                        rowNew.CreateCell(3).SetCellValue(item.注册联系人电话);
                        rowNew.CreateCell(4).SetCellValue(item.试验负责人);
                        rowNew.CreateCell(5).SetCellValue(item.试验负责人电话);
                        rowNew.CreateCell(6).SetCellValue(item.试验负责人所在单位);
                        rowNew.CreateCell(7).SetCellValue(item.已发号的注册号码);
                        if (item.已发号时间.HasValue)
                            rowNew.CreateCell(8).SetCellValue(item.已发号时间.Value.ToString("yyyy-MM-dd"));
                        if (item.征募研究对象开始时间.HasValue)
                            rowNew.CreateCell(9).SetCellValue(item.征募研究对象开始时间.Value.ToString("yyyy-MM-dd"));
                        if (item.首次提交时间.HasValue)
                            rowNew.CreateCell(10).SetCellValue(item.首次提交时间.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                        rowNew.CreateCell(11).SetCellValue(item.流程状态);
                        rowNew.CreateCell(12).SetCellValue(item.审核状态);
                        rowNew.CreateCell(13).SetCellValue(item.目前审核人);
                        if (item.一审分配时间.HasValue)
                            rowNew.CreateCell(14).SetCellValue(item.一审分配时间.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                        if (item.二审最后一次审核时间.HasValue)
                            rowNew.CreateCell(15).SetCellValue(item.二审最后一次审核时间.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                        if (item.用户最后一次提交时间.HasValue)
                            rowNew.CreateCell(16).SetCellValue(item.用户最后一次提交时间.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                        if (item.再修改申请记录时间.HasValue)
                            rowNew.CreateCell(17).SetCellValue(item.再修改申请记录时间.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                        rowNew.CreateCell(18).SetCellValue(item.再修改申请记录原因);
                    }
                    book.Write(fs);
                }
                return File(fs.ToArray(), "application/octet-stream", $"数据导出{DateTime.Now.ToString("yyyyMMdd")}.xlsx");
            }
        }

        public ActionResult NewsList(int pageNo = 0)
        {
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_News").Where("IsRelease =1 AND IsDeleted=0 ").OrderBy("ReleaseTime desc");
            Core.BLL.SA_NewsBLL bll = new Core.BLL.SA_NewsBLL();
            var resPage = bll.GetPageList(pageNo, 10, sql);
            return View(resPage);
        }

        public ActionResult Details(string Nid)
        {
            Core.BLL.SA_NewsBLL bll = new Core.BLL.SA_NewsBLL();
            var model = bll.GetById(Guid.Parse(Nid));
            return View(model);
        }
    }
}