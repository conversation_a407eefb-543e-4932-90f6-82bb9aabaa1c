using ITMCTR.App.Common;
using ITMCTR.App.Filter;
using ITMCTR.App.Models;
using ITMCTR.Core;
using ITMCTR.Core.Models;
using Microsoft.Ajax.Utilities;
using Newtonsoft.Json;
using PetaPoco;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Mvc;
using System.Web.Razor.Parser.SyntaxTree;
using System.Web.UI.WebControls;
using System.Windows.Input;

namespace ITMCTR.App.Controllers
{
    [Localization]
    [UserAuth(LoginUrl = "/UserPlatform/Login", Validate = true)]
    public partial class UserPlatformController : BaseController
    {
        #region 项目列表
        public ActionResult ProjectList(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            //ViewBag.txtSecondaryID = Request["txtSecondaryID"];

            List<SelectListItem> listItem = new List<SelectListItem>();
            listItem.Add(new SelectListItem { Value = "", Text = "不限/All" });
            listItem.Add(new SelectListItem { Value = "1", Text = "待审核/Not be verified" });
            listItem.Add(new SelectListItem { Value = "3", Text = "通过审核/Successful" });
            listItem.Add(new SelectListItem { Value = "0", Text = "未填完/Incompleted" });
            ViewBag.listVerify = new SelectList(listItem, "Value", "Text", ViewBag.listVerifyStatus);

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" createUserID=@0 ", UserProfile.CurrentProfile.UserID);

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");
            if (!string.IsNullOrEmpty(ViewBag.listVerifyStatus))
                sql = sql.Where(" status=@0 ", ViewBag.listVerifyStatus);
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
            sql = sql.OrderBy("regTime desc");

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        public ActionResult BeSubmitPros(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            //ViewBag.txtSecondaryID = Request["txtSecondaryID"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" createUserID=@0 and status=@1 ", UserProfile.CurrentProfile.UserID, 0);
            
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
            sql = sql.OrderBy("regTime desc");

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        public ActionResult SndBackPros(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            //ViewBag.txtSecondaryID = Request["txtSecondaryID"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" createUserID=@0 and status=@1 ", UserProfile.CurrentProfile.UserID, 2);
            sql = sql.OrderBy("regTime desc");

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        public ActionResult InProcessPros(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            //ViewBag.txtSecondaryID = Request["txtSecondaryID"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" createUserID=@0 and status=@1 ", UserProfile.CurrentProfile.UserID, 1);
            sql = sql.OrderBy("regTime desc");

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        public ActionResult SuccPros(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            //ViewBag.txtSecondaryID = Request["txtSecondaryID"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" createUserID=@0 and status=@1 AND regNumber IS NOT NULL", UserProfile.CurrentProfile.UserID, 3);

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
            sql = sql.OrderBy("regTime desc");

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        #endregion
        #region 项目添加
        public ActionResult ProjectAdd()
        {
            var closeModel = new Core.BLL.SA_WebStatusBLL().GetAll().FirstOrDefault();
            var Lang = CultureInfo.CurrentUICulture.Name;
            if (closeModel != null && closeModel.IsOpen == false)
            {
                if (Lang == "zh-CN")
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                    closeModel.Content,
                                    Url.Action("Index", "UserPlatform"),
                                    "确定");
                }
                else
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                     closeModel.ContentEn,
                                     Url.Action("Index", "UserPlatform"),
                                     "确定");
                }
            }

            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");

            ViewBag.listLangDict = new SelectList(CommonList.getLanglist(), "Key", "Value");
            ViewBag.listRegStatusDict = new SelectList(CommonList.getRegStatuslist(), "Key", "Value");
            ViewBag.listRecruitmentStatusDict = new SelectList(CommonList.getRecruitmentStatuslist(), "Key", "Value");
            ViewBag.listGenderDict = new SelectList(CommonList.getGenderlist(), "Key", "Value");
            ViewBag.listFollowUpTimeUnitDict = new SelectList(CommonList.getFollowUpTimeUnitlist(), "Key", "Value");
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(CommonList.getStatisticalEffectChiCTRPubliclist(), "Key", "Value");
            ViewBag.txtDataCollectionUnitDict = new SelectList(CommonList.getDataCollectionUnitlist(), "Key", "Value");
            ViewBag.txtDataManagemenBoardDict = new SelectList(CommonList.getDataManagemenBoardlist(), "Key", "Value");

            UserProjectModel model = new UserProjectModel();
            return View(model);
        }
        [HttpPost, ValidateInput(false)]
        public ActionResult ProjectAdd(UserProjectModel model)
        {
            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");

            ViewBag.listLangDict = new SelectList(CommonList.getLanglist(), "Key", "Value");
            ViewBag.listRegStatusDict = new SelectList(CommonList.getRegStatuslist(), "Key", "Value");
            ViewBag.listRecruitmentStatusDict = new SelectList(CommonList.getRecruitmentStatuslist(), "Key", "Value");
            ViewBag.listGenderDict = new SelectList(CommonList.getGenderlist(), "Key", "Value");
            ViewBag.listFollowUpTimeUnitDict = new SelectList(CommonList.getFollowUpTimeUnitlist(), "Key", "Value");
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(CommonList.getStatisticalEffectChiCTRPubliclist(), "Key", "Value");
            ViewBag.txtDataCollectionUnitDict = new SelectList(CommonList.getDataCollectionUnitlist(), "Key", "Value");
            ViewBag.txtDataManagemenBoardDict = new SelectList(CommonList.getDataManagemenBoardlist(), "Key", "Value");

            var bll = new Core.BLL.SA_NewProjectBLL();
            Core.Models.SA_NewProject proInfo = new Core.Models.SA_NewProject();
            proInfo.Pid = Guid.NewGuid();
            proInfo.regIP = Common.IP.GetIP();
            //proInfo.modifyTime = DateTime.Now;
            proInfo.createUserID = UserProfile.CurrentProfile.UserID;
            proInfo.sourcefrom = 1;
            proInfo.isDeleted = 0;
            string action = model.btn;
            if (action == "save")
            {
                bll.ProjectStatusChange(proInfo, Core.ProjectStatus.未填完);
            }
            else
            {
                proInfo.regTime = DateTime.Now;
                bll.ProjectStatusChange(proInfo, Core.ProjectStatus.待审核);
            }
            proInfo.studyTypeID = model.listStudyType;
            proInfo.studyDesignID = model.listStudyDesign;
            proInfo.studyPhaseID = model.listStudyStage;
            proInfo.filloutLanguage = model.listLang;
            proInfo.registrationStatus = model.listRegStatus;
            proInfo.studyID = model.txtSubjectID;
            proInfo.secondaryID = model.txtSecondaryID;
            proInfo.applicantTelephone = model.txtApplierPhone;
            proInfo.studyTelephone = model.txtStudyLeaderPhone;
            proInfo.applicanFax = model.txtApplierFax;
            proInfo.studyFax = model.txtStudyLeaderFax;
            proInfo.applicantEmail = model.txtApplierEmail;
            proInfo.studyEmail = model.txtStudyLeaderEmail;
            proInfo.applicantWebsite = model.txtApplierWebsite;
            proInfo.studyWebsite = model.txtStudyLeaderWebsite;
            proInfo.applicantPostcode = model.txtApplierPostcode;
            proInfo.studyPostcode = model.txtStudyLeaderPostcode;
            proInfo.targetCode = model.txtStudyAilmentCode;
            proInfo.dataSFDA = model.txtNationalFDASanctionDate;
            proInfo.studyTimeStart = model.txtStudyExecuteTime;
            proInfo.studyTimeEnd = model.txtStudyEndTime;
            proInfo.recruitingTimeStart = model.txtEnlistBeginTime;
            proInfo.recruitingTimeEnd = model.txtEnlistEndTime;
            proInfo.totalSampleSize = model.txtTotalSampleSize;
            proInfo.recruitingStatus = model.listRecruitmentStatus;
            proInfo.ageMin = model.txtMinAge;
            proInfo.ageMax = model.txtMaxAge;
            proInfo.sex = model.listGender;
            proInfo.signConsent = model.listAgreeToSign;
            proInfo.followupTime = model.txtFollowUpFrequency;
            proInfo.followup = model.listFollowUpTimeUnit;
            proInfo.whetherPublic = model.listStatisticalEffectChiCTRPublic;
            proInfo.of_SFDA = model.txtNationalFDASanctionNO;
            var text2 = Utils.SaveRequestFile("fileNationalFDASanction", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
            if (text2 != string.Empty && !text2.Equals("1"))
            {
                proInfo.fileSFDA = text2;
            }
            var text3 = Utils.SaveRequestFile("fileStudyPlan", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
            if (text3 != string.Empty && !text3.Equals("1"))
            {
                proInfo.studyPlanfile = text3;
            }
            var text4 = Utils.SaveRequestFile("fileInformedConsent", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
            if (text4 != string.Empty && !text4.Equals("1"))
            {
                proInfo.informedConsentfile = text4;
            }
            var text6 = Utils.SaveRequestFile("fileExperimentalresults", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
            if (text6 != string.Empty && !text6.Equals("1"))
            {
                proInfo.fileExperimentalresults = text6;
            }
            proInfo.publicTitleEN = model.txtTitleEn;
            proInfo.titleAcronymEN = model.txtTitleAcronymEn;
            proInfo.scientifirTitleEN = model.txtOfficialNameEn;
            proInfo.scientifirAcronymEN = model.txtOfficialNameAcronymEn;
            proInfo.applicantEN = model.txtApplierEn;
            proInfo.studyLeaderEN = model.txtStudyLeaderEn;
            proInfo.applicantAddressEN = model.txtApplierAddressEn;
            proInfo.studyAddressEN = model.txtStudyLeaderAddressEn;
            proInfo.applicantInstitutionEN = model.txtApplierCompanyEn;
            proInfo.primarySponsorEN = model.txtSponsorEn;
            proInfo.primarySponsorAddressEN = model.txtSponsorAddressEn;
            proInfo.sourceFundingEN = model.txtSourceOfSpendsEn;
            proInfo.targetDiseaseEN = model.txtStudyAilmentEn;
            proInfo.objectivesStudyEN = model.txtStudyAimEn;
            proInfo.contentsDrugEN = model.txtDrugsCompositionEn;
            proInfo.inclusionCriteriaEN = model.txtSelectionCriteriaEn;
            proInfo.exclusionCrteriaEN = model.txtEliminateCriteriaEn;
            proInfo.randomMethodEN = model.txtGenerafionMethodEn;
            proInfo.processConcealmentEN = model.txtConcealmentEn;
            proInfo.blindingEN = model.txtBlindingEn;
            proInfo.RulesblindingEN = model.txtUncoverPrincipleEn;
            proInfo.statisticalMethodEN = model.txtStatisticalMethodEn;
            proInfo.calculatedResultsEN = model.txtStatisticalEffectEn;
            //proInfo.DataCollectionEN = model.txtDataCollectionUnitEn;
            proInfo.DataCollectionUnit = model.txtDataCollectionUnit;
            proInfo.dataManagementEN = model.txtDataChargeUnitEn;
            proInfo.dataAnalysisEN = model.txtDataAnalysisUnitEn;
            proInfo.SubmitStatus = 1;
            proInfo.approvedCommittee = model.listEthicalCommitteeSanction;
            if (proInfo.approvedCommittee == 1)
            {
                proInfo.ethicalCommitteeFileID = model.txtEthicalCommitteeFileID;
                var text5 = Utils.SaveRequestFile("fileEthicalCommittee", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text5 != string.Empty && !text5.Equals("1"))
                {
                    proInfo.fileEthicalCommittee = text5;
                }
                proInfo.ethicalCommitteeName = model.txtEthicalCommitteeName;
                proInfo.ethicalCommitteeNameEN = model.txtEthicalCommitteeNameEn;
                proInfo.ethicalCommitteeSanctionDate = model.txtEthicalCommitteeSanctionDate;
            }
            proInfo.publicTitleCN = model.txtTitle;
            proInfo.titleAcronymCN = model.txtTitleAcronym;
            proInfo.scientifirTitleCN = model.txtOfficialName;
            proInfo.scientifirAcronymCN = model.txtOfficialNameAcronym;
            proInfo.applicantCN = model.txtApplier;
            proInfo.studyLeaderCN = model.txtStudyLeader;
            proInfo.applicantAddressCN = model.txtApplierAddress;
            proInfo.studyAddressCN = model.txtStudyLeaderAddress;
            proInfo.applicantInstitutionCN = model.txtApplierCompany;
            proInfo.primarySponsorCN = model.txtSponsor;
            proInfo.primarySponsorAddressCN = model.txtSponsorAddress;
            proInfo.sourceFundingCN = model.txtSourceOfSpends;
            proInfo.targetDiseaseCN = model.txtStudyAilment;
            proInfo.objectivesStudyCN = model.txtStudyAim;
            proInfo.contentsDrugCN = model.txtDrugsComposition;
            proInfo.inclusionCriteriaCN = model.txtSelectionCriteria;
            proInfo.exclusionCrteriaCN = model.txtEliminateCriteria;
            proInfo.randomMethodCN = model.txtGenerafionMethod;
            proInfo.processConcealmentCN = model.txtConcealment;
            proInfo.blindingCN = model.txtBlinding;
            proInfo.RulesblindingCN = model.txtUncoverPrinciple;
            proInfo.statisticalMethodCN = model.txtStatisticalMethod;
            proInfo.calculatedResultsCN = model.txtStatisticalEffect;
            proInfo.dataManagementCN = model.txtDataChargeUnit;
            proInfo.dataAnalysisCN = model.txtDataAnalysisUnit;
            proInfo.UTN = model.txtUTN;
            proInfo.studyLeaderCompanyCN = model.txtStudyLeaderCompany;
            proInfo.studyLeaderCompanyEN = model.txtStudyLeaderCompanyEn;
            proInfo.DataManagemenBoard = model.txtDataManagemenBoard;
            proInfo.studyReport = model.txtStudyReport;
            proInfo.studyReportEN = model.txtStudyReportEN;
            proInfo.ethicalCommitteeCName = model.txtEthicalCommitteeCName;
            proInfo.ethicalCommitteeCNameEN = model.txtEthicalCommitteeCNameEN;
            proInfo.ethicalCommitteeCAddress = model.txtEthicalCommitteeCAddress;
            proInfo.ethicalCommitteeCAddressEN = model.txtEthicalCommitteeCAddressEN;
            proInfo.ethicalCommitteeCPhone = model.txtEthicalCommitteeCPhone;
            proInfo.ethicalCommitteeCEmail = model.txtEthicalCommitteeCEmail;
            model.pid = proInfo.Pid;
            if (bll.GetAll().Exists(x => x.Pid != proInfo.Pid && x.publicTitleCN == proInfo.publicTitleCN))
            {
                SetMessageAlert(false,
                                "系统提示 System prompt",
                                "项目名称已存在，请重新填写! <br/> The project name already exists, please re-enter it.",
                                Url.Action("ProjectAdd"),
                                "确定");
                return View(model);
            }
            if (action != "save")
            {
                var message = ValidateProject(proInfo);
                if (!string.IsNullOrWhiteSpace(message))
                {
                    SetMessageAlert(false,
                                    "系统提示 System prompt",
                                    message,
                                    "",
                                    "确定");
                    return View(model);
                }
            }

            var ret = bll.Insert(proInfo);
            if (ret)
            {
                InsertSecondarySponsor(proInfo.Pid);
                InsertDiagnostic(proInfo.Pid);
                InsertInterventions(proInfo.Pid);
                InsertResearchAddress(proInfo.Pid);
                InsertOutcomes(proInfo.Pid);
                InsertCollectingSample(proInfo.Pid);
                if (action == "save")
                {
                    SetMessageAlert(false,
                                    "系统提示 System prompt",
                                    "项目已成功保存，您可以多次保存! <br/>The project has been saved successfully, and you can save it many times.",
                                    Url.Action("ProjectEdit", new { pid = proInfo.Pid }),
                                    "确定");
                }
                else
                {
                    SetMessageAlert(false,
                                   "系统提示 System prompt",
                                   "项目已成功提交! <br/>Submitted successfully!",
                                   Url.Action("ProjectList"),
                                   "确定");
                }
            }
            else
            {
                SetMessageAlert(true,
                                "系统提示 System prompt",
                                "添加失败，请完整填写内容后再提交! <br/> Failed to add!  Please complete the content before submitting.",
                                Url.Action("ProjectList"),
                                "确定");
            }
            return View(model);
        }
        private bool InsertSecondarySponsor(Guid newpid)
        {
            Core.BLL.SA_SecondarySponsorBLL bll = new Core.BLL.SA_SecondarySponsorBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            string formString = form["hdnSecSponsorCount"];
            int num2 = Utils.StrToInt(formString, 0);
            if (num2 < 1)
            {
                string formString2 = form["SecSponsorCountryEn0"];
                string formString3 = form["SecSponsorProvinceEn0"];
                string formString4 = form["SecSponsorCityEn0"];
                string formString5 = form["SecSponsorInstitutionEn0"];
                string formString6 = form["SecSponsorAddressEn0"];
                string formString7 = form["SecSponsorCountry0"];
                string formString8 = form["SecSponsorProvince0"];
                string formString9 = form["SecSponsorCity0"];
                string formString10 = form["SecSponsorInstitution0"];
                string formString11 = form["SecSponsorAddress0"];
                if (formString2 == string.Empty && formString3 == string.Empty && formString4 == string.Empty && formString5 == string.Empty && formString6 == string.Empty && formString7 == string.Empty && formString8 == string.Empty && formString9 == string.Empty && formString10 == string.Empty && formString11 == string.Empty)
                {
                    return false;
                }
                var secondarySponsorInfo = new Core.Models.SA_SecondarySponsor();
                secondarySponsorInfo.ssId = Guid.NewGuid();
                secondarySponsorInfo.Pid = newpid;
                secondarySponsorInfo.countryEN = formString2;
                secondarySponsorInfo.provinceEN = formString3;
                secondarySponsorInfo.cityEN = formString4;
                secondarySponsorInfo.institutionEN = formString5;
                secondarySponsorInfo.specificAddressEN = formString6;
                secondarySponsorInfo.countryCN = formString7;
                secondarySponsorInfo.provinceCN = formString8;
                secondarySponsorInfo.cityCN = formString9;
                secondarySponsorInfo.institutionCN = formString10;
                secondarySponsorInfo.specificAddressCN = formString11;
                bll.Insert(secondarySponsorInfo);
                return true;
            }
            List<string> list7 = new List<string>();
            for (int i = 0; i <= num2; i++)
            {
                string formString12 = form["SecSponsorCountryEn" + i];
                list7.Add(formString12);
            }
            var list8 = new List<Core.Models.SA_SecondarySponsor>();
            for (int j = 0; j < list7.Count; j++)
            {
                var secondarySponsorInfo2 = new Core.Models.SA_SecondarySponsor();
                secondarySponsorInfo2.ssId = Guid.NewGuid();
                secondarySponsorInfo2.Pid = newpid;
                secondarySponsorInfo2.countryEN = form["SecSponsorCountryEn" + j];
                secondarySponsorInfo2.provinceEN = form["SecSponsorProvinceEn" + j];
                secondarySponsorInfo2.cityEN = form["SecSponsorCityEn" + j];
                secondarySponsorInfo2.institutionEN = form["SecSponsorInstitutionEn" + j];
                secondarySponsorInfo2.specificAddressEN = form["SecSponsorAddressEn" + j];
                secondarySponsorInfo2.countryCN = form["SecSponsorCountry" + j];
                secondarySponsorInfo2.provinceCN = form["SecSponsorProvince" + j];
                secondarySponsorInfo2.cityCN = form["SecSponsorCity" + j];
                secondarySponsorInfo2.institutionCN = form["SecSponsorInstitution" + j];
                secondarySponsorInfo2.specificAddressCN = form["SecSponsorAddress" + j];
                list8.Add(secondarySponsorInfo2);
            }
            foreach (Core.Models.SA_SecondarySponsor item in list8)
            {
                bll.Insert(item);
            }
            return true;
        }
        private bool InsertInterventions(Guid newpid)
        {
            int num2 = Utils.StrToInt(Request["hdnInterCount"], 0);
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            var bll = new Core.BLL.SA_InterventionsBLL();
            if (num2 < 1)
            {
                string formString = form["GroupNameEn0"];
                string formString2 = form["MeasureEn0"];
                string formString3 = form["SampleSize0"];
                string formString4 = form["InterCode0"];
                string formString5 = form["GroupName0"];
                string formString6 = form["Measure0"];
                if (formString == string.Empty && formString2 == string.Empty && formString3 == string.Empty && formString4 == string.Empty && formString5 == string.Empty && formString6 == string.Empty)
                {
                    return false;
                }
                var interventionsInfo = new Core.Models.SA_Interventions();
                interventionsInfo.inId = Guid.NewGuid();
                interventionsInfo.Pid = newpid;
                interventionsInfo.groupsEN = formString;
                interventionsInfo.interventionEN = formString2;
                if (formString3 != string.Empty)
                {
                    interventionsInfo.sampleSize = Utils.StrToInt(formString3, 0);
                }
                interventionsInfo.interventionCode = formString4;
                interventionsInfo.groupsCN = formString5;
                interventionsInfo.interventionCN = formString6;
                return bll.Insert(interventionsInfo);
            }
            List<string> list7 = new List<string>();
            for (int i = 0; i <= num2; i++)
            {
                string formString7 = form["GroupNameEn" + i];
                list7.Add(formString7);
            }
            var list8 = new List<Core.Models.SA_Interventions>();
            for (int j = 0; j < list7.Count; j++)
            {
                var interventionsInfo2 = new Core.Models.SA_Interventions();
                interventionsInfo2.inId = Guid.NewGuid();
                interventionsInfo2.Pid = newpid;
                interventionsInfo2.groupsEN = form["GroupNameEn" + j];
                interventionsInfo2.interventionEN = form["MeasureEn" + j];
                if (form["SampleSize" + j] != string.Empty)
                {
                    interventionsInfo2.sampleSize = Utils.StrToInt(form["SampleSize" + j], 0);
                }
                interventionsInfo2.interventionCode = form["InterCode" + j];
                interventionsInfo2.groupsCN = form["GroupName" + j];
                interventionsInfo2.interventionCN = form["Measure" + j];
                list8.Add(interventionsInfo2);
            }
            foreach (var item in list8)
            {
                var ret = bll.Insert(item);
                if (!ret)
                {
                    return false;
                }
            }
            return true;
        }
        private bool InsertDiagnostic(Guid newpid)
        {
            var bll = new Core.BLL.SA_DiagnosticBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);

            string formString = form["txtStandard"];
            string formString2 = form["txtIndexTest"];
            string formString3 = form["txtTargetCondition"];
            string formString4 = form["txtSampleSizeT"];
            string formString5 = form["txtDifficultCondition"];
            string formString6 = form["txtSampleSizeD"];
            string formString7 = form["txtStandardEn"];
            string formString8 = form["txtIndexTestEn"];
            string formString9 = form["txtTargetConditionEn"];
            string formString10 = form["txtDifficultConditionEn"];
            if (formString == string.Empty && formString2 == string.Empty && formString3 == string.Empty && formString4 == string.Empty && formString5 == string.Empty && formString6 == string.Empty)
            {
                return false;
            }
            var diagnostic = new Core.Models.SA_Diagnostic();
            diagnostic.id = Guid.NewGuid();
            diagnostic.Pid = newpid;
            diagnostic.standard = formString;
            diagnostic.indexTest = formString2;
            diagnostic.targetCondition = formString3;
            diagnostic.difficultCondition = formString5;
            diagnostic.standardEn = formString7;
            diagnostic.indexTestEn = formString8;
            diagnostic.targetConditionEn = formString9;
            diagnostic.difficultConditionEn = formString10;
            if (formString4 != string.Empty)
            {
                diagnostic.sampleSizeT = Utils.StrToInt(formString4, 0);
            }
            if (formString6 != string.Empty)
            {
                diagnostic.sampleSizeD = Utils.StrToInt(formString6, 0);
            }
            return bll.Insert(diagnostic);
        }
        private bool InsertResearchAddress(Guid newpid)
        {
            var bll = new Core.BLL.SA_ResearchAddressBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            int num2 = Utils.StrToInt(form["hdnPlaceCount"], 0);
            if (num2 < 1)
            {
                string formString = form["CountryEn0"];
                string formString2 = form["ProvinceEn0"];
                string formString3 = form["CityEn0"];
                string formString4 = form["InstitutionEn0"];
                string formString5 = form["InstitutionLevelEn0"];
                string formString6 = form["Country0"];
                string formString7 = form["Province0"];
                string formString8 = form["City0"];
                string formString9 = form["Institution0"];
                string formString10 = form["InstitutionLevel0"];
                if (formString == string.Empty && formString2 == string.Empty && formString3 == string.Empty && formString4 == string.Empty && formString5 == string.Empty && formString6 == string.Empty && formString7 == string.Empty && formString8 == string.Empty && formString9 == string.Empty && formString10 == string.Empty)
                {
                    return false;
                }
                var researchAddressInfo = new Core.Models.SA_ResearchAddress();
                researchAddressInfo.raId = Guid.NewGuid();
                researchAddressInfo.Pid = newpid;
                researchAddressInfo.countryEN = formString;
                researchAddressInfo.provinceEN = formString2;
                researchAddressInfo.cityEN = formString3;
                researchAddressInfo.hospitalEN = formString4;
                researchAddressInfo.levelInstitutionEN = formString5;
                researchAddressInfo.countryCN = formString6;
                researchAddressInfo.provinceCN = formString7;
                researchAddressInfo.cityCN = formString8;
                researchAddressInfo.hospitalCN = formString9;
                researchAddressInfo.levelInstitutionCN = formString10;
                return bll.Insert(researchAddressInfo);
            }
            List<string> list7 = new List<string>();
            for (int i = 0; i <= num2; i++)
            {
                string formString11 = form["CountryEn" + i];
                list7.Add(formString11);
            }
            var list8 = new List<Core.Models.SA_ResearchAddress>();
            for (int j = 0; j < list7.Count; j++)
            {
                var researchAddressInfo2 = new Core.Models.SA_ResearchAddress();
                researchAddressInfo2.raId = Guid.NewGuid();
                researchAddressInfo2.Pid = newpid;
                researchAddressInfo2.countryEN = form["CountryEn" + j];
                researchAddressInfo2.provinceEN = form["ProvinceEn" + j];
                researchAddressInfo2.cityEN = form["CityEn" + j];
                researchAddressInfo2.hospitalEN = form["InstitutionEn" + j];
                researchAddressInfo2.levelInstitutionEN = form["InstitutionLevelEn" + j];
                researchAddressInfo2.countryCN = form["Country" + j];
                researchAddressInfo2.provinceCN = form["Province" + j];
                researchAddressInfo2.cityCN = form["City" + j];
                researchAddressInfo2.hospitalCN = form["Institution" + j];
                researchAddressInfo2.levelInstitutionCN = form["InstitutionLevel" + j];
                list8.Add(researchAddressInfo2);
            }
            foreach (var item in list8)
            {
                var ret = bll.Insert(item);
                if (!ret)
                {
                    return ret;
                }
            }
            return true;
        }
        private bool InsertOutcomes(Guid newpid)
        {
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            int num2 = Utils.StrToInt(form["hdnIndexCount"], 0);
            var bll = new Core.BLL.SA_OutcomesBLL();
            if (num2 < 1)
            {
                string formString = form["IndexName0"];
                string formString2 = form["IndexTypeId0"];
                string formString3 = form["IndexNameEn0"];
                string formString4 = form["txtIndexNamePointTime0"];
                string formString5 = form["txtIndexNameMeasureMethod0"];
                string formString6 = form["txtIndexNamePointTimeEn0"];
                string formString7 = form["txtIndexNameMeasureMethodEn0"];
                if (formString == string.Empty && formString3 == string.Empty)
                {
                    return false;
                }
                var outcomesInfo = new Core.Models.SA_Outcomes();
                outcomesInfo.ouId = Guid.NewGuid();
                outcomesInfo.Pid = newpid;
                outcomesInfo.outcomeNameCN = formString;
                outcomesInfo.pointerType = formString2;
                outcomesInfo.outcomeNameEN = formString3;
                outcomesInfo.measureTimeCN = formString4;
                outcomesInfo.measureMethodCN = formString5;
                outcomesInfo.measureTimeEN = formString6;
                outcomesInfo.measureMethodEN = formString7;
                return bll.Insert(outcomesInfo);
            }
            List<string> list7 = new List<string>();
            for (int i = 0; i <= num2; i++)
            {
                string formString8 = form["IndexNameEn" + i];
                list7.Add(formString8);
            }
            var list8 = new List<Core.Models.SA_Outcomes>();
            for (int j = 0; j < list7.Count; j++)
            {
                var outcomesInfo2 = new Core.Models.SA_Outcomes();
                outcomesInfo2.ouId = Guid.NewGuid();
                outcomesInfo2.Pid = newpid;
                outcomesInfo2.outcomeNameCN = form["IndexName" + j];
                outcomesInfo2.pointerType = form["IndexTypeId" + j];
                outcomesInfo2.outcomeNameEN = form["IndexNameEn" + j];
                outcomesInfo2.measureTimeCN = form["txtIndexNamePointTime" + j];
                outcomesInfo2.measureMethodCN = form["txtIndexNameMeasureMethod" + j];
                outcomesInfo2.measureTimeEN = form["txtIndexNamePointTimeEn" + j];
                outcomesInfo2.measureMethodEN = form["txtIndexNameMeasureMethodEn" + j];
                list8.Add(outcomesInfo2);
            }
            foreach (var item in list8)
            {
                var ret = bll.Insert(item);
                if (!ret)
                {
                    return ret;
                }
            }
            return true;
        }
        private bool InsertCollectingSample(Guid newpid)
        {
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            int num2 = Utils.StrToInt(form["hdnSpecimenCount"], 0);
            var bll = new Core.BLL.SA_CollectingSampleBLL();
            if (num2 < 1)
            {
                string formString = form["SpecimenName0"];
                string formString2 = form["SpecimenTakeFrom0"];
                string formString3 = form["SpecimenAfterUse0"];
                string formString4 = form["SpecimenNote0"];
                string formString5 = form["SpecimenNameEn0"];
                string formString6 = form["SpecimenTakeFromEn0"];
                string formString7 = form["SpecimenNoteEn0"];
                if (formString == string.Empty && formString2 == string.Empty && formString4 == string.Empty && formString5 == string.Empty && formString6 == string.Empty && formString7 == string.Empty)
                {
                    return false;
                }
                var collectingSampleInfo = new Core.Models.SA_CollectingSample();
                collectingSampleInfo.csId = Guid.NewGuid();
                collectingSampleInfo.Pid = newpid;
                collectingSampleInfo.sampleNameCN = formString;
                collectingSampleInfo.tissueCN = formString2;
                collectingSampleInfo.fateSample = formString3;
                collectingSampleInfo.noteCN = formString4;
                collectingSampleInfo.sampleNameEN = formString5;
                collectingSampleInfo.tissueEN = formString6;
                collectingSampleInfo.noteEN = formString7;
                return bll.Insert(collectingSampleInfo);
            }
            List<string> list7 = new List<string>();
            for (int i = 0; i <= num2; i++)
            {
                string formString8 = form["SpecimenNameEn" + i];
                list7.Add(formString8);
            }
            var list8 = new List<Core.Models.SA_CollectingSample>();
            for (int j = 0; j < list7.Count; j++)
            {
                var collectingSampleInfo2 = new Core.Models.SA_CollectingSample();
                collectingSampleInfo2.csId = Guid.NewGuid();
                collectingSampleInfo2.Pid = newpid;
                collectingSampleInfo2.sampleNameCN = form["SpecimenName" + j];
                collectingSampleInfo2.tissueCN = form["SpecimenTakeFrom" + j];
                collectingSampleInfo2.fateSample = form["SpecimenAfterUse" + j];
                collectingSampleInfo2.noteCN = form["SpecimenNote" + j];
                collectingSampleInfo2.sampleNameEN = form["SpecimenNameEn" + j];
                collectingSampleInfo2.tissueEN = form["SpecimenTakeFromEn" + j];
                collectingSampleInfo2.noteEN = form["SpecimenNoteEn" + j];
                list8.Add(collectingSampleInfo2);
            }
            foreach (var item in list8)
            {
                var ret = bll.Insert(item);
                if (!ret)
                {
                    return ret;
                }
            }
            return true;
        }
        #endregion
        #region 项目编辑
        public ActionResult ProjectEdit(Guid? pid)
        {
            var closeModel = new Core.BLL.SA_WebStatusBLL().GetAll().FirstOrDefault();
            var Lang = CultureInfo.CurrentUICulture.Name;
            if (closeModel != null && closeModel.IsOpen == false)
            {
                if (Lang == "zh-CN")
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                    closeModel.Content,
                                    Url.Action("Index", "UserPlatform"),
                                    "确定");
                }
                else
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                     closeModel.ContentEn,
                                     Url.Action("Index", "UserPlatform"),
                                     "确定");
                }
            }
            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");

            ViewBag.listLangDict = new SelectList(CommonList.getLanglist(), "Key", "Value");
            ViewBag.listRegStatusDict = new SelectList(CommonList.getRegStatuslist(), "Key", "Value");
            ViewBag.listRecruitmentStatusDict = new SelectList(CommonList.getRecruitmentStatuslist(), "Key", "Value");
            ViewBag.listGenderDict = new SelectList(CommonList.getGenderlist(), "Key", "Value");
            ViewBag.listFollowUpTimeUnitDict = new SelectList(CommonList.getFollowUpTimeUnitlist(), "Key", "Value");
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(CommonList.getStatisticalEffectChiCTRPubliclist(), "Key", "Value");
            ViewBag.txtDataCollectionUnitDict = new SelectList(CommonList.getDataCollectionUnitlist(), "Key", "Value");
            ViewBag.txtDataManagemenBoardDict = new SelectList(CommonList.getDataManagemenBoardlist(), "Key", "Value");

            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    if (proInfo.createUserID != UserProfile.CurrentProfile.UserID)
                    {
                        SetMessageAlert(true, "系统提示 System prompt", "无效请求! <br/> Invalid request.", Url.Action("ProjectList"), "确定");
                        return View(model);
                    }
                    if (proInfo.status != (int)ProjectStatus.未填完 && proInfo.status != (int)ProjectStatus.未通过审核)
                    {
                        SetMessageAlert(true, "系统提示 System prompt", "该项目已经通过审核，不能再修改项目信息! <br/> This trial has been verified,you can't edit it any more.", Url.Action("ProjectList"), "确定");
                        return View(model);
                    }
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    //proInfo.dataCollectionCN =txtDataCollectionUnit;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.reverifyFailReason = proInfo.reverifyFailReason;
                    model.Reason = "";
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                    var record = new Core.BLL.SA_ProjectVerifyTaskFlowRecordBLL().GetLastByProjectId(proInfo.Pid);
                    if (record != null && !string.IsNullOrWhiteSpace(record.Reason))
                    {
                        model.Reason = record.Reason;
                    }
                    model.Status = -1;
                    if (proInfo.status.HasValue)
                        model.Status = proInfo.status.Value;
                    model.TaskStatus = proInfo.taskStatus ?? 0;
                }
            }
            return View(model);
        }

        [HttpPost, ValidateInput(false)]
        public ActionResult ProjectEdit(UserProjectModel model)
        {

            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");

            ViewBag.listLangDict = new SelectList(CommonList.getLanglist(), "Key", "Value");
            ViewBag.listRegStatusDict = new SelectList(CommonList.getRegStatuslist(), "Key", "Value");
            ViewBag.listRecruitmentStatusDict = new SelectList(CommonList.getRecruitmentStatuslist(), "Key", "Value");
            ViewBag.listGenderDict = new SelectList(CommonList.getGenderlist(), "Key", "Value");
            ViewBag.listFollowUpTimeUnitDict = new SelectList(CommonList.getFollowUpTimeUnitlist(), "Key", "Value");
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(CommonList.getStatisticalEffectChiCTRPubliclist(), "Key", "Value");
            ViewBag.txtDataCollectionUnitDict = new SelectList(CommonList.getDataCollectionUnitlist(), "Key", "Value");
            ViewBag.txtDataManagemenBoardDict = new SelectList(CommonList.getDataManagemenBoardlist(), "Key", "Value");

            var bll = new Core.BLL.SA_NewProjectBLL();
            var message = new MessageInfoModel();
            if (model.pid.HasValue)
            {
                Core.Models.SA_NewProject proInfo = bll.GetById(model.pid.Value);
                if (proInfo.createUserID != UserProfile.CurrentProfile.UserID ||
                    (proInfo.status != (int)Core.ProjectStatus.未填完 && proInfo.status != (int)ProjectStatus.未通过审核))
                {
                    SetMessageAlert(true, "系统提示 System prompt",
                        "无效提交! <br/> Invalid submission.",
                               Url.Action("ProjectList"),
                               "确定");
                    return View(model);
                }
                //if (model.listRegStatus != proInfo.registrationStatus)
                //{
                //    if (proInfo.registrationStatus == "1008002" &&
                //        proInfo.recruitingTimeStart.HasValue &&
                //        proInfo.recruitingTimeEnd.HasValue &&
                //        proInfo.recruitingTimeStart > DateTime.Now.Date)
                //    {
                //        SetMessageAlert(true, "系统提示 System prompt", "补注册；征募研究对象时间不能晚于当前日期<br/>Retrospective registration:Starting of recruiting time is later than today");
                //        return View(model);
                //    }
                //    if (proInfo.registrationStatus == "1008001" &&
                //        proInfo.recruitingTimeStart.HasValue &&
                //        proInfo.recruitingTimeEnd.HasValue &&
                //        proInfo.recruitingTimeStart < DateTime.Now.Date)
                //    {
                //        SetMessageAlert(true, "系统提示 System prompt", "预注册；征募研究对象时间不能早于当前日期<br/>Prospective registration:Starting of recruiting time is earlier than today");
                //        return View(model);
                //    }
                //}
                string action = model.btn;
                if (action == "save")
                {
                    bll.ProjectStatusChange(proInfo, Core.ProjectStatus.未填完);
                }
                else
                {
                    bll.ProjectStatusChange(proInfo, Core.ProjectStatus.待审核);
                }
                proInfo.studyTypeID = model.listStudyType;
                proInfo.studyDesignID = model.listStudyDesign;
                proInfo.studyPhaseID = model.listStudyStage;
                proInfo.filloutLanguage = model.listLang;
                proInfo.registrationStatus = model.listRegStatus;
                proInfo.studyID = model.txtSubjectID;
                proInfo.secondaryID = model.txtSecondaryID;
                proInfo.applicantTelephone = model.txtApplierPhone;
                proInfo.studyTelephone = model.txtStudyLeaderPhone;
                proInfo.applicanFax = model.txtApplierFax;
                proInfo.studyFax = model.txtStudyLeaderFax;
                proInfo.applicantEmail = model.txtApplierEmail;
                proInfo.studyEmail = model.txtStudyLeaderEmail;
                proInfo.applicantWebsite = model.txtApplierWebsite;
                proInfo.studyWebsite = model.txtStudyLeaderWebsite;
                proInfo.applicantPostcode = model.txtApplierPostcode;
                proInfo.studyPostcode = model.txtStudyLeaderPostcode;
                proInfo.targetCode = model.txtStudyAilmentCode;
                proInfo.dataSFDA = model.txtNationalFDASanctionDate;
                proInfo.studyTimeStart = model.txtStudyExecuteTime;
                proInfo.studyTimeEnd = model.txtStudyEndTime;
                proInfo.recruitingTimeStart = model.txtEnlistBeginTime;
                proInfo.recruitingTimeEnd = model.txtEnlistEndTime;
                proInfo.totalSampleSize = model.txtTotalSampleSize;
                proInfo.recruitingStatus = model.listRecruitmentStatus;
                proInfo.ageMin = model.txtMinAge;
                proInfo.ageMax = model.txtMaxAge;
                proInfo.sex = model.listGender;
                proInfo.signConsent = model.listAgreeToSign;
                proInfo.followupTime = model.txtFollowUpFrequency;
                proInfo.followup = model.listFollowUpTimeUnit;
                proInfo.whetherPublic = model.listStatisticalEffectChiCTRPublic;
                proInfo.of_SFDA = model.txtNationalFDASanctionNO;
                var text2 = Utils.SaveRequestFile("fileNationalFDASanction", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text2 != string.Empty && !text2.Equals("1"))
                {
                    proInfo.fileSFDA = text2;
                }
                var text3 = Utils.SaveRequestFile("fileStudyPlan", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text3 != string.Empty && !text3.Equals("1"))
                {
                    proInfo.studyPlanfile = text3;
                }
                var text4 = Utils.SaveRequestFile("fileInformedConsent", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text4 != string.Empty && !text4.Equals("1"))
                {
                    proInfo.informedConsentfile = text4;
                }
                var text6 = Utils.SaveRequestFile("fileExperimentalresults", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text6 != string.Empty && !text6.Equals("1"))
                {
                    proInfo.fileExperimentalresults = text6;
                }
                proInfo.publicTitleEN = model.txtTitleEn;
                proInfo.titleAcronymEN = model.txtTitleAcronymEn;
                proInfo.scientifirTitleEN = model.txtOfficialNameEn;
                proInfo.scientifirAcronymEN = model.txtOfficialNameAcronymEn;
                proInfo.applicantEN = model.txtApplierEn;
                proInfo.studyLeaderEN = model.txtStudyLeaderEn;
                proInfo.applicantAddressEN = model.txtApplierAddressEn;
                proInfo.studyAddressEN = model.txtStudyLeaderAddressEn;
                proInfo.applicantInstitutionEN = model.txtApplierCompanyEn;
                proInfo.primarySponsorEN = model.txtSponsorEn;
                proInfo.primarySponsorAddressEN = model.txtSponsorAddressEn;
                proInfo.sourceFundingEN = model.txtSourceOfSpendsEn;
                proInfo.targetDiseaseEN = model.txtStudyAilmentEn;
                proInfo.objectivesStudyEN = model.txtStudyAimEn;
                proInfo.contentsDrugEN = model.txtDrugsCompositionEn;
                proInfo.inclusionCriteriaEN = model.txtSelectionCriteriaEn;
                proInfo.exclusionCrteriaEN = model.txtEliminateCriteriaEn;
                proInfo.randomMethodEN = model.txtGenerafionMethodEn;
                proInfo.processConcealmentEN = model.txtConcealmentEn;
                proInfo.blindingEN = model.txtBlindingEn;
                proInfo.RulesblindingEN = model.txtUncoverPrincipleEn;
                proInfo.statisticalMethodEN = model.txtStatisticalMethodEn;
                proInfo.calculatedResultsEN = model.txtStatisticalEffectEn;
                //proInfo.DataCollectionEN = model.txtDataCollectionUnitEn;
                proInfo.DataCollectionUnit = model.txtDataCollectionUnit;
                proInfo.dataManagementEN = model.txtDataChargeUnitEn;
                proInfo.dataAnalysisEN = model.txtDataAnalysisUnitEn;
                proInfo.SubmitStatus = 1;
                proInfo.approvedCommittee = Utils.StrToInt(model.listEthicalCommitteeSanction, 0);
                if (proInfo.approvedCommittee == 1)
                {
                    proInfo.ethicalCommitteeFileID = model.txtEthicalCommitteeFileID;
                    var text5 = Utils.SaveRequestFile("fileEthicalCommittee", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                    if (text5 != string.Empty && !text5.Equals("1"))
                    {
                        proInfo.fileEthicalCommittee = text5;
                    }
                    proInfo.ethicalCommitteeName = model.txtEthicalCommitteeName;
                    proInfo.ethicalCommitteeNameEN = model.txtEthicalCommitteeNameEn;
                    proInfo.ethicalCommitteeSanctionDate = model.txtEthicalCommitteeSanctionDate;
                }
                proInfo.publicTitleCN = model.txtTitle;
                proInfo.titleAcronymCN = model.txtTitleAcronym;
                proInfo.scientifirTitleCN = model.txtOfficialName;
                proInfo.scientifirAcronymCN = model.txtOfficialNameAcronym;
                proInfo.applicantCN = model.txtApplier;
                proInfo.studyLeaderCN = model.txtStudyLeader;
                proInfo.applicantAddressCN = model.txtApplierAddress;
                proInfo.studyAddressCN = model.txtStudyLeaderAddress;
                proInfo.applicantInstitutionCN = model.txtApplierCompany;
                proInfo.primarySponsorCN = model.txtSponsor;
                proInfo.primarySponsorAddressCN = model.txtSponsorAddress;
                proInfo.sourceFundingCN = model.txtSourceOfSpends;
                proInfo.targetDiseaseCN = model.txtStudyAilment;
                proInfo.objectivesStudyCN = model.txtStudyAim;
                proInfo.contentsDrugCN = model.txtDrugsComposition;
                proInfo.inclusionCriteriaCN = model.txtSelectionCriteria;
                proInfo.exclusionCrteriaCN = model.txtEliminateCriteria;
                proInfo.randomMethodCN = model.txtGenerafionMethod;
                proInfo.processConcealmentCN = model.txtConcealment;
                proInfo.blindingCN = model.txtBlinding;
                proInfo.RulesblindingCN = model.txtUncoverPrinciple;
                proInfo.statisticalMethodCN = model.txtStatisticalMethod;
                proInfo.calculatedResultsCN = model.txtStatisticalEffect;
                proInfo.dataManagementCN = model.txtDataChargeUnit;
                proInfo.dataAnalysisCN = model.txtDataAnalysisUnit;
                proInfo.UTN = model.txtUTN;
                proInfo.studyLeaderCompanyCN = model.txtStudyLeaderCompany;
                proInfo.studyLeaderCompanyEN = model.txtStudyLeaderCompanyEn;
                proInfo.DataManagemenBoard = model.txtDataManagemenBoard;
                proInfo.studyReport = model.txtStudyReport;
                proInfo.studyReportEN = model.txtStudyReportEN;
                proInfo.ethicalCommitteeCName = model.txtEthicalCommitteeCName;
                proInfo.ethicalCommitteeCNameEN = model.txtEthicalCommitteeCNameEN;
                proInfo.ethicalCommitteeCAddress = model.txtEthicalCommitteeCAddress;
                proInfo.ethicalCommitteeCAddressEN = model.txtEthicalCommitteeCAddressEN;
                proInfo.ethicalCommitteeCPhone = model.txtEthicalCommitteeCPhone;
                proInfo.ethicalCommitteeCEmail = model.txtEthicalCommitteeCEmail;
                //proInfo.modifyTime = DateTime.Now;
                if (!proInfo.regTime.HasValue && action != "save")
                    proInfo.regTime = DateTime.Now;
                if (bll.GetAll().Exists(x => x.Pid != proInfo.Pid && x.publicTitleCN == proInfo.publicTitleCN))
                {
                    SetMessageAlert(false,
                                    "系统提示 System prompt",
                                    "项目名称已存在，请重新填写! <br/> The project name already exists, please re-enter it.",
                                    Url.Action("ProjectAdd"),
                                    "确定");
                    return View(model);
                }
                var ret = bll.Update(proInfo);
                if (ret)
                {
                    UpdateDiagnostic(proInfo.Pid);
                    if (action == "save")
                    {
                        SetMessageAlert(false, "系统提示 System prompt",
                                        "项目已成功保存，您可以多次保存! <br/>The project has been saved successfully, and you can save it many times.",
                                        Url.Action("ProjectEdit", new { pid = proInfo.Pid }),
                                        "确定");
                    }
                    else
                    {
                        SetMessageAlert(false, "系统提示 System prompt",
                                       "项目已成功提交! <br/>Project submitted successfully.",
                                       Url.Action("ProjectList"),
                                       "确定");
                        if (model.TaskStatus == (int)ProjectTaskStatus.复核未通过 || (model.TaskStatus == (int)ProjectTaskStatus.无 || model.TaskStatus == null))
                        {
                            var record = new Core.Models.SA_ProjectVerifyTaskFlowRecord();
                            record.PvtfrId = Guid.NewGuid();
                            record.Pid = proInfo.Pid;
                            record.VerifyTaskStatus = (int)VerifyTaskStatus.用户提交;
                            record.CreateTime = DateTime.Now;
                            record.ProjectStatus = proInfo.status;
                            record.Reason = Request["Reason2"];
                            record.TaskFlowCreateSysUserId = UserProfile.CurrentProfile.UserModel.Name;
                            record.LevelOneSysUserId = proInfo.sendTaskSysUserId;
                            record.LevelSecondSysUserId = proInfo.executeTaskSysUserId;
                            record.SubmitUserId = proInfo.createUserID.Value;
                            record.Insert();
                        }
                    }
                }
                else
                {
                    SetMessageAlert(true, "系统提示 System prompt",
                                    "添加失败，请完整填写内容后再提交! <br/> Failed to add!  Please complete the content before submitting.",
                                    Url.Action("ProjectList"),
                                    "确定");
                }
            }
            return View(model);
        }
        private bool UpdateDiagnostic(Guid newpid)
        {
            var bll = new Core.BLL.SA_DiagnosticBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);

            string formString = form["txtStandard"];
            string formString2 = form["txtIndexTest"];
            string formString3 = form["txtTargetCondition"];
            string formString4 = form["txtSampleSizeT"];
            string formString5 = form["txtDifficultCondition"];
            string formString6 = form["txtSampleSizeD"];
            string formString7 = form["txtStandardEn"];
            string formString8 = form["txtIndexTestEn"];
            string formString9 = form["txtTargetConditionEn"];
            string formString10 = form["txtDifficultConditionEn"];
            if (formString == string.Empty && formString2 == string.Empty && formString3 == string.Empty && formString4 == string.Empty && formString5 == string.Empty && formString6 == string.Empty)
            {
                return false;
            }
            var diagnostic = bll.GetByProjectId(newpid).FirstOrDefault();
            if (diagnostic == null)
            {
                diagnostic = new SA_Diagnostic();
                diagnostic.id = Guid.NewGuid();
                diagnostic.Pid = newpid;
                diagnostic.standard = formString;
                diagnostic.indexTest = formString2;
                diagnostic.targetCondition = formString3;
                diagnostic.difficultCondition = formString5;
                diagnostic.standardEn = formString7;
                diagnostic.indexTestEn = formString8;
                diagnostic.targetConditionEn = formString9;
                diagnostic.difficultConditionEn = formString10;
                if (formString4 != string.Empty)
                {
                    diagnostic.sampleSizeT = Utils.StrToInt(formString4, 0);
                }
                if (formString6 != string.Empty)
                {
                    diagnostic.sampleSizeD = Utils.StrToInt(formString6, 0);
                }
                return bll.Insert(diagnostic);
            }
            else
            {
                diagnostic.Pid = newpid;
                diagnostic.standard = formString;
                diagnostic.indexTest = formString2;
                diagnostic.targetCondition = formString3;
                diagnostic.difficultCondition = formString5;
                diagnostic.standardEn = formString7;
                diagnostic.indexTestEn = formString8;
                diagnostic.targetConditionEn = formString9;
                diagnostic.difficultConditionEn = formString10;
                if (formString4 != string.Empty)
                {
                    diagnostic.sampleSizeT = Utils.StrToInt(formString4, 0);
                }
                if (formString6 != string.Empty)
                {
                    diagnostic.sampleSizeD = Utils.StrToInt(formString6, 0);
                }
                return bll.Update(diagnostic);
            }
        }
        public JsonResult EditProjectInfo()
        {
            string text = Utils.HtmlDecode(Request["type"]);
            string caozuo = Utils.HtmlDecode(Request["caozuo"]);
            switch (text)
            {
                case "collinfo":
                    return CollectingSample(caozuo);
                case "outinfo":
                    return Outcomes(caozuo);
                case "ininfo":
                    return Interventions(caozuo);
                case "rainfo":
                    return ResearchAddress(caozuo);
                case "ssinfo":
                    return SecondarySponsor(caozuo);
            }
            return Json(true, JsonRequestBehavior.AllowGet);
        }
        private JsonResult CollectingSample(string caozuo)
        {
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            var bll = new Core.BLL.SA_CollectingSampleBLL();
            switch (caozuo)
            {
                case "up":
                    {
                        var csid = Utils.StrToGuid(Request["csid"]);
                        string string3 = form["SampleNameCN"];
                        string string4 = form["SampleNameEN"];
                        string string5 = form["TissueCN"];
                        string string6 = form["TissueEN"];
                        string string7 = form["NoteCN"];
                        string string8 = form["NoteEN"];
                        string string9 = form["FateSample"];
                        var collectingSampleInfo2 = bll.GetById(csid.Value);
                        collectingSampleInfo2.sampleNameCN = string3;
                        collectingSampleInfo2.sampleNameEN = string4;
                        collectingSampleInfo2.tissueCN = string5;
                        collectingSampleInfo2.tissueEN = string6;
                        collectingSampleInfo2.noteCN = string7;
                        collectingSampleInfo2.noteEN = string8;
                        collectingSampleInfo2.fateSample = string9;
                        var ret = bll.Update(collectingSampleInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string sampleNameCN = form["SampleNameCN"];
                        string sampleNameEN = form["SampleNameEN"];
                        string tissueCN = form["TissueCN"];
                        string tissueEN = form["TissueEN"];
                        string noteCN = form["NoteCN"];
                        string noteEN = form["NoteEN"];
                        string fateSample = form["FateSample"];
                        var collectingSampleInfo = new Core.Models.SA_CollectingSample();
                        collectingSampleInfo.csId = Guid.NewGuid();
                        collectingSampleInfo.Pid = pid.Value;
                        collectingSampleInfo.sampleNameCN = sampleNameCN;
                        collectingSampleInfo.sampleNameEN = sampleNameEN;
                        collectingSampleInfo.tissueCN = tissueCN;
                        collectingSampleInfo.tissueEN = tissueEN;
                        collectingSampleInfo.noteCN = noteCN;
                        collectingSampleInfo.noteEN = noteEN;
                        collectingSampleInfo.fateSample = fateSample;
                        var ret = bll.Insert(collectingSampleInfo);
                        return Json(new { succ = ret, message = "", data = collectingSampleInfo.csId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var csid = Utils.StrToGuid(Request["csid"]);
                        var ret = bll.Delete(csid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        private JsonResult Outcomes(string caozuo)
        {
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            var bll = new Core.BLL.SA_OutcomesBLL();
            switch (caozuo)
            {
                case "up":
                    {
                        var ouid = Utils.StrToGuid(form["ouid"]);
                        string string10 = form["name"];
                        string string11 = form["nameen"];
                        string string12 = form["pointtime"];
                        string string13 = form["measuremethod"];
                        string string14 = form["pointtimeen"];
                        string string15 = form["measuremethoden"];
                        var outcomesInfo2 = bll.GetById(ouid.Value);
                        outcomesInfo2.outcomeNameCN = string10;
                        outcomesInfo2.outcomeNameEN = string11;
                        outcomesInfo2.measureTimeCN = string12;
                        outcomesInfo2.measureTimeEN = string14;
                        outcomesInfo2.measureMethodCN = string13;
                        outcomesInfo2.measureMethodEN = string15;
                        var ret = bll.Update(outcomesInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string string2 = form["name"];
                        string string3 = form["nameen"];
                        string string4 = form["outype"];
                        string string5 = form["pointtime"];
                        string string6 = form["measuremethod"];
                        string string7 = form["pointtimeen"];
                        string string8 = form["measuremethoden"];
                        var outcomesInfo = new Core.Models.SA_Outcomes();
                        outcomesInfo.ouId = Guid.NewGuid();
                        outcomesInfo.Pid = pid.Value;
                        outcomesInfo.outcomeNameCN = string2;
                        outcomesInfo.outcomeNameEN = string3;
                        outcomesInfo.pointerType = string4;
                        outcomesInfo.measureTimeCN = string5;
                        outcomesInfo.measureTimeEN = string7;
                        outcomesInfo.measureMethodCN = string6;
                        outcomesInfo.measureMethodEN = string8;
                        var ret = bll.Insert(outcomesInfo);
                        return Json(new { succ = ret, message = "", data = outcomesInfo.ouId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var ouid = Utils.StrToGuid(Request["ouid"]);
                        var ret = bll.Delete(ouid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统繁忙！！" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        private JsonResult Interventions(string caozuo)
        {
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            var bll = new Core.BLL.SA_InterventionsBLL();
            switch (caozuo)
            {
                case "up":
                    {
                        var inid = Utils.StrToGuid(form["inid"]);
                        string string9 = form["group"];
                        string string10 = form["samplesize"];
                        string string11 = form["groupen"];
                        string string12 = form["measure"];
                        string string13 = form["intercode"];
                        string string14 = form["measureen"];
                        var interventionsInfo2 = bll.GetById(inid.Value);
                        interventionsInfo2.groupsCN = string9;
                        interventionsInfo2.sampleSize = Utils.StrToInt(string10, 0);
                        interventionsInfo2.groupsEN = string11;
                        interventionsInfo2.interventionCN = string12;
                        interventionsInfo2.interventionCode = string13;
                        interventionsInfo2.interventionEN = string14;
                        var ret = bll.Update(interventionsInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string string2 = form["group"];
                        string string3 = form["samplesize"];
                        string string4 = form["groupen"];
                        string string5 = form["measure"];
                        string string6 = form["intercode"];
                        string string7 = form["measureen"];
                        var interventionsInfo = new Core.Models.SA_Interventions();
                        interventionsInfo.inId = Guid.NewGuid();
                        interventionsInfo.Pid = pid.Value;
                        interventionsInfo.groupsCN = string2;
                        interventionsInfo.sampleSize = Utils.StrToInt(string3, 0);
                        interventionsInfo.groupsEN = string4;
                        interventionsInfo.interventionCN = string5;
                        interventionsInfo.interventionCode = string6;
                        interventionsInfo.interventionEN = string7;
                        var ret = bll.Insert(interventionsInfo);
                        return Json(new { succ = ret, message = "", data = interventionsInfo.inId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var inid = Utils.StrToGuid(Request["inid"]);
                        var ret = bll.Delete(inid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统繁忙！！" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        private JsonResult ResearchAddress(string caozuo)
        {
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            var bll = new Core.BLL.SA_ResearchAddressBLL();
            switch (caozuo)
            {
                case "up":
                    {
                        var raid = Utils.StrToGuid(Request["raid"]);
                        string string13 = form["country0"];
                        string string14 = form["province0"];
                        string string15 = form["city0"];
                        string string16 = form["countryen0"];
                        string string17 = form["provinceen0"];
                        string string18 = form["cityen0"];
                        string string19 = form["institution0"];
                        string string20 = form["institutionLevel0"];
                        string string21 = form["institutionen0"];
                        string string22 = form["institutionLevelen0"];
                        var researchAddressInfo2 = bll.GetById(raid.Value);
                        researchAddressInfo2.countryCN = string13;
                        researchAddressInfo2.provinceCN = string14;
                        researchAddressInfo2.cityCN = string15;
                        researchAddressInfo2.countryEN = string16;
                        researchAddressInfo2.provinceEN = string17;
                        researchAddressInfo2.cityEN = string18;
                        researchAddressInfo2.hospitalCN = string19;
                        researchAddressInfo2.levelInstitutionCN = string20;
                        researchAddressInfo2.hospitalEN = string21;
                        researchAddressInfo2.levelInstitutionEN = string22;
                        var ret = bll.Update(researchAddressInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string string2 = form["country"];
                        string string3 = form["province"];
                        string string4 = form["city"];
                        string string5 = form["countryen"];
                        string string6 = form["provinceen"];
                        string string7 = form["cityen"];
                        string string8 = form["institution"];
                        string string9 = form["institutionLevel"];
                        string string10 = form["institutionen"];
                        string string11 = form["institutionLevelen"];
                        var researchAddressInfo = new Core.Models.SA_ResearchAddress();
                        researchAddressInfo.raId = Guid.NewGuid();
                        researchAddressInfo.Pid = pid.Value;
                        researchAddressInfo.countryCN = string2;
                        researchAddressInfo.provinceCN = string3;
                        researchAddressInfo.cityCN = string4;
                        researchAddressInfo.countryEN = string5;
                        researchAddressInfo.provinceEN = string6;
                        researchAddressInfo.cityEN = string7;
                        researchAddressInfo.hospitalCN = string8;
                        researchAddressInfo.levelInstitutionCN = string9;
                        researchAddressInfo.hospitalEN = string10;
                        researchAddressInfo.levelInstitutionEN = string11;
                        var ret = bll.Insert(researchAddressInfo);
                        return Json(new { succ = ret, message = "", data = researchAddressInfo.raId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var raid = Utils.StrToGuid(Request["raid"]);
                        var ret = bll.Delete(raid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统繁忙！！" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        private JsonResult SecondarySponsor(string caozuo)
        {
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            var bll = new Core.BLL.SA_SecondarySponsorBLL();
            switch (caozuo)
            {
                case "up":
                    {
                        var ssid = Utils.StrToGuid(form["ssid"]);
                        string string13 = form["country"];
                        string string14 = form["province"];
                        string string15 = form["city"];
                        string string16 = form["countryen"];
                        string string17 = form["provinceen"];
                        string string18 = form["cityen"];
                        string string19 = form["institution"];
                        string string20 = form["address"];
                        string string21 = form["institutionen"];
                        string string22 = form["addressen"];
                        var secondarySponsorInfo2 = bll.GetById(ssid.Value);

                        secondarySponsorInfo2.countryCN = string13;
                        secondarySponsorInfo2.provinceCN = string14;
                        secondarySponsorInfo2.cityCN = string15;
                        secondarySponsorInfo2.countryEN = string16;
                        secondarySponsorInfo2.provinceEN = string17;
                        secondarySponsorInfo2.cityEN = string18;
                        secondarySponsorInfo2.institutionCN = string19;
                        secondarySponsorInfo2.specificAddressCN = string20;
                        secondarySponsorInfo2.institutionEN = string21;
                        secondarySponsorInfo2.specificAddressEN = string22;

                        var ret = bll.Update(secondarySponsorInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string string2 = form["country"];
                        string string3 = form["province"];
                        string string4 = form["city"];
                        string string5 = form["countryen"];
                        string string6 = form["provinceen"];
                        string string7 = form["cityen"];
                        string string8 = form["institution"];
                        string string9 = form["address"];
                        string string10 = form["institutionen"];
                        string string11 = form["addressen"];
                        var secondarySponsorInfo = new Core.Models.SA_SecondarySponsor();
                        secondarySponsorInfo.ssId = Guid.NewGuid();
                        secondarySponsorInfo.Pid = pid.Value;
                        secondarySponsorInfo.countryCN = string2;
                        secondarySponsorInfo.provinceCN = string3;
                        secondarySponsorInfo.cityCN = string4;
                        secondarySponsorInfo.countryEN = string5;
                        secondarySponsorInfo.provinceEN = string6;
                        secondarySponsorInfo.cityEN = string7;
                        secondarySponsorInfo.institutionCN = string8;
                        secondarySponsorInfo.specificAddressCN = string9;
                        secondarySponsorInfo.institutionEN = string10;
                        secondarySponsorInfo.specificAddressEN = string11;
                        var ret = bll.Insert(secondarySponsorInfo);
                        return Json(new { succ = ret, message = "", data = secondarySponsorInfo.ssId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var ssid = Utils.StrToGuid(Request["ssid"]);
                        var ret = bll.Delete(ssid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统繁忙！！" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        #endregion
        #region 项目查看
        public ActionResult ProjectView(Guid? pid)
        {
            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");

            ViewBag.listLangDict = new SelectList(CommonList.getLanglist(), "Key", "Value");
            ViewBag.listRegStatusDict = new SelectList(CommonList.getRegStatuslist(), "Key", "Value");
            ViewBag.listRecruitmentStatusDict = new SelectList(CommonList.getRecruitmentStatuslist(), "Key", "Value");
            ViewBag.listGenderDict = new SelectList(CommonList.getGenderlist(), "Key", "Value");
            ViewBag.listFollowUpTimeUnitDict = new SelectList(CommonList.getFollowUpTimeUnitlist(), "Key", "Value");
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(CommonList.getStatisticalEffectChiCTRPubliclist(), "Key", "Value");
            ViewBag.txtDataCollectionUnitDict = new SelectList(CommonList.getDataCollectionUnitlist(), "Key", "Value");
            ViewBag.txtDataManagemenBoardDict = new SelectList(CommonList.getDataManagemenBoardlist(), "Key", "Value");

            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {

                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.reverifyFailReason = proInfo.reverifyFailReason;
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                    model.regNumber = proInfo.regNumber;
                    model.Status = -1;
                    if (proInfo.status.HasValue)
                        model.Status = proInfo.status.Value;
                    model.TaskStatus = -1;
                    if (proInfo.taskStatus.HasValue)
                        model.TaskStatus = proInfo.taskStatus;
                }
            }

            return View(model);
        }
        #endregion
        #region 试验文件上传
        public ActionResult TrialDataUpload(int pageNo = 0)
        {
            Core.BLL.SA_ProjectDataUploadBLL bll = new Core.BLL.SA_ProjectDataUploadBLL();
            var sql = PetaPoco.Sql.Builder.Select("data.*,p.regNumber,p.publicTitleCN,p.publicTitleEN,p.ReleaseNumber")
                .From("SA_ProjectDataUpload data")
                .LeftJoin("SA_NewProject p")
                .On("p.pid=data.pid");
            sql = sql.Where(" p.createUserID=@0 ", UserProfile.CurrentProfile.UserID);
            sql = sql.OrderBy("data.CreateTime desc");
            var page_ret = bll.GetPageList<App.Models.ProjectDataUploadViewModel>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        public ActionResult TrialDataUploadAdd()
        {
            var Items = (new Core.BLL.SA_NewProjectBLL()).GetDataUploadByCreateUserId(UserProfile.CurrentProfile.UserID);
            ViewBag.projectlist = new SelectList(Items, "Pid", "regNumber");
            return View();
        }
        [HttpPost]
        public ActionResult TrialDataUploadAdd(Core.Models.SA_ProjectDataUpload model)
        {
            var bll = new Core.BLL.SA_ProjectDataUploadBLL();
            if (string.IsNullOrEmpty(model.FilePath))
            {
                return WriteJsonResponse(false, "请先上传文件再进行提交！");
            }
            if (model.Pid == default(Guid))
            {
                return WriteJsonResponse(false, "请先选择一个项目！");
            }
            if (new Core.BLL.SA_NewProjectBLL().CheckExistTrialDataUpload(model.Pid))
            {
                return WriteJsonResponse(false, "已经上传过数据，不能重复上传！");
            }
            model.PduId = Guid.NewGuid();
            model.CreateUserId = UserProfile.CurrentProfile.UserID;
            model.CreateTime = DateTime.Now;
            var ret = bll.Insert(model);
            if (ret)
            {
                return WriteJsonResponse(true, "添加成功");
            }
            else
            {
                return WriteJsonResponse(false, "添加失败");
            }
        }
        public ActionResult TrialDataUploadUpdate(Guid PduId)
        {
            var bll = new Core.BLL.SA_ProjectDataUploadBLL();
            var model = bll.GetById(PduId);
            return View(model);
        }
        [HttpPost]
        public ActionResult TrialDataUploadUpdate(Core.Models.SA_ProjectDataUpload model)
        {
            var bll = new Core.BLL.SA_ProjectDataUploadBLL();
            var info = bll.GetById(model.PduId);
            info.FilePath = model.FilePath;
            info.DataTitle = model.DataTitle;
            info.DataDesc = model.DataDesc;
            info.CreateTime = DateTime.Now;
            var ret = bll.Update(info);
            if (ret)
            {
                return WriteJsonResponse(true, "上传成功");
            }
            else
            {
                return WriteJsonResponse(false, "上传失败");
            }
        }

        public ActionResult TrialRepUpload(int pageNo = 0)
        {
            Core.BLL.SA_ProjectReportUploadBLL bll = new Core.BLL.SA_ProjectReportUploadBLL();
            var sql = PetaPoco.Sql.Builder.Select("data.*,p.regNumber,p.publicTitleCN,p.publicTitleEN,p.ReleaseNumber")
                .From("SA_ProjectReportUpload data")
                .LeftJoin("SA_NewProject p")
                .On("p.pid=data.pid");
            sql = sql.Where(" p.createUserID=@0 ", UserProfile.CurrentProfile.UserID);
            sql = sql.OrderBy("data.CreateTime desc");
            var page_ret = bll.GetPageList<App.Models.ProjectRepUploadViewModel>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        public ActionResult TrialRepUploadAdd()
        {
            var closeModel = new Core.BLL.SA_WebStatusBLL().GetAll().FirstOrDefault();
            var Lang = CultureInfo.CurrentUICulture.Name;
            if (closeModel != null && closeModel.IsOpen == false)
            {
                if (Lang == "zh-CN")
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                    closeModel.Content,
                                    Url.Action("Index", "UserPlatform"),
                                    "确定");
                }
                else
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                     closeModel.ContentEn,
                                     Url.Action("Index", "UserPlatform"),
                                     "确定");
                }
            }
            var Items = (new Core.BLL.SA_NewProjectBLL()).GetReportUploadByCreateUserId(UserProfile.CurrentProfile.UserID);
            ViewBag.projectlist = new SelectList(Items, "Pid", "regNumber");
            return View();
        }
        [HttpPost]
        public ActionResult TrialRepUploadAdd(Core.Models.SA_ProjectReportUpload model)
        {
            var bll = new Core.BLL.SA_ProjectReportUploadBLL();
            if (string.IsNullOrEmpty(model.FilePath))
            {
                return WriteJsonResponse(false, "请先上传文件再进行提交！");
            }
            if (model.Pid == default(Guid))
            {
                return WriteJsonResponse(false, "请先选择一个项目！");
            }
            if (new Core.BLL.SA_NewProjectBLL().CheckExistProjectReportUpload(model.Pid))
            {
                return WriteJsonResponse(false, "已经上传过数据，不能重复上传！");
            }
            model.PruId = Guid.NewGuid();
            model.CreateUserId = UserProfile.CurrentProfile.UserID;
            model.CreateTime = DateTime.Now;
            var ret = bll.Insert(model);
            if (ret)
            {
                return WriteJsonResponse(true, "添加成功");
            }
            else
            {
                return WriteJsonResponse(false, "添加失败");
            }
        }

        public ActionResult TrialRepUploadUpdate(Guid PruId)
        {
            var closeModel = new Core.BLL.SA_WebStatusBLL().GetAll().FirstOrDefault();
            var Lang = CultureInfo.CurrentUICulture.Name;
            if (closeModel != null && closeModel.IsOpen == false)
            {
                if (Lang == "zh-CN")
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                    closeModel.Content,
                                    Url.Action("Index", "UserPlatform"),
                                    "确定");
                }
                else
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                     closeModel.ContentEn,
                                     Url.Action("Index", "UserPlatform"),
                                     "确定");
                }
            }
            var bll = new Core.BLL.SA_ProjectReportUploadBLL();
            var model = bll.GetById(PruId);
            return View(model);
        }
        [HttpPost]
        public ActionResult TrialRepUploadUpdate(Core.Models.SA_ProjectReportUpload model)
        {
            var bll = new Core.BLL.SA_ProjectReportUploadBLL();
            var info = bll.GetById(model.PruId);
            info.FilePath = model.FilePath;
            info.ReportTitle = model.ReportTitle;
            info.ReportDesc = model.ReportDesc;
            info.CreateTime = DateTime.Now;
            var ret = bll.Update(model);
            if (ret)
            {
                return WriteJsonResponse(true, "上传成功");
            }
            else
            {
                return WriteJsonResponse(false, "上传失败");
            }
        }
        [HttpPost]
        public JsonResult GetUserProject(string search, int page, int page_limit)
        {
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_NewProject");
            sql = sql.Where(" createUserID=@0 ", UserProfile.CurrentProfile.UserID);
            sql = sql.Where($"regNumber like @0", $"%{search}%").OrderBy($"regNumber asc");
            var page_info = bll.GetPageList<Core.Models.SA_NewProject>(page, page_limit, sql);
            var Items = page_info.Items.ConvertAll(x => new { id = x.Pid, text = x.regNumber });
            return Json(new { items = Items, more = false }, JsonRequestBehavior.AllowGet);
        }
        public ActionResult UploaderFile(string types)
        {
            if (Request.Files.Count > 0)
            {
                var fileinfo = new FileInfo(Request.Files[0].FileName);
                var directiory_path = "/uploads/file/" + types;
                var filename = $"{DateTime.Now:yyyyMMddHHmmssffff}{fileinfo.Extension}";
                var localfile = Path.Combine(directiory_path, filename);
                filename = Path.Combine(Server.MapPath(directiory_path), filename);
                if (!Directory.Exists(Path.GetDirectoryName(filename)))
                {
                    Directory.CreateDirectory(Path.GetDirectoryName(filename));
                }
                Request.Files[0].SaveAs(filename);
                return Content(localfile);
            }
            else
            {
                return Content("");
            }
        }
        #endregion
        #region 项目重编辑
        public ActionResult RequestEditFlow(Guid? pid)
        {
            var bll = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var info = bll.GetById(pid.Value);
                if (info != null)
                {
                    ViewBag.regNumber = info.regNumber;
                    ViewBag.ReleaseNumber = info.ReleaseNumber;
                    ViewBag.publicTitle = (info.filloutLanguage == "1009002") ? info.publicTitleEN : info.publicTitleCN;
                    var model = new Core.Models.SA_ProjectRequestEditFlow();
                    model.Pid = pid;
                    if (info.createUserID != UserProfile.CurrentProfile.UserID)
                    {
                        SetMessageAlert(true, "系统提示 System prompt", "无效请求! <br/> Invalid request.", Url.Action("ProjectList"), "确定");
                    }
                    return View(model);
                }
            }
            return View();
        }
        [HttpPost]
        public ActionResult RequestEditFlow(Core.Models.SA_ProjectRequestEditFlow info)
        {
            if (info.Pid.HasValue)
            {
                var text3 = Utils.SaveRequestFile("editRequestAttachment", $"/uploads/file/{info.Pid.Value.ToString("N")}/1/");
                if (text3 != string.Empty && !text3.Equals("1"))
                {
                    info.EditRequestAttachment = text3;
                }
                else
                {
                    return WriteJsonResponse(false, "请上传支持试验修改的相应试验方案");
                }
                var text4 = Utils.SaveRequestFile("editRequestAttachment2", $"/uploads/file/{info.Pid.Value.ToString("N")}/2/");
                if (text4 != string.Empty && !text4.Equals("1"))
                {
                    info.EditRequestAttachment2 = text4;
                }
                else
                {
                    return WriteJsonResponse(false, "请上传支持试验修改的相应伦理批件");
                }
                if (string.IsNullOrEmpty(info.EditRequestReason))
                {
                    return WriteJsonResponse(false, "请填写修改原因与计划修改内容");
                }
                var bll = new Core.BLL.SA_NewProjectBLL();
                var ret = bll.RequestEditFlow(info.Pid.Value, info.EditRequestReason, info.EditRequestAttachment, info.EditRequestAttachment2, UserProfile.CurrentProfile.UserID);
                if (ret)
                {
                    return WriteJsonResponse(true, "提交申请成功");
                }
                else
                {
                    return WriteJsonResponse(false, "提交申请失败");
                }
            }
            return WriteJsonResponse(false, "提交申请失败");
        }
        #endregion
        #region 下载文件
        public ActionResult DownFile(Guid pid, string path)
        {
            var file = new FileInfo(path);
            string filePath = Server.MapPath(path);
            if (System.IO.File.Exists(filePath))
            {
                return File(new FileStream(filePath, FileMode.Open), "text/plain", file.Name);
            }
            else
            {
                return Content("File not found");
            }
        }
        #endregion
        #region 消息跳转
        public ActionResult MessageAction(Guid? Nid, string Type)
        {
            if (Nid.HasValue)
            {
                if (Type == "Notice")
                {
                    var n = new Core.BLL.SA_NoticeBLL().GetById(Nid.Value);
                    if (string.IsNullOrWhiteSpace(n.AccessUrl))
                    {
                        return Content("该项目不存在");
                    }
                    n.IsRead = 1;
                    new Core.BLL.SA_NoticeBLL().Update(n);
                    if (n.Type.HasValue && n.Type.Value == 1)
                    {
                        var bllProj = new Core.BLL.SA_NewProjectBLL();
                        var proInfo = bllProj.GetById(n.Pid.Value);
                        var url = "";
                        if (proInfo != null)
                        {
                            switch ((Core.ProjectTaskStatus)proInfo.taskStatus)
                            {
                                case Core.ProjectTaskStatus.无:
                                    break;
                                case Core.ProjectTaskStatus.待分配:
                                    return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                    break;
                                case Core.ProjectTaskStatus.已分配:
                                    return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                    break;
                                case Core.ProjectTaskStatus.待复核:
                                    return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                    break;
                                case Core.ProjectTaskStatus.复核未通过:
                                    return RedirectToAction("ProjectEdit", "UserPlatform", new { Pid = proInfo.Pid });
                                    break;
                                case Core.ProjectTaskStatus.复核通过:
                                    return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                    break;
                                case Core.ProjectTaskStatus.再修改申请:
                                    return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                    break;
                                case Core.ProjectTaskStatus.申请通过:
                                    return RedirectToAction("ProjectEdit", "UserPlatform", new { Pid = proInfo.Pid });
                                    break;
                                case Core.ProjectTaskStatus.申请未通过:
                                    return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                    else
                    {
                        return RedirectToAction("Notices", "UserPlatform", new { Nid = n.Nid });
                    }
                }
                else if (Type == "Message")
                {
                    var m = new Core.BLL.SA_MessagingBLL().GetById(Nid.Value);
                    var bllProj = new Core.BLL.SA_NewProjectBLL();
                    var proInfo = bllProj.GetById(m.Pid.Value);
                    var url = "";
                    if (proInfo != null)
                    {
                        switch ((Core.ProjectTaskStatus)proInfo.taskStatus)
                        {
                            case Core.ProjectTaskStatus.无:
                                break;
                            case Core.ProjectTaskStatus.待分配:
                                return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                break;
                            case Core.ProjectTaskStatus.已分配:
                                return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                break;
                            case Core.ProjectTaskStatus.待复核:
                                return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                break;
                            case Core.ProjectTaskStatus.复核未通过:
                                return RedirectToAction("ProjectEdit", "UserPlatform", new { Pid = proInfo.Pid });
                                break;
                            case Core.ProjectTaskStatus.复核通过:
                                return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                break;
                            case Core.ProjectTaskStatus.再修改申请:
                                return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                break;
                            case Core.ProjectTaskStatus.申请通过:
                                return RedirectToAction("ProjectEdit", "UserPlatform", new { Pid = proInfo.Pid });
                                break;
                            case Core.ProjectTaskStatus.申请未通过:
                                return RedirectToAction("ProjectView", "UserPlatform", new { Pid = proInfo.Pid });
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            else
            {
                return Content("参数传入错误");
            }
            return View();
        }
        #endregion
        #region 数据检索
        public ActionResult TrialSearch(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listRegStatus = Request["listRegStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            ViewBag.txtSecondaryID = Request["txtSecondaryID"];
            ViewBag.txtApplier = Request["txtApplier"];
            ViewBag.txtStudyLeader = Request["txtStudyLeader"];
            ViewBag.listCreateYear = Request["listCreateYear"];
            ViewBag.txtSponsor = Request["txtSponsor"];
            ViewBag.txtSecSponsor = Request["txtSecSponsor"];
            ViewBag.txtSourceOfSpends = Request["txtSourceOfSpends"];
            ViewBag.txtStudyAilment = Request["txtStudyAilment"];
            ViewBag.txtStudyAilmentCode = Request["txtStudyAilmentCode"];
            ViewBag.listStudyType = Request["listStudyType"];
            ViewBag.listStudyStage = Request["listStudyStage"];
            ViewBag.listStudyDesign = Request["listStudyDesign"];
            ViewBag.txtMinStudyExecuteTime = Request["txtMinStudyExecuteTime"];
            ViewBag.txtMaxStudyExecuteTime = Request["txtMaxStudyExecuteTime"];
            ViewBag.listRecruitmentStatus = Request["listRecruitmentStatus"];
            ViewBag.listGender = Request["listGender"];
            ViewBag.listAgreeToSign = Request["listAgreeToSign"];
            ViewBag.txtMeasure = Request["txtMeasure"];
            ViewBag.txtIntercode = Request["txtIntercode"];
            ViewBag.listEthicalCommitteeSanction = Request["listEthicalCommitteeSanction"];
            ViewBag.listWhetherPublic = Request["listWhetherPublic"];
            ViewBag.listIsUploadRF = Request["listIsUploadRF"];

            ViewBag.txtProvince = Request["txtProvince"];
            ViewBag.txtCountry = Request["txtCountry"];
            ViewBag.txtCity = Request["txtCity"];
            ViewBag.txtInstitution = Request["txtInstitution"];
            ViewBag.txtInstitutionLevel = Request["txtInstitutionLevel"];

            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.listRegStatusDict = new SelectList(CommonList.getRegStatuslist(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listRegStatus);
            ViewBag.listCreateYearDict = new SelectList(CommonList.getCreateYearlist(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listCreateYear);
            ViewBag.listRecruitmentStatusDict = new SelectList(CommonList.getRecruitmentStatuslist(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(CommonList.getGenderlist(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listGender);
            ViewBag.listEthicalCommitteeSanctionDict = new SelectList(CommonList.getTrueOrFalse(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listEthicalCommitteeSanction);
            ViewBag.listWhetherPublicDict = new SelectList(CommonList.getTrueOrFalse(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listWhetherPublic);
            ViewBag.listIsUploadRFDict = new SelectList(CommonList.getTrueOrFalse(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listIsUploadRF);
            ViewBag.listAgreeToSignDict = new SelectList(CommonList.getTrueOrFalse(), "Key", IsLang("en-US") ? "EN" : "CN", ViewBag.listAgreeToSign);
            ViewBag.listStudyTypeDict = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = IsLang("en-US") ? x.DescriptionEn : x.DescriptionCn }), "Key", "Text", ViewBag.listStudyType);
            ViewBag.listStudyStageDict = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = IsLang("en-US") ? x.DescriptionEn : x.DescriptionCn }), "Key", "Text", ViewBag.listStudyStage);
            ViewBag.listStudyDesignDict = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = IsLang("en-US") ? x.DescriptionEn : x.DescriptionCn }), "Key", "Text", ViewBag.listStudyDesign);

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");

            sql = sql.Where("status=3");

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");

            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");

            if (!string.IsNullOrEmpty(ViewBag.txtSubjectID))
                sql = sql.Where(" studyID=@0 ", $"%{ViewBag.txtSubjectID}%");

            if (!string.IsNullOrEmpty(ViewBag.listRegStatus))
                sql = sql.Where(" registrationStatus = @0 ", ViewBag.listRegStatus);

            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);

            if (!string.IsNullOrEmpty(ViewBag.txtSecondaryID))
                sql = sql.Where(" secondaryID = @0 ", ViewBag.txtSecondaryID);

            if (!string.IsNullOrEmpty(ViewBag.txtApplier))
                sql = sql.Where(" (applicantCN like @0 or applicantEN like @0) ", $"%{ViewBag.txtApplier}%");

            if (!string.IsNullOrEmpty(ViewBag.txtStudyLeader))
                sql = sql.Where(" (studyLeaderCN = @0 or studyLeaderEN = @0) ", ViewBag.txtStudyLeader);

            if (!string.IsNullOrEmpty(ViewBag.listCreateYear))
                sql = sql.Where("year(regTime)= @0 ", ViewBag.listCreateYear);

            if (!string.IsNullOrEmpty(ViewBag.txtSponsor))
                sql = sql.Where(" (primarySponsorCN = @0 or primarySponsorEN = @0) ", ViewBag.txtSponsor);

            //从表 SA_SecondarySponsor
            if (!string.IsNullOrEmpty(ViewBag.txtSecSponsor))
                sql = sql.Where(" pid in(select pid from SA_SecondarySponsor AS SS where (SS.institutionCN  = @0 or SS.institutionEN = @0)) ", ViewBag.txtSecSponsor);

            if (!string.IsNullOrEmpty(ViewBag.txtSourceOfSpends))
                sql = sql.Where(" (sourceFundingCN = @0 or sourceFundingEN = @0) ", ViewBag.txtSourceOfSpends);

            if (!string.IsNullOrEmpty(ViewBag.txtStudyAilment))
                sql = sql.Where("  (targetDiseaseCN = @0 or targetDiseaseEN = @0) ", ViewBag.txtStudyAilment);

            if (!string.IsNullOrEmpty(ViewBag.txtStudyAilmentCode))
                sql = sql.Where(" (targetCode = @0) ", ViewBag.txtStudyAilmentCode);

            if (!string.IsNullOrEmpty(ViewBag.listStudyType))
                sql = sql.Where(" (studyTypeID = @0) ", ViewBag.listStudyType);

            if (!string.IsNullOrEmpty(ViewBag.listStudyStage))
                sql = sql.Where(" (studyPhaseID = @0) ", ViewBag.listStudyStage);

            if (!string.IsNullOrEmpty(ViewBag.listStudyDesign))
                sql = sql.Where(" (studyDesignID = @0) ", ViewBag.listStudyDesign);

            if (!string.IsNullOrEmpty(ViewBag.txtMinStudyExecuteTime))
                sql = sql.Where(" (studyTimeStart >= @0) ", ViewBag.txtMinStudyExecuteTime);

            if (!string.IsNullOrEmpty(ViewBag.txtMaxStudyExecuteTime))
                sql = sql.Where(" (studyTimeEnd <= @0) ", ViewBag.txtMaxStudyExecuteTime);

            if (!string.IsNullOrEmpty(ViewBag.listRecruitmentStatus))
                sql = sql.Where(" (recruitingStatus = @0) ", ViewBag.listRecruitmentStatus);

            if (!string.IsNullOrEmpty(ViewBag.listGender))
                sql = sql.Where(" (sex = @0) ", ViewBag.listGender);

            if (!string.IsNullOrEmpty(ViewBag.listAgreeToSign))
                sql = sql.Where(" (signConsent = @0) ", ViewBag.listAgreeToSign);

            //从表 SA_Interventions
            if (!string.IsNullOrEmpty(ViewBag.txtMeasure))
                sql = sql.Where(" pid in(select pid from SA_Interventions AS SI where (SI.interventionCN  = @0 or SI.interventionEN = @0)) ", ViewBag.txtMeasure);

            //从表 SA_Interventions
            if (!string.IsNullOrEmpty(ViewBag.txtIntercode))
                sql = sql.Where(" pid in(select pid from SA_Interventions AS SI where (SI.interventionCode  = @0 or SI.interventionCode = @0)) ", ViewBag.txtIntercode);

            if (!string.IsNullOrEmpty(ViewBag.listEthicalCommitteeSanction))
                sql = sql.Where(" (approvedCommittee = @0) ", ViewBag.listEthicalCommitteeSanction);

            if (!string.IsNullOrEmpty(ViewBag.listWhetherPublic))
                sql = sql.Where(" (whetherPublic = @0) ", ViewBag.listWhetherPublic);

            if (!string.IsNullOrEmpty(ViewBag.listIsUploadRF))
            {
                if (ViewBag.listIsUpload == 1)
                {
                    sql = sql.Where("( isnull(fileExperimentalresults,'') <> '' )", ViewBag.listIsUploadRF);
                }
                else
                {
                    sql = sql.Where("( isnull(FileExperimentalresults,'') = '' )", ViewBag.listIsUploadRF);
                }
            }
            if (!string.IsNullOrEmpty(ViewBag.txtProvince))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.provinceCN  = @0 or SS.provinceEN = @0)) ", ViewBag.txtProvince);
            if (!string.IsNullOrEmpty(ViewBag.txtCountry))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.countryCN  = @0 or SS.countryEN = @0)) ", ViewBag.txtCountry);
            if (!string.IsNullOrEmpty(ViewBag.txtCity))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.cityCN  = @0 or SS.cityEN = @0)) ", ViewBag.txtCity);
            if (!string.IsNullOrEmpty(ViewBag.txtInstitution))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.hospitalCN  = @0 or SS.hospitalEN = @0)) ", ViewBag.txtInstitution);
            if (!string.IsNullOrEmpty(ViewBag.txtInstitutionLevel))
                sql = sql.Where(" pid in(select pid from SA_ResearchAddress AS SS where (SS.levelInstitutionCN  = @0 or SS.levelInstitutionEN = @0)) ", ViewBag.txtInstitutionLevel);


            sql = sql.OrderBy("regTime desc");
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        #endregion

        #region 注册
        [HttpGet]
        [UserAuth(Validate = false)]
        public ActionResult Register()
        {
            var model = new App.Models.RegisterModel();
            var sexList = Common.CommonList.getSexList();
            ViewBag.SexList = new SelectList(sexList, "Key", "Value");
            var countryList = Common.CommonList.getCountryList();
            ViewBag.CountryList = new SelectList(countryList, "id", "name");
            return View(model);
        }
        [HttpGet]
        [UserAuth(Validate = false)]
        public ActionResult RegistMessageInfo(MessageInfoModel info)
        {
            return View(info);
        }

        [HttpPost]
        [UserAuth(Validate = false)]
        public ActionResult Register(App.Models.RegisterModel model)
        {
            var sexList = Common.CommonList.getSexList();
            ViewBag.SexList = new SelectList(sexList, "Key", "Value");
            var countryList = Common.CommonList.getCountryList();
            ViewBag.CountryList = new SelectList(countryList, "id", "name");
            if (!model.Password.Equals(model.RePassword))
            {
                ModelState.AddModelError("", "密码不一致");
                return View(model);
            }
            if (!Regex.IsMatch(model.Email, "^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"))
            {
                ModelState.AddModelError("", "邮箱格式不正确");
                return View(model);
            }
            if (string.IsNullOrEmpty(model.Username) || string.IsNullOrEmpty(model.Password))
            {
                ModelState.AddModelError("", "用户名密码不能为空");
                return View(model);
            }
            var bll = new Core.BLL.SA_UsersBLL();
            var IsExists = bll.CheckUserName(model.Username);
            if (!IsExists)
            {
                ModelState.AddModelError("", "用户名已存在");
                return View(model);
            }
            if (!string.IsNullOrWhiteSpace(model.Email))
            {
                var hasEmail = bll.CheckEmail(model.Email);
                if (!hasEmail)
                {
                    ModelState.AddModelError("", "Email已存在");
                    return View(model);
                }
            }
            var entity = new Core.Models.SA_Users();
            entity.Uid = Guid.NewGuid();
            entity.Username = model.Username;
            entity.Name = model.Name;
            entity.Password = bll.ExecutePassword(model.Username, model.Password);
            entity.Email = model.Email;
            entity.RegDate = DateTime.Now;
            entity.RegIP = IP.GetIP4Address();
            entity.Sex = model.Sex;
            entity.Phone = model.Phone;
            entity.Country = model.Country;
            entity.CellPhone = model.CellPhone;
            entity.RegUnit = model.RegUnit;
            entity.RegAddress = model.RegAddress;
            entity.Status = 1;
            var res = new Core.BLL.SA_UsersBLL().Insert(entity);
            var message = new MessageInfoModel();
            if (res)
            {
                message.IsError = true;
                message.MessageBody = "恭喜您注册成功！";
                message.MessageTitle = "系统提示";
                message.TargetUrl = Url.Action("Login", "UserPlatform");
            }
            else
            {
                message.IsError = false;
                message.MessageBody = "注册失败，系统繁忙，请稍后在进行尝试！";
                message.MessageTitle = "系统提示";
                message.TargetUrl = Url.Action("Login", "UserPlatform");
            }
            return View("RegistMessageInfo", message);
        }
        [HttpPost]
        [UserAuth(Validate = false)]
        public ActionResult isExistsUserName(string user_name)
        {
            var bll = new Core.BLL.SA_UsersBLL();
            var isExist = bll.CheckUserName(user_name);
            return Json(isExist);
        }
        #endregion
        #region 新闻查看
        public ActionResult NewsInfo(Guid Nid)
        {
            var model = new Core.BLL.SA_NewsBLL().GetById(Nid);
            return View(model);
        }
        #endregion
        #region 登录
        [HttpGet]
        [UserAuth(Validate = false)]
        public ActionResult Login()
        {
            var closeModel = new Core.BLL.SA_WebStatusBLL().GetAll().FirstOrDefault();
            var Lang = CultureInfo.CurrentUICulture.Name;

            if (closeModel != null && closeModel.IsShowLogin == true)
            {
                if (Lang == "zh-CN")
                {
                    ViewData["Content"] = closeModel.Content;
                }
                else
                {
                    ViewData["Content"] = closeModel.ContentEn;
                }
            }
            var model = new Models.LoginModel() { Encrypt = 1 };
            return View(model);
        }

        [HttpPost]
        [UserAuth(Validate = false)]
        public ActionResult Login(Models.LoginModel model)
        {
            var bll = new Core.BLL.SA_UsersBLL();
            if (Session["ValidateCode"] == null || model.Captcha != Session["ValidateCode"].ToString())
            {
                ModelState.AddModelError("", Resources.Global.UR_LOGIN_验证码错误);
                Session["ValidateCode"] = null;
                return View(model);
            }
            if (model.Encrypt == 1)
            {
                JsEncryptHelper jsHelper = new JsEncryptHelper();
                model.Password = jsHelper.Decrypt(model.Password);
            }
            var key = $"Errlogin_{model.Username}";
            var key2 = $"Errlogin_{model.Username}_time";
            if (HttpContext.Application[key] != null && (int)HttpContext.Application[key] >= 3)
            {
                if (HttpContext.Application[key2] != null && (DateTime.Now - (DateTime)HttpContext.Application[key2]).TotalMinutes > 30)
                {
                    HttpContext.Application[key] = 0;
                    HttpContext.Application[key2] = DateTime.Now;
                }
                else
                {
                    ModelState.AddModelError("", Resources.Global.UR_LOGIN_锁定);
                    return View(model);
                }
            }
            var user = bll.Login(model.Username, model.Password);
            if (user == null)
            {
                if (HttpContext.Application[key] == null)
                {
                    HttpContext.Application[key] = 1;
                    HttpContext.Application[key2] = DateTime.Now;
                }
                else
                {
                    HttpContext.Application[key] = (int)HttpContext.Application[key] + 1;
                    HttpContext.Application[key2] = DateTime.Now;
                }
                ModelState.AddModelError("", Resources.Global.UR_LOGIN_用户名或密码错误);
                return View(model);
            }
            if (user.Status == 2)
            {
                ModelState.AddModelError("", Resources.Global.UR_LOGIN_此账号正在审核中);
                return View(model);
            }
            else if (user.Status == 0)
            {
                ModelState.AddModelError("", Resources.Global.UR_LOGIN_此账号未通过审核请联系管理员);
                return View(model);
            }
            user.LastLoginDate = DateTime.Now;
            user.LastLoginIp = Common.IP.GetIP4Address();
            new Core.BLL.SA_UsersBLL().Update(user);
            Models.UserProfile.CurrentProfile.UserID = user.Uid;
            Models.UserProfile.CurrentProfile.InitUser(user.Uid);
            Models.UserProfile.CurrentProfile.SaveProfile();

            return RedirectToAction("Index", "UserPlatform");
        }
        #endregion
        #region 退出登录
        public ActionResult LogOut()
        {
            Session.Clear();
            return RedirectToAction("Index", "Home");
        }
        #endregion
        #region 加载菜单

        [ChildActionOnly]
        public ActionResult HomeMenu()
        {
            var list = new Core.BLL.SA_SysFunsBLL().GetAll().Where(x => x.FunFloor.Length >= 9 && x.FunFloor.IndexOf("100200") == 0).ToList();

            List<Models.MenuModel> menuList = new List<Models.MenuModel>();
            var first = list.Where(x => x.FunFloor.Length == 9).OrderBy(x => x.FunFloor).ToList();
            foreach (var item in first)
            {

                var model = new Models.MenuModel();
                model.Floor = item.FunFloor;
                model.Name = item.FunName;
                model.Url = item.FunURL;

                var childList = list.Where(x => x.FunFloor.IndexOf(item.FunFloor) == 0 && x.FunFloor.Length == item.FunFloor.Length + 3).OrderBy(x => x.FunFloor).ToList();
                if (childList.Count > 0)
                {
                    AddHeadMenu(model, childList, list);
                }
                menuList.Add(model);
            }

            return PartialView(menuList);
        }

        private void AddHeadMenu(Models.MenuModel Menu, List<Core.Models.SA_SysFuns> children, List<Core.Models.SA_SysFuns> allPower)
        {
            foreach (var item in children)
            {

                var model = new Models.MenuModel();
                model.Floor = item.FunFloor;
                model.Name = item.FunName;
                model.Url = item.FunURL;

                var childList = allPower.Where(x => x.FunFloor.IndexOf(item.FunFloor) == 0 && x.FunFloor.Length == item.FunFloor.Length + 3).OrderBy(x => x.FunFloor).ToList();
                if (childList.Count > 0)
                {
                    AddHeadMenu(model, childList, allPower);
                }
                Menu.Child.Add(model);
            }
        }
        #endregion
        #region 个人资料
        public ActionResult PersonalData()
        {
            var sexList = Common.CommonList.getSexList();
            ViewBag.SexList = new SelectList(sexList, "Key", "Value");
            var countryList = Common.CommonList.getCountryList();
            ViewBag.CountryList = new SelectList(countryList, "id", "name");
            var user = new Core.BLL.SA_UsersBLL().GetById(Models.UserProfile.CurrentProfile.UserID);
            user.Photograph = "http://www.placehold.it/200x150/EFEFEF/AAAAAA&text=no+image";
            var model = new Models.SysUserModel();
            model.Uid = user.Uid;
            model.Username = user.Username;
            model.Password = user.Password;
            model.Name = user.Name;
            model.Email = user.Email;
            model.RegDate = user.RegDate;
            model.RegIP = user.RegIP;
            model.LastLoginDate = user.LastLoginDate;
            model.LastLoginIp = user.LastLoginIp;
            model.Sex = user.Sex;
            model.Phone = user.Phone;
            model.Country = user.Country;
            model.CellPhone = user.CellPhone;
            model.RegUnit = user.RegUnit;
            model.RegAddress = user.RegAddress;
            model.Photograph = user.Photograph;
            return View(model);
        }

        [HttpPost]
        public ActionResult PersonalData(Models.SysUserModel model)
        {
            if (!string.IsNullOrWhiteSpace(model.Email))
            {
                var hasEmail = new Core.BLL.SA_UsersBLL().CheckEmail(model.Email, model.Uid.ToString());
                if (!hasEmail)
                {
                    ModelState.AddModelError("", "Email已存在");
                    return View(model);
                }
            }
            var user = new Core.BLL.SA_UsersBLL().GetById(model.Uid);
            user.Name = model.Name;
            user.Email = model.Email;
            user.Sex = model.Sex;
            user.Phone = model.Phone;
            user.Country = model.Country;
            user.CellPhone = model.CellPhone;
            user.RegUnit = model.RegUnit;
            user.RegAddress = model.RegAddress;
            var files = Request.Files;
            if (files.Count > 0 && !string.IsNullOrWhiteSpace(files[0].FileName))
            {
                var f = new FileInfo(files[0].FileName);
                var fileName = Common.CreateFileUpPath.GetSysUserPhotoPath() + Guid.NewGuid().ToString() + f.Extension;
                var path = Server.MapPath(fileName);
                user.Photograph = fileName;
                files[0].SaveAs(path);
            }
            var res = new Core.BLL.SA_UsersBLL().Update(user);
            if (res)
            {
                return RedirectToAction("index", "UserPlatform");
            }
            else
            {
                var sexList = Common.CommonList.getSexList();
                ViewBag.SexList = new SelectList(sexList, "Key", "Value");
                var countryList = Common.CommonList.getCountryList();
                ViewBag.CountryList = new SelectList(countryList, "id", "name");
                ModelState.AddModelError("", "个人资料保存失败");
                return View(model);
            }
        }
        #endregion
        #region 修改密码
        public ActionResult ModifyPwd()
        {
            var user = new Core.BLL.SA_UsersBLL().GetById(Models.UserProfile.CurrentProfile.UserID);
            var model = new Models.ModifyPwdModel();
            model.Uid = user.Uid;
            model.Username = user.Username;
            return View(model);
        }

        [HttpPost]
        public ActionResult ModifyPwd(Models.ModifyPwdModel model)
        {
            var bll = new Core.BLL.SA_UsersBLL();
            var user = bll.GetById(Models.UserProfile.CurrentProfile.UserID);
            model.Username = user.Username;
            var pwd = bll.ExecutePassword(user.Username, model.OldPassword);
            if (pwd != user.Password)
            {
                ModelState.AddModelError("", "原始密码错误");
                return View(model);
            }
            if (!model.NewPassword.Equals(model.ReNewPassword))
            {
                ModelState.AddModelError("", "新密码与确认密码不一致");
                return View(model);
            }
            user.Password = bll.ExecutePassword(user.Username, model.NewPassword);
            var res = new Core.BLL.SA_UsersBLL().Update(user);
            if (res)
            {
                return RedirectToAction("index", "UserPlatform");
            }
            else
            {
                ModelState.AddModelError("", "修改密码失败");
                return View(model);
            }
        }
        #endregion
        #region 站内信
        public ActionResult Notices(Guid Nid)
        {
            var notice = new Core.BLL.SA_NoticeBLL().GetById(Nid);
            if (notice.IsRead.Value != 1)
            {
                notice.IsRead = 1;
                new Core.BLL.SA_NoticeBLL().Update(notice);
            }
            return View(notice);
        }

        public ActionResult NoticeList(int pageNo = 0)
        {

            Core.BLL.SA_NoticeBLL bll = new Core.BLL.SA_NoticeBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_Notice");
            sql = sql.Where(" ReceiveUserId = @0 ", Models.UserProfile.CurrentProfile.UserID);
            sql = sql.OrderBy(" IsRead asc,CreateTime desc");
            var page_ret = bll.GetPageList<Core.Models.SA_Notice>(pageNo, PageSize, sql);
            var resPage = new Page<Models.NoticeListModel>();
            resPage.Items = page_ret.Items.ConvertAll(x => new Models.NoticeListModel
            {
                Nid = x.Nid,
                Title = x.Title,
                Content = x.Content,
                AccessUrl = x.AccessUrl,
                SendUserId = x.SendUserId.HasValue ? new Core.BLL.SA_SysUserBLL().GetUserName(x.SendUserId.Value) : "",
                ReceiveUserId = x.ReceiveUserId.HasValue ? new Core.BLL.SA_UsersBLL().GetUserName(x.ReceiveUserId.Value) : "",
                IsRead = x.IsRead.Value == 1 ? "已读/Read" : "未读/Unread",
                CreateTime = x.CreateTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                TitleEn = x.TitleEN,
                ContentEn = x.ContentEN
            }).ToList();
            resPage.ItemsPerPage = page_ret.ItemsPerPage;
            resPage.TotalItems = page_ret.TotalItems;
            resPage.TotalPages = page_ret.TotalPages;
            resPage.CurrentPage = page_ret.CurrentPage;
            resPage.Context = GetUrlParms();
            return View(resPage);
        }

        public JsonResult NoticeDel(Guid Nid)
        {
            var msg = "";
            var res = new Core.BLL.SA_NoticeBLL().Delete(Nid);
            if (res)
            {
                return WriteJsonResponse(true, "删除消息成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "删除消息失败", false);
            }
        }
        #endregion
        #region 首页
        public ActionResult Index()
        {
            var proBll = new Core.BLL.SA_NewProjectBLL();
            var user = new Core.BLL.SA_UsersBLL().GetById(ITMCTR.App.Models.UserProfile.CurrentProfile.UserID);
            //待审核
            int proToBeReview = proBll.db.ExecuteScalar<int>($"select count(1) from SA_NewProject where createUserID = @0 AND (status = 1 or status = 2 )", user.Uid);
            //已通过审核
            int proSuccess = proBll.db.ExecuteScalar<int>($"select count(1) from SA_NewProject where status=3 AND createUserID =@0", user.Uid);
            //查询项目总数，自己建的
            int proSum = proBll.db.ExecuteScalar<int>($"select count(1) from SA_NewProject where createUserID =@0 AND status <> 0", user.Uid);
            ViewBag.proToBeReview = proToBeReview;
            ViewBag.proSuccess = proSuccess;
            ViewBag.proSum = proSum;

            var ReadNotices = new Core.BLL.SA_NoticeBLL().GetListByReceiveUserId(ITMCTR.App.Models.UserProfile.CurrentProfile.UserID);
            ViewBag.NoReadList = ReadNotices.Where(x => x.IsRead == 0).ToList();
            ViewBag.IsReadList = ReadNotices.Where(x => x.IsRead == 1).ToList();

            var news = new Core.BLL.SA_NewsBLL().GetAll().Where(x => x.IsDeleted.HasValue && x.IsDeleted == 0
            && x.IsRelease.HasValue && x.IsRelease.Value == 1
            && x.IsViewIndex.HasValue && x.IsViewIndex.Value == 1).OrderByDescending(x => x.CreateTime).ToList();
            ViewBag.News = news;
            return View();
        }
        #endregion
        #region 获取实时站内信
        [HttpGet]
        public JsonResult LoadMessage()
        {
            var userId = Models.UserProfile.CurrentProfile.UserID;
            var list = new Core.BLL.SA_NoticeBLL().GetNoReadList(userId).ConvertAll(x => new NoticeModels
            {
                Nid = x.Nid,
                Title = x.Title,
                CreateTime = x.CreateTime.Value.ToString("yyyy-MM-dd"),
                TitleEN = x.TitleEN,
                ContentEN = x.ContentEN,
                Type = "Notice"
            }).ToList();

            var messages = new Core.BLL.SA_MessagingBLL().GetOwnListByUid(ITMCTR.App.Models.UserProfile.CurrentProfile.UserID);
            var NoMsgList = messages.Where(x => x.UserRead == 0).ToList().ConvertAll(x => new NoticeModels
            {
                Nid = x.MessagingId,
                Title = "您有一条消息待回复",
                CreateTime = x.CreateTime.ToString("yyyy-MM-dd"),
                TitleEN = "You have a message to reply",
                ContentEN = "Click to view",
                Type = "Message"
            });
            list.AddRange(NoMsgList);
            return Json(new { dataList = list }, JsonRequestBehavior.AllowGet);
        }

        #endregion
        #region 站内信设置已读
        public JsonResult SetNoticeRead(Guid Nid)
        {
            try
            {
                var model = new Core.BLL.SA_NoticeBLL().GetById(Nid);
                model.IsRead = 1;
                new Core.BLL.SA_NoticeBLL().Update(model);
                if (string.IsNullOrWhiteSpace(model.AccessUrl))
                    model.AccessUrl = "";
                return Json(new { Success = true, url = model.AccessUrl }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { Success = false, url = "" }, JsonRequestBehavior.AllowGet);
            }
        }
        #endregion
        #region 待修改待审核
        public ActionResult ModifiedReviewed(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" createUserID=@0 and (status=1 or status = 2 or(status = 3 and taskstatus = 9))", UserProfile.CurrentProfile.UserID);

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
            sql = sql.OrderBy("regTime desc");

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        #endregion
        #region 异步刷新站内信
        public JsonResult ajaxLoadNotice()
        {
            try
            {
                var ReadNotices = new Core.BLL.SA_NoticeBLL().GetListByReceiveUserId(ITMCTR.App.Models.UserProfile.CurrentProfile.UserID);
                var NoReadList = ReadNotices.Where(x => x.IsRead == 0).ToList().ConvertAll(x => new NoticeModels
                {
                    Nid = x.Nid,
                    Title = x.Title,
                    CreateTime = x.CreateTime.Value.ToString("yyyy-MM-dd"),
                    TitleEN = x.TitleEN,
                    ContentEN = x.ContentEN,
                    Type = "Notice"
                }).ToList();
                var IsReadList = ReadNotices.Where(x => x.IsRead == 1).ToList().ConvertAll(x => new NoticeModels
                {
                    Nid = x.Nid,
                    Title = x.Title,
                    CreateTime = x.CreateTime.Value.ToString("yyyy-MM-dd"),
                    TitleEN = x.TitleEN,
                    ContentEN = x.ContentEN,
                    Type = "Notice"
                }).ToList();

                var messages = new Core.BLL.SA_MessagingBLL().GetOwnListByUid(ITMCTR.App.Models.UserProfile.CurrentProfile.UserID);
                var NoMsgList = messages.Where(x => x.UserRead == 0).ToList().ConvertAll(x => new NoticeModels
                {
                    Nid = x.MessagingId,
                    Title = "您有一条消息待回复",
                    CreateTime = x.CreateTime.ToString("yyyy-MM-dd"),
                    TitleEN = "You have a message to reply",
                    ContentEN = "Click to view",
                    Type = "Message"
                });
                var IsReadMsgList = messages.Where(x => x.UserRead == 1).ToList().ConvertAll(x => new NoticeModels
                {
                    Nid = x.MessagingId,
                    Title = "您有一条消息待回复",
                    CreateTime = x.CreateTime.ToString("yyyy-MM-dd"),
                    TitleEN = "You have a message to reply",
                    ContentEN = "Click to view",
                    Type = "Message"
                });
                NoReadList.AddRange(NoMsgList);
                IsReadList.AddRange(IsReadMsgList);

                return Json(new { Success = true, NoReadList = NoReadList, IsReadList = IsReadList }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { Success = false }, JsonRequestBehavior.AllowGet);
            }

        }
        #endregion

        [ChildActionOnly]
        public ActionResult SecondBackResultView(Guid? Pid, string ViewName)
        {

            var list = new List<Core.Models.SA_ProjectVerifyTaskFlowRecord>();
            ViewData["ViewName"] = ViewName;
            #region 二审拒绝原因
            if (Pid.HasValue)
            {
                Core.Models.SA_NewProject proInfo = new Core.BLL.SA_NewProjectBLL().GetById(Pid.Value);
                if (proInfo.taskStatus == (int)Core.ProjectTaskStatus.复核未通过)
                {
                    list = new Core.BLL.SA_ProjectVerifyTaskFlowRecordBLL()
                        .GetListByProjectId(proInfo.Pid)
                        .Where(x => (x.ProjectStatus == 2 && x.VerifyTaskStatus == 2) || (x.ProjectStatus == 1 && x.VerifyTaskStatus == 5 && !string.IsNullOrEmpty(x.Reason)))
                        .OrderByDescending(x => x.CreateTime).ToList();
                    var user = new Core.BLL.SA_SysUserBLL().GetAll();

                    var query = from l in list
                                join u in user on l.SubmitUserId equals u.Uid into bb
                                from bbdata in bb.DefaultIfEmpty()
                                where (bbdata is null && l.VerifyTaskStatus == 5) || bbdata.SysRoleId == Guid.Parse("3d64080d-042f-45da-84be-570b360236a6")
                                select l;
                    list = query.ToList();
                }
            }
            #endregion
            return View(list);
        }
        [ChildActionOnly]
        public ActionResult FirstAgainModifyReaust(Guid? Pid, string ViewName)
        {

            var list = new List<Core.Models.SA_ProjectRequestEditFlow>();
            ViewData["ViewName"] = ViewName;
            #region 一审拒绝原因
            if (Pid.HasValue)
            {
                Core.Models.SA_NewProject proInfo = new Core.BLL.SA_NewProjectBLL().GetById(Pid.Value);
                if (proInfo.taskStatus == (int)Core.ProjectTaskStatus.申请未通过)
                {
                    list = new Core.BLL.SA_ProjectRequestEditFlowBLL().GetByListByPid(proInfo.Pid).Where(x => x.RequestStatus == 8).OrderByDescending(x => x.RequestTime).ToList();
                }
            }
            #endregion
            return View(list);
        }

        #region 忘记密码
        [UserAuth(Validate = false)]
        public ActionResult ForgotPassword()
        {
            return View();
        }
        /// <summary>
        /// 检测账号
        /// </summary>
        /// <param name="Account"></param>
        /// <returns></returns>
        [UserAuth(Validate = false)]
        public JsonResult CheckUserName(string Account)
        {
            var user = new Core.BLL.SA_UsersBLL().GetByUsername(Account);
            if (user == null)
            {
                return Json(new { succ = false }, JsonRequestBehavior.AllowGet);
            }
            else
            {
                string mail = Regex.Replace(user.Email, @"(?<=\S\S*?)[^@\s](?=\S*?@\S+)", "*");
                Regex Rzz = new Regex(@"(.{2}).+(.{2}@.+)", RegexOptions.IgnoreCase | RegexOptions.Singleline | RegexOptions.IgnorePatternWhitespace);
                mail = Rzz.Replace(user.Email, "$1***$2", 1);
                return Json(new { succ = true, Uid = user.Uid, asteriskEmail = mail, Email = user.Email }, JsonRequestBehavior.AllowGet);
            }
        }

        private string xGetReplaceString(Match xmatch)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < xmatch.Groups[1].Length; i++)
            {
                sb.Append("*");
            }
            sb.Append(xmatch.Groups[2].Value);
            return sb.ToString();
        }
        /// <summary>
        /// 获取验证码并发送邮件
        /// </summary>
        /// <param name="Uid"></param>
        /// <param name="Email"></param>
        /// <returns></returns>
        [UserAuth(Validate = false)]
        public JsonResult SendForgotPwdMail(Guid Uid, string Email)
        {
            try
            {
                var user = new Core.BLL.SA_UsersBLL().GetById(Uid);
                byte[] bytes = new byte[4];
                System.Security.Cryptography.RNGCryptoServiceProvider rng = new System.Security.Cryptography.RNGCryptoServiceProvider();
                rng.GetBytes(bytes);
                var RandomSeed = BitConverter.ToInt32(bytes, 0);
                int ranNumer = new Random(RandomSeed).Next(100000, 999999);
                var vcode = new Core.Models.SA_VerificationCode();
                vcode.Vid = Guid.NewGuid();
                vcode.Uid = user.Uid;
                vcode.RandomCode = ranNumer.ToString();
                vcode.CreateTime = DateTime.Now;
                vcode.UseType = 1;
                vcode.IsUsed = false;
                var vcList = new Core.BLL.SA_VerificationCodeBLL().GetNoUsedList(Uid, 1);
                new Core.BLL.SA_VerificationCodeBLL().Insert(vcode, vcList);
                var msg = $"<div style=\"text-align: center;\"><b><font size=\"4\">国际传统医学临床试验注册平台找回密码</font></b></div><div style=\"text-align: center;\"><b><font size=\"4\"><br></font></b></div><div style=\"text-align: center;\"><font size=\"2\">验证码为 <b style=\"color: rgb(0, 0, 255);\">{ranNumer} </b>请您在<font color=\"#ff0000\"><b>15</b></font>分钟内前往平台进行操作</font></div>";
                EmailManager.SendMail(Email, "", "国际传统医学临床试验注册平台找回密码", msg, null);
                return Json(new { succ = true }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { succ = false }, JsonRequestBehavior.AllowGet);
            }
        }
        /// <summary>
        /// 检测验证码
        /// </summary>
        /// <param name="Uid"></param>
        /// <param name="Code"></param>
        /// <returns></returns>
        [UserAuth(Validate = false)]
        public JsonResult CheckVerificationCode(Guid Uid, string Code)
        {
            try
            {
                var vc = new Core.BLL.SA_VerificationCodeBLL().GetByUidCode(Uid, Code, 1);
                if (vc == null)
                    return Json(new { succ = false, message = "验证码不存在" }, JsonRequestBehavior.AllowGet);
                if (vc.IsUsed)
                    return Json(new { succ = false, message = "验证码已使用" }, JsonRequestBehavior.AllowGet);
                if (vc.CreateTime.AddMinutes(15) < DateTime.Now)
                    return Json(new { succ = false, message = "验证码已过期" }, JsonRequestBehavior.AllowGet);
                vc.IsUsed = true;
                new Core.BLL.SA_VerificationCodeBLL().Update(vc);
                return Json(new { succ = true, message = "" }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { succ = false }, JsonRequestBehavior.AllowGet);
            }
        }
        [UserAuth(Validate = false)]
        public JsonResult ModifyUserPassword(Guid Uid, string Pwd, string RePwd)
        {
            try
            {
                if (!Pwd.Equals(RePwd))
                {
                    return Json(new { succ = false, message = "密码不一致" }, JsonRequestBehavior.AllowGet);
                }
                var regex = new Regex(@"(?=.*[0-9])(?=.*[a-zA-Z])(?=([\x21-\x7e]+)[^a-zA-Z0-9]).{8,30}", RegexOptions.Multiline | RegexOptions.IgnorePatternWhitespace);
                //校验密码是否符合
                bool pwdIsMatch = regex.IsMatch(Pwd);
                if (!pwdIsMatch)
                {
                    return Json(new { succ = false, message = "密码中必须包含含数字、字母、特殊符号" }, JsonRequestBehavior.AllowGet);

                }
                var bll = new Core.BLL.SA_UsersBLL();
                var user = bll.GetById(Uid);
                user.Password = bll.ExecutePassword(user.Username, Pwd);
                var res = bll.Update(user);
                if (res)
                {
                    //a123bbaaa!
                    return Json(new { succ = true, message = "" }, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    return Json(new { succ = false, message = "修改失败请联系管理员" }, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                return Json(new { succ = false, message = "修改失败请联系管理员" }, JsonRequestBehavior.AllowGet);
            }
        }
        #endregion
        #region 审核疑问站内信
        [ChildActionOnly]
        public ActionResult Message(Guid? Pid)
        {
            new Core.BLL.SA_MessagingBLL().UpdataUserRead(Pid.Value);
            var msgList = new Core.BLL.SA_MessagingBLL().GetListByPid(Pid.Value).ConvertAll(x => new Core.Models.MessageModel
            {
                MessagingId = x.MessagingId,
                Pid = x.Pid,
                ToUser = x.ToUser,
                ToUserName = x.ToUserName,
                FromUser = x.FromUser,
                FromUserName = x.FromUserName,
                CreateTime = string.Format("{0:yyyy-MM-dd HH:mm:ss}", x.CreateTime),
                UserRead = x.UserRead,
                ManagerRead = x.ManagerRead,
                Content = x.Content,
                isIn = UserProfile.CurrentProfile.UserID == x.FromUser.Value ? 1 : 0,
            }).ToList();
            return View(msgList);
        }

        public JsonResult InsertMsg(Guid Pid, string Message)
        {
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            var bllUser = new Core.BLL.SA_SysUserBLL();
            var bllMsg = new Core.BLL.SA_MessagingBLL();
            var proInfo = bllProj.GetById(Pid);
            if (proInfo == null)
            {
                return WriteJsonResponse(false, "记录不存在", false);
            }
            var temp = new Core.BLL.SA_MessagingBLL().GetListByPid(Pid);
            if (temp.Count == 0)
            {
                return WriteJsonResponse(false, "暂无疑问", false);
            }
            var user = bllUser.GetById(proInfo.executeTaskSysUserId.Value);
            var msgEntity = new Core.Models.SA_Messaging();
            msgEntity.MessagingId = Guid.NewGuid();
            msgEntity.Pid = Pid;
            msgEntity.ToUser = proInfo.executeTaskSysUserId;
            msgEntity.ToUserName = user.Name;
            msgEntity.FromUser = UserProfile.CurrentProfile.UserID;
            msgEntity.FromUserName = UserProfile.CurrentProfile.UserModel.Name;
            msgEntity.CreateTime = DateTime.Now;
            msgEntity.UserRead = 0;
            msgEntity.ManagerRead = 1;
            msgEntity.Content = Message;
            msgEntity.isIn = 1;
            var res = bllMsg.Insert(msgEntity);
            if (res)
            {
                List<SA_Messaging> list = new List<SA_Messaging>();
                list.Add(msgEntity);
                var msgList = list.ConvertAll(x => new
                {
                    x.MessagingId,
                    x.Pid,
                    x.ToUser,
                    x.ToUserName,
                    x.FromUser,
                    x.FromUserName,
                    CreateTime = string.Format("{0:yyyy-MM-dd HH:mm:ss}", x.CreateTime),
                    x.UserRead,
                    x.ManagerRead,
                    x.Content,
                    isIn = 1,
                }).ToList();
                return Json(new
                {
                    success = true,
                    message = "操作成功",
                    data = JsonConvert.SerializeObject(msgList)
                }, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }
        #endregion

        private string ValidateProject(Core.Models.SA_NewProject model)
        {
            var Lang = CultureInfo.CurrentUICulture.Name;
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            string message = "";
            if (Lang == "zh-CN")
            {
                if (string.IsNullOrWhiteSpace(model.publicTitleCN))
                {
                    return "注册题目不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.publicTitleEN))
                {
                    return "Public title不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.scientifirTitleCN))
                {
                    return "研究课题的正式科学名称不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.scientifirTitleEN))
                {
                    return "Scientific title不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.applicantCN))
                {
                    return "申请注册联系人不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.applicantEN))
                {
                    return "Applicant不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.studyLeaderCN))
                {
                    return "研究负责人不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.studyLeaderEN))
                {
                    return "Study leader不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.applicantTelephone))
                {
                    return "申请注册联系人电话不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.studyTelephone))
                {
                    return "研究负责人电话不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.applicantEmail))
                {
                    return "申请注册联系人电子邮件不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.studyEmail))
                {
                    return "研究负责人电子邮件不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.applicantAddressCN))
                {
                    return "申请注册联系人通讯地址不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.applicantAddressEN))
                {
                    return "Applicant's address不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.studyAddressCN))
                {
                    return "研究负责人通讯地址不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.studyAddressEN))
                {
                    return "Study leader's address不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.applicantInstitutionCN))
                {
                    return "申请人所在单位不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.applicantInstitutionEN))
                {
                    return "Affiliation of the Registrant不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.studyLeaderCompanyCN))
                {
                    return "研究负责人所在单位不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.studyLeaderCompanyEN))
                {
                    return "Affiliation of the Leader不能为空";
                }
                if (model.approvedCommittee.HasValue && model.approvedCommittee.Value == 1)
                {
                    if (string.IsNullOrWhiteSpace(model.fileEthicalCommittee))
                    {
                        return "伦理委员会批件文号不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.fileEthicalCommittee))
                    {
                        return "伦理委员会批件附件不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeName))
                    {
                        return "批准本研究的伦理委员会名称不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeNameEN))
                    {
                        return "Name of the ethic committee不能为空";
                    }
                    if (!model.ethicalCommitteeSanctionDate.HasValue)
                    {
                        return "伦理委员会批准日期不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCName))
                    {
                        return "伦理委员会联系人不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCNameEN))
                    {
                        return "Contact Name of the ethic committee不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCAddress))
                    {
                        return "伦理委员会联系地址不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCAddressEN))
                    {
                        return "Contact Address of the ethic committee不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCPhone))
                    {
                        return "伦理委员会联系人电话不能为空";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCEmail))
                    {
                        return "伦理委员会联系人邮箱不能为空";
                    }
                }
                if (string.IsNullOrWhiteSpace(model.primarySponsorCN))
                {
                    return "研究实施负责（组长）单位不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.primarySponsorEN))
                {
                    return "Primary sponsor不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.primarySponsorAddressCN))
                {
                    return "研究实施负责（组长）单位地址不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.primarySponsorAddressEN))
                {
                    return "Primary sponsor's address不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorCountry0"]))
                {
                    return "国家不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorCountryEn0"]))
                {
                    return "Country不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorProvince0"]))
                {
                    return "省(直辖市)不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorProvinceEn0"]))
                {
                    return "Province不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorInstitution0"]))
                {
                    return "单位不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorInstitutionEn0"]))
                {
                    return "Institution不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorAddress0"]))
                {
                    return "具体地址不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorAddressEn0"]))
                {
                    return "Address不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.sourceFundingCN))
                {
                    return "经费或物资来源不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.sourceFundingEN))
                {
                    return "Source(s) of funding不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.targetDiseaseCN))
                {
                    return "研究疾病不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.targetDiseaseEN))
                {
                    return "Target disease不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.objectivesStudyCN))
                {
                    return "研究目的不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.objectivesStudyEN))
                {
                    return "Objectives of Study不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.inclusionCriteriaCN))
                {
                    return "纳入标准不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.inclusionCriteriaEN))
                {
                    return "Inclusion criteria不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.exclusionCrteriaCN))
                {
                    return "排除标准不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.exclusionCrteriaEN))
                {
                    return "Exclusion criteria不能为空";
                }
                if (!model.studyTimeStart.HasValue)
                {
                    return "研究实施时间不能为空";
                }
                if (!model.studyTimeEnd.HasValue)
                {
                    return "研究实施时间不能为空";
                }
                if (!model.recruitingTimeStart.HasValue)
                {
                    return "征募观察对象时间不能为空";
                }
                if (!model.recruitingTimeEnd.HasValue)
                {
                    return "征募观察对象时间不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["GroupName0"]))
                {
                    return "组别不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SampleSize0"]))
                {
                    return "样本量不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["GroupNameEn0"]))
                {
                    return "Group不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["Measure0"]))
                {
                    return "干预措施不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["MeasureEn0"]))
                {
                    return "Intervention不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["Country0"]))
                {
                    return "研究实施地点-国家 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["CountryEn0"]))
                {
                    return "研究实施地点-Country 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["Province0"]))
                {
                    return "研究实施地点-省(直辖市) 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["ProvinceEn0"]))
                {
                    return "研究实施地点-Province 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["Institution0"]))
                {
                    return "研究实施地点-单位(医院) 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["InstitutionEn0"]))
                {
                    return "研究实施地点-Institution hospital 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["InstitutionLevel0"]))
                {
                    return "研究实施地点-单位级别 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["InstitutionLevelEn0"]))
                {
                    return "研究实施地点-Level of the institution 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["IndexName0"]))
                {
                    return "测量指标-指标中文名 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["IndexNameEn0"]))
                {
                    return "测量指标-Outcome Name 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SpecimenName0"]))
                {
                    return "采集人体标本-标本中文名 不能为空";
                }
                if (string.IsNullOrWhiteSpace(form["SpecimenNameEn0"]))
                {
                    return "采集人体标本-Sample Name 不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.ageMin))
                {
                    return "年龄范围-最小年龄 不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.ageMax))
                {
                    return "年龄范围-最大年龄 不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.randomMethodCN))
                {
                    return "随机方法不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.randomMethodEN))
                {
                    return "Randomization Procedure (please state who generates the random number sequence and by what method) 不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.dataManagementCN))
                {
                    return "共享原始数据的方式 不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.dataManagementEN))
                {
                    return "The way of sharing IPD 不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.dataAnalysisCN))
                {
                    return "数据采集和管理 不能为空";
                }
                if (string.IsNullOrWhiteSpace(model.dataAnalysisEN))
                {
                    return "Data collection and Management 不能为空";
                }



            }
            else
            {
                if (string.IsNullOrWhiteSpace(model.publicTitleCN))
                {
                    return "注册题目 is required";
                }
                if (string.IsNullOrWhiteSpace(model.publicTitleEN))
                {
                    return "Public title is required";
                }
                if (string.IsNullOrWhiteSpace(model.scientifirTitleCN))
                {
                    return "研究课题的正式科学名称 is required";
                }
                if (string.IsNullOrWhiteSpace(model.scientifirTitleEN))
                {
                    return "Scientific title is required";
                }
                if (string.IsNullOrWhiteSpace(model.applicantCN))
                {
                    return "申请注册联系人 is required";
                }
                if (string.IsNullOrWhiteSpace(model.applicantEN))
                {
                    return "Applicant is required";
                }
                if (string.IsNullOrWhiteSpace(model.studyLeaderCN))
                {
                    return "研究负责人 is required";
                }
                if (string.IsNullOrWhiteSpace(model.studyLeaderEN))
                {
                    return "Study leader is required";
                }
                if (string.IsNullOrWhiteSpace(model.applicantTelephone))
                {
                    return "Applicant's telephone is required";
                }
                if (string.IsNullOrWhiteSpace(model.studyTelephone))
                {
                    return "Study leader's telephone is required";
                }
                if (string.IsNullOrWhiteSpace(model.applicantEmail))
                {
                    return "Applicant's E-mail is required";
                }
                if (string.IsNullOrWhiteSpace(model.studyEmail))
                {
                    return "Study leader's E-mail is required";
                }
                if (string.IsNullOrWhiteSpace(model.applicantAddressCN))
                {
                    return "申请注册联系人通讯地址 is required";
                }
                if (string.IsNullOrWhiteSpace(model.applicantAddressEN))
                {
                    return "Applicant's address is required";
                }
                if (string.IsNullOrWhiteSpace(model.studyAddressCN))
                {
                    return "研究负责人通讯地址 is required";
                }
                if (string.IsNullOrWhiteSpace(model.studyAddressEN))
                {
                    return "Study leader's address is required";
                }
                if (string.IsNullOrWhiteSpace(model.applicantInstitutionCN))
                {
                    return "申请人所在单位 is required";
                }
                if (string.IsNullOrWhiteSpace(model.applicantInstitutionEN))
                {
                    return "Affiliation of the Registrant is required";
                }
                if (string.IsNullOrWhiteSpace(model.studyLeaderCompanyCN))
                {
                    return "研究负责人所在单位 is required";
                }
                if (string.IsNullOrWhiteSpace(model.studyLeaderCompanyEN))
                {
                    return "Affiliation of the Leader is required";
                }
                if (model.approvedCommittee.HasValue && model.approvedCommittee.Value == 1)
                {
                    if (string.IsNullOrWhiteSpace(model.fileEthicalCommittee))
                    {
                        return "Approved No. of ethic committee is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.fileEthicalCommittee))
                    {
                        return "Approved file of Ethical Committee is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeName))
                    {
                        return "批准本研究的伦理委员会名称 is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeNameEN))
                    {
                        return "Name of the ethic committee is required";
                    }
                    if (!model.ethicalCommitteeSanctionDate.HasValue)
                    {
                        return "Date of approved by ethic committee is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCName))
                    {
                        return "伦理委员会联系人 is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCNameEN))
                    {
                        return "Contact Name of the ethic committee is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCAddress))
                    {
                        return "伦理委员会联系地址 is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCAddressEN))
                    {
                        return "Contact Address of the ethic committee is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCPhone))
                    {
                        return "Contact phone of the ethic committee is required";
                    }
                    if (string.IsNullOrWhiteSpace(model.ethicalCommitteeCEmail))
                    {
                        return "伦理委员会联系人邮箱 is required";
                    }
                }
                if (string.IsNullOrWhiteSpace(model.primarySponsorCN))
                {
                    return "研究实施负责（组长）单位 is required";
                }
                if (string.IsNullOrWhiteSpace(model.primarySponsorEN))
                {
                    return "Primary sponsor is required";
                }
                if (string.IsNullOrWhiteSpace(model.primarySponsorAddressCN))
                {
                    return "研究实施负责（组长）单位地址 is required";
                }
                if (string.IsNullOrWhiteSpace(model.primarySponsorAddressEN))
                {
                    return "Primary sponsor's address is required";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorCountry0"]))
                {
                    return "国家 is required";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorCountryEn0"]))
                {
                    return "Country is required";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorProvince0"]))
                {
                    return "省(直辖市) is required";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorProvinceEn0"]))
                {
                    return "Province is required";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorInstitution0"]))
                {
                    return "单位 is required";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorInstitutionEn0"]))
                {
                    return "Institution is required";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorAddress0"]))
                {
                    return "具体地址 is required";
                }
                if (string.IsNullOrWhiteSpace(form["SecSponsorAddressEn0"]))
                {
                    return "Address is required";
                }
                if (string.IsNullOrWhiteSpace(model.sourceFundingCN))
                {
                    return "经费或物资来源 is required";
                }
                if (string.IsNullOrWhiteSpace(model.sourceFundingEN))
                {
                    return "Source(s) of funding is required";
                }
                if (string.IsNullOrWhiteSpace(model.targetDiseaseCN))
                {
                    return "研究疾病 is required";
                }
                if (string.IsNullOrWhiteSpace(model.targetDiseaseEN))
                {
                    return "Target disease is required";
                }
                if (string.IsNullOrWhiteSpace(model.objectivesStudyCN))
                {
                    return "研究目的 is required";
                }
                if (string.IsNullOrWhiteSpace(model.objectivesStudyEN))
                {
                    return "Objectives of Study is required";
                }
                if (string.IsNullOrWhiteSpace(model.inclusionCriteriaCN))
                {
                    return "纳入标准 is required";
                }
                if (string.IsNullOrWhiteSpace(model.inclusionCriteriaEN))
                {
                    return "Inclusion criteria is required";
                }
                if (string.IsNullOrWhiteSpace(model.exclusionCrteriaCN))
                {
                    return "排除标准 is required";
                }
                if (string.IsNullOrWhiteSpace(model.exclusionCrteriaEN))
                {
                    return "Exclusion criteria is required";
                }
                if (!model.studyTimeStart.HasValue)
                {
                    return "Study execute time is required";
                }
                if (!model.studyTimeEnd.HasValue)
                {
                    return "Study execute time is required";
                }
                if (!model.recruitingTimeStart.HasValue)
                {
                    return "Recruiting time is required";
                }
                if (!model.recruitingTimeEnd.HasValue)
                {
                    return "Recruiting time is required";
                }
                if (string.IsNullOrWhiteSpace(form["GroupName0"]))
                {
                    return "组别 is required";
                }
                if (string.IsNullOrWhiteSpace(form["SampleSize0"]))
                {
                    return "Sample size is required";
                }
                if (string.IsNullOrWhiteSpace(form["GroupNameEn0"]))
                {
                    return "Group is required";
                }
                if (string.IsNullOrWhiteSpace(form["Measure0"]))
                {
                    return "干预措施 is required";
                }
                if (string.IsNullOrWhiteSpace(form["MeasureEn0"]))
                {
                    return "Intervention is required";
                }
                if (string.IsNullOrWhiteSpace(form["Country0"]))
                {
                    return "研究实施地点-国家 is required";
                }
                if (string.IsNullOrWhiteSpace(form["CountryEn0"]))
                {
                    return "Countries of recruitment and research settings-Country is required";
                }
                if (string.IsNullOrWhiteSpace(form["Province0"]))
                {
                    return "Countries of recruitment and research settings-省(直辖市) is required";
                }
                if (string.IsNullOrWhiteSpace(form["ProvinceEn0"]))
                {
                    return "Countries of recruitment and research settings-Province is required";
                }
                if (string.IsNullOrWhiteSpace(form["Institution0"]))
                {
                    return "Countries of recruitment and research settings-单位(医院) is required";
                }
                if (string.IsNullOrWhiteSpace(form["InstitutionEn0"]))
                {
                    return "Countries of recruitment and research settings-Institution hospital is required";
                }
                if (string.IsNullOrWhiteSpace(form["InstitutionLevel0"]))
                {
                    return "Countries of recruitment and research settings-单位级别 is required";
                }
                if (string.IsNullOrWhiteSpace(form["InstitutionLevelEn0"]))
                {
                    return "Countries of recruitment and research settings-Level of the institution is required";
                }
                if (string.IsNullOrWhiteSpace(form["IndexName0"]))
                {
                    return "Outcomes-指标中文名 is required";
                }
                if (string.IsNullOrWhiteSpace(form["IndexNameEn0"]))
                {
                    return "Outcomes-Outcome Name is required";
                }
                if (string.IsNullOrWhiteSpace(form["SpecimenName0"]))
                {
                    return "Collecting sample(s) from participants-标本中文名 is required";
                }
                if (string.IsNullOrWhiteSpace(form["SpecimenNameEn0"]))
                {
                    return "Collecting sample(s) from participants-Sample Name is required";
                }
                if (string.IsNullOrWhiteSpace(model.ageMin))
                {
                    return "Participant age-Min age is required";
                }
                if (string.IsNullOrWhiteSpace(model.ageMax))
                {
                    return "Participant age-Max age is required";
                }
                if (string.IsNullOrWhiteSpace(model.randomMethodCN))
                {
                    return "随机方法 is required";
                }
                if (string.IsNullOrWhiteSpace(model.randomMethodEN))
                {
                    return "Randomization Procedure (please state who generates the random number sequence and by what method) is required";
                }
                if (string.IsNullOrWhiteSpace(model.dataManagementCN))
                {
                    return "共享原始数据的方式 is required";
                }
                if (string.IsNullOrWhiteSpace(model.dataManagementEN))
                {
                    return "The way of sharing IPD is required";
                }
                if (string.IsNullOrWhiteSpace(model.dataAnalysisCN))
                {
                    return "数据采集和管理 is required";
                }
                if (string.IsNullOrWhiteSpace(model.dataAnalysisEN))
                {
                    return "Data collection and Management is required";
                }


            }

            //if (model.recruitingTimeStart.HasValue &&
            //    model.ethicalCommitteeSanctionDate.HasValue &&
            //    model.recruitingTimeStart.Value.Date < model.ethicalCommitteeSanctionDate.Value.Date)
            //{
            //    return "【征募研究对象时间】不得早于【伦理批准时间】";
            //}

            //if (model.recruitingTimeEnd.HasValue &&
            //    model.ethicalCommitteeSanctionDate.HasValue &&
            //    model.recruitingTimeEnd.Value.Date < model.ethicalCommitteeSanctionDate.Value.Date)
            //{
            //    return "【征募研究对象时间】不得早于【伦理批准时间】";
            //}

            //if (model.studyTimeStart.HasValue &&
            //    model.studyTimeEnd.HasValue &&
            //    model.recruitingTimeStart.HasValue &&
            //    model.recruitingTimeEnd.HasValue &&
            //    (model.studyTimeStart > model.recruitingTimeStart || model.studyTimeEnd > model.recruitingTimeStart)
            //    )
            //{
            //    return "【研究实施的时间】不能晚于【招募研究对象的时间】";
            //}


            return "";
        }
    }
}