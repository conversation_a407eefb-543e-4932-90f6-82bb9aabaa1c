using ITMCTR.App.Filter;
using PetaPoco;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Newtonsoft.Json;
using ITMCTR.App.Common;
using ITMCTR.App.Models;
using System.Windows;
using System.Threading;
using AutoMapper;
using System.Threading.Tasks;
using System.Xml.Serialization;
using System.IO.Compression;
using ITMCTR.Core;
using System.Security.Cryptography;
using System.Web.Services.Description;
using ITMCTR.Core.Models;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using Aspose.Cells;
using ITMCTR.App.Helper;
using NPOI.SS.Formula.Functions;
using System.Reflection;
using System.Windows.Media.Media3D;
using System.Web.UI.WebControls;

namespace ITMCTR.App.Controllers
{
    [Localization]
    [SysUserAuth(Validate = true)]
    public partial class ManagerController : BaseController
    {
        #region 登录
        [HttpGet]
        [SysUserAuth(Validate = false)]
        public ActionResult Login()
        {
            var model = new Models.ManagerLoginModel() { Encrypt = 1 };
            return View(model);
        }

        [HttpPost]
        [SysUserAuth(Validate = false)]
        public ActionResult Login(Models.ManagerLoginModel model)
        {
            var key = $"Errlogin_{model.Username}";
            var key2 = $"Errlogin_{model.Username}_time";
            if (HttpContext.Application[key] != null && (int)HttpContext.Application[key] >= 3)
            {
                if (HttpContext.Application[key2] != null && (DateTime.Now - (DateTime)HttpContext.Application[key2]).TotalMinutes > 30)
                {
                    HttpContext.Application[key] = 0;
                    HttpContext.Application[key2] = DateTime.Now;
                }
                else
                    ModelState.AddModelError("", "账号已锁定,请稍后尝试");
            }
            if (Session["ValidateCode"] == null || model.Captcha != Session["ValidateCode"].ToString())
            {
                ModelState.AddModelError("", "图形验证码不正确.");
                Session["ValidateCode"] = null;
                return View(model);
            }
            if (model.Encrypt == 1)
            {
                JsEncryptHelper jsHelper = new JsEncryptHelper();
                model.Password = jsHelper.Decrypt(model.Password);
            }
            var user = new Core.BLL.SA_SysUserBLL().Login(model.Username, model.Password);
            if (user == null)
            {
                if (HttpContext.Application[key] == null)
                {
                    HttpContext.Application[key] = 1;
                    HttpContext.Application[key2] = DateTime.Now;
                }
                else
                {
                    HttpContext.Application[key] = (int)HttpContext.Application[key] + 1;
                    HttpContext.Application[key2] = DateTime.Now;
                }
                ModelState.AddModelError("", "用户不存在或密码错误");
                return View(model);
            }
            user.LastLoginDate = DateTime.Now;
            user.LastLoginIp = Common.IP.GetIP4Address();
            new Core.BLL.SA_SysUserBLL().Update(user);
            Models.SysUserProfile.CurrentProfile.InitUser(user.Uid);
            Models.SysUserProfile.CurrentProfile.SaveProfile();
            return RedirectToAction("Index");
        }
        #endregion
        #region 退出登录
        public ActionResult LogOut()
        {
            Session.Clear();
            return RedirectToAction("Index", "Manager");
        }
        #endregion
        #region 角色管理

        public ActionResult SysRolesList(int pageNo = 0)
        {
            ViewBag.txtRoleName = Request["txtRoleName"];
            ViewBag.txtRoleDesc = Request["txtRoleDesc"];
            Core.BLL.SA_SysRolesBLL bll = new Core.BLL.SA_SysRolesBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_SysRoles");
            if (!string.IsNullOrEmpty(ViewBag.txtRoleName))
                sql = sql.Where(" RoleName like @0 ", $"%{ViewBag.txtRoleName}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRoleDesc))
                sql = sql.Where(" RoleDesc like @0 ", $"%{ViewBag.txtRoleDesc}%");
            var page_ret = bll.GetPageList<Core.Models.SA_SysRoles>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }

        [HttpGet]
        public ActionResult SysRolesAdd()
        {
            var model = new Core.Models.SA_SysRoles();
            return View(model);
        }

        [HttpPost]
        public ActionResult SysRolesAdd(Core.Models.SA_SysRoles model)
        {
            model.RoleId = Guid.NewGuid();
            model.IsSysRole = 0;
            var res = new Core.BLL.SA_SysRolesBLL().Insert(model);
            if (res)
            {
                return RedirectToAction("SysRolesList");
            }
            else
            {
                ModelState.AddModelError("", "新建角色失败");
            }
            return View(model);
        }

        [HttpGet]
        public ActionResult SysRolesEdit(Guid RoleId)
        {
            var model = new Core.BLL.SA_SysRolesBLL().GetById(RoleId);
            return View(model);
        }

        [HttpPost]
        public ActionResult SysRolesEdit(Core.Models.SA_SysRoles model)
        {
            var entity = new Core.BLL.SA_SysRolesBLL().GetById(model.RoleId);
            entity.RoleName = model.RoleName;
            entity.RoleDesc = model.RoleDesc;
            var res = new Core.BLL.SA_SysRolesBLL().Update(entity);
            if (res)
            {
                return RedirectToAction("SysRolesList");
            }
            else
            {
                ModelState.AddModelError("", "编辑角色失败");
            }
            return View(model);
        }

        [HttpPost]
        public JsonResult SysRolesDel(Guid RoleId)
        {
            var msg = "";
            var count = new Core.BLL.SA_SysUserBLL().GetCountBySysRoleId(RoleId);
            if (count > 0)
            {
                return WriteJsonResponse(false, "该角色正在使用无法删除", false);
            }
            var res = new Core.BLL.SA_SysRolesBLL().Delete(RoleId);
            if (res)
            {
                return WriteJsonResponse(true, "删除角色成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "删除角色失败", false);
            }
        }
        #endregion
        #region 系统用户管理

        public ActionResult SysUserList(int pageNo = 0)
        {
            ViewBag.txtUsername = Request["txtUsername"];
            ViewBag.txtName = Request["txtName"];
            Core.BLL.SA_SysUserBLL bll = new Core.BLL.SA_SysUserBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_SysUser");
            if (!string.IsNullOrEmpty(ViewBag.txtUsername))
                sql = sql.Where(" Username like @0 ", $"%{ViewBag.txtUsername}%");
            if (!string.IsNullOrEmpty(ViewBag.txtName))
                sql = sql.Where(" Name like @0 ", $"%{ViewBag.txtName}%");
            var page_ret = bll.GetPageList<Core.Models.SA_SysUser>(pageNo, PageSize, sql);
            var resPage = new Page<Models.SA_SysUserListModel>();
            resPage.Items = page_ret.Items.ConvertAll(x => new Models.SA_SysUserListModel
            {
                Uid = x.Uid.ToString(),
                Username = x.Username,
                Name = x.Name,
                Password = x.Password,
                Email = x.Email,
                RegDate = x.RegDate.HasValue ? x.RegDate.Value.ToString("yyyy-MM-dd HH:mm:ss") : "",
                RegIP = x.RegIP,
                LastLoginDate = x.LastLoginDate.HasValue ? x.LastLoginDate.Value.ToString("yyyy-MM-dd HH:mm:ss") : "",
                LastLoginIp = x.LastLoginIp,
                Sex = x.Sex,
                Phone = x.Phone,
                Country = x.Country,
                CellPhone = x.CellPhone,
                RegUnit = x.RegUnit,
                RegAddress = x.RegAddress,
                SysRoleId = x.SysRoleId.HasValue ? new Core.BLL.SA_SysRolesBLL().GetRoleName(x.SysRoleId.Value) : "",
                IsSysUser = x.IsSysUser
            }).ToList();
            resPage.ItemsPerPage = page_ret.ItemsPerPage;
            resPage.TotalItems = page_ret.TotalItems;
            resPage.TotalPages = page_ret.TotalPages;
            resPage.CurrentPage = page_ret.CurrentPage;
            resPage.Context = GetUrlParms();
            return View(resPage);
        }

        [HttpGet]
        public ActionResult SysUserAdd()
        {
            var sexList = Common.CommonList.getSexList();
            ViewBag.Sex = new SelectList(sexList, "Key", "Value");
            var countryList = Common.CommonList.getCountryList();
            ViewBag.Country = new SelectList(countryList, "id", "name");
            var roleList = new Core.BLL.SA_SysRolesBLL().GetAll().Where(x => x.RoleId != Guid.Parse("f707cae7-17c3-49e3-b70c-a1d9fb6e5afe")).ToList();
            ViewBag.RoleList = new SelectList(roleList, "RoleId", "RoleName");

            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.SysRoleId == Guid.Parse("49bb2491-2ba5-492d-9f54-6317bcc05baa")).ToList();
            ViewBag.UserList = new SelectList(userList, "Uid", "Name");
            var model = new Models.SysUserModel();
            model.Photograph = "http://www.placehold.it/200x150/EFEFEF/AAAAAA&text=no+image";
            return View(model);
        }

        [HttpPost]
        public ActionResult SysUserAdd(Models.SysUserModel model)
        {
            var sexList = Common.CommonList.getSexList();
            ViewBag.Sex = new SelectList(sexList, "Key", "Value");
            var countryList = Common.CommonList.getCountryList();
            ViewBag.Country = new SelectList(countryList, "id", "name");
            var roleList = new Core.BLL.SA_SysRolesBLL().GetAll().Where(x => x.RoleId != Guid.Parse("f707cae7-17c3-49e3-b70c-a1d9fb6e5afe")).ToList();
            ViewBag.RoleList = new SelectList(roleList, "RoleId", "RoleName");

            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.SysRoleId == Guid.Parse("49bb2491-2ba5-492d-9f54-6317bcc05baa")).ToList();
            ViewBag.UserList = new SelectList(userList, "Uid", "Name");
            if (!model.Password.Equals(model.RePassword))
            {
                ModelState.AddModelError("", "密码不一致");
                return View(model);
            }
            var hasUsername = new Core.BLL.SA_SysUserBLL().HasUsername(model.Username);
            if (hasUsername)
            {
                ModelState.AddModelError("", "用户名已存在");
                return View(model);
            }
            var bll = new Core.BLL.SA_SysUserBLL();
            var entity = new Core.Models.SA_SysUser();
            entity.Uid = Guid.NewGuid();
            entity.Username = model.Username;
            entity.Name = model.Name;
            entity.Password = bll.ExecutePassword(model.Username, model.Password);
            entity.Email = model.Email;
            entity.RegDate = DateTime.Now;
            entity.RegIP = Common.IP.GetIP4Address();
            entity.Sex = model.Sex;
            entity.Phone = model.Phone;
            entity.Country = model.Country;
            entity.CellPhone = model.CellPhone;
            entity.RegUnit = model.RegUnit;
            entity.RegAddress = model.RegAddress;
            entity.SysRoleId = model.SysRoleId.Value;
            if (model.IsSysUser)
                entity.IsSysUser = 1;
            else
                entity.IsSysUser = 0;
            entity.ParentId = model.ParentId;
            var files = Request.Files;
            if (files.Count > 0 && !string.IsNullOrWhiteSpace(files[0].FileName))
            {
                var f = new FileInfo(files[0].FileName);
                var fileName = Common.CreateFileUpPath.GetSysUserPhotoPath() + Guid.NewGuid().ToString() + f.Extension;
                var path = Server.MapPath(fileName);
                entity.Photograph = fileName;
                files[0].SaveAs(path);
            }
            var res = bll.Insert(entity);
            if (res)
            {
                return RedirectToAction("SysUserList");
            }
            else
            {
                ModelState.AddModelError("", "新建用户失败");
                return View(model);
            }
            return View(model);
        }


        [HttpGet]
        public ActionResult SysUserEdit(Guid Uid)
        {
            var sexList = Common.CommonList.getSexList();
            ViewBag.SexList = new SelectList(sexList, "Key", "Value");
            var countryList = Common.CommonList.getCountryList();
            ViewBag.CountryList = new SelectList(countryList, "id", "name");
            var roleList = new Core.BLL.SA_SysRolesBLL().GetAll().Where(x => x.RoleId != Guid.Parse("f707cae7-17c3-49e3-b70c-a1d9fb6e5afe")).ToList();
            ViewBag.RoleList = new SelectList(roleList, "RoleId", "RoleName");
            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.SysRoleId == Guid.Parse("49bb2491-2ba5-492d-9f54-6317bcc05baa")).ToList();
            ViewBag.UserList = new SelectList(userList, "Uid", "Name");
            Models.SysUserModel model = new Models.SysUserModel();
            model.Photograph = "http://www.placehold.it/200x150/EFEFEF/AAAAAA&text=no+image";
            var entity = new Core.BLL.SA_SysUserBLL().GetById(Uid);
            model.Uid = entity.Uid;
            model.Username = entity.Username;
            model.Name = entity.Name;
            model.Password = entity.Password;
            model.Email = entity.Email;
            model.Sex = entity.Sex;
            model.Phone = entity.Phone;
            model.Country = entity.Country;
            model.CellPhone = entity.CellPhone;
            model.RegUnit = entity.RegUnit;
            model.RegAddress = entity.RegAddress;
            model.SysRoleId = entity.SysRoleId;
            if (entity.IsSysUser == 1)
                model.IsSysUser = true;
            else
                model.IsSysUser = false;
            if (!string.IsNullOrWhiteSpace(entity.Photograph))
                model.Photograph = entity.Photograph;
            model.ParentId = entity.ParentId;
            return View(model);
        }

        [HttpPost]
        public ActionResult SysUserEdit(Models.SysUserModel model)
        {
            var sexList = Common.CommonList.getSexList();
            ViewBag.Sex = new SelectList(sexList, "Key", "Value");
            var countryList = Common.CommonList.getCountryList();
            ViewBag.Country = new SelectList(countryList, "id", "name");
            var roleList = new Core.BLL.SA_SysRolesBLL().GetAll().Where(x => x.RoleId == Guid.Parse("f707cae7-17c3-49e3-b70c-a1d9fb6e5afe")).ToList();
            ViewBag.RoleList = new SelectList(roleList, "RoleId", "RoleName");

            var entity = new Core.BLL.SA_SysUserBLL().GetById(model.Uid);
            //entity.Username = model.Username;
            entity.Name = model.Name;
            entity.Email = model.Email;
            entity.Sex = model.Sex;
            entity.Phone = model.Phone;
            entity.Country = model.Country;
            entity.CellPhone = model.CellPhone;
            entity.RegUnit = model.RegUnit;
            entity.RegAddress = model.RegAddress;
            entity.SysRoleId = model.SysRoleId.Value;
            entity.ParentId = model.ParentId;
            if (model.IsSysUser)
                entity.IsSysUser = 1;
            else
                entity.IsSysUser = 0;
            var files = Request.Files;
            if (files.Count > 0 && !string.IsNullOrWhiteSpace(files[0].FileName))
            {

                var f = new FileInfo(files[0].FileName);
                var fileName = Common.CreateFileUpPath.GetSysUserPhotoPath() + Guid.NewGuid().ToString() + f.Extension;
                var path = Server.MapPath(fileName);
                entity.Photograph = fileName;
                files[0].SaveAs(path);
            }
            var res = new Core.BLL.SA_SysUserBLL().Update(entity);
            if (res)
            {
                return RedirectToAction("SysUserList");
            }
            else
            {
                ModelState.AddModelError("", "编辑用户失败");
            }
            return View(model);
        }

        public JsonResult SysUserDel(Guid Uid)
        {
            var msg = "";
            var res = new Core.BLL.SA_SysUserBLL().Delete(Uid);
            if (res)
            {
                return WriteJsonResponse(true, "删除用户成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "删除用户失败", false);
            }
        }


        [HttpPost]
        public JsonResult ResetSysUserPwd(Guid Uid)
        {
            var user = new Core.BLL.SA_SysUserBLL().GetById(Uid);
            if (user == null)
                return WriteJsonResponse(false, "此账号不存在", false);
            var md5pw = Common.MD5Utils.MD5(Guid.NewGuid().ToString());
            md5pw = md5pw.Length >= 8 ? md5pw.Substring(0, 8) : md5pw;
            user.Password = new Core.BLL.SA_SysUserBLL().ExecutePassword(user.Username, md5pw);
            var res = new Core.BLL.SA_SysUserBLL().Update(user);
            if (res)
            {
                return Json(new { Success = true, message = "密码重置成功", Pw = md5pw }, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(new { Success = true, message = "密码重置失败" }, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public JsonResult GetParentUser(string RoleId)
        {
            if (RoleId == "49bb2491-2ba5-492d-9f54-6317bcc05baa")//二级
            {
                RoleId = "f2c6f6a5-069a-4f8e-8d11-6d336045529b";
            }
            else if (RoleId == "87977f61-df64-41dd-8517-bda64116cc38")//三级
            {
                RoleId = "49bb2491-2ba5-492d-9f54-6317bcc05baa";
            }
            else if  (RoleId == "3d64080d-042f-45da-84be-570b360236a6")//四级
            {
                RoleId = "87977f61-df64-41dd-8517-bda64116cc38";
            }
            else
            {
                RoleId = default(Guid).ToString();
            }
            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.SysRoleId == Guid.Parse(RoleId)).ToList();
            var data = new SelectList(userList, "Uid", "Name");
            return Json(new { Success = true, data = data }, JsonRequestBehavior.AllowGet);
        }

        #endregion
        #region 研究字典

        public ActionResult StudyDictionaryList(int pageNo = 0)
        {
            ViewBag.txtStudyValue = Request["txtStudyValue"];
            ViewBag.txtDescription = Request["txtDescription"];

            //List<SelectListItem> listItem = new List<SelectListItem>();
            //listItem.Add(new SelectListItem { Value = "", Text = "不限/All" });
            //listItem.Add(new SelectListItem { Value = "待审核/Not be verified", Text = "待审核" });
            //listItem.Add(new SelectListItem { Value = "通过审核", Text = "通过审核/Successful" });
            //listItem.Add(new SelectListItem { Value = "未填完", Text = "未填完/Incompleted" });
            //ViewBag.listVerify = new SelectList(listItem, "Value", "Text", ViewBag.listVerifyStatus);

            Core.BLL.SA_StudyDictionaryBLL bll = new Core.BLL.SA_StudyDictionaryBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_StudyDictionary");
            if (!string.IsNullOrEmpty(ViewBag.txtStudyValue))
                sql = sql.Where(" StudyValue like @0", $"%{ViewBag.txtStudyValue}%");
            if (!string.IsNullOrEmpty(ViewBag.txtDescription))
                sql = sql.Where(" (DescriptionCn like @0 or DescriptionEn like @0) ", $"%{ViewBag.txtDescription}%");


            var page_ret = bll.GetPageList<Core.Models.SA_StudyDictionary>(pageNo, PageSize, sql);

            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }

        [HttpGet]
        public ActionResult StudyDictionaryAdd()
        {
            var model = new Core.Models.SA_StudyDictionary();
            var sexList = Common.CommonList.getStudyNameList();
            ViewBag.StudyNameList = new SelectList(sexList, "id", "name");
            return View(model);
        }

        [HttpPost]
        public ActionResult StudyDictionaryAdd(Core.Models.SA_StudyDictionary model)
        {
            model.Sid = Guid.NewGuid();
            model.CreateDatetime = DateTime.Now;
            var res = new Core.BLL.SA_StudyDictionaryBLL().Insert(model);
            if (res)
            {
                return RedirectToAction("StudyDictionaryList");
            }
            else
            {
                var sexList = Common.CommonList.getStudyNameList();
                ViewBag.StudyNameList = new SelectList(sexList, "id", "name");
                ModelState.AddModelError("", "新建研究字典失败");
            }
            return View(model);
        }

        [HttpGet]
        public ActionResult StudyDictionaryEdit(Guid Sid)
        {
            var model = new Core.BLL.SA_StudyDictionaryBLL().GetById(Sid);
            var sexList = Common.CommonList.getStudyNameList();
            ViewBag.StudyNameList = new SelectList(sexList, "id", "name");
            return View(model);
        }

        [HttpPost]
        public ActionResult StudyDictionaryEdit(Core.Models.SA_StudyDictionary model)
        {
            var entity = new Core.BLL.SA_StudyDictionaryBLL().GetById(model.Sid);
            entity.StudyName = model.StudyName;
            entity.StudyValue = model.StudyValue;
            entity.DescriptionCn = model.DescriptionCn;
            entity.DescriptionEn = model.DescriptionEn;
            entity.Ishide = model.Ishide;
            var res = new Core.BLL.SA_StudyDictionaryBLL().Update(model);
            if (res)
            {
                return RedirectToAction("StudyDictionaryList");
            }
            else
            {
                var sexList = Common.CommonList.getStudyNameList();
                ViewBag.StudyNameList = new SelectList(sexList, "id", "name");
                ModelState.AddModelError("", "编辑研究字典失败");
            }
            return View(model);
        }

        public JsonResult StudyDictionaryDel(Guid Sid)
        {
            var msg = "";
            var res = new Core.BLL.SA_StudyDictionaryBLL().Delete(Sid);
            if (res)
            {
                return WriteJsonResponse(true, "删除研究字典成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "删除研究字典失败", false);
            }
        }
        #endregion
        #region 个人资料
        public ActionResult PersonalData()
        {
            var sexList = Common.CommonList.getSexList();
            ViewBag.SexList = new SelectList(sexList, "Key", "Value");
            var countryList = Common.CommonList.getCountryList();
            ViewBag.CountryList = new SelectList(countryList, "id", "name");
            var roleList = new Core.BLL.SA_SysRolesBLL().GetAll();
            ViewBag.RoleList = new SelectList(roleList, "RoleId", "RoleName");
            var user = new Core.BLL.SA_SysUserBLL().GetById(Models.SysUserProfile.CurrentProfile.UserID);
            user.Photograph = "http://www.placehold.it/200x150/EFEFEF/AAAAAA&text=no+image";
            var model = new Models.SysUserModel();
            model.Uid = user.Uid;
            model.Username = user.Username;
            model.Password = user.Password;
            model.Name = user.Name;
            model.Email = user.Email;
            model.RegDate = user.RegDate;
            model.RegIP = user.RegIP;
            model.LastLoginDate = user.LastLoginDate;
            model.LastLoginIp = user.LastLoginIp;
            model.Sex = user.Sex;
            model.Phone = user.Phone;
            model.Country = user.Country;
            model.CellPhone = user.CellPhone;
            model.RegUnit = user.RegUnit;
            model.RegAddress = user.RegAddress;
            model.SysRoleId = user.SysRoleId;
            model.Photograph = user.Photograph;
            return View(model);
        }

        [HttpPost]
        public ActionResult PersonalData(Models.SysUserModel model)
        {
            var user = new Core.BLL.SA_SysUserBLL().GetById(model.Uid);
            user.Name = model.Name;
            user.Email = model.Email;
            user.Sex = model.Sex;
            user.Phone = model.Phone;
            user.Country = model.Country;
            user.CellPhone = model.CellPhone;
            user.RegUnit = model.RegUnit;
            user.RegAddress = model.RegAddress;
            var files = Request.Files;
            if (files.Count > 0 && !string.IsNullOrWhiteSpace(files[0].FileName))
            {
                var f = new FileInfo(files[0].FileName);
                var fileName = Common.CreateFileUpPath.GetSysUserPhotoPath() + Guid.NewGuid().ToString() + f.Extension;
                var path = Server.MapPath(fileName);
                user.Photograph = fileName;
                files[0].SaveAs(path);
            }
            var res = new Core.BLL.SA_SysUserBLL().Update(user);
            if (res)
            {
                return RedirectToAction("index", "Manager");
            }
            else
            {
                var sexList = Common.CommonList.getSexList();
                ViewBag.SexList = new SelectList(sexList, "Key", "Value");
                var countryList = Common.CommonList.getCountryList();
                ViewBag.CountryList = new SelectList(countryList, "id", "name");
                var roleList = new Core.BLL.SA_SysRolesBLL().GetAll();
                ViewBag.RoleList = new SelectList(roleList, "RoleId", "RoleName");
                ModelState.AddModelError("", "个人资料保存失败");
                return View(model);
            }
        }
        #endregion
        #region 修改密码
        public ActionResult ModifyPwd()
        {
            var user = new Core.BLL.SA_SysUserBLL().GetById(Models.SysUserProfile.CurrentProfile.UserID);
            var model = new Models.ModifyPwdModel();
            model.Uid = user.Uid;
            model.Username = user.Username;
            return View(model);
        }

        [HttpPost]
        public ActionResult ModifyPwd(Models.ModifyPwdModel model)
        {
            var user = new Core.BLL.SA_SysUserBLL().GetById(Models.SysUserProfile.CurrentProfile.UserID);
            var bll = new Core.BLL.SA_SysUserBLL();
            var pwd = new Core.BLL.SA_SysUserBLL().ExecutePassword(user.Username, model.OldPassword);
            if (pwd != user.Password)
            {
                ModelState.AddModelError("", "原始密码错误");
                return View(model);
            }
            if (!model.NewPassword.Equals(model.ReNewPassword))
            {
                ModelState.AddModelError("", "新密码与确认密码不一致");
                return View(model);
            }
            user.Password = bll.ExecutePassword(model.Username, model.NewPassword);
            var res = bll.Update(user);
            if (res)
            {
                return RedirectToAction("Index", "Manager");
            }
            else
            {
                ModelState.AddModelError("", "修改密码失败");
                return View(model);
            }
        }
        #endregion
        #region 设置菜单权限
        public ActionResult SettingFun(Guid RoleId)
        {
            List<Models.TreeModel> treeList = new List<Models.TreeModel>();
            var allFuns = new Core.BLL.SA_SysFunsBLL().GetAll().OrderBy(x => x.FunFloor).ToList();
            var allBinds = new Core.BLL.SA_RoleFunsBLL().GetAll().Where(x => x.RoleId == RoleId).ToList();
            var first = allFuns.Where(x => x.FunFloor.Length == 3).ToList();
            foreach (var item in first)
            {
                Models.TreeModel model = new Models.TreeModel();
                model.text = item.FunName;
                var temp = allBinds.Find(x => x.FunId == item.FunId);
                if (temp != null)
                    model.state.selected = true;
                else
                    model.state.selected = false;
                model.a_attr.Id = item.FunId.ToString();
                model.children = new List<Models.TreeModel>();
                var childList = allFuns.FindAll(x => x.FunFloor.IndexOf(item.FunFloor) == 0 && x.FunFloor.Length == item.FunFloor.Length + 3).OrderBy(x => x.FunFloor).ToList();
                AddChild(model, childList, allBinds, allFuns);
                treeList.Add(model);
            }
            ViewData["TreeData"] = JsonConvert.SerializeObject(treeList);
            ViewData["RoleId"] = RoleId;
            return View();
        }

        public void AddChild(Models.TreeModel Menu, List<Core.Models.SA_SysFuns> children, List<Core.Models.SA_RoleFuns> bindList, List<Core.Models.SA_SysFuns> allPower)
        {
            foreach (var item in children)
            {
                var menuJson = new Models.TreeModel();
                menuJson.text = item.FunName;
                var temp = bindList.Find(x => x.FunId == item.FunId);
                if (temp != null)
                    menuJson.state.selected = true;
                else
                    menuJson.state.selected = false;
                menuJson.a_attr.Id = item.FunId.ToString();
                menuJson.children = new List<Models.TreeModel>();
                var child = allPower.FindAll(x => x.FunFloor.IndexOf(item.FunFloor) == 0 && x.FunFloor.Length == item.FunFloor.Length + 3);
                if (child.Count > 0)
                {
                    AddChild(menuJson, child, bindList, allPower);
                }
                Menu.children.Add(menuJson);
            }
        }

        [HttpPost]
        public JsonResult PostSelectFun(string strTreeData, Guid RoleId)
        {
            var treeList = JsonConvert.DeserializeObject<List<Models.TreeModel>>(strTreeData);
            List<Core.Models.SA_RoleFuns> list = new List<Core.Models.SA_RoleFuns>();
            foreach (var item in treeList)
            {
                if (item.state.selected)
                {
                    list.Add(new Core.Models.SA_RoleFuns
                    {
                        RoleFunId = Guid.NewGuid(),
                        FunId = Guid.Parse(item.a_attr.Id),
                        RoleId = RoleId
                    });
                    AddSaveChild(item.children, list, RoleId);
                }
            }
            var res = new Core.BLL.SA_RoleFunsBLL().Insert(list, RoleId);
            if (res)
            {
                return WriteJsonResponse(true, "保存成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "保存失败", false);
            }
        }

        private void AddSaveChild(List<Models.TreeModel> Menu, List<Core.Models.SA_RoleFuns> bindList, Guid Role_Id)
        {
            foreach (var item in Menu)
            {
                if (item.state.selected)
                {
                    bindList.Add(new Core.Models.SA_RoleFuns
                    {
                        RoleFunId = Guid.NewGuid(),
                        FunId = Guid.Parse(item.a_attr.Id),
                        RoleId = Role_Id
                    });
                    AddSaveChild(item.children, bindList, Role_Id);
                }
            }
        }
        #endregion
        #region 加载菜单

        [ChildActionOnly]
        public ActionResult HomeMenu()
        {
            var list = new Core.BLL.SA_SysFunsBLL().GetAll().Where(x => x.FunFloor.Length >= 9).ToList();
            var user = new Core.BLL.SA_SysUserBLL().GetById(Models.SysUserProfile.CurrentProfile.UserID);
            var bindFun = new Core.BLL.SA_RoleFunsBLL().GetByRoleId(user.SysRoleId.Value);
            List<Models.MenuModel> menuList = new List<Models.MenuModel>();
            var first = list.Where(x => x.FunFloor.Length == 9).OrderBy(x => x.FunFloor).ToList();
            foreach (var item in first)
            {
                var temp = bindFun.Where(x => x.FunId == item.FunId).FirstOrDefault();
                if (temp != null)
                {
                    var model = new Models.MenuModel();
                    model.Floor = item.FunFloor;
                    model.Name = item.FunName;
                    model.Url = item.FunURL;

                    var childList = list.Where(x => x.FunFloor.IndexOf(item.FunFloor) == 0 && x.FunFloor.Length == item.FunFloor.Length + 3).OrderBy(x => x.FunFloor).ToList();
                    if (childList.Count > 0)
                    {
                        AddHeadMenu(model, childList, bindFun, list);
                    }
                    if (item.FunFloor == "100100103" && Models.SysUserProfile.CurrentProfile.UserModel.IsSysUser.HasValue && Models.SysUserProfile.CurrentProfile.UserModel.IsSysUser.Value == 1)
                    {
                        var tempMenu = new Models.MenuModel();
                        tempMenu.Floor = "100100103104";
                        tempMenu.Name = "全部项目/All Projects";
                        tempMenu.Url = "/Manager/ProAll";
                        model.Child.Insert(3,tempMenu);
                    }
                    menuList.Add(model);
                }
            }

            return PartialView(menuList);
        }

        private void AddHeadMenu(Models.MenuModel Menu, List<Core.Models.SA_SysFuns> children, List<Core.Models.SA_RoleFuns> bindList, List<Core.Models.SA_SysFuns> allPower)
        {
            foreach (var item in children)
            {
                var temp = bindList.Where(x => x.FunId == item.FunId).FirstOrDefault();
                if (temp != null)
                {
                    var model = new Models.MenuModel();
                    model.Floor = item.FunFloor;
                    model.Name = item.FunName;
                    model.Url = item.FunURL;

                    var childList = allPower.Where(x => x.FunFloor.IndexOf(item.FunFloor) == 0 && x.FunFloor.Length == item.FunFloor.Length + 3).OrderBy(x => x.FunFloor).ToList();
                    if (childList.Count > 0)
                    {
                        AddHeadMenu(model, childList, bindList, allPower);
                    }

                    
                    Menu.Child.Add(model);
                }
            }
        }
        #endregion
        #region 注册用户审核
        public ActionResult RegUserCheckList(int pageNo = 0)
        {
            ViewBag.txtUsername = Request["txtUsername"];
            ViewBag.txtName = Request["txtName"];
            Core.BLL.SA_UsersBLL bll = new Core.BLL.SA_UsersBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_Users").Where(" Status = 2 ");

            if (!string.IsNullOrEmpty(ViewBag.txtUsername))
                sql = sql.Where(" Username like @0 ", $"%{ViewBag.txtUsername}%");
            if (!string.IsNullOrEmpty(ViewBag.txtName))
                sql = sql.Where(" Name like @0 ", $"%{ViewBag.txtName}%");
            var page_ret = bll.GetPageList<Core.Models.SA_Users>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }

        /// <summary>
        /// 注册用户审核查看
        /// </summary>
        /// <param name="Uid"></param>
        /// <returns></returns>
        public ActionResult RegUserCheck(Guid Uid)
        {
            var model = new Core.BLL.SA_UsersBLL().GetById(Uid);
            return View(model);
        }

        public JsonResult ajaxCheckRegUser(Guid Uid, int CheckType)
        {
            var user = new Core.BLL.SA_UsersBLL().GetById(Uid);
            if (CheckType == 1)
            {
                user.Status = 1;
            }
            else if (CheckType == 2)
            {
                user.Status = 0;
            }
            var res = new Core.BLL.SA_UsersBLL().Update(user);
            if (res)
            {
                return WriteJsonResponse(true, "操作成功/Operated Successfully", false);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }
        #endregion
        #region 用户注册管理
        public ActionResult RegUserResetList(int pageNo = 0)
        {
            ViewBag.txtUsername = Request["txtUsername"];
            ViewBag.txtName = Request["txtName"];
            Core.BLL.SA_UsersBLL bll = new Core.BLL.SA_UsersBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_Users").Where(" Status = 1 ");

            if (!string.IsNullOrEmpty(ViewBag.txtUsername))
                sql = sql.Where(" Username like @0 ", $"%{ViewBag.txtUsername}%");
            if (!string.IsNullOrEmpty(ViewBag.txtName))
                sql = sql.Where(" Name like @0 ", $"%{ViewBag.txtName}%");
            var page_ret = bll.GetPageList<Core.Models.SA_Users>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }

        [HttpPost]
        public JsonResult ResetPwd(Guid Uid)
        {
            var user = new Core.BLL.SA_UsersBLL().GetById(Uid);
            if (user == null)
                return WriteJsonResponse(false, "此账号不存在", false);
            var md5pw = Common.MD5Utils.MD5(Guid.NewGuid().ToString());
            md5pw = md5pw.Length >= 8 ? md5pw.Substring(0, 8) : md5pw;
            user.Password = new Core.BLL.SA_UsersBLL().ExecutePassword(user.Username, md5pw);
            var res = new Core.BLL.SA_UsersBLL().Update(user);
            if (res)
            {
                return Json(new { Success = true, message = "密码重置成功", Pw = md5pw }, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(new { Success = true, message = "密码重置失败" }, JsonRequestBehavior.AllowGet);
            }
        }
        #endregion
        #region 分配任务
        /// <summary>
        /// 
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public ActionResult DistributionProject(string ids)
        {
            var strParams = "'" + ids.Trim(',').Replace(",", "','") + "'";
            var list = new Core.BLL.SA_NewProjectBLL().GetListByIds(strParams);
            var model = new Models.DistributionProjectModel();
            model.PList = list;
            model.Pids = ids.Trim(',');
            var rid = Guid.Parse("3d64080d-042f-45da-84be-570b360236a6");
            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.SysRoleId == rid).ToList();
            ViewBag.UserList = new SelectList(userList, "Uid", "Name");

            return View(model);
        }

        [HttpPost]
        public ActionResult DistributionProject(Models.DistributionProjectModel model)
        {
            var res = new Core.BLL.SA_NewProjectBLL().DistributionProject(model.Pids, model.Uid, Models.SysUserProfile.CurrentProfile.UserID);
            if (res)
            {
                return RedirectToAction("ProToBeAllot");
            }
            else
            {
                var strParams = "'" + model.Pids.Trim(',').Replace(",", "','") + "'";
                var list = new Core.BLL.SA_NewProjectBLL().GetListByIds(strParams);
                model.PList = list;
                ModelState.AddModelError("", "分配失败");
                return View(model);
            }
        }
        #endregion
        #region 待审核 批量审核
        public ActionResult BatchAudit(string ids)
        {
            var strParams = "'" + ids.Trim(',').Replace(",", "','") + "'";
            var list = new Core.BLL.SA_NewProjectBLL().GetListByIds(strParams);
            var model = new Models.BatchAuditModel();
            model.PList = list;
            model.Pids = ids.Trim(',');
            return View(model);
        }

        [HttpPost]
        public JsonResult BatchAudit(string ids, int type, string reverifyFailReason)
        {
            var strParams = ids.Trim(',');
            var res = new Core.BLL.SA_NewProjectBLL().BatchAudit(strParams, type, reverifyFailReason, Models.SysUserProfile.CurrentProfile.UserID);
            if (res)
            {
                return WriteJsonResponse(true, "操作成功/Operated Successfully", false);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }
        #endregion
        // GET: Manager
        #region 重新分配任务
        public ActionResult SingleDistributionChange(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            var currentReviewerId = "";
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    if (proInfo.executeTaskSysUserId.HasValue)
                    {
                        currentReviewerId = proInfo.executeTaskSysUserId.Value.ToString();
                        model.executeTaskSysUserName = new Core.BLL.SA_SysUserBLL().GetUserName(proInfo.executeTaskSysUserId.Value);
                    }
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);


            var rid = Guid.Parse("3d64080d-042f-45da-84be-570b360236a6");
            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.ParentId == SysUserProfile.CurrentProfile.UserID.ToString()).ToList();
            var list = new List<Common.dataObjectList>();
            var temp = userList.ConvertAll(x => new Common.dataObjectList
            {
                id = x.Uid.ToString(),
                name = $"{x.Username}[{x.Name}]({bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count})",
                Count = bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count
            });
            if (!string.IsNullOrWhiteSpace(currentReviewerId))
            {
                var currentReviewer = temp.Where(x => x.id == currentReviewerId).FirstOrDefault();
                if (currentReviewer != null)
                    temp.Remove(currentReviewer);
            }
            list.Add(new Common.dataObjectList { id = "", name = "请选择" });
            list.AddRange(temp.OrderBy(x => x.Count).ToList());
            ViewBag.UserList = new SelectList(list, "id", "name");

            return View(model);
        }

        [HttpPost]
        public JsonResult SingleDistributionChange(Guid pid, Guid UserId)
        {
            var res = new Core.BLL.SA_NewProjectBLL().DistributionChangeProject(pid.ToString(), UserId, Models.SysUserProfile.CurrentProfile.UserID);

            if (res)
            {
                return WriteJsonResponse(true, "分配成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "分配失败", false);
            }
        }
        #endregion
        #region 单个审核
        public ActionResult SingleAudit(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.RegTime = proInfo.regTime.HasValue ? proInfo.regTime.Value.ToString("yyyy-MM-dd") : "";
                    model.createUserName = new Core.BLL.SA_UsersBLL().GetUserName(proInfo.createUserID.Value);
                    model.regNumber = proInfo.regNumber;
                    model.Reason = "";
                    model.editRequestResult = proInfo.editRequestResult;
                    var record = new Core.BLL.SA_ProjectVerifyTaskFlowRecordBLL().GetLastByProjectId(proInfo.Pid);
                    if (record != null && !string.IsNullOrWhiteSpace(record.Reason))
                    {
                        model.Reason = record.Reason;
                    }
                    model.TaskStatus = proInfo.taskStatus;
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);

            var list = Common.CommonList.getAuditState();
            ViewBag.AuditStateList = new SelectList(list, "id", "name");

            return View(model);
        }

        [HttpPost]
        public JsonResult SingleAudit(Guid Pid, int AuditState, string RejectReason)
        {
            var bll = new Core.BLL.SA_NewProjectBLL();
            var project = bll.GetById(Pid);
            if (project.executeTaskSysUserId != Models.SysUserProfile.CurrentProfile.UserID)
            {
                return Json(new { success = false, message = "该项目已分配给其他用户,无法操作此项目", IsRedirect = true }, JsonRequestBehavior.AllowGet);
            }
            if (project.taskStatus != 2)
            {
                var strStatus = GetStatusName.GettaskStatusName(project.taskStatus ?? 0);

                return Json(new { success = false, message = $"该项目状态为{strStatus},无法操作此项目", IsRedirect = true }, JsonRequestBehavior.AllowGet);
            }
            var res = bll.BatchAudit(Pid.ToString(), AuditState == 3 ? 1 : 2, RejectReason, Models.SysUserProfile.CurrentProfile.UserID);
            if (res)
            {
                return Json(new { success = true, message = "操作成功/Operated Successfully", IsRedirect = true }, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(new { success = false, message = "操作失败", IsRedirect = false }, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public JsonResult SingleAuditTemp(Guid Pid, int AuditState, string RejectReason)
        {
            var bll = new Core.BLL.SA_NewProjectBLL();
            var project = bll.GetById(Pid);
            project.editRequestResult = RejectReason;

            var res = bll.Update(project);
            if (res)
            {
                return Json(new { success = true, message = "操作成功/Operated Successfully", IsRedirect = true }, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return Json(new { success = false, message = "操作失败", IsRedirect = false }, JsonRequestBehavior.AllowGet);
            }
        }

        #endregion
        #region 单个复审

        public ActionResult SingleSecAudit(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.RegTime = proInfo.regTime.HasValue ? proInfo.regTime.Value.ToString("yyyy-MM-dd") : "";
                    model.createUserName = new Core.BLL.SA_UsersBLL().GetUserName(proInfo.createUserID.Value);
                    //proInfo.
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                    if (!string.IsNullOrWhiteSpace(proInfo.regNumber) && proInfo.regSerialNumber.HasValue)
                    {
                        ViewBag.IsCanModifyRegNum = false;
                        model.regSerialNumber = proInfo.regSerialNumber.Value.ToString();
                        model.regNumber = proInfo.regNumber;
                        model.strRegNumber = model.regNumber;
                    }
                    else
                    {
                        var year = DateTime.Now.Year.ToString();
                        model.regSerialNumber = bllProj.GetMaxRegNumber(DateTime.Now.Year);
                        model.regNumber = "ITMCTR";
                        model.strRegNumber = model.regNumber + year + model.regSerialNumber;
                    }

                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();

            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);

            var list = Common.CommonList.getSingleSecAuditState();
            ViewBag.AuditStateList = new SelectList(list, "id", "name");
            return View(model);
        }

        [HttpPost]
        public JsonResult SingleSecAudit(Guid Pid, int AuditState, string RejectReason)
        {
            var model = new Core.BLL.SA_NewProjectBLL().GetById(Pid);
            if (model.taskStatus != 3)
            {
                var strStatus = GetStatusName.GettaskStatusName(model.taskStatus ?? 0);

                return Json(new { success = false, message = $"该项目状态为{strStatus},无法操作此项目", IsRedirect = true }, JsonRequestBehavior.AllowGet);
            }
            var res = new Core.BLL.SA_NewProjectBLL().SingleSecAudit(Pid.ToString(), AuditState == 3 ? 1 : 2, RejectReason, Models.SysUserProfile.CurrentProfile.UserID);
            if (res)
            {
                return WriteJsonResponse(true, "操作成功/Operated Successfully", false);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }

        /// <summary>
        /// 重置注册号
        /// </summary>
        /// <returns></returns>

        public JsonResult ResetRegNumber()
        {
            try
            {
                var regNumber = new Core.BLL.SA_NewProjectBLL().GetMaxRegNumber(DateTime.Now.Year);
                var regSerialNumber = "ITMCTR";
                var strRegNumber = regNumber;
                return Json(new { Success = true, regNumber = regNumber, regSerialNumber = regSerialNumber, strRegNumber = strRegNumber }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                return Json(new { Success = false }, JsonRequestBehavior.AllowGet);
            }
        }
        #endregion
        #region 再修改审核
        //SingleAgainAudit
        public ActionResult SingleAgainAudit(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.editRequestAttachment = proInfo.editRequestAttachment;
                    model.TaskStatus = proInfo.taskStatus;
                    model.Reason = "";
                    var record = new Core.BLL.SA_ProjectRequestEditFlowBLL().GetByLastModelByPid(proInfo.Pid);
                    if (record != null)
                    {
                        if (!string.IsNullOrWhiteSpace(record.EditRequestReason))
                            model.EditRequestReason = record.EditRequestReason;
                        model.EditRequestDate = record.RequestTime;
                        model.editRequestAttachment = record.EditRequestAttachment;
                        model.editRequestAttachment2 = record.EditRequestAttachment2;
                    }
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);

            var list = Common.CommonList.getAgainAuditState();
            ViewBag.AuditStateList = new SelectList(list, "id", "name");
            return View(model);
        }

        [HttpPost]
        public JsonResult SingleAgainAudit(Guid Pid, int AuditState, string RejectReason)
        {
            var res = new Core.BLL.SA_NewProjectBLL().AgainAudit(Pid.ToString(), AuditState == 3 ? 1 : 2, RejectReason, Models.SysUserProfile.CurrentProfile.UserID);
            if (res)
            {
                return WriteJsonResponse(true, "操作成功/Operated Successfully", false);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }
        #endregion
        #region 项目信息
        public ActionResult AllProjects(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            ViewBag.regfrom = Request["regfrom"];
            ViewBag.regto = Request["regto"];
            ViewBag.numberfrom = Request["numberfrom"];
            ViewBag.numberto = Request["numberto"];

            List<SelectListItem> listItem = new List<SelectListItem>();
            listItem.Add(new SelectListItem { Value = "", Text = "不限/All" });
            listItem.Add(new SelectListItem { Value = "1", Text = "待审核/Not be verified" });
            listItem.Add(new SelectListItem { Value = "3", Text = "通过审核/Successful" });

            ViewBag.listVerify = new SelectList(listItem, "Value", "Text", ViewBag.listVerifyStatus);
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" isDeleted <> 1");

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");

            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");

            if (!string.IsNullOrEmpty(ViewBag.listVerifyStatus))
                sql = sql.Where(" status=@0 ", ViewBag.listVerifyStatus);

            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);

            if (!string.IsNullOrEmpty(ViewBag.numberfrom) && !string.IsNullOrEmpty(ViewBag.numberto))
                sql = sql.Where(" regNumberTime >= @0 AND regNumberTime <= @0", ViewBag.numberfrom, ViewBag.numberto);

            if (!string.IsNullOrEmpty(ViewBag.regfrom) && !string.IsNullOrEmpty(ViewBag.regto))
                sql = sql.Where(" regDate >= @0 AND regDate <= @0", ViewBag.regfrom, ViewBag.regto);

            sql = sql.OrderBy("regTime desc");

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        /// <summary>
        /// 项目查询+导出
        /// </summary>
        /// <param name="pageNo"></param>
        /// <returns></returns>
        public ActionResult AllProjectsExport(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            ViewBag.regfrom = Request["regfrom"];
            ViewBag.regto = Request["regto"];
            ViewBag.numberfrom = Request["numberfrom"];
            ViewBag.numberto = Request["numberto"];

            List<SelectListItem> listItem = new List<SelectListItem>();
            listItem.Add(new SelectListItem { Value = "", Text = "不限/All" });
            listItem.Add(new SelectListItem { Value = "1", Text = "待审核/Not be verified" });
            listItem.Add(new SelectListItem { Value = "3", Text = "通过审核/Successful" });

            ViewBag.listVerify = new SelectList(listItem, "Value", "Text", ViewBag.listVerifyStatus);
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where("  status = 3 and isDeleted <> 1");
            var strwhere = " where  status = 3 and isDeleted <> 1";
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
            {
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
                strwhere += $" and  (publicTitleCN like %{ViewBag.txtTitle}% or publicTitleEN like @%{ViewBag.txtTitle}%)";
            }
            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
            {
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");
                strwhere += $" and  (ScientifirTitleCN like %{ViewBag.txtOfficialName}% or ScientifirTitleEN like %{ViewBag.txtOfficialName}%)";
            }
            if (!string.IsNullOrEmpty(ViewBag.listVerifyStatus))
            {
                sql = sql.Where(" status=@0 ", ViewBag.listVerifyStatus);
                strwhere += $" and  status='{ViewBag.listVerifyStatus}'";
            }

            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
            {
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
                strwhere += $" and  regNumber='{ViewBag.txtRegNo}'";
            }
            if (!string.IsNullOrEmpty(ViewBag.numberfrom) && !string.IsNullOrEmpty(ViewBag.numberto))
            {
                sql = sql.Where(" CONVERT(varchar, regNumberTime, 23)  >= @0 AND CONVERT(varchar, regNumberTime, 23)  <= @1", ViewBag.numberfrom, ViewBag.numberto);
                strwhere += $" and  CONVERT(varchar, regNumberTime, 23)  >= '{ViewBag.numberfrom}' AND CONVERT(varchar, regNumberTime, 23)  <= '{ViewBag.numberto}'";
            }

            if (!string.IsNullOrEmpty(ViewBag.regfrom) && !string.IsNullOrEmpty(ViewBag.regto))
            {
                sql = sql.Where(" CONVERT(varchar, regDate, 23)   >= @0 AND CONVERT(varchar, regDate, 23)  <= @0", ViewBag.regfrom, ViewBag.regto);
                strwhere += $" and  CONVERT(varchar, regDate, 23)  >= '{ViewBag.regfrom}' AND CONVERT(varchar, regDate, 23)  <= '{ViewBag.regto}'";
            }

            sql = sql.OrderBy("regTime desc");

            Session["AllProjectsExport"] = strwhere;

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        /// <summary>
        /// 导出EXCEL
        /// </summary>
        /// <param name="pids"></param>
        /// <returns></returns>
        [NoAsyncTimeout]
        async public Task<FileResult> AllProjectsExportToExcel(string pids)
        {
            var ret = await Task.Run(() =>
            {
                using (MemoryStream fs = new MemoryStream())
                {
                    var sqlAll = @"SELECT   
         Pid
        ,[regNumber] 注册号
        ,[regNumberTime] 注册时间
        ,(CASE [registrationStatus] WHEN '1008001' THEN '预注册' WHEN '1008002' THEN '补注册' ELSE '' END) 注册状态 
        ,[publicTitleCN] 注册题目
        ,[publicTitleEN] 注册题目EN
        ,[scientifirTitleCN] 正式科学名
        ,[scientifirTitleEN] 正式科学名EN
        ,[applicantCN] 申请注册联系人
        ,[applicantEN] 申请注册联系人EN
        ,[studyLeaderCN] 研究负责人
        ,[studyLeaderEN] 研究负责人EN
        ,[applicantTelephone] 注册联系人电话
        ,[studyTelephone] 试验负责人电话
        ,[applicanFax] 申请注册联系人传真
        ,[studyFax] 研究负责人传真
        ,[applicantEmail] 申请注册联系人电子邮件
        ,[studyEmail] 研究负责人电子邮件
        ,[applicantWebsite] 申请单位网址
        ,[studyWebsite] 研究负责人网址
        ,[applicantAddressCN] 申请注册联系人通讯地址
        ,[applicantAddressEN] 申请注册联系人通讯地址EN
        ,[studyAddressCN] 研究负责人通讯地址
        ,[studyAddressEN] 研究负责人通讯地址EN
        ,[applicantPostcode] 申请注册联系人邮政编码
        ,[studyPostcode] 研究负责人邮政编码
        ,[applicantInstitutionCN] 申请人所在单位
        ,[applicantInstitutionEN] 申请人所在单位EN
        ,[studyLeaderCompanyCN] 研究负责人所在单位
        ,[studyLeaderCompanyEN] 研究负责人所在单位EN
        ,[approvedCommittee] 是否获伦理委员会批准
        ,[ethicalCommitteeFileID] 伦理委员会批件文号
        --,[fileEthicalCommittee]
        ,[ethicalCommitteeName] 批准本研究的伦理委员会名称
        ,[ethicalCommitteeNameEN] 批准本研究的伦理委员会名称EN
        ,[ethicalCommitteeSanctionDate] 伦理委员会批准日期
        ,[ethicalCommitteeCName] 伦理委员会联系人
        ,[ethicalCommitteeCNameEN] 伦理委员会联系人EN
        ,[ethicalCommitteeCAddress] 伦理委员会联系地址
        ,[ethicalCommitteeCAddressEN] 伦理委员会联系地址EN
        ,[ethicalCommitteeCPhone] 伦理委员会联系人电话
        ,[ethicalCommitteeCEmail] 伦理委员会联系人邮箱
        ,[of_SFDA] 国家药监局批准文号
        ,[fileSFDA] 国家药监局批准附件
        ,[dataSFDA] 国家药监局批准日期
        ,[studyPlanfile] 研究计划书
        --,[informedConsentfile]
        ,[primarySponsorCN] 研究实施负责单位
        ,[primarySponsorEN] 研究实施负责单位EN
        ,[primarySponsorAddressCN] 研究实施负责单位地址
        ,[primarySponsorAddressEN] 研究实施负责单位地址EN
        ,[sourceFundingCN] 经费或物资来源
        ,[sourceFundingEN] 经费或物资来源EN
        ,[targetDiseaseCN] 研究疾病
        ,[targetDiseaseEN] 研究疾病EN
        ,[targetCode] 研究疾病代码
		,(SELECT TOP 1 DescriptionCn FROM SA_StudyDictionary WHERE StudyName ='StudyType' AND StudyValue  = [studyTypeID]) 研究类型
		,(SELECT TOP 1 DescriptionCn FROM SA_StudyDictionary WHERE StudyName ='StudyDesign' AND StudyValue  = [studyDesignID]) 研究设计
		,(SELECT TOP 1 DescriptionCn FROM SA_StudyDictionary WHERE StudyName ='StudyPhase' AND StudyValue  = [studyPhaseID]) 研究所处阶段
        ,[objectivesStudyCN] 研究目的
        ,[objectivesStudyEN] 研究目的EN
        ,[contentsDrugCN] 药物成份或治疗方案详述
        ,[contentsDrugEN] 药物成份或治疗方案详述EN
        ,[inclusionCriteriaCN] 纳入标准
        ,[inclusionCriteriaEN] 纳入标准EN
        ,[exclusionCrteriaCN] 排除标准
        ,[exclusionCrteriaEN] 排除标准EN
        ,[studyTimeStart] 研究实施时间开始
        ,[studyTimeEnd] 研究实施时间结束
        ,[recruitingTimeStart] 征募观察对象时间开始
        ,[recruitingTimeEnd] 征募观察对象时间结束
        ,[totalSampleSize] 样本总量
		,(CASE [recruitingStatus] WHEN '1004001' THEN '尚未开始' WHEN '1004002' THEN '正在进行' WHEN '1004003' THEN '暂停或中断' WHEN '1004004' THEN '结束'ELSE '' END) 征募研究对象情况 
        ,[ageMin] 年龄范围最小
        ,[ageMax] 年龄范围最大
        ,[randomMethodCN] 随机方法
        ,[randomMethodEN] 随机方法EN
		,(CASE [sex] WHEN '1006001' THEN '男性' WHEN '1006002' THEN '女性' WHEN '1006003' THEN '男女均可' ELSE '' END) 性别 
        ,[signConsent] 研究对象是否签署知情同意书
        ,[followupTime] 随访时间
		,(CASE [followup] WHEN '1007001' THEN '天' WHEN '1007002' THEN '周' WHEN '1007003' THEN '月' WHEN '1007004' THEN '年' WHEN '1007005' THEN '小时' ELSE '' END) 随访时间单位 
        ,[processConcealmentCN] 隐蔽分组方法和过程
        ,[processConcealmentEN] 隐蔽分组方法和过程EN
        ,[blindingCN] 盲法
        ,[blindingEN] 盲法EN
        ,[RulesblindingCN] 揭盲或破盲原则和方法
        ,[RulesblindingEN] 揭盲或破盲原则和方法EN
        ,[statisticalMethodCN] 统计方法名称
        ,[statisticalMethodEN] 统计方法名称EN
        ,[calculatedResultsCN] 试验完成后的统计结果
        ,[calculatedResultsEN] 试验完成后的统计结果EN
        ,[whetherPublic] 是否公开试验完成后的统计结果
        ,[dataManagementCN] 共享原始数据的方式
        ,[dataManagementEN] 共享原始数据的方式EN
        ,[dataAnalysisCN] 数据采集和管理
        ,[dataAnalysisEN] 数据采集和管理EN
        ,[studyReport] 研究计划书或研究结果报告发表信息
        ,[studyReportEN] 研究计划书或研究结果报告发表信息EN
        ,[UTN] 全球唯一识别码
		,(CASE [DataManagemenBoard] WHEN '1' THEN '有' WHEN '0' THEN '无' WHEN '-1' THEN '暂未确定' ELSE '' END) 数据与安全监察委员会 
        ,[DataCollectionUnit] 是否共享原始数据
		,[secondaryID] 二级注册机注册号
        ,[ReleaseNumber] 其它机构的注册号
  FROM   SA_NewProject";
                    var wherestr = "";
                    if (Session["AllProjectsExport"] != null)
                    {

                        sqlAll += Session["AllProjectsExport"].ToString();
                        wherestr = Session["AllProjectsExport"].ToString();
                        sqlAll += " order by regTime desc";
                    }
                    //采集人体标本
                    var sql1 = $@"SELECT   
                                P.Pid,
                                P.regNumber 注册号, 
                                S.sampleNameCN 标本中文名, 
                                S.sampleNameEN 标本中文名EN, 
                                S.tissueCN 组织, 
                                S.tissueEN 组织EN, 
                                (CASE S.fateSample WHEN '1012001' THEN '使用后销毁' WHEN '1012002' THEN '使用后保存' WHEN '1012003' THEN '其它' ELSE '' END) 人体标本去向, 
                                S.noteCN 说明, 
                                S.noteEN 说明EN				
                                FROM SA_CollectingSample AS S INNER JOIN  SA_NewProject AS P ON S.Pid = P.Pid
                                WHERE P.pid in (select pid from SA_NewProject {wherestr})";
                    //测量指标
                    var sql2 = $@"SELECT P.Pid
                               ,p.regNumber 注册号
                               ,S.[outcomeNameCN] 指标中文名
                               ,S.[outcomeNameEN] 指标英文名
	                           ,(CASE S.[pointerType] WHEN '4002001' THEN '主要指标' WHEN '4002002' THEN '次要指标' WHEN '4002003' THEN '附加指标' WHEN '4002004' THEN '副作用指标' ELSE '' END) 指标类型
                               ,S.[measureTimeCN] 测量时间点
	                           ,S.[measureTimeEN] 测量时间点EN
	                           ,S.[measureMethodCN] 测量方法
	                           ,S.[measureMethodEN] 测量方法EN
                               FROM [SA_Outcomes] AS S INNER JOIN  SA_NewProject AS P ON S.Pid = P.Pid
                               WHERE P.pid in (select pid from SA_NewProject {wherestr})";
                    //干预措施
                    var sql3 = $@"SELECT P.Pid
                               ,P.regNumber 注册号
                               ,S.[groupsCN] 组别
                               ,S.[groupsEN] 组别EN
                               ,S.[sampleSize] 样本量
                               ,S.[interventionCN] 干预措施
                               ,S.[interventionEN] 干预措施EN
                               ,S.[interventionCode] 干预措施代码
                               FROM [SA_Interventions] AS S INNER JOIN  SA_NewProject AS P ON S.Pid = P.Pid
                               WHERE P.pid in (select pid from SA_NewProject {wherestr})";
                    //试验主办单位如下
                    var sql4 = $@"SELECT P.Pid
                              ,P.regNumber 注册号
                              ,[countryCN] 国家
                              ,[countryEN] 国家EN
                              ,[provinceCN] 省
                              ,[provinceEN] 省EN
                              ,[cityCN] 市
                              ,[cityEN] 市EN
                              ,[institutionCN] 单位
                              ,[institutionEN] 单位EN
                              ,[specificAddressCN] 具体地址
                              ,[specificAddressEN] 具体地址EN
                        FROM [SA_SecondarySponsor] AS S INNER JOIN  SA_NewProject AS P ON S.Pid = P.Pid
                               WHERE P.pid in (select pid from SA_NewProject {wherestr})";
                    //研究实施地点
                    var sql5 = $@"SELECT P.Pid
                              ,P.regNumber 注册号
                              ,[countryCN] 国家
                              ,[countryEN] 国家EN
                              ,[provinceCN] 省
                              ,[provinceEN] 省EN
                              ,[cityCN] 市
                              ,[cityEN] 市EN
                              ,[hospitalCN] 单位
                              ,[hospitalEN] 单位EN
                              ,[levelInstitutionCN] 单位级别
                              ,[levelInstitutionEN] 单位级别EN
                        FROM [SA_ResearchAddress] AS S INNER JOIN  SA_NewProject AS P ON S.Pid = P.Pid
                               WHERE P.pid in (select pid from SA_NewProject {wherestr})";
                    //诊断试验
                    var sql6 = $@"SELECT P.Pid
                              ,P.regNumber 注册号
                              ,S.[standard] 金标准或参考标准
                              ,S.[standardEn] 金标准或参考标准EN
                              ,S.[indexTest] 指标试验
                              ,S.[indexTestEn] 指标试验EN
                              ,S.[targetCondition] 目标人群
                              ,S.[targetConditionEn] 目标人群EN
                              ,S.[sampleSizeT] 例数
                              ,S.[difficultCondition] 容易混淆的疾病人群
                              ,S.[difficultConditionEn] 容易混淆的疾病人群EN
                              ,S.[sampleSizeD] 例数2
                          FROM [SA_Diagnostic] AS S INNER JOIN  SA_NewProject AS P ON S.Pid = P.Pid
                               WHERE P.pid in (select pid from SA_NewProject {wherestr})";


                    Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
                    var sql = new PetaPoco.Sql(sqlAll);
                    var list = bll.GetPageList<AllProjectsExportModel>(0, -1, sql);

                    var list_1 = bll.db.Query<AllProjectsExport1Model>(sql1).ToList();
                    var list_2 = bll.db.Query<AllProjectsExport2Model>(sql2).ToList();
                    var list_3 = bll.db.Query<AllProjectsExport3Model>(sql3).ToList();
                    var list_4 = bll.db.Query<AllProjectsExport4Model>(sql4).ToList();
                    var list_5 = bll.db.Query<AllProjectsExport5Model>(sql5).ToList();
                    var list_6 = bll.db.Query<AllProjectsExport6Model>(sql6).ToList();

                    AsposeLicenseHelper.SetCellLicense();
                    Workbook worksheet = new Workbook(FileFormatType.Xlsx);
                    worksheet.Worksheets.Add(SheetType.Worksheet);

                    worksheet.Worksheets[0].Cells[0, 1].PutValue("注册号");
                    worksheet.Worksheets[0].Cells[0, 2].PutValue("注册时间");
                    worksheet.Worksheets[0].Cells[0, 3].PutValue("注册状态");
                    worksheet.Worksheets[0].Cells[0, 4].PutValue("注册题目");
                    worksheet.Worksheets[0].Cells[0, 5].PutValue("注册题目EN");
                    worksheet.Worksheets[0].Cells[0, 6].PutValue("正式科学名");
                    worksheet.Worksheets[0].Cells[0, 7].PutValue("正式科学名EN");
                    worksheet.Worksheets[0].Cells[0, 8].PutValue("申请注册联系人");
                    worksheet.Worksheets[0].Cells[0, 9].PutValue("申请注册联系人EN");
                    worksheet.Worksheets[0].Cells[0, 10].PutValue("研究负责人");
                    worksheet.Worksheets[0].Cells[0, 11].PutValue("研究负责人EN");
                    worksheet.Worksheets[0].Cells[0, 12].PutValue("注册联系人电话");
                    worksheet.Worksheets[0].Cells[0, 13].PutValue("试验负责人电话");
                    worksheet.Worksheets[0].Cells[0, 14].PutValue("申请注册联系人传真");
                    worksheet.Worksheets[0].Cells[0, 15].PutValue("研究负责人传真");
                    worksheet.Worksheets[0].Cells[0, 16].PutValue("申请注册联系人电子邮件");
                    worksheet.Worksheets[0].Cells[0, 17].PutValue("研究负责人电子邮件");
                    worksheet.Worksheets[0].Cells[0, 18].PutValue("申请单位网址");
                    worksheet.Worksheets[0].Cells[0, 19].PutValue("研究负责人网址");
                    worksheet.Worksheets[0].Cells[0, 20].PutValue("申请注册联系人通讯地址");
                    worksheet.Worksheets[0].Cells[0, 21].PutValue("申请注册联系人通讯地址EN");
                    worksheet.Worksheets[0].Cells[0, 22].PutValue("研究负责人通讯地址");
                    worksheet.Worksheets[0].Cells[0, 23].PutValue("研究负责人通讯地址EN");
                    worksheet.Worksheets[0].Cells[0, 24].PutValue("申请注册联系人邮政编码");
                    worksheet.Worksheets[0].Cells[0, 25].PutValue("研究负责人邮政编码");
                    worksheet.Worksheets[0].Cells[0, 26].PutValue("申请人所在单位");
                    worksheet.Worksheets[0].Cells[0, 27].PutValue("研究负责人所在单位");
                    worksheet.Worksheets[0].Cells[0, 28].PutValue("研究负责人所在单位EN");
                    worksheet.Worksheets[0].Cells[0, 29].PutValue("是否获伦理委员会批准");
                    worksheet.Worksheets[0].Cells[0, 30].PutValue("伦理委员会批件文号");
                    worksheet.Worksheets[0].Cells[0, 31].PutValue("批准本研究的伦理委员会名称");
                    worksheet.Worksheets[0].Cells[0, 32].PutValue("批准本研究的伦理委员会名称EN");
                    worksheet.Worksheets[0].Cells[0, 33].PutValue("伦理委员会批准日期");
                    worksheet.Worksheets[0].Cells[0, 34].PutValue("伦理委员会联系人");
                    worksheet.Worksheets[0].Cells[0, 35].PutValue("伦理委员会联系人EN");
                    worksheet.Worksheets[0].Cells[0, 36].PutValue("伦理委员会联系地址");
                    worksheet.Worksheets[0].Cells[0, 37].PutValue("伦理委员会联系地址EN");
                    worksheet.Worksheets[0].Cells[0, 38].PutValue("伦理委员会联系人电话");
                    worksheet.Worksheets[0].Cells[0, 39].PutValue("伦理委员会联系人邮箱");
                    worksheet.Worksheets[0].Cells[0, 40].PutValue("国家药监局批准文号");
                    worksheet.Worksheets[0].Cells[0, 41].PutValue("国家药监局批准附件");
                    worksheet.Worksheets[0].Cells[0, 42].PutValue("国家药监局批准日期");
                    worksheet.Worksheets[0].Cells[0, 43].PutValue("研究计划书");
                    worksheet.Worksheets[0].Cells[0, 44].PutValue("研究实施负责单位");
                    worksheet.Worksheets[0].Cells[0, 45].PutValue("研究实施负责单位EN");
                    worksheet.Worksheets[0].Cells[0, 46].PutValue("研究实施负责单位地址");
                    worksheet.Worksheets[0].Cells[0, 47].PutValue("研究实施负责单位地址EN");
                    worksheet.Worksheets[0].Cells[0, 48].PutValue("经费或物资来源");
                    worksheet.Worksheets[0].Cells[0, 49].PutValue("经费或物资来源EN");
                    worksheet.Worksheets[0].Cells[0, 50].PutValue("研究疾病");
                    worksheet.Worksheets[0].Cells[0, 51].PutValue("研究疾病EN");
                    worksheet.Worksheets[0].Cells[0, 52].PutValue("研究疾病代码");
                    worksheet.Worksheets[0].Cells[0, 53].PutValue("研究类型");
                    worksheet.Worksheets[0].Cells[0, 54].PutValue("研究设计");
                    worksheet.Worksheets[0].Cells[0, 55].PutValue("研究所处阶段");
                    worksheet.Worksheets[0].Cells[0, 56].PutValue("研究目的");
                    worksheet.Worksheets[0].Cells[0, 57].PutValue("研究目的EN");
                    worksheet.Worksheets[0].Cells[0, 58].PutValue("药物成份或治疗方案详述");
                    worksheet.Worksheets[0].Cells[0, 59].PutValue("药物成份或治疗方案详述EN");
                    worksheet.Worksheets[0].Cells[0, 60].PutValue("纳入标准");
                    worksheet.Worksheets[0].Cells[0, 61].PutValue("纳入标准EN");
                    worksheet.Worksheets[0].Cells[0, 62].PutValue("排除标准");
                    worksheet.Worksheets[0].Cells[0, 63].PutValue("排除标准EN");
                    worksheet.Worksheets[0].Cells[0, 64].PutValue("研究实施时间开始");
                    worksheet.Worksheets[0].Cells[0, 65].PutValue("研究实施时间结束");
                    worksheet.Worksheets[0].Cells[0, 66].PutValue("征募观察对象时间开始");
                    worksheet.Worksheets[0].Cells[0, 67].PutValue("征募观察对象时间结束");
                    worksheet.Worksheets[0].Cells[0, 68].PutValue("样本总量");
                    worksheet.Worksheets[0].Cells[0, 69].PutValue("征募研究对象情况");
                    worksheet.Worksheets[0].Cells[0, 70].PutValue("年龄范围最小");
                    worksheet.Worksheets[0].Cells[0, 71].PutValue("年龄范围最大");
                    worksheet.Worksheets[0].Cells[0, 72].PutValue("随机方法");
                    worksheet.Worksheets[0].Cells[0, 73].PutValue("随机方法EN");
                    worksheet.Worksheets[0].Cells[0, 74].PutValue("研究对象是否签署知情同意书");
                    worksheet.Worksheets[0].Cells[0, 75].PutValue("随访时间");
                    worksheet.Worksheets[0].Cells[0, 76].PutValue("随访时间单位");
                    worksheet.Worksheets[0].Cells[0, 77].PutValue("隐蔽分组方法和过程");
                    worksheet.Worksheets[0].Cells[0, 78].PutValue("隐蔽分组方法和过程EN");
                    worksheet.Worksheets[0].Cells[0, 79].PutValue("盲法");
                    worksheet.Worksheets[0].Cells[0, 80].PutValue("盲法EN");
                    worksheet.Worksheets[0].Cells[0, 81].PutValue("揭盲或破盲原则和方法");
                    worksheet.Worksheets[0].Cells[0, 82].PutValue("揭盲或破盲原则和方法EN");
                    worksheet.Worksheets[0].Cells[0, 83].PutValue("统计方法名称");
                    worksheet.Worksheets[0].Cells[0, 84].PutValue("统计方法名称EN");
                    worksheet.Worksheets[0].Cells[0, 85].PutValue("试验完成后的统计结果");
                    worksheet.Worksheets[0].Cells[0, 86].PutValue("试验完成后的统计结果EN");
                    worksheet.Worksheets[0].Cells[0, 87].PutValue("是否公开试验完成后的统计结果");
                    worksheet.Worksheets[0].Cells[0, 88].PutValue("共享原始数据的方式");
                    worksheet.Worksheets[0].Cells[0, 89].PutValue("共享原始数据的方式EN");
                    worksheet.Worksheets[0].Cells[0, 90].PutValue("数据采集和管理");
                    worksheet.Worksheets[0].Cells[0, 91].PutValue("数据采集和管理EN");
                    worksheet.Worksheets[0].Cells[0, 92].PutValue("研究计划书或研究结果报告发表信息");
                    worksheet.Worksheets[0].Cells[0, 93].PutValue("研究计划书或研究结果报告发表信息EN");
                    worksheet.Worksheets[0].Cells[0, 94].PutValue("全球唯一识别码");
                    worksheet.Worksheets[0].Cells[0, 95].PutValue("数据与安全监察委员会");
                    worksheet.Worksheets[0].Cells[0, 96].PutValue("是否共享原始数据");
                    worksheet.Worksheets[0].Cells[0, 97].PutValue("二级注册机注册号");
                    worksheet.Worksheets[0].Cells[0, 98].PutValue("其它机构的注册号");

                    worksheet.Worksheets[0].Cells[0, 99].PutValue("采集人体标本");
                    worksheet.Worksheets[0].Cells[0, 100].PutValue("测量指标");
                    worksheet.Worksheets[0].Cells[0, 101].PutValue("干预措施");
                    worksheet.Worksheets[0].Cells[0, 102].PutValue("试验主办单位如下");
                    worksheet.Worksheets[0].Cells[0, 103].PutValue("研究实施地点");
                    worksheet.Worksheets[0].Cells[0, 104].PutValue("诊断试验");

                    int index = 1;
                    foreach (var item in list.Items)
                    {
                        var q1 = list_1.Where(x => x.Pid == item.Pid).ToList().Select(x => $"{x.标本中文名},{x.标本中文名EN},{x.组织},{x.组织EN},{x.人体标本去向},{x.说明},{x.说明EN}");
                        var q2 = list_2.Where(x => x.Pid == item.Pid).ToList().Select(x => $"{x.指标中文名},{x.指标英文名},{x.指标类型},{x.测量时间点},{x.测量时间点EN},{x.测量方法},{x.测量方法EN}");
                        var q3 = list_3.Where(x => x.Pid == item.Pid).ToList().Select(x => $"{x.组别},{x.组别EN},{x.样本量},{x.干预措施},{x.干预措施EN},{x.干预措施代码}");
                        var q4 = list_4.Where(x => x.Pid == item.Pid).ToList().Select(x => $"{x.国家},{x.国家EN},{x.省},{x.省EN},{x.市},{x.市EN},{x.单位},{x.单位EN},{x.具体地址},{x.具体地址EN}");
                        var q5 = list_5.Where(x => x.Pid == item.Pid).ToList().Select(x => $"{x.国家},{x.国家EN},{x.省},{x.省EN},{x.市},{x.市EN},{x.单位},{x.单位EN},{x.单位级别},{x.单位级别EN}");
                        var q6 = list_6.Where(x => x.Pid == item.Pid).ToList().Select(x => $"{x.金标准或参考标准},{x.金标准或参考标准EN},{x.指标试验},{x.指标试验EN},{x.目标人群},{x.目标人群EN},{x.例数},{x.容易混淆的疾病人群},{x.容易混淆的疾病人群EN},{x.例数2}");

                        if (q1.Any())
                            worksheet.Worksheets[0].Cells[index, 99].PutValue(string.Join(System.Environment.NewLine, q1));
                        if (q2.Any())
                            worksheet.Worksheets[0].Cells[index, 100].PutValue(string.Join(System.Environment.NewLine, q2));
                        if (q3.Any())
                            worksheet.Worksheets[0].Cells[index, 101].PutValue(string.Join(System.Environment.NewLine, q3));
                        if (q4.Any())
                            worksheet.Worksheets[0].Cells[index, 102].PutValue(string.Join(System.Environment.NewLine, q4));
                        if (q5.Any())
                            worksheet.Worksheets[0].Cells[index, 103].PutValue(string.Join(System.Environment.NewLine, q5));
                        if (q6.Any())
                            worksheet.Worksheets[0].Cells[index, 104].PutValue(string.Join(System.Environment.NewLine, q6));

                        worksheet.Worksheets[0].Cells[index, 1].PutValue(item.注册号.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 2].PutValue(item.注册时间.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 3].PutValue(item.注册状态.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 4].PutValue(item.注册题目.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 5].PutValue(item.注册题目EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 6].PutValue(item.正式科学名.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 7].PutValue(item.正式科学名EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 8].PutValue(item.申请注册联系人.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 9].PutValue(item.申请注册联系人EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 10].PutValue(item.研究负责人.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 11].PutValue(item.研究负责人EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 12].PutValue(item.注册联系人电话.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 13].PutValue(item.试验负责人电话.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 14].PutValue(item.申请注册联系人传真.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 15].PutValue(item.研究负责人传真.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 16].PutValue(item.申请注册联系人电子邮件.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 17].PutValue(item.研究负责人电子邮件.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 18].PutValue(item.申请单位网址.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 19].PutValue(item.研究负责人网址.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 20].PutValue(item.申请注册联系人通讯地址.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 21].PutValue(item.申请注册联系人通讯地址EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 22].PutValue(item.研究负责人通讯地址.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 23].PutValue(item.研究负责人通讯地址EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 24].PutValue(item.申请注册联系人邮政编码.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 25].PutValue(item.研究负责人邮政编码.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 26].PutValue(item.申请人所在单位.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 27].PutValue(item.研究负责人所在单位.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 28].PutValue(item.研究负责人所在单位EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 29].PutValue(item.是否获伦理委员会批准.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 30].PutValue(item.伦理委员会批件文号.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 31].PutValue(item.批准本研究的伦理委员会名称.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 32].PutValue(item.批准本研究的伦理委员会名称EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 33].PutValue(item.伦理委员会批准日期.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 34].PutValue(item.伦理委员会联系人.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 35].PutValue(item.伦理委员会联系人EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 36].PutValue(item.伦理委员会联系地址.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 37].PutValue(item.伦理委员会联系地址EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 38].PutValue(item.伦理委员会联系人电话.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 39].PutValue(item.伦理委员会联系人邮箱.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 40].PutValue(item.国家药监局批准文号.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 41].PutValue(item.国家药监局批准附件.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 42].PutValue(item.国家药监局批准日期.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 43].PutValue(item.研究计划书.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 44].PutValue(item.研究实施负责单位.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 45].PutValue(item.研究实施负责单位EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 46].PutValue(item.研究实施负责单位地址.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 47].PutValue(item.研究实施负责单位地址EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 48].PutValue(item.经费或物资来源.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 49].PutValue(item.经费或物资来源EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 50].PutValue(item.研究疾病.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 51].PutValue(item.研究疾病EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 52].PutValue(item.研究疾病代码.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 53].PutValue(item.研究类型.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 54].PutValue(item.研究设计.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 55].PutValue(item.研究所处阶段.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 56].PutValue(item.研究目的.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 57].PutValue(item.研究目的EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 58].PutValue(item.药物成份或治疗方案详述.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 59].PutValue(item.药物成份或治疗方案详述EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 60].PutValue(item.纳入标准.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 61].PutValue(item.纳入标准EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 62].PutValue(item.排除标准.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 63].PutValue(item.排除标准EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 64].PutValue(item.研究实施时间开始.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 65].PutValue(item.研究实施时间结束.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 66].PutValue(item.征募观察对象时间开始.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 67].PutValue(item.征募观察对象时间结束.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 68].PutValue(item.样本总量.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 69].PutValue(item.征募研究对象情况.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 70].PutValue(item.年龄范围最小.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 71].PutValue(item.年龄范围最大.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 72].PutValue(item.随机方法.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 73].PutValue(item.随机方法EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 74].PutValue(item.研究对象是否签署知情同意书.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 75].PutValue(item.随访时间.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 76].PutValue(item.随访时间单位.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 77].PutValue(item.隐蔽分组方法和过程.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 78].PutValue(item.隐蔽分组方法和过程EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 79].PutValue(item.盲法.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 80].PutValue(item.盲法EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 81].PutValue(item.揭盲或破盲原则和方法.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 82].PutValue(item.揭盲或破盲原则和方法EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 83].PutValue(item.统计方法名称.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 84].PutValue(item.统计方法名称EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 85].PutValue(item.试验完成后的统计结果.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 86].PutValue(item.试验完成后的统计结果EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 87].PutValue(item.是否公开试验完成后的统计结果.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 88].PutValue(item.共享原始数据的方式.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 89].PutValue(item.共享原始数据的方式EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 90].PutValue(item.数据采集和管理.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 91].PutValue(item.数据采集和管理EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 92].PutValue(item.研究计划书或研究结果报告发表信息.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 93].PutValue(item.研究计划书或研究结果报告发表信息EN.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 94].PutValue(item.全球唯一识别码.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 95].PutValue(item.数据与安全监察委员会.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 96].PutValue(item.是否共享原始数据.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 97].PutValue(item.二级注册机注册号.CleanInvalidCharsForExcel());
                        worksheet.Worksheets[0].Cells[index, 98].PutValue(item.其它机构的注册号.CleanInvalidCharsForExcel());

                        index++;
                    }
                    worksheet.Save(fs, SaveFormat.Xlsx);
                    return File(fs.ToArray(), "application/octet-stream", $"数据导出{DateTime.Now.ToString("yyyyMMdd")}.xlsx");
                }
            });
            return ret;
        }
        /// <summary>
        /// 项目查询
        /// </summary>
        /// <returns></returns>
        public ActionResult AllProjectsQuery()
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            ViewBag.regfrom = Request["regfrom"];
            ViewBag.regto = Request["regto"];
            ViewBag.numberfrom = Request["numberfrom"];
            ViewBag.numberto = Request["numberto"];
            ViewBag.txtRegNo2 = Request["txtRegNo2"];
            ViewBag.modifyTimefrom = Request["modifyTimefrom"];
            ViewBag.modifyTimeto = Request["modifyTimeto"];
            ViewBag.modifyRefusefrom = Request["modifyRefusefrom"];
            ViewBag.modifyRefuseto = Request["modifyRefuseto"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" isDeleted <> 1 AND taskStatus > 0");

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");

            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");

            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);

            if (!string.IsNullOrEmpty(ViewBag.numberfrom) && !string.IsNullOrEmpty(ViewBag.numberto))
                sql = sql.Where(" CONVERT(varchar, regNumberTime, 23) >= @0 AND  CONVERT(varchar, regNumberTime, 23)  <= @1", ViewBag.numberfrom, ViewBag.numberto);

            if (!string.IsNullOrEmpty(ViewBag.regfrom) && !string.IsNullOrEmpty(ViewBag.regto))
                sql = sql.Where(" CONVERT(varchar, regTime, 23) >= @0 AND CONVERT(varchar, regTime, 23) <= @1", ViewBag.regfrom, ViewBag.regto);

            if (!string.IsNullOrEmpty(ViewBag.modifyTimefrom) && !string.IsNullOrEmpty(ViewBag.modifyTimeto))
                sql = sql.Where(" CONVERT(varchar, modifyTime, 23)  >= @0 AND CONVERT(varchar, modifyTime, 23)  <= @1", ViewBag.modifyTimefrom, ViewBag.modifyTimeto);

            if (!string.IsNullOrEmpty(ViewBag.modifyRefusefrom) && !string.IsNullOrEmpty(ViewBag.modifyRefuseto))
                sql = sql.Where(" CONVERT(varchar, flowRecordBackTime, 23)   >= @0 AND CONVERT(varchar, flowRecordBackTime, 23)  <= @1", ViewBag.modifyRefusefrom, ViewBag.modifyRefuseto);

            if (!string.IsNullOrEmpty(ViewBag.txtRegNo2))
            {
                var ids = ((string)ViewBag.txtRegNo2).Replace("；", ";").Split(',', ';', '\r').Where(x => !string.IsNullOrEmpty(x)).Select(x => x.Trim()).ToList();
                if (ids.Count > 0)
                    sql = sql.Where(" regNumber in ( @0 )", ids);
            }
            sql = sql.OrderBy("regNumberTime desc,regNumber desc");

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(0, -1, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }

        public ActionResult ProjectEdit(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);
            return View(model);
        }
        [HttpPost, ValidateInput(false)]
        public ActionResult ProjectEdit(UserProjectModel model)
        {
            var bll = new Core.BLL.SA_NewProjectBLL();
            var message = new MessageInfoModel();
            var dicts = new Core.BLL.SA_StudyDictionaryBLL().GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);
            if (model.pid.HasValue)
            {
                Core.Models.SA_NewProject proInfo = bll.GetById(model.pid.Value);
                string action = model.btn;
                proInfo.studyTypeID = model.listStudyType;
                proInfo.studyDesignID = model.listStudyDesign;
                proInfo.studyPhaseID = model.listStudyStage;
                proInfo.filloutLanguage = model.listLang;
                proInfo.registrationStatus = model.listRegStatus;
                proInfo.studyID = model.txtSubjectID;
                proInfo.secondaryID = model.txtSecondaryID;
                proInfo.applicantTelephone = model.txtApplierPhone;
                proInfo.studyTelephone = model.txtStudyLeaderPhone;
                proInfo.applicanFax = model.txtApplierFax;
                proInfo.studyFax = model.txtStudyLeaderFax;
                proInfo.applicantEmail = model.txtApplierEmail;
                proInfo.studyEmail = model.txtStudyLeaderEmail;
                proInfo.applicantWebsite = model.txtApplierWebsite;
                proInfo.studyWebsite = model.txtStudyLeaderWebsite;
                proInfo.applicantPostcode = model.txtApplierPostcode;
                proInfo.studyPostcode = model.txtStudyLeaderPostcode;
                proInfo.targetCode = model.txtStudyAilmentCode;
                proInfo.dataSFDA = model.txtNationalFDASanctionDate;
                proInfo.studyTimeStart = model.txtStudyExecuteTime;
                proInfo.studyTimeEnd = model.txtStudyEndTime;
                proInfo.recruitingTimeStart = model.txtEnlistBeginTime;
                proInfo.recruitingTimeEnd = model.txtEnlistEndTime;
                proInfo.totalSampleSize = model.txtTotalSampleSize;
                proInfo.recruitingStatus = model.listRecruitmentStatus;
                proInfo.ageMin = model.txtMinAge;
                proInfo.ageMax = model.txtMaxAge;
                proInfo.sex = model.listGender;
                proInfo.signConsent = model.listAgreeToSign;
                proInfo.followupTime = model.txtFollowUpFrequency;
                proInfo.followup = model.listFollowUpTimeUnit;
                proInfo.whetherPublic = model.listStatisticalEffectChiCTRPublic;
                proInfo.of_SFDA = model.txtNationalFDASanctionNO;
                var text2 = Utils.SaveRequestFile("fileNationalFDASanction", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text2 != string.Empty && !text2.Equals("1"))
                {
                    proInfo.fileSFDA = text2;
                }
                var text3 = Utils.SaveRequestFile("fileStudyPlan", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text3 != string.Empty && !text3.Equals("1"))
                {
                    proInfo.studyPlanfile = text3;
                }
                var text4 = Utils.SaveRequestFile("fileInformedConsent", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text4 != string.Empty && !text4.Equals("1"))
                {
                    proInfo.informedConsentfile = text4;
                }
                var text6 = Utils.SaveRequestFile("fileExperimentalresults", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                if (text6 != string.Empty && !text6.Equals("1"))
                {
                    proInfo.fileExperimentalresults = text6;
                }
                proInfo.publicTitleEN = model.txtTitleEn;
                proInfo.titleAcronymEN = model.txtTitleAcronymEn;
                proInfo.scientifirTitleEN = model.txtOfficialNameEn;
                proInfo.scientifirAcronymEN = model.txtOfficialNameAcronymEn;
                proInfo.applicantEN = model.txtApplierEn;
                proInfo.studyLeaderEN = model.txtStudyLeaderEn;
                proInfo.applicantAddressEN = model.txtApplierAddressEn;
                proInfo.studyAddressEN = model.txtStudyLeaderAddressEn;
                proInfo.applicantInstitutionEN = model.txtApplierCompanyEn;
                proInfo.primarySponsorEN = model.txtSponsorEn;
                proInfo.primarySponsorAddressEN = model.txtSponsorAddressEn;
                proInfo.sourceFundingEN = model.txtSourceOfSpendsEn;
                proInfo.targetDiseaseEN = model.txtStudyAilmentEn;
                proInfo.objectivesStudyEN = model.txtStudyAimEn;
                proInfo.contentsDrugEN = model.txtDrugsCompositionEn;
                proInfo.inclusionCriteriaEN = model.txtSelectionCriteriaEn;
                proInfo.exclusionCrteriaEN = model.txtEliminateCriteriaEn;
                proInfo.randomMethodEN = model.txtGenerafionMethodEn;
                proInfo.processConcealmentEN = model.txtConcealmentEn;
                proInfo.blindingEN = model.txtBlindingEn;
                proInfo.RulesblindingEN = model.txtUncoverPrincipleEn;
                proInfo.statisticalMethodEN = model.txtStatisticalMethodEn;
                proInfo.calculatedResultsEN = model.txtStatisticalEffectEn;
                //proInfo.DataCollectionEN = model.txtDataCollectionUnitEn;
                proInfo.DataCollectionUnit = model.txtDataCollectionUnit;
                proInfo.dataManagementEN = model.txtDataChargeUnitEn;
                proInfo.dataAnalysisEN = model.txtDataAnalysisUnitEn;
                proInfo.SubmitStatus = 1;
                proInfo.approvedCommittee = Utils.StrToInt(model.listEthicalCommitteeSanction, 0);
                if (proInfo.approvedCommittee == 1)
                {
                    proInfo.ethicalCommitteeFileID = model.txtEthicalCommitteeFileID;
                    var text5 = Utils.SaveRequestFile("fileEthicalCommittee", $"/uploads/file/{proInfo.Pid.ToString("N")}/");
                    if (text5 != string.Empty && !text5.Equals("1"))
                    {
                        proInfo.fileEthicalCommittee = text5;
                    }
                    proInfo.ethicalCommitteeName = model.txtEthicalCommitteeName;
                    proInfo.ethicalCommitteeNameEN = model.txtEthicalCommitteeNameEn;
                    proInfo.ethicalCommitteeSanctionDate = model.txtEthicalCommitteeSanctionDate;
                }
                proInfo.publicTitleCN = model.txtTitle;
                proInfo.titleAcronymCN = model.txtTitleAcronym;
                proInfo.scientifirTitleCN = model.txtOfficialName;
                proInfo.scientifirAcronymCN = model.txtOfficialNameAcronym;
                proInfo.applicantCN = model.txtApplier;
                proInfo.studyLeaderCN = model.txtStudyLeader;
                proInfo.applicantAddressCN = model.txtApplierAddress;
                proInfo.studyAddressCN = model.txtStudyLeaderAddress;
                proInfo.applicantInstitutionCN = model.txtApplierCompany;
                proInfo.primarySponsorCN = model.txtSponsor;
                proInfo.primarySponsorAddressCN = model.txtSponsorAddress;
                proInfo.sourceFundingCN = model.txtSourceOfSpends;
                proInfo.targetDiseaseCN = model.txtStudyAilment;
                proInfo.objectivesStudyCN = model.txtStudyAim;
                proInfo.contentsDrugCN = model.txtDrugsComposition;
                proInfo.inclusionCriteriaCN = model.txtSelectionCriteria;
                proInfo.exclusionCrteriaCN = model.txtEliminateCriteria;
                proInfo.randomMethodCN = model.txtGenerafionMethod;
                proInfo.processConcealmentCN = model.txtConcealment;
                proInfo.blindingCN = model.txtBlinding;
                proInfo.RulesblindingCN = model.txtUncoverPrinciple;
                proInfo.statisticalMethodCN = model.txtStatisticalMethod;
                proInfo.calculatedResultsCN = model.txtStatisticalEffect;
                proInfo.dataManagementCN = model.txtDataChargeUnit;
                proInfo.dataAnalysisCN = model.txtDataAnalysisUnit;
                proInfo.UTN = model.txtUTN;
                proInfo.studyLeaderCompanyCN = model.txtStudyLeaderCompany;
                proInfo.studyLeaderCompanyEN = model.txtStudyLeaderCompanyEn;
                proInfo.DataManagemenBoard = model.txtDataManagemenBoard;
                proInfo.studyReport = model.txtStudyReport;
                proInfo.studyReportEN = model.txtStudyReportEN;
                proInfo.ethicalCommitteeCName = model.txtEthicalCommitteeCName;
                proInfo.ethicalCommitteeCNameEN = model.txtEthicalCommitteeCNameEN;
                proInfo.ethicalCommitteeCAddress = model.txtEthicalCommitteeCAddress;
                proInfo.ethicalCommitteeCAddressEN = model.txtEthicalCommitteeCAddressEN;
                proInfo.ethicalCommitteeCPhone = model.txtEthicalCommitteeCPhone;
                proInfo.ethicalCommitteeCEmail = model.txtEthicalCommitteeCEmail;
                // proInfo.modifyTime = DateTime.Now;
                var ret = bll.Update(proInfo);
                if (ret)
                {
                    UpdateDiagnostic(proInfo.Pid);
                    SetMessageAlert(false, "系统提示 System prompt",
                          "项目已成功保存，您可以多次保存! <br/>The project has been saved successfully, and you can save it many times.",
                          Url.Action("AllProjects"),
                          "确定");
                }
                else
                {
                    SetMessageAlert(false, "系统提示 System prompt",
                                     "项目已成功提交! <br/>Project submitted successfully.",
                                     Url.Action("AllProjects"),
                                     "确定");
                }
            }
            else
            {
                SetMessageAlert(true, "系统提示 System prompt",
                                        "添加失败，请完整填写内容后再提交! <br/> Failed to add!  Please complete the content before submitting.",
                                        Url.Action("AllProjects"),
                                        "确定");
            }

            return View(model);
        }
        private bool UpdateDiagnostic(Guid newpid)
        {
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            var bll = new Core.BLL.SA_DiagnosticBLL();
            string formString = form["txtStandard"];
            string formString2 = form["txtIndexTest"];
            string formString3 = form["txtTargetCondition"];
            string formString4 = form["txtSampleSizeT"];
            string formString5 = form["txtDifficultCondition"];
            string formString6 = form["txtSampleSizeD"];
            string formString7 = form["txtStandardEn"];
            string formString8 = form["txtIndexTestEn"];
            string formString9 = form["txtTargetConditionEn"];
            string formString10 = form["txtDifficultConditionEn"];
            if (formString == string.Empty && formString2 == string.Empty && formString3 == string.Empty && formString4 == string.Empty && formString5 == string.Empty && formString6 == string.Empty)
            {
                return false;
            }
            var diagnostic = bll.GetByProjectId(newpid).FirstOrDefault();
            if (diagnostic == null)
            {
                diagnostic = new SA_Diagnostic();
                diagnostic.id = Guid.NewGuid();
                diagnostic.Pid = newpid;
                diagnostic.standard = formString;
                diagnostic.indexTest = formString2;
                diagnostic.targetCondition = formString3;
                diagnostic.difficultCondition = formString5;
                diagnostic.standardEn = formString7;
                diagnostic.indexTestEn = formString8;
                diagnostic.targetConditionEn = formString9;
                diagnostic.difficultConditionEn = formString10;
                if (formString4 != string.Empty)
                {
                    diagnostic.sampleSizeT = Utils.StrToInt(formString4, 0);
                }
                if (formString6 != string.Empty)
                {
                    diagnostic.sampleSizeD = Utils.StrToInt(formString6, 0);
                }
                return bll.Insert(diagnostic);
            }
            else
            {
                diagnostic.Pid = newpid;
                diagnostic.standard = formString;
                diagnostic.indexTest = formString2;
                diagnostic.targetCondition = formString3;
                diagnostic.difficultCondition = formString5;
                diagnostic.standardEn = formString7;
                diagnostic.indexTestEn = formString8;
                diagnostic.targetConditionEn = formString9;
                diagnostic.difficultConditionEn = formString10;
                if (formString4 != string.Empty)
                {
                    diagnostic.sampleSizeT = Utils.StrToInt(formString4, 0);
                }
                if (formString6 != string.Empty)
                {
                    diagnostic.sampleSizeD = Utils.StrToInt(formString6, 0);
                }
                return bll.Update(diagnostic);
            }
        }
        public JsonResult EditProjectInfo()
        {
            string text = Utils.HtmlDecode(Request["type"]);
            string caozuo = Utils.HtmlDecode(Request["caozuo"]);
            switch (text)
            {
                case "collinfo":
                    return CollectingSample(caozuo);
                case "outinfo":
                    return Outcomes(caozuo);
                case "ininfo":
                    return Interventions(caozuo);
                case "rainfo":
                    return ResearchAddress(caozuo);
                case "ssinfo":
                    return SecondarySponsor(caozuo);
            }
            return Json(true, JsonRequestBehavior.AllowGet);
        }
        private JsonResult CollectingSample(string caozuo)
        {
            var bll = new Core.BLL.SA_CollectingSampleBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            switch (caozuo)
            {
                case "up":
                    {
                        var csid = Utils.StrToGuid(form["csid"]);
                        string string3 = form["SampleNameCN"];
                        string string4 = form["SampleNameEN"];
                        string string5 = form["TissueCN"];
                        string string6 = form["TissueEN"];
                        string string7 = form["NoteCN"];
                        string string8 = form["NoteEN"];
                        string string9 = form["FateSample"];
                        var collectingSampleInfo2 = bll.GetById(csid.Value);
                        collectingSampleInfo2.sampleNameCN = string3;
                        collectingSampleInfo2.sampleNameEN = string4;
                        collectingSampleInfo2.tissueCN = string5;
                        collectingSampleInfo2.tissueEN = string6;
                        collectingSampleInfo2.noteCN = string7;
                        collectingSampleInfo2.noteEN = string8;
                        collectingSampleInfo2.fateSample = string9;
                        var ret = bll.Update(collectingSampleInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string sampleNameCN = form["SampleNameCN"];
                        string sampleNameEN = form["SampleNameEN"];
                        string tissueCN = form["TissueCN"];
                        string tissueEN = form["TissueEN"];
                        string noteCN = form["NoteCN"];
                        string noteEN = form["NoteEN"];
                        string fateSample = form["FateSample"];
                        var collectingSampleInfo = new Core.Models.SA_CollectingSample();
                        collectingSampleInfo.csId = Guid.NewGuid();
                        collectingSampleInfo.Pid = pid.Value;
                        collectingSampleInfo.sampleNameCN = sampleNameCN;
                        collectingSampleInfo.sampleNameEN = sampleNameEN;
                        collectingSampleInfo.tissueCN = tissueCN;
                        collectingSampleInfo.tissueEN = tissueEN;
                        collectingSampleInfo.noteCN = noteCN;
                        collectingSampleInfo.noteEN = noteEN;
                        collectingSampleInfo.fateSample = fateSample;
                        var ret = bll.Insert(collectingSampleInfo);
                        return Json(new { succ = ret, message = "", data = collectingSampleInfo.csId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var csid = Utils.StrToGuid(Request["csid"]);
                        var ret = bll.Delete(csid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        private JsonResult Outcomes(string caozuo)
        {
            var bll = new Core.BLL.SA_OutcomesBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            switch (caozuo)
            {
                case "up":
                    {
                        var ouid = Utils.StrToGuid(form["ouid"]);
                        string string10 = form["name"];
                        string string11 = form["nameen"];
                        string string12 = form["pointtime"];
                        string string13 = form["measuremethod"];
                        string string14 = form["pointtimeen"];
                        string string15 = form["measuremethoden"];
                        var outcomesInfo2 = bll.GetById(ouid.Value);
                        outcomesInfo2.outcomeNameCN = string10;
                        outcomesInfo2.outcomeNameEN = string11;
                        outcomesInfo2.measureTimeCN = string12;
                        outcomesInfo2.measureTimeEN = string14;
                        outcomesInfo2.measureMethodCN = string13;
                        outcomesInfo2.measureMethodEN = string15;
                        var ret = bll.Update(outcomesInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string string2 = form["name"];
                        string string3 = form["nameen"];
                        string string4 = form["outype"];
                        string string5 = form["pointtime"];
                        string string6 = form["measuremethod"];
                        string string7 = form["pointtimeen"];
                        string string8 = form["measuremethoden"];
                        var outcomesInfo = new Core.Models.SA_Outcomes();
                        outcomesInfo.ouId = Guid.NewGuid();
                        outcomesInfo.Pid = pid.Value;
                        outcomesInfo.outcomeNameCN = string2;
                        outcomesInfo.outcomeNameEN = string3;
                        outcomesInfo.pointerType = string4;
                        outcomesInfo.measureTimeCN = string5;
                        outcomesInfo.measureTimeEN = string7;
                        outcomesInfo.measureMethodCN = string6;
                        outcomesInfo.measureMethodEN = string8;
                        var ret = bll.Insert(outcomesInfo);
                        return Json(new { succ = ret, message = "", data = outcomesInfo.ouId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var ouid = Utils.StrToGuid(Request["ouid"]);
                        var ret = bll.Delete(ouid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统繁忙！！" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        private JsonResult Interventions(string caozuo)
        {
            var bll = new Core.BLL.SA_InterventionsBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            switch (caozuo)
            {
                case "up":
                    {
                        var inid = Utils.StrToGuid(form["inid"]);
                        string string9 = form["group"];
                        string string10 = form["samplesize"];
                        string string11 = form["groupen"];
                        string string12 = form["measure"];
                        string string13 = form["intercode"];
                        string string14 = form["measureen"];
                        var interventionsInfo2 = bll.GetById(inid.Value);
                        interventionsInfo2.groupsCN = string9;
                        interventionsInfo2.sampleSize = Utils.StrToInt(string10, 0);
                        interventionsInfo2.groupsEN = string11;
                        interventionsInfo2.interventionCN = string12;
                        interventionsInfo2.interventionCode = string13;
                        interventionsInfo2.interventionEN = string14;
                        var ret = bll.Update(interventionsInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string string2 = form["group"];
                        string string3 = form["samplesize"];
                        string string4 = form["groupen"];
                        string string5 = form["measure"];
                        string string6 = form["intercode"];
                        string string7 = form["measureen"];
                        var interventionsInfo = new Core.Models.SA_Interventions();
                        interventionsInfo.inId = Guid.NewGuid();
                        interventionsInfo.Pid = pid.Value;
                        interventionsInfo.groupsCN = string2;
                        interventionsInfo.sampleSize = Utils.StrToInt(string3, 0);
                        interventionsInfo.groupsEN = string4;
                        interventionsInfo.interventionCN = string5;
                        interventionsInfo.interventionCode = string6;
                        interventionsInfo.interventionEN = string7;
                        var ret = bll.Insert(interventionsInfo);
                        return Json(new { succ = ret, message = "", data = interventionsInfo.inId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var inid = Utils.StrToGuid(Request["inid"]);
                        var ret = bll.Delete(inid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统繁忙！！" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        private JsonResult ResearchAddress(string caozuo)
        {
            var bll = new Core.BLL.SA_ResearchAddressBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            switch (caozuo)
            {
                case "up":
                    {
                        var raid = Utils.StrToGuid(form["raid"]);
                        string string13 = form["country0"];
                        string string14 = form["province0"];
                        string string15 = form["city0"];
                        string string16 = form["countryen0"];
                        string string17 = form["provinceen0"];
                        string string18 = form["cityen0"];
                        string string19 = form["institution0"];
                        string string20 = form["institutionLevel0"];
                        string string21 = form["institutionen0"];
                        string string22 = form["institutionLevelen0"];
                        var researchAddressInfo2 = bll.GetById(raid.Value);
                        researchAddressInfo2.countryCN = string13;
                        researchAddressInfo2.provinceCN = string14;
                        researchAddressInfo2.cityCN = string15;
                        researchAddressInfo2.countryEN = string16;
                        researchAddressInfo2.provinceEN = string17;
                        researchAddressInfo2.cityEN = string18;
                        researchAddressInfo2.hospitalCN = string19;
                        researchAddressInfo2.levelInstitutionCN = string20;
                        researchAddressInfo2.hospitalEN = string21;
                        researchAddressInfo2.levelInstitutionEN = string22;
                        var ret = bll.Update(researchAddressInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string string2 = form["country"];
                        string string3 = form["province"];
                        string string4 = form["city"];
                        string string5 = form["countryen"];
                        string string6 = form["provinceen"];
                        string string7 = form["cityen"];
                        string string8 = form["institution"];
                        string string9 = form["institutionLevel"];
                        string string10 = form["institutionen"];
                        string string11 = form["institutionLevelen"];
                        var researchAddressInfo = new Core.Models.SA_ResearchAddress();
                        researchAddressInfo.raId = Guid.NewGuid();
                        researchAddressInfo.Pid = pid.Value;
                        researchAddressInfo.countryCN = string2;
                        researchAddressInfo.provinceCN = string3;
                        researchAddressInfo.cityCN = string4;
                        researchAddressInfo.countryEN = string5;
                        researchAddressInfo.provinceEN = string6;
                        researchAddressInfo.cityEN = string7;
                        researchAddressInfo.hospitalCN = string8;
                        researchAddressInfo.levelInstitutionCN = string9;
                        researchAddressInfo.hospitalEN = string10;
                        researchAddressInfo.levelInstitutionEN = string11;
                        var ret = bll.Insert(researchAddressInfo);
                        return Json(new { succ = ret, message = "", data = researchAddressInfo.raId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var raid = Utils.StrToGuid(Request["raid"]);
                        var ret = bll.Delete(raid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统繁忙！！" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        private JsonResult SecondarySponsor(string caozuo)
        {
            var bll = new Core.BLL.SA_SecondarySponsorBLL();
            FormCollection form = new FormCollection(Request.Unvalidated.Form);
            switch (caozuo)
            {
                case "up":
                    {
                        var ssid = Utils.StrToGuid(form["ssid"]);
                        string string13 = form["country"];
                        string string14 = form["province"];
                        string string15 = form["city"];
                        string string16 = form["countryen"];
                        string string17 = form["provinceen"];
                        string string18 = form["cityen"];
                        string string19 = form["institution"];
                        string string20 = form["address"];
                        string string21 = form["institutionen"];
                        string string22 = form["addressen"];
                        var secondarySponsorInfo2 = bll.GetById(ssid.Value);

                        secondarySponsorInfo2.countryCN = string13;
                        secondarySponsorInfo2.provinceCN = string14;
                        secondarySponsorInfo2.cityCN = string15;
                        secondarySponsorInfo2.countryEN = string16;
                        secondarySponsorInfo2.provinceEN = string17;
                        secondarySponsorInfo2.cityEN = string18;
                        secondarySponsorInfo2.institutionCN = string19;
                        secondarySponsorInfo2.specificAddressCN = string20;
                        secondarySponsorInfo2.institutionEN = string21;
                        secondarySponsorInfo2.specificAddressEN = string22;

                        var ret = bll.Update(secondarySponsorInfo2);
                        return Json(new { succ = ret }, JsonRequestBehavior.AllowGet);
                    }
                case "add":
                    {
                        var pid = Utils.StrToGuid(form["pid"]);
                        string string2 = form["country"];
                        string string3 = form["province"];
                        string string4 = form["city"];
                        string string5 = form["countryen"];
                        string string6 = form["provinceen"];
                        string string7 = form["cityen"];
                        string string8 = form["institution"];
                        string string9 = form["address"];
                        string string10 = form["institutionen"];
                        string string11 = form["addressen"];
                        var secondarySponsorInfo = new Core.Models.SA_SecondarySponsor();
                        secondarySponsorInfo.ssId = Guid.NewGuid();
                        secondarySponsorInfo.Pid = pid.Value;
                        secondarySponsorInfo.countryCN = string2;
                        secondarySponsorInfo.provinceCN = string3;
                        secondarySponsorInfo.cityCN = string4;
                        secondarySponsorInfo.countryEN = string5;
                        secondarySponsorInfo.provinceEN = string6;
                        secondarySponsorInfo.cityEN = string7;
                        secondarySponsorInfo.institutionCN = string8;
                        secondarySponsorInfo.specificAddressCN = string9;
                        secondarySponsorInfo.institutionEN = string10;
                        secondarySponsorInfo.specificAddressEN = string11;
                        var ret = bll.Insert(secondarySponsorInfo);
                        return Json(new { succ = ret, message = "", data = secondarySponsorInfo.ssId }, JsonRequestBehavior.AllowGet);
                    }
                case "del":
                    {
                        var ssid = Utils.StrToGuid(Request["ssid"]);
                        var ret = bll.Delete(ssid.Value);
                        if (ret)
                            return Json(new { succ = true, message = "删除成功" }, JsonRequestBehavior.AllowGet);
                        else
                            return Json(new { succ = false, message = "系统繁忙！！" }, JsonRequestBehavior.AllowGet);
                    }
                default:
                    return Json(new { succ = false, message = "系统异常" }, JsonRequestBehavior.AllowGet);
            }
        }
        public ActionResult ProjectView(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                    model.regNumber = proInfo.regNumber;
                    model.TaskStatus = proInfo.taskStatus;
                    var record = new Core.BLL.SA_ProjectRequestEditFlowBLL().GetByLastModelByPid(proInfo.Pid, 6);
                    if (record != null && !string.IsNullOrWhiteSpace(record.EditRequestReason))
                    {
                        model.EditRequestReason = record.EditRequestReason;
                        model.editRequestAttachment = record.EditRequestAttachment;
                    }
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);

            return View(model);
        }
        #endregion
        #region
        public ActionResult CreateRegNum()
        {
            return View();
        }
        #endregion
        #region 下载文件
        public ActionResult DownFile(Guid pid, string path)
        {
            var file = new FileInfo(path);
            string filePath = Server.MapPath(path);
            if (System.IO.File.Exists(filePath))
            {
                return File(new FileStream(filePath, FileMode.Open), "text/plain", file.Name);
            }
            else
            {
                return Content("File not found");
            }
        }
        #endregion
        #region 待分配
        public ActionResult ProToBeAllot(int pageNo = 0)
        {
            //ViewBag.txtRoleName = Request["txtRoleName"];
            //ViewBag.txtRoleDesc = Request["txtRoleDesc"];
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            sql = sql.Where(" taskStatus = 1 AND sendTaskSysUserId =@0 AND isTraditionalMedicine <> 0 ", SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");//1=待分配
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 待审核
        public ActionResult ProFirReview(int pageNo = 0, string SortName = "flowRecordTime", string OrderByDescending = "desc")
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtRegNo = Request["txtRegNo"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
            sql = sql.Where("taskStatus =2 and executeTaskSysUserId=@0 ", Models.SysUserProfile.CurrentProfile.UserID).OrderBy($"{SortName} {OrderByDescending}");//2已分配，且执行人为当前用户,
            if (OrderByDescending == "desc")
            {
                ViewData["sort"] = "asc";
            }
            else
            {
                ViewData["sort"] = "desc";
            }
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 待审核-退回
        public ActionResult ProFirReviewBack(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtApplier = Request["txtApplier"];
            ViewBag.txtStudyLeader = Request["txtStudyLeader"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            sql = sql.Where("taskStatus =@1 and executeTaskSysUserId=@0 ", Models.SysUserProfile.CurrentProfile.UserID, (int)ProjectTaskStatus.复核未通过);//2已分配，且执行人为当前用户,

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtApplier))
                sql = sql.Where(" (applicantCN like @0 or applicantEN like @0)", $"%{ViewBag.txtApplier}%");
            if (!string.IsNullOrEmpty(ViewBag.txtStudyLeader))
                sql = sql.Where(" (studyLeaderCN like @0 or studyLeaderEN like @0)", $"%{ViewBag.txtStudyLeader}%");

            sql = sql.OrderBy("flowRecordTime desc");

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 待复审
        public ActionResult ProSecReview(int pageNo = 0)
        {
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            sql = sql.Where(" taskStatus = 3 AND sendTaskSysUserId = @0 ", Models.SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");//3待复核，且为当前用户，分配任务的人

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 待复审-退回
        public ActionResult ProSecReviewBack(int pageNo = 0)
        {

            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtApplier = Request["txtApplier"];
            ViewBag.txtStudyLeader = Request["txtStudyLeader"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            sql = sql.Where("taskStatus =@1 and sendTaskSysUserId=@0 ", Models.SysUserProfile.CurrentProfile.UserID, (int)ProjectTaskStatus.复核未通过);

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtApplier))
                sql = sql.Where(" (applicantCN like @0 or applicantEN like @0)", $"%{ViewBag.txtApplier}%");
            if (!string.IsNullOrEmpty(ViewBag.txtStudyLeader))
                sql = sql.Where(" (studyLeaderCN like @0 or studyLeaderEN like @0)", $"%{ViewBag.txtStudyLeader}%");

            sql = sql.OrderBy("flowRecordTime desc");
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 已审核
        public ActionResult ProReviewed(int pageNo = 0)
        {
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            if (SysUserProfile.CurrentProfile.UserModel.SysRoleId == Guid.Parse("f2c6f6a5-069a-4f8e-8d11-6d336045529b"))
            {
                sql = sql.Where("(taskStatus = 8 OR taskStatus = 5) AND regNumber IS NOT NULL", Models.SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");//5审核通过，执行人或分配人当前用户
            }
            else
            {
                sql = sql.Where("(taskStatus = 8 AND (sendTaskSysUserId=@0 or executeTaskSysUserId=@0) AND regNumber IS NOT NULL) OR ( taskStatus = 5 and (sendTaskSysUserId=@0 or executeTaskSysUserId=@0) AND regNumber IS NOT NULL)", Models.SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");//5审核通过，执行人或分配人当前用户
            }
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 再修改申请列表
        public ActionResult ProToAgain(int pageNo = 0)
        {
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            sql = sql.Where(" taskStatus = 6").OrderBy("flowRecordTime desc");
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 已分配项目
        public ActionResult ProAllot(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtRegNo = Request["txtRegNo"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
            sql = sql.Where(" taskStatus = 2 AND sendTaskSysUserId = @0 ", Models.SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);

            var resPage = new Page<Models.NewProjectListModel>();
            resPage.Items = page_ret.Items.ConvertAll(x => new Models.NewProjectListModel
            {
                Pid = x.Pid,
                regNumber = string.IsNullOrWhiteSpace(x.regNumber) ? "" : x.regNumber,
                ReleaseNumber = string.IsNullOrWhiteSpace(x.ReleaseNumber) ? "" : x.ReleaseNumber,
                publicTitleCN = x.publicTitleCN,
                publicTitleEN = x.publicTitleEN,
                regTime = x.regTime.HasValue ? x.regTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : "",
                ProjectOriginCn = x.ProjectOriginCn,
                executeTaskSysUserName = new Core.BLL.SA_SysUserBLL().GetUserName(x.executeTaskSysUserId.Value),
                IsEnglist = x.filloutLanguage == "1009002",
                IsChinese = x.filloutLanguage == "1009001",
            }).ToList();
            resPage.ItemsPerPage = page_ret.ItemsPerPage;
            resPage.TotalItems = page_ret.TotalItems;
            resPage.TotalPages = page_ret.TotalPages;
            resPage.CurrentPage = page_ret.CurrentPage;
            //executeTaskSysUserId
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        #endregion
        #region 再修改退回列表
        public ActionResult ProToAgainBack(int pageNo = 0)
        {
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            if (SysUserProfile.CurrentProfile.UserModel.SysRoleId == Guid.Parse("f2c6f6a5-069a-4f8e-8d11-6d336045529b"))
            {
                sql = sql.Where(" taskStatus = 8 ").OrderBy("flowRecordTime desc");
            }
            else
            {
                sql = sql.Where(" taskStatus = 8  AND sendTaskSysUserId = @0 ", Models.SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");
            }
            
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 二审待审核
        public ActionResult ProSecRecheck(int pageNo = 0)
        {
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            sql = sql.Where("taskStatus = 3 and (executeTaskSysUserId=@0 or sendTaskSysUserId = @0)", Models.SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");//2已分配，且执行人为当前用户,
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 单个分配任务
        public ActionResult SingleDistribution(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);

            var rid = Guid.Parse("3d64080d-042f-45da-84be-570b360236a6");
            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.ParentId == SysUserProfile.CurrentProfile.UserID.ToString()).ToList();

            var list = new List<Common.dataObjectList>();
            var temp = userList.ConvertAll(x => new Common.dataObjectList
            {
                id = x.Uid.ToString(),
                name = $"{x.Username}[{x.Name}]({bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count})",
                Count = bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count
            });
            list.Add(new Common.dataObjectList { id = "", name = "请选择" });
            list.AddRange(temp.OrderBy(x => x.Count).ToList());
            ViewBag.UserList = new SelectList(list, "id", "name");


            ViewBag.IsBack = new SelectList(CommonList.getFalseOrTrue(), "Key", IsLang("en-US") ? "EN" : "CN");

            return View(model);
        }

        [HttpPost]
        public JsonResult SingleDistribution(Guid pid, Guid UserId)
        {
            var res = new Core.BLL.SA_NewProjectBLL().DistributionProject(pid.ToString(), UserId, Models.SysUserProfile.CurrentProfile.UserID);

            if (res)
            {
                return WriteJsonResponse(true, "分配成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "分配失败", false);
            }
        }

        [HttpPost]
        public JsonResult SingleSendBack(Guid pid)
        {
            var res = new Core.BLL.SA_NewProjectBLL().SingleSendBack(pid);

            if (res)
            {
                return WriteJsonResponse(true, "退回成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "退回失败", false);
            }
        }
        #endregion
        #region 首页
        public ActionResult Index()
        {
            var user = new Core.BLL.SA_SysUserBLL().GetById(ITMCTR.App.Models.SysUserProfile.CurrentProfile.UserID);
            ViewBag.RoleId = user.SysRoleId.Value.ToString();
            var proBll = new Core.BLL.SA_NewProjectBLL();

            if (ViewBag.RoleId == "49bb2491-2ba5-492d-9f54-6317bcc05baa")//一级审核员
            {

                ViewBag.First_proDistribute = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where  taskStatus = 1 AND sendTaskSysUserId =@0 AND isTraditionalMedicine <> 0", user.Uid);//待分配
                ViewBag.First_proFaild = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where  taskStatus =@0 AND sendTaskSysUserId=@1", Core.ProjectTaskStatus.待复核, user.Uid);//待复审
                ViewBag.First_proReRequest = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where taskStatus = 6");//待复审

                //未包含【用户提交再修改申请，一审未审核再修改申请】
                ViewBag.First_proSuccess = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where (taskStatus = 8 AND (sendTaskSysUserId=@0 or executeTaskSysUserId=@0) AND regNumber IS NOT NULL) OR ( taskStatus = 5 and (sendTaskSysUserId=@0 or executeTaskSysUserId=@0) AND regNumber IS NOT NULL)",  user.Uid);//已审核
                ViewBag.First_proSum = ViewBag.First_proDistribute + ViewBag.First_proSuccess;//项目总数
            }
            if (ViewBag.RoleId == "3d64080d-042f-45da-84be-570b360236a6")//二级审核员
            {
                //待审核
                ViewBag.Sec_proToBeReview = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where  taskStatus = 2 AND executeTaskSysUserId=@0 ", user.Uid);//待审核（查自己待审核项目数
                //已通过
                ViewBag.Sec_proSuccess = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where (taskStatus = 8 AND (sendTaskSysUserId=@0 or executeTaskSysUserId=@0) AND regNumber IS NOT NULL) OR ( taskStatus = 5 and (sendTaskSysUserId=@0 or executeTaskSysUserId=@0) AND regNumber IS NOT NULL) ", user.Uid);//已通过审核（查自己通过复核的项目数量）
                ViewBag.Sec_proSum = ViewBag.Sec_proToBeReview + ViewBag.Sec_proSuccess;//审核项目总数（自己参与审核的项目总数）
            }

            if (ViewBag.RoleId == "f707cae7-17c3-49e3-b70c-a1d9fb6e5afe")//管理员
            {

                ViewBag.Admin_proToBeReview = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where  status = @0", Core.ProjectStatus.待审核);//待审核（全站）
                ViewBag.Admin_proFaild = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where  status =@0", Core.ProjectStatus.未通过审核);//未通过审核（全站）
                ViewBag.Admin_proSuccess = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where  status = @0 AND  isDeleted  <> 1", Core.ProjectStatus.通过审核);//已通过审核（全站）
                ViewBag.Admin_proSum = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where isDeleted  <> 1");//项目总数（全站已提交的项目数）

                //1004001：尚未开始；
                //1004002：正在进行；
                //1004003：暂停或中断；
                //1004004：结束
                //征募-未开始
                int incNoStart = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where recruitingStatus=@0", "1004001");
                //征募-进行中
                int incInProcess = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where recruitingStatus=@0", "1004002");
                //征募-暂停
                int incStop = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where recruitingStatus=@0", "1004003");

                //征募-完成
                int incFinish = proBll.db.ExecuteScalar<int>("select count(1) from SA_NewProject where recruitingStatus=@0", "1004004");


                ViewBag.incNoStart = incNoStart;
                ViewBag.incInProcess = incInProcess;
                ViewBag.incStop = incStop;
                ViewBag.incFinish = incFinish;
            }






            var ReadNotices = new Core.BLL.SA_NoticeBLL().GetListByReceiveUserId(ITMCTR.App.Models.SysUserProfile.CurrentProfile.UserID);
            ViewBag.NoReadList = ReadNotices.Where(x => x.IsRead == 0).ToList();
            ViewBag.IsReadList = ReadNotices.Where(x => x.IsRead == 1).ToList();

            var news = new Core.BLL.SA_NewsBLL().GetAll().Where(x => x.IsDeleted.HasValue && x.IsDeleted == 0
            && x.IsRelease.HasValue && x.IsRelease.Value == 1
            && x.IsViewIndex.HasValue && x.IsViewIndex.Value == 1).OrderByDescending(x => x.CreateTime).ToList();

            ViewBag.News = news;

            return View();
        }
        #endregion
        #region 站内信
        public ActionResult Notices(Guid Nid)
        {
            var notice = new Core.BLL.SA_NoticeBLL().GetById(Nid);
            if (notice.IsRead.Value != 1)
            {
                notice.IsRead = 1;
                new Core.BLL.SA_NoticeBLL().Update(notice);
            }
            return View(notice);
        }
        #endregion
        #region 新闻
        public ActionResult NewsList(int pageNo = 0)
        {
            Core.BLL.SA_NewsBLL bll = new Core.BLL.SA_NewsBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_News").Where("IsDeleted=0");
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" Title like @0 ", $"%{ViewBag.txtTitle}%");
            var page_ret = bll.GetPageList<Core.Models.SA_News>(pageNo, PageSize, sql);
            var resPage = new Page<NewsViewModel>();
            var bllNewType = new Core.BLL.SA_NewsTypeBLL();
            resPage.Items = page_ret.Items.ConvertAll(x => new Models.NewsViewModel
            {
                Nid = x.Nid,
                Title = x.Title,
                Subtitle = x.Subtitle,
                Author = x.Author,
                AuthorUnit = x.AuthorUnit,
                Content = x.Content,
                IsDeleted = x.IsDeleted,
                IsRelease = x.IsRelease,
                IsViewIndex = x.IsViewIndex,
                ReleaseTime = x.ReleaseTime,
                TypeName = bllNewType.GetById(x.NtId)?.Name
            }).ToList();
            resPage.ItemsPerPage = page_ret.ItemsPerPage;
            resPage.TotalItems = page_ret.TotalItems;
            resPage.TotalPages = page_ret.TotalPages;
            resPage.CurrentPage = page_ret.CurrentPage;
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        public ActionResult NewsTypeList(int pageNo = 0)
        {
            Core.BLL.SA_NewsTypeBLL bll = new Core.BLL.SA_NewsTypeBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_NewsType");
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" Name like @0 ", $"%{ViewBag.txtTitle}%");
            var page_ret = bll.GetPageList<Core.Models.SA_NewsType>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        public ActionResult NewsAdd()
        {
            Core.BLL.SA_NewsTypeBLL bll = new Core.BLL.SA_NewsTypeBLL();
            ViewBag.listNtId = new SelectList(bll.GetAll(), "NtId", "Name");
            return View();
        }
        [HttpPost, ValidateInput(false)]
        public ActionResult NewsAdd(NewsEditModel model)
        {
            ViewBag.listNtId = new SelectList(new Core.BLL.SA_NewsTypeBLL().GetAll(), "NtId", "Name");
            Core.BLL.SA_NewsBLL bll = new Core.BLL.SA_NewsBLL();
            model.Nid = Guid.NewGuid();
            model.Content = model.Content;
            model.CreateSysUserId = UserProfile.CurrentProfile.UserID;
            model.IsDeleted = 0;
            var ret = bll.Insert(model);
            if (ret)
            {
                return RedirectToAction("NewsList");
            }
            else
            {
                ModelState.AddModelError("", "新建失败");
                return View(model);
            }
        }
        public ActionResult NewsEdit(Guid? Nid)
        {
            Core.BLL.SA_NewsBLL bll = new Core.BLL.SA_NewsBLL();
            ViewBag.listNtId = new SelectList(new Core.BLL.SA_NewsTypeBLL().GetAll(), "NtId", "Name");
            if (Nid.HasValue)
            {
                var model = bll.GetById(Nid.Value);
                if (model != null)
                {
                    var news = new Models.NewsEditModel();
                    news = Mapper.Map<Models.NewsEditModel>(model);
                    return View(news);
                }
            }
            return View();
        }
        [HttpPost, ValidateInput(false)]
        public ActionResult NewsEdit(NewsEditModel model)
        {
            ViewBag.listNtId = new SelectList(new Core.BLL.SA_NewsTypeBLL().GetAll(), "NtId", "Name");
            Core.BLL.SA_NewsBLL bll = new Core.BLL.SA_NewsBLL();
            Core.Models.SA_News news = bll.GetById(model.Nid);

            //news.Nid = model.Nid;
            news.Author = model.Author;
            news.AuthorUnit = model.AuthorUnit;
            news.Content = model.Content;
            //news.CreateSysUserId = CurrentProfile.UserID;
            //news.CreateTime = DateTime.Now;
            //news.IsDeleted = 0;
            news.IsRelease = model.IsRelease;
            news.IsViewIndex = model.IsViewIndex;
            news.NtId = model.NtId;
            news.ReleaseTime = model.ReleaseTime;
            news.Source = model.Source;
            news.Subtitle = model.Subtitle;
            news.Title = model.Title;
            news.AuthorEN = model.AuthorEN;
            news.AuthorUnitEN = model.AuthorUnitEN;
            news.ContentEN = model.ContentEN;
            news.SourceEN = model.SourceEN;
            news.SubtitleEN = model.SubtitleEN;
            news.TitleEN = model.TitleEN;

            var ret = bll.Update(news);
            if (ret)
            {
                return RedirectToAction("NewsList");
            }
            else
            {
                ModelState.AddModelError("", "更新失败");
                return View(model);
            }
        }
        public JsonResult NewsDelete(Guid? Nid)
        {
            Core.BLL.SA_NewsBLL bll = new Core.BLL.SA_NewsBLL();
            if (Nid.HasValue)
            {
                Core.Models.SA_News news = bll.GetById(Nid.Value);
                news.IsDeleted = 1;
                var ret = bll.Update(news);
                return Json(new { success = ret }, JsonRequestBehavior.AllowGet);
            }
            else
                return Json(new { success = false }, JsonRequestBehavior.AllowGet);
        }
        public ActionResult NewsTypeAdd()
        {
            return View();
        }
        [HttpPost, ValidateInput(false)]
        public ActionResult NewsTypeAdd(NewsTypeEditModel model)
        {
            Core.BLL.SA_NewsTypeBLL bll = new Core.BLL.SA_NewsTypeBLL();
            model.NtId = Guid.NewGuid();
            model.CreateTime = DateTime.Now;
            model.CreateSysUserId = SysUserProfile.CurrentProfile.UserID;
            var ret = bll.Insert(model);
            if (ret)
            {
                return RedirectToAction("NewsTypeList");
            }
            else
            {
                ModelState.AddModelError("", "更新失败");
                return View(model);
            }
        }
        public ActionResult NewsTypeEdit(Guid? NtId)
        {
            Core.BLL.SA_NewsTypeBLL bll = new Core.BLL.SA_NewsTypeBLL();
            if (NtId.HasValue)
            {
                var model = bll.GetById(NtId.Value);
                if (model != null)
                {
                    Models.NewsTypeEditModel view = new NewsTypeEditModel();
                    view.CreateSysUserId = model.CreateSysUserId;
                    view.CreateTime = model.CreateTime;
                    view.Desc = model.Desc;
                    view.IsIndexView = model.IsIndexView;
                    view.IsView = model.IsView;
                    view.Name = model.Name;
                    view.NtId = model.NtId;
                    return View(view);
                }
            }
            return View();
        }
        [HttpPost, ValidateInput(false)]
        public ActionResult NewsTypeEdit(NewsTypeEditModel model)
        {
            Core.BLL.SA_NewsTypeBLL bll = new Core.BLL.SA_NewsTypeBLL();
            var model2 = bll.GetById(model.NtId);
            if (model2 == null)
            {
                return RedirectToAction("NewsList");
            }
            //model2.CreateSysUserId = model.CreateSysUserId;
            //model2.CreateTime = model.CreateTime;
            model2.Desc = model.Desc;
            model2.IsIndexView = model.IsIndexView;
            model2.IsView = model.IsView;
            model2.Name = model.Name;
            model2.NtId = model.NtId;
            var ret = bll.Update(model2);
            if (ret)
            {
                return RedirectToAction("NewsTypeList");
            }
            else
            {
                ModelState.AddModelError("", "更新失败");
                return View(model);
            }

        }
        public JsonResult NewsTypeDelete(Guid? NtId)
        {
            Core.BLL.SA_NewsTypeBLL bll = new Core.BLL.SA_NewsTypeBLL();
            if (NtId.HasValue)
            {
                Core.BLL.SA_NewsBLL bllnews = new Core.BLL.SA_NewsBLL();
                Core.Models.SA_NewsType newstype = bll.GetById(NtId.Value);
                if (newstype == null || bllnews.GetAll().Where(x => x.NtId == newstype.NtId).Count() > 0)
                {
                    return Json(new { success = false, message = "请先删除该分类下的新闻" }, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    var ret = bll.Delete(newstype);
                    return Json(new { success = ret }, JsonRequestBehavior.AllowGet);
                }
            }
            else
                return Json(new { success = false }, JsonRequestBehavior.AllowGet);
        }
        [HttpPost]
        public ActionResult UploadMediaImage(HttpPostedFileBase uploadfile, string types)
        {
            var fileinfo = new FileInfo(uploadfile.FileName);
            if (fileinfo.Extension.ToLower() != ".jpg" && fileinfo.Extension.ToLower() != ".png")
            {
                return WriteJsonResponse(false, "图片格式不正确，请上传.jpg格式！");
            }
            var directiory_path = "/uploads/file/" + types;
            var filename = $"{DateTime.Now:yyyyMMddHHmmssffff}{fileinfo.Extension}";
            var localfile = Path.Combine(directiory_path, filename);
            filename = Path.Combine(Server.MapPath(directiory_path), filename);
            if (!Directory.Exists(Path.GetDirectoryName(filename)))
            {
                Directory.CreateDirectory(Path.GetDirectoryName(filename));
            }
            uploadfile.SaveAs(filename);
            //file_path
            return Json(new
            {
                success = true,
                message = "上传成功",
                file_path = localfile,
            }, JsonRequestBehavior.AllowGet);
        }
        #endregion
        #region 网站内容管理
        public ActionResult InformationList(int pageNo = 0)
        {
            ViewBag.txtTitle = Request["txtTitle"];
            Core.BLL.SA_WebsiteInformationBLL bll = new Core.BLL.SA_WebsiteInformationBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("SA_WebsiteInformation").Where("IsDeleted=0")
                .OrderBy("CreateTime desc");
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" Title like @0 ", $"%{ViewBag.txtTitle}%");
            var page_ret = bll.GetPageList<Models.WebsiteInformationViewModel>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        public ActionResult InformationAdd()
        {
            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.InfoCodeDict = new SelectList(dicts.Where(x => x.StudyName == "WebSiteInfo" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");

            return View();
        }
        [HttpPost, ValidateInput(false)]
        public ActionResult InformationAdd(WebsiteInformationViewModel model)
        {
            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.InfoCodeDict = new SelectList(dicts.Where(x => x.StudyName == "WebSiteInfo" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            Core.BLL.SA_WebsiteInformationBLL bll = new Core.BLL.SA_WebsiteInformationBLL();
            model.WiId = Guid.NewGuid();
            model.CreateTime = DateTime.Now;
            model.CreateSysUserId = SysUserProfile.CurrentProfile.UserID;
            model.IsDeleted = 0;
            var ret = bll.Insert(model);
            if (ret)
            {
                return RedirectToAction("InformationList");
            }
            else
            {
                ModelState.AddModelError("", "更新失败");
                return View(model);
            }
        }
        public ActionResult InformationEdit(Guid? WiId)
        {
            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.InfoCodeDict = new SelectList(dicts.Where(x => x.StudyName == "WebSiteInfo" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            Core.BLL.SA_WebsiteInformationBLL bll = new Core.BLL.SA_WebsiteInformationBLL();
            if (WiId.HasValue)
            {
                var model = bll.GetById(WiId.Value);
                if (model != null)
                {
                    Models.WebsiteInformationViewModel viewModel = new WebsiteInformationViewModel();
                    viewModel = Mapper.Map<Models.WebsiteInformationViewModel>(model);
                    return View(viewModel);
                }
            }
            return View();
        }
        [HttpPost, ValidateInput(false)]
        public ActionResult InformationEdit(WebsiteInformationViewModel model)
        {
            var dicts = (new Core.BLL.SA_StudyDictionaryBLL()).GetALLBySort();
            ViewBag.InfoCodeDict = new SelectList(dicts.Where(x => x.StudyName == "WebSiteInfo" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            Core.BLL.SA_WebsiteInformationBLL bll = new Core.BLL.SA_WebsiteInformationBLL();
            var model2 = bll.GetById(model.WiId);
            if (model2 == null)
            {
                return RedirectToAction("InformationList");
            }
            model2.InfoCode = model.InfoCode;
            model2.Author = model.Author;
            model2.AuthorEN = model.AuthorEN;
            model2.AuthorUnit = model.AuthorUnit;
            model2.AuthorUnitEN = model.AuthorUnitEN;
            model2.Content = model.Content;
            model2.ContentEN = model.ContentEN;
            model2.IsRelease = model.IsRelease;
            model2.IsTop = model.IsTop;
            model2.IsViewIndex = model.IsViewIndex;
            model2.ReleaseTime = model.ReleaseTime;
            model2.Sort = model.Sort;
            model2.Source = model.Source;
            model2.SourceEN = model.SourceEN;
            model2.Subtitle = model.Subtitle;
            model2.SubtitleEN = model.SubtitleEN;
            model2.Title = model.Title;
            model2.TitleEN = model.TitleEN;
            var ret = bll.Update(model2);
            if (ret)
            {
                return RedirectToAction("InformationList");
            }
            else
            {
                ModelState.AddModelError("", "更新失败");
                return View(model);
            }
        }
        public ActionResult InformationDelete(Guid? WiId)
        {
            Core.BLL.SA_WebsiteInformationBLL bll = new Core.BLL.SA_WebsiteInformationBLL();
            if (WiId.HasValue)
            {
                var ret = bll.Delete(WiId.Value);
                return Json(new { success = ret }, JsonRequestBehavior.AllowGet);
            }
            else
                return Json(new { success = false }, JsonRequestBehavior.AllowGet);
        }
        #endregion
        #region 试验文件上传
        public ActionResult TrialDataUpload(int pageNo = 0)
        {
            Core.BLL.SA_ProjectDataUploadBLL bll = new Core.BLL.SA_ProjectDataUploadBLL();
            var sql = PetaPoco.Sql.Builder.Select("data.*,p.regNumber,p.publicTitleCN,p.publicTitleEN,p.ReleaseNumber")
                .From("SA_ProjectDataUpload data")
                .LeftJoin("SA_NewProject p")
                .On("p.pid=data.pid");
            sql = sql.Where(" p.sendTaskSysUserId=@0 AND p.status = 3 ", SysUserProfile.CurrentProfile.UserID);
            sql = sql.OrderBy("data.CreateTime desc");
            var page_ret = bll.GetPageList<App.Models.ProjectDataUploadViewModel>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }

        public ActionResult TrialRepUpload(int pageNo = 0)
        {
            Core.BLL.SA_ProjectReportUploadBLL bll = new Core.BLL.SA_ProjectReportUploadBLL();
            var sql = PetaPoco.Sql.Builder.Select("data.*,p.regNumber,p.publicTitleCN,p.publicTitleEN,p.ReleaseNumber")
                .From("SA_ProjectReportUpload data")
                .LeftJoin("SA_NewProject p")
                .On("p.pid=data.pid");
            sql = sql.Where(" p.sendTaskSysUserId=@0 AND p.status = 3 ", SysUserProfile.CurrentProfile.UserID);
            sql = sql.OrderBy("data.CreateTime desc");
            var page_ret = bll.GetPageList<App.Models.ProjectRepUploadViewModel>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 网站状态管理
        public ActionResult SetWebStatus()
        {
            var model = new Core.BLL.SA_WebStatusBLL().GetAll().FirstOrDefault();
            if (model == null)
            {
                model = new Core.Models.SA_WebStatus();
                model.WebStatusId = Guid.NewGuid();
                model.IsOpen = true;
                model.IsShowLogin = false;
                model.Insert();
            }
            return View(model);
        }
        [HttpPost]
        public ActionResult SetWebStatus(Core.Models.SA_WebStatus model)
        {
            var res = new Core.BLL.SA_WebStatusBLL().Update(model);
            if (res)
            {
                SetMessageAlert(false,
                                   "系统提示 System prompt",
                                   "保存成功! <br/>Saved successfully!",
                                   "", "确定");
            }
            else
            {
                SetMessageAlert(false,
                                   "系统提示 System prompt",
                                   "保存失败! <br/>Save failed!",
                                   "",
                                   "确定");
            }

            return View(model);
        }
        #endregion

        [ChildActionOnly]
        public ActionResult ProModifyTimeView(Guid? Pid)
        {
            if (!Pid.HasValue)
                return View();
            var p = new Core.BLL.SA_NewProjectBLL().GetById(Pid.Value);
            var list = new Core.BLL.SA_ProjectVerifyTaskFlowRecordBLL().GetListByProjectId(Pid.Value).Where(x => x.VerifyTaskStatus == 5).OrderBy(x => x.CreateTime).ToList();
            return View(list);
        }
        [ChildActionOnly]
        public ActionResult ProLastRequestEdit(Guid? Pid)
        {
            var record = new Core.BLL.SA_ProjectRequestEditFlowBLL().GetByLastModelByPid(Pid.Value);
            return View(record);
        }

        #region 导出XML
        public ActionResult ExportXmlList()
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtOfficialName = Request["txtOfficialName"];
            ViewBag.txtSubjectID = Request["txtSubjectID"];
            ViewBag.listVerifyStatus = Request["listVerifyStatus"];
            ViewBag.txtRegNo = Request["txtRegNo"];
            ViewBag.regfrom = Request["regfrom"];
            ViewBag.regto = Request["regto"];
            ViewBag.numberfrom = Request["numberfrom"];
            ViewBag.numberto = Request["numberto"];
            ViewBag.txtRegNo2 = Request["txtRegNo2"];

            ViewBag.modifyTimefrom = Request["modifyTimefrom"];
            ViewBag.modifyTimeto = Request["modifyTimeto"];


            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("V_NewProject");
            sql = sql.Where(" status=3 and isDeleted <> 1");

            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");

            if (!string.IsNullOrEmpty(ViewBag.txtOfficialName))
                sql = sql.Where(" (ScientifirTitleCN like @0 or ScientifirTitleEN like @0) ", $"%{ViewBag.txtOfficialName}%");

            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);

            if (!string.IsNullOrEmpty(ViewBag.numberfrom) && !string.IsNullOrEmpty(ViewBag.numberto))
                sql = sql.Where(" CONVERT(varchar, regNumberTime, 23)  >= @0 AND CONVERT(varchar, regNumberTime, 23)  <= @1", ViewBag.numberfrom, ViewBag.numberto);

            if (!string.IsNullOrEmpty(ViewBag.regfrom) && !string.IsNullOrEmpty(ViewBag.regto))
                sql = sql.Where(" CONVERT(varchar, regTime, 23)  >= @0 AND CONVERT(varchar, regTime, 23)  <= @1", ViewBag.regfrom, ViewBag.regto);

            if (!string.IsNullOrEmpty(ViewBag.modifyTimefrom) && !string.IsNullOrEmpty(ViewBag.modifyTimeto))
                sql = sql.Where(" CONVERT(varchar, modifyTime, 23)  >= @0 AND CONVERT(varchar, modifyTime, 23)  <= @1", ViewBag.modifyTimefrom, ViewBag.modifyTimeto);

            if (!string.IsNullOrEmpty(ViewBag.txtRegNo2))
            {
                var ids = ((string)ViewBag.txtRegNo2).Replace("；", ";").Split(',', ';', '\r').Where(x => !string.IsNullOrEmpty(x)).Select(x => x.Trim()).ToList();
                if (ids.Count > 0)
                    sql = sql.Where(" regNumber in ( @0 )", ids);
            }
            sql = sql.OrderBy("regNumberTime desc,regNumber desc");

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(0, -1, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            return View(resPage);
        }
        [NoAsyncTimeout]
        async public Task<FileResult> ExportXml(string pids)
        {
            var ret = await Task.Run(() =>
            {
                using (MemoryStream fs = new MemoryStream())
                {
                    var bll = new Core.BLL.SA_NewProjectBLL();
                    var bllSample = new Core.BLL.SA_CollectingSampleBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllDict = new Core.BLL.SA_StudyDictionaryBLL();
                    var ids = pids.Replace("|", ",").Split(',').Select(x => Guid.Parse(x));
                    var finds = bll.GetAll().Where(x => ids.Contains(x.Pid)).OrderByDescending(x => x.regNumberTime).ThenByDescending(x => x.regNumber);
                    ExportXmlInfo exportXmlInfo = new ExportXmlInfo();
                    List<TrialInfo> list = new List<TrialInfo>();
                    var dictlist = bllDict.GetALLBySort();
                    foreach (var trialInfo in finds)
                    {
                        var list1 = bllSample.GetByProjectId(trialInfo.Pid);
                        var list2 = bllDiagnostic.GetByProjectId(trialInfo.Pid);
                        var list3 = bllInterventions.GetByProjectId(trialInfo.Pid);
                        var list4 = bllOutcomes.GetByProjectId(trialInfo.Pid);
                        var list5 = bllResearchAddress.GetByProjectId(trialInfo.Pid);
                        var list6 = bllSecondarySponsor.GetByProjectId(trialInfo.Pid);

                        TrialInfo triallcs = new TrialInfo();
                        triallcs.Main.Trial_id = trialInfo.regNumber.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        triallcs.Main.Utrn = trialInfo.UTN.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        if (!string.IsNullOrEmpty(trialInfo.secondaryID))
                            triallcs.Secondary_ids.Secondary_id.Add(new Secondary_idStr() { Sec_id = trialInfo.secondaryID.CleanInvalidCharsForXML().ConvertToEn() });
                        if (!string.IsNullOrEmpty(trialInfo.ReleaseNumber))
                            triallcs.Secondary_ids.Secondary_id.Add(new Secondary_idStr() { Sec_id = trialInfo.ReleaseNumber.CleanInvalidCharsForXML().ConvertToEn() });
                        triallcs.Main.Reg_name = "ITMCTR";
                        if (trialInfo.regNumberTime.HasValue)
                            triallcs.Main.Date_registration = trialInfo.regNumberTime.Value.ToString("yyyy-MM-dd");
                        triallcs.Main.Primary_sponsor = trialInfo.primarySponsorEN.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        triallcs.Main.Public_title = trialInfo.publicTitleEN.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        triallcs.Main.Scientific_title = trialInfo.scientifirTitleEN.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        triallcs.Main.Acronym = trialInfo.titleAcronymEN.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        triallcs.Main.Scientific_acronym = trialInfo.scientifirAcronymEN.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        if (trialInfo.recruitingTimeStart.HasValue)
                            triallcs.Main.Date_enrolment = trialInfo.recruitingTimeStart.Value.ToString("yyyy-MM-dd");

                        string txtsize = "";
                        string txtcode = "";
                        string txtfreetext = "";
                        if (trialInfo.studyTypeID == "1001003")
                        {
                            var info = list2.FirstOrDefault();
                            txtsize = $"Target condition:{info?.sampleSizeT};Difficult condition:{info?.sampleSizeD}";
                            txtcode = "";
                            txtfreetext = $"Gold Standard:{Utils.RestoreSql(info?.standardEn)};Index test:{info?.indexTestEn};";
                        }
                        else
                        {
                            foreach (var info in list3)
                            {
                                int num3 = info.sampleSize ?? 0;
                                txtsize += string.Format("{0}:{1};", Utils.HtmlDecode(info.groupsEN ?? ""), num3);
                                txtcode = txtcode + info.interventionCode ?? "" + ";";
                                txtfreetext += string.Format("{0}:{1};", Utils.HtmlDecode(info.groupsEN ?? ""), Utils.HtmlDecode(info.interventionEN ?? ""));
                            }
                        }
                        triallcs.Intervention_code.I_code = txtcode.CleanInvalidCharsForXML().ConvertToEn();
                        triallcs.Main.Target_size = txtsize.CleanInvalidCharsForXML().ConvertToEn();
                        triallcs.Main.I_freetext = txtfreetext.CleanInvalidCharsForXML().ConvertToEn();
                        string recruitingStatus = trialInfo.recruitingStatus.Trim();
                        triallcs.Main.Recruitment_status = "Pending";
                        if (recruitingStatus == "1004001")
                        {
                            triallcs.Main.Recruitment_status = "Pending";
                        }
                        if (recruitingStatus == "1004002")
                        {
                            triallcs.Main.Recruitment_status = "Recruiting";
                        }
                        if (recruitingStatus == "1004003")
                        {
                            triallcs.Main.Recruitment_status = "Temporary halt";
                        }
                        if (recruitingStatus == "1004004")
                        {
                            triallcs.Main.Recruitment_status = "Completed";
                        }
                        //string currentFullHost = "";
                        //if (!Request.Url.IsDefaultPort)
                        //{
                        //    currentFullHost = $"{Request.Url.Host}:{Request.Url.Port.ToString()}";
                        //}
                        //else
                        //    currentFullHost = Request.Url.Host;
                        triallcs.Main.Url = $"http://itmctr.ccebtcm.org.cn/en-US/Home/ProjectView?pid={trialInfo.Pid}";

                        var studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyTypeID && x.StudyName == "StudyType");
                        if (studyDictionaryInfo != null)
                        {
                            triallcs.Main.Study_type = studyDictionaryInfo.DescriptionEn.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        }
                        studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyDesignID && x.StudyName == "StudyDesign");
                        if (studyDictionaryInfo != null)
                        {
                            triallcs.Main.Study_design = studyDictionaryInfo.DescriptionEn.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                            //1003005 随机平行对照 Parallel
                            //10030010 随机交叉对照 Cross-over
                            switch (trialInfo.studyDesignID)
                            {
                                case "1003005":
                                    triallcs.Main.Study_design = "Parallel".CleanInvalidCharsForXML().ConvertToEn() ?? "";
                                    break;
                                case "10030010":
                                    triallcs.Main.Study_design = "Cross-over".CleanInvalidCharsForXML().ConvertToEn() ?? "";
                                    break;
                            }
                        }
                        studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyPhaseID && x.StudyName == "StudyPhase");
                        if (studyDictionaryInfo != null)
                        {
                            triallcs.Main.Phase = studyDictionaryInfo.DescriptionEn.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        }
                        triallcs.Main.Hc_freetext = Utils.HtmlDecode(trialInfo.targetDiseaseEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");

                        ContactStr[] array3 = new ContactStr[2];
                        ContactStr contactStr = new ContactStr();
                        contactStr.Type = "Scientific";
                        contactStr.Firstname = Utils.HtmlDecode(trialInfo.applicantEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr.Address = Utils.HtmlDecode(trialInfo.applicantAddressEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr.Affiliation = Utils.HtmlDecode(trialInfo.applicantInstitutionEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr.Zip = Utils.HtmlDecode(trialInfo.applicantPostcode.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr.Telephone = Utils.HtmlDecode(trialInfo.applicantTelephone.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr.Email = Utils.HtmlDecode(trialInfo.applicantEmail.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        array3.SetValue(contactStr, 0);

                        ContactStr contactStr2 = new ContactStr();
                        contactStr2.Type = "Public";
                        contactStr2.Firstname = Utils.HtmlDecode(trialInfo.studyLeaderEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr2.Address = Utils.HtmlDecode(trialInfo.studyAddressEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr2.Zip = Utils.HtmlDecode(trialInfo.studyPostcode.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr2.Telephone = Utils.HtmlDecode(trialInfo.studyTelephone.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr2.Email = Utils.HtmlDecode(trialInfo.studyEmail.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        contactStr2.Affiliation = Utils.HtmlDecode(trialInfo.studyLeaderCompanyEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        array3.SetValue(contactStr2, 1);

                        triallcs.Contacts.Contact = array3;

                        triallcs.Health_condition_code.Hc_code = trialInfo.targetCode.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        foreach (var info in list5)
                        {
                            triallcs.Countries.Country2 = Utils.HtmlDecode(info.countryEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        }

                        triallcs.Criteria.Inclusion_criteria = Utils.HtmlDecode(trialInfo.inclusionCriteriaEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        triallcs.Criteria.Agemin = trialInfo.ageMin.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        triallcs.Criteria.Agemax = trialInfo.ageMax.CleanInvalidCharsForXML().ConvertToEn() ?? "";

                        string txtsex = trialInfo.sex.Trim();
                        if (txtsex == "1006001")
                        {
                            triallcs.Criteria.Gender = "Male";
                        }
                        if (txtsex == "1006002")
                        {
                            triallcs.Criteria.Gender = "Female";
                        }
                        if (txtsex == "1006003")
                        {
                            triallcs.Criteria.Gender = "Both";
                        }
                        triallcs.Criteria.Exclusion_criteria = Utils.HtmlDecode(trialInfo.exclusionCrteriaEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        string txtPrim = "";
                        string txtSec = "";
                        foreach (var info in list4)
                        {
                            if (info.pointerType == "4002001")
                            {
                                txtPrim += string.Format("{0};", Utils.HtmlDecode(info.outcomeNameEN ?? ""));
                            }
                            if (info.pointerType == "4002002")
                            {
                                txtSec += string.Format("{0};", Utils.HtmlDecode(info.outcomeNameEN ?? ""));
                            }
                        }
                        triallcs.Primary_outcome.Prim_outcome = txtPrim.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        triallcs.Secondary_outcome.Sec_outcome = txtSec.CleanInvalidCharsForXML().ConvertToEn() ?? "";
                        triallcs.Source_support.Source_name = Utils.HtmlDecode(trialInfo.sourceFundingEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");


                        if (trialInfo.approvedCommittee == 1)
                        {
                            triallcs.Ethics_Reviews.Ethics_review.Status = "Approved";
                        }
                        else if (trialInfo.approvedCommittee == 0)
                        {
                            triallcs.Ethics_Reviews.Ethics_review.Status = "Not approved";
                        }
                        else
                        {
                            triallcs.Ethics_Reviews.Ethics_review.Status = "NA";
                        }
                        if (trialInfo.ethicalCommitteeSanctionDate.HasValue)
                            triallcs.Ethics_Reviews.Ethics_review.Approval_date = trialInfo.ethicalCommitteeSanctionDate.Value.ToString("yyyy-MM-dd");
                        triallcs.Ethics_Reviews.Ethics_review.Contact_name = Utils.HtmlDecode(trialInfo.ethicalCommitteeCNameEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        triallcs.Ethics_Reviews.Ethics_review.Contact_address = Utils.HtmlDecode(trialInfo.ethicalCommitteeCAddressEN.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        triallcs.Ethics_Reviews.Ethics_review.Contact_phone = Utils.HtmlDecode(trialInfo.ethicalCommitteeCPhone.CleanInvalidCharsForXML().ConvertToEn() ?? "");
                        triallcs.Ethics_Reviews.Ethics_review.Contact_email = Utils.HtmlDecode(trialInfo.ethicalCommitteeCEmail.CleanInvalidCharsForXML().ConvertToEn() ?? "");

                        //ProjectResult projectResultByPId = UserAPI.GetProjectResultByPId(num);
                        //triallcs.Main.Results_date_completed = Utils.DateTimeToStr(projectResultByPId.CompletedDate);
                        //triallcs.Main.Results_date_posted = Utils.DateTimeToStr(projectResultByPId.PostedDate);
                        //triallcs.Main.Results_date_first_publication = Utils.DateTimeToStr(projectResultByPId.FirstPublicationDate);
                        //triallcs.Main.Results_baseline_char = Utils.HtmlDecode(projectResultByPId.BaselineCharEN);
                        //triallcs.Main.Results_participant_flow = Utils.HtmlDecode(projectResultByPId.ParticipantFlowEN);
                        //triallcs.Main.Results_adverse_events = Utils.HtmlDecode(projectResultByPId.AdverseEventsEN);
                        //triallcs.Main.Results_outcome_measures = Utils.HtmlDecode(projectResultByPId.OutcomeMeasureEN);
                        //triallcs.Main.Results_url_link = Utils.HtmlDecode(projectResultByPId.URLLink);
                        //triallcs.Main.Results_summary = Utils.HtmlDecode(projectResultByPId.SummaryEN);
                        //triallcs.Main.Results_url_protocol = Utils.HtmlDecode(projectResultByPId.URLProtocol);
                        //triallcs.Main.Results_Actual_Enrolment = Utils.HtmlDecode(projectResultByPId.ActualEnrolmentEN);

                        //if (projectResultByPId.IPDPlan.Equals("y"))
                        //{
                        //    triallcs.Main.Results_IPD_plan = "Yes";
                        //}
                        //else if (projectResultByPId.IPDPlan.Equals("n"))
                        //{
                        //    triallcs.Main.Results_IPD_plan = "No";
                        //}
                        //else if (projectResultByPId.IPDPlan.Equals("u"))
                        //{
                        //    triallcs.Main.Results_IPD_plan = "Undecided";
                        //}
                        //triallcs.Main.Results_IPD_description = Utils.HtmlDecode(projectResultByPId.IPDDescriptionEN);
                        //array.SetValue(triallcs, i);
                        list.Add(triallcs);
                    }
                    exportXmlInfo.Triall = list;
                    exportXmlInfo.Subjects = list.Count;
                    XmlSerializerNamespaces xmlSerializerNamespaces = new XmlSerializerNamespaces();
                    xmlSerializerNamespaces.Add("", "");
                    XmlSerializer xmlSerializer = new XmlSerializer(exportXmlInfo.GetType());
                    xmlSerializer.Serialize(fs, exportXmlInfo, xmlSerializerNamespaces);

                    string arg = Utils.OrderIdCreate("", DateTime.Now);
                    string path = $"/uploads/exportxml/{DateTime.Now:yyyy/MM/dd}";
                    string filename = Server.MapPath(path);
                    if (!Directory.Exists(filename))
                    {
                        Directory.CreateDirectory(filename);
                    }
                    filename += $"\\ITMCTR{arg}.xml";
                    SerializationHelper.Save(exportXmlInfo, filename, xmlSerializerNamespaces);
                    return File(fs.ToArray(), "application/octet-stream", $"{arg}.xml");
                }
            });
            return ret;
        }

        [NoAsyncTimeout]
        async public Task<FileResult> ExportXmlError(int mode = 1)
        {
            var ret = await Task.Run(() =>
            {
                using (MemoryStream fs = new MemoryStream())
                {
                    var bll = new Core.BLL.SA_NewProjectBLL();
                    var bllSample = new Core.BLL.SA_CollectingSampleBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllDict = new Core.BLL.SA_StudyDictionaryBLL();
                    var finds = bll.GetAll().Where(x => x.isDeleted == 0).OrderByDescending(x => x.regNumberTime).ThenByDescending(x => x.regNumber);
                    ExportXmlInfo exportXmlInfo = new ExportXmlInfo();
                    List<TrialInfo> list = new List<TrialInfo>();
                    var dictlist = bllDict.GetALLBySort();
                    foreach (var trialInfo in finds)
                    {
                        var list1 = bllSample.GetByProjectId(trialInfo.Pid);
                        var list2 = bllDiagnostic.GetByProjectId(trialInfo.Pid);
                        var list3 = bllInterventions.GetByProjectId(trialInfo.Pid);
                        var list4 = bllOutcomes.GetByProjectId(trialInfo.Pid);
                        var list5 = bllResearchAddress.GetByProjectId(trialInfo.Pid);
                        var list6 = bllSecondarySponsor.GetByProjectId(trialInfo.Pid);
                        bool hasChinese = false;
                        TrialInfo triallcs = new TrialInfo();
                        triallcs.Main.Trial_id = trialInfo.regNumber.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        triallcs.Main.Utrn = trialInfo.UTN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        if (!string.IsNullOrEmpty(trialInfo.secondaryID))
                            triallcs.Secondary_ids.Secondary_id.Add(new Secondary_idStr() { Sec_id = trialInfo.secondaryID.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) });
                        if (!string.IsNullOrEmpty(trialInfo.ReleaseNumber))
                            triallcs.Secondary_ids.Secondary_id.Add(new Secondary_idStr() { Sec_id = trialInfo.ReleaseNumber.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) });
                        triallcs.Main.Reg_name = "ITMCTR";
                        if (trialInfo.regTime.HasValue)
                            triallcs.Main.Date_registration = trialInfo.regTime.Value.ToString("yyyy-MM-dd");
                        triallcs.Main.Primary_sponsor = trialInfo.primarySponsorEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        triallcs.Main.Public_title = trialInfo.publicTitleEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        triallcs.Main.Scientific_title = trialInfo.scientifirTitleEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        triallcs.Main.Acronym = trialInfo.titleAcronymEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        triallcs.Main.Scientific_acronym = trialInfo.scientifirAcronymEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        if (trialInfo.recruitingTimeStart.HasValue)
                            triallcs.Main.Date_enrolment = trialInfo.recruitingTimeStart.Value.ToString("yyyy-MM-dd");

                        string txtsize = "";
                        string txtcode = "";
                        string txtfreetext = "";
                        if (trialInfo.studyTypeID == "1001003")
                        {
                            var info = list2.FirstOrDefault();
                            txtsize = $"Target condition:{info?.sampleSizeT};Difficult condition:{info?.sampleSizeD}";
                            txtcode = "";
                            txtfreetext = $"Gold Standard:{Utils.RestoreSql(info?.standardEn)};Index test:{info?.indexTestEn};";
                        }
                        else
                        {
                            foreach (var info in list3)
                            {
                                int num3 = info.sampleSize ?? 0;
                                txtsize += string.Format("{0}:{1};", Utils.HtmlDecode(info.groupsEN ?? ""), num3);
                                txtcode = txtcode + info.interventionCode ?? "" + ";";
                                txtfreetext += string.Format("{0}:{1};", Utils.HtmlDecode(info.groupsEN ?? ""), Utils.HtmlDecode(info.interventionEN ?? ""));
                            }
                        }
                        triallcs.Intervention_code.I_code = txtcode.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode);
                        triallcs.Main.Target_size = txtsize.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode);
                        triallcs.Main.I_freetext = txtfreetext.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode);
                        string recruitingStatus = trialInfo.recruitingStatus.Trim();
                        triallcs.Main.Recruitment_status = "Pending";
                        if (recruitingStatus == "1004001")
                        {
                            triallcs.Main.Recruitment_status = "Pending";
                        }
                        if (recruitingStatus == "1004002")
                        {
                            triallcs.Main.Recruitment_status = "Recruiting";
                        }
                        if (recruitingStatus == "1004003")
                        {
                            triallcs.Main.Recruitment_status = "Temporary halt";
                        }
                        if (recruitingStatus == "1004004")
                        {
                            triallcs.Main.Recruitment_status = "Completed";
                        }
                        //string currentFullHost = "";
                        //if (!Request.Url.IsDefaultPort)
                        //{
                        //    currentFullHost = $"{Request.Url.Host}:{Request.Url.Port.ToString()}";
                        //}
                        //else
                        //    currentFullHost = Request.Url.Host;
                        triallcs.Main.Url = $"http://itmctr.ccebtcm.org.cn/en-US/Home/ProjectView?pid={trialInfo.Pid}";

                        var studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyTypeID && x.StudyName == "StudyType");
                        if (studyDictionaryInfo != null)
                        {
                            triallcs.Main.Study_type = studyDictionaryInfo.DescriptionEn.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        }
                        studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyDesignID && x.StudyName == "StudyDesign");
                        if (studyDictionaryInfo != null)
                        {
                            triallcs.Main.Study_design = studyDictionaryInfo.DescriptionEn.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        }
                        studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyPhaseID && x.StudyName == "StudyPhase");
                        if (studyDictionaryInfo != null)
                        {
                            triallcs.Main.Phase = studyDictionaryInfo.DescriptionEn.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        }
                        triallcs.Main.Hc_freetext = Utils.HtmlDecode(trialInfo.targetDiseaseEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");

                        ContactStr[] array3 = new ContactStr[2];
                        ContactStr contactStr = new ContactStr();
                        contactStr.Type = "Scientific";
                        contactStr.Firstname = Utils.HtmlDecode(trialInfo.applicantEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr.Address = Utils.HtmlDecode(trialInfo.applicantAddressEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr.Affiliation = Utils.HtmlDecode(trialInfo.applicantInstitutionEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr.Zip = Utils.HtmlDecode(trialInfo.applicantPostcode.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr.Telephone = Utils.HtmlDecode(trialInfo.applicantTelephone.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr.Email = Utils.HtmlDecode(trialInfo.applicantEmail.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        array3.SetValue(contactStr, 0);

                        ContactStr contactStr2 = new ContactStr();
                        contactStr2.Type = "Public";
                        contactStr2.Firstname = Utils.HtmlDecode(trialInfo.studyLeaderEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr2.Address = Utils.HtmlDecode(trialInfo.studyAddressEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr2.Zip = Utils.HtmlDecode(trialInfo.studyPostcode.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr2.Telephone = Utils.HtmlDecode(trialInfo.studyTelephone.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr2.Email = Utils.HtmlDecode(trialInfo.studyEmail.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        contactStr2.Affiliation = Utils.HtmlDecode(trialInfo.studyLeaderCompanyEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        array3.SetValue(contactStr2, 1);

                        triallcs.Contacts.Contact = array3;

                        triallcs.Health_condition_code.Hc_code = trialInfo.targetCode.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        foreach (var info in list5)
                        {
                            triallcs.Countries.Country2 = Utils.HtmlDecode(info.countryEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        }

                        triallcs.Criteria.Inclusion_criteria = Utils.HtmlDecode(trialInfo.inclusionCriteriaEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        triallcs.Criteria.Agemin = trialInfo.ageMin.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        triallcs.Criteria.Agemax = trialInfo.ageMax.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";

                        string txtsex = trialInfo.sex.Trim();
                        if (txtsex == "1006001")
                        {
                            triallcs.Criteria.Gender = "Male";
                        }
                        if (txtsex == "1006002")
                        {
                            triallcs.Criteria.Gender = "Female";
                        }
                        if (txtsex == "1006003")
                        {
                            triallcs.Criteria.Gender = "Both";
                        }
                        triallcs.Criteria.Exclusion_criteria = Utils.HtmlDecode(trialInfo.exclusionCrteriaEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        string txtPrim = "";
                        string txtSec = "";
                        foreach (var info in list4)
                        {
                            if (info.pointerType == "4002001")
                            {
                                txtPrim += string.Format("{0};", Utils.HtmlDecode(info.outcomeNameEN ?? ""));
                            }
                            if (info.pointerType == "4002002")
                            {
                                txtSec += string.Format("{0};", Utils.HtmlDecode(info.outcomeNameEN ?? ""));
                            }
                        }
                        triallcs.Primary_outcome.Prim_outcome = txtPrim.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        triallcs.Secondary_outcome.Sec_outcome = txtSec.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "";
                        triallcs.Source_support.Source_name = Utils.HtmlDecode(trialInfo.sourceFundingEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");


                        if (trialInfo.approvedCommittee == 1)
                        {
                            triallcs.Ethics_Reviews.Ethics_review.Status = "Approved";
                        }
                        else if (trialInfo.approvedCommittee == 0)
                        {
                            triallcs.Ethics_Reviews.Ethics_review.Status = "Not approved";
                        }
                        else
                        {
                            triallcs.Ethics_Reviews.Ethics_review.Status = "NA";
                        }
                        if (trialInfo.ethicalCommitteeSanctionDate.HasValue)
                            triallcs.Ethics_Reviews.Ethics_review.Approval_date = trialInfo.ethicalCommitteeSanctionDate.Value.ToString("yyyy-MM-dd");
                        triallcs.Ethics_Reviews.Ethics_review.Contact_name = Utils.HtmlDecode(trialInfo.ethicalCommitteeCNameEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        triallcs.Ethics_Reviews.Ethics_review.Contact_address = Utils.HtmlDecode(trialInfo.ethicalCommitteeCAddressEN.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        triallcs.Ethics_Reviews.Ethics_review.Contact_phone = Utils.HtmlDecode(trialInfo.ethicalCommitteeCPhone.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        triallcs.Ethics_Reviews.Ethics_review.Contact_email = Utils.HtmlDecode(trialInfo.ethicalCommitteeCEmail.CleanInvalidCharsForXML().HasChinese(ref hasChinese, mode) ?? "");
                        if (hasChinese)
                            list.Add(triallcs);
                    }
                    exportXmlInfo.Triall = list;
                    exportXmlInfo.Subjects = list.Count;
                    XmlSerializerNamespaces xmlSerializerNamespaces = new XmlSerializerNamespaces();
                    xmlSerializerNamespaces.Add("", "");
                    XmlSerializer xmlSerializer = new XmlSerializer(exportXmlInfo.GetType());
                    xmlSerializer.Serialize(fs, exportXmlInfo, xmlSerializerNamespaces);
                    string arg = Utils.OrderIdCreate("", DateTime.Now);
                    return File(fs.ToArray(), "application/octet-stream", $"{arg}.xml");
                }
            });
            return ret;
        }

        [NoAsyncTimeout]
        public FileResult ExportXmlTest()
        {
            using (MemoryStream fs = new MemoryStream())
            {
                var bll = new Core.BLL.SA_NewProjectBLL();
                var bllSample = new Core.BLL.SA_CollectingSampleBLL();
                var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                var bllDict = new Core.BLL.SA_StudyDictionaryBLL();
                ExportXmlInfo exportXmlInfo = new ExportXmlInfo();
                XmlSerializer xml = new XmlSerializer(exportXmlInfo.GetType());
                using (var ff = System.IO.File.OpenRead(@".\100.xml"))
                {
                    exportXmlInfo = (ExportXmlInfo)xml.Deserialize(ff);
                }
                var ids = exportXmlInfo.Triall.Select(x => x.Main.Trial_id);
                var finds = bll.GetAll().Where(x => ids.Contains(x.regNumber));
                List<TrialInfo> list = new List<TrialInfo>();
                var dictlist = bllDict.GetALLBySort();
                foreach (var trialInfo in finds)
                {
                    var list1 = bllSample.GetByProjectId(trialInfo.Pid);
                    var list2 = bllDiagnostic.GetByProjectId(trialInfo.Pid);
                    var list3 = bllInterventions.GetByProjectId(trialInfo.Pid);
                    var list4 = bllOutcomes.GetByProjectId(trialInfo.Pid);
                    var list5 = bllResearchAddress.GetByProjectId(trialInfo.Pid);
                    var list6 = bllSecondarySponsor.GetByProjectId(trialInfo.Pid);

                    TrialInfo triallcs = new TrialInfo();
                    triallcs.Main.Trial_id = trialInfo.regNumber ?? "";
                    triallcs.Main.Utrn = trialInfo.UTN ?? "";
                    if (!string.IsNullOrEmpty(trialInfo.secondaryID))
                        triallcs.Secondary_ids.Secondary_id.Add(new Secondary_idStr() { Sec_id = trialInfo.secondaryID });
                    if (!string.IsNullOrEmpty(trialInfo.ReleaseNumber))
                        triallcs.Secondary_ids.Secondary_id.Add(new Secondary_idStr() { Sec_id = trialInfo.ReleaseNumber });
                    triallcs.Main.Reg_name = "ITMCTR";
                    if (trialInfo.regTime.HasValue)
                        triallcs.Main.Date_registration = trialInfo.regTime.Value.ToString("yyyy-MM-dd");
                    triallcs.Main.Primary_sponsor = trialInfo.primarySponsorEN ?? "";
                    triallcs.Main.Public_title = trialInfo.publicTitleEN ?? "";
                    triallcs.Main.Scientific_title = trialInfo.scientifirTitleEN ?? "";
                    triallcs.Main.Acronym = trialInfo.titleAcronymEN ?? "";
                    triallcs.Main.Scientific_acronym = trialInfo.scientifirAcronymEN ?? "";
                    if (trialInfo.recruitingTimeStart.HasValue)
                        triallcs.Main.Date_enrolment = trialInfo.recruitingTimeStart.Value.ToString("yyyy-MM-dd");

                    string txtsize = "";
                    string txtcode = "";
                    string txtfreetext = "";
                    if (trialInfo.studyTypeID == "1001003")
                    {
                        var info = list2.FirstOrDefault();
                        txtsize = $"Target condition:{info?.sampleSizeT};Difficult condition:{info?.sampleSizeD}";
                        txtcode = "";
                        txtfreetext = $"Gold Standard:{Utils.RestoreSql(info?.standardEn)};Index test:{info?.indexTestEn};";
                    }
                    else
                    {
                        foreach (var info in list3)
                        {
                            int num3 = info.sampleSize ?? 0;
                            txtsize += string.Format("{0}:{1};", Utils.HtmlDecode(info.groupsEN ?? ""), num3);
                            txtcode = txtcode + info.interventionCode ?? "" + ";";
                            txtfreetext += string.Format("{0}:{1};", Utils.HtmlDecode(info.groupsEN ?? ""), Utils.HtmlDecode(info.interventionEN ?? ""));
                        }
                    }
                    triallcs.Intervention_code.I_code = txtcode;
                    triallcs.Main.Target_size = txtsize;
                    triallcs.Main.I_freetext = txtfreetext;
                    string recruitingStatus = trialInfo.recruitingStatus.Trim();
                    triallcs.Main.Recruitment_status = "Pending";
                    if (recruitingStatus == "1004001")
                    {
                        triallcs.Main.Recruitment_status = "Pending";
                    }
                    if (recruitingStatus == "1004002")
                    {
                        triallcs.Main.Recruitment_status = "Recruiting";
                    }
                    if (recruitingStatus == "1004003")
                    {
                        triallcs.Main.Recruitment_status = "Temporary halt";
                    }
                    if (recruitingStatus == "1004004")
                    {
                        triallcs.Main.Recruitment_status = "Completed";
                    }
                    //string currentFullHost = "";
                    //if (!Request.Url.IsDefaultPort)
                    //{
                    //    currentFullHost = $"{Request.Url.Host}:{Request.Url.Port.ToString()}";
                    //}
                    //else
                    //    currentFullHost = Request.Url.Host;
                    triallcs.Main.Url = $"http://itmctr.ccebtcm.org.cn/en-US/Home/ProjectView?pid={trialInfo.Pid}";

                    var studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyTypeID && x.StudyName == "StudyType");
                    if (studyDictionaryInfo != null)
                    {
                        triallcs.Main.Study_type = studyDictionaryInfo.DescriptionEn ?? "";
                    }
                    studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyDesignID && x.StudyName == "StudyDesign");
                    if (studyDictionaryInfo != null)
                    {
                        triallcs.Main.Study_design = studyDictionaryInfo.DescriptionEn ?? "";
                    }
                    studyDictionaryInfo = dictlist.FirstOrDefault(x => x.StudyValue == trialInfo.studyPhaseID && x.StudyName == "StudyPhase");
                    if (studyDictionaryInfo != null)
                    {
                        triallcs.Main.Phase = studyDictionaryInfo.DescriptionEn ?? "";
                    }
                    triallcs.Main.Hc_freetext = Utils.HtmlDecode(trialInfo.targetDiseaseEN ?? "");

                    ContactStr[] array3 = new ContactStr[2];
                    ContactStr contactStr = new ContactStr();
                    contactStr.Type = "Scientific";
                    contactStr.Firstname = Utils.HtmlDecode(trialInfo.applicantEN ?? "");
                    contactStr.Address = Utils.HtmlDecode(trialInfo.applicantAddressEN ?? "");
                    contactStr.Affiliation = Utils.HtmlDecode(trialInfo.applicantInstitutionEN ?? "");
                    contactStr.Zip = Utils.HtmlDecode(trialInfo.applicantPostcode ?? "");
                    contactStr.Telephone = Utils.HtmlDecode(trialInfo.applicantTelephone ?? "");
                    contactStr.Email = Utils.HtmlDecode(trialInfo.applicantEmail ?? "");
                    array3.SetValue(contactStr, 0);

                    ContactStr contactStr2 = new ContactStr();
                    contactStr2.Type = "Public";
                    contactStr2.Firstname = Utils.HtmlDecode(trialInfo.studyLeaderEN ?? "");
                    contactStr2.Address = Utils.HtmlDecode(trialInfo.studyAddressEN ?? "");
                    contactStr2.Zip = Utils.HtmlDecode(trialInfo.studyPostcode ?? "");
                    contactStr2.Telephone = Utils.HtmlDecode(trialInfo.studyTelephone ?? "");
                    contactStr2.Email = Utils.HtmlDecode(trialInfo.studyEmail ?? "");
                    contactStr2.Affiliation = Utils.HtmlDecode(trialInfo.studyLeaderCompanyEN ?? "");
                    array3.SetValue(contactStr2, 1);

                    triallcs.Contacts.Contact = array3;

                    triallcs.Health_condition_code.Hc_code = trialInfo.targetCode ?? "";
                    foreach (var info in list5)
                    {
                        triallcs.Countries.Country2 = Utils.HtmlDecode(info.countryEN ?? "");
                    }

                    triallcs.Criteria.Inclusion_criteria = Utils.HtmlDecode(trialInfo.inclusionCriteriaEN ?? "");
                    triallcs.Criteria.Agemin = trialInfo.ageMin;
                    triallcs.Criteria.Agemax = trialInfo.ageMax;

                    string txtsex = trialInfo.sex.Trim();
                    if (txtsex == "1006001")
                    {
                        triallcs.Criteria.Gender = "Male";
                    }
                    if (txtsex == "1006002")
                    {
                        triallcs.Criteria.Gender = "Female";
                    }
                    if (txtsex == "1006003")
                    {
                        triallcs.Criteria.Gender = "Both";
                    }
                    triallcs.Criteria.Exclusion_criteria = Utils.HtmlDecode(trialInfo.exclusionCrteriaEN ?? "");
                    string txtPrim = "";
                    string txtSec = "";
                    foreach (var info in list4)
                    {
                        if (info.pointerType == "4002001")
                        {
                            txtPrim += string.Format("{0};", Utils.HtmlDecode(info.outcomeNameEN ?? ""));
                        }
                        if (info.pointerType == "4002002")
                        {
                            txtSec += string.Format("{0};", Utils.HtmlDecode(info.outcomeNameEN ?? ""));
                        }
                    }
                    triallcs.Primary_outcome.Prim_outcome = txtPrim;
                    triallcs.Secondary_outcome.Sec_outcome = txtSec;
                    triallcs.Source_support.Source_name = Utils.HtmlDecode(trialInfo.sourceFundingEN ?? "");


                    if (trialInfo.approvedCommittee == 1)
                    {
                        triallcs.Ethics_Reviews.Ethics_review.Status = "Approved";
                    }
                    else if (trialInfo.approvedCommittee == 0)
                    {
                        triallcs.Ethics_Reviews.Ethics_review.Status = "Not approved";
                    }
                    else
                    {
                        triallcs.Ethics_Reviews.Ethics_review.Status = "NA";
                    }
                    if (trialInfo.ethicalCommitteeSanctionDate.HasValue)
                        triallcs.Ethics_Reviews.Ethics_review.Approval_date = trialInfo.ethicalCommitteeSanctionDate.Value.ToString("yyyy-MM-dd");
                    triallcs.Ethics_Reviews.Ethics_review.Contact_name = Utils.HtmlDecode(trialInfo.ethicalCommitteeCNameEN ?? "");
                    triallcs.Ethics_Reviews.Ethics_review.Contact_address = Utils.HtmlDecode(trialInfo.ethicalCommitteeCAddressEN ?? "");
                    triallcs.Ethics_Reviews.Ethics_review.Contact_phone = Utils.HtmlDecode(trialInfo.ethicalCommitteeCPhone ?? "");
                    triallcs.Ethics_Reviews.Ethics_review.Contact_email = Utils.HtmlDecode(trialInfo.ethicalCommitteeCEmail ?? "");

                    //ProjectResult projectResultByPId = UserAPI.GetProjectResultByPId(num);
                    //triallcs.Main.Results_date_completed = Utils.DateTimeToStr(projectResultByPId.CompletedDate);
                    //triallcs.Main.Results_date_posted = Utils.DateTimeToStr(projectResultByPId.PostedDate);
                    //triallcs.Main.Results_date_first_publication = Utils.DateTimeToStr(projectResultByPId.FirstPublicationDate);
                    //triallcs.Main.Results_baseline_char = Utils.HtmlDecode(projectResultByPId.BaselineCharEN);
                    //triallcs.Main.Results_participant_flow = Utils.HtmlDecode(projectResultByPId.ParticipantFlowEN);
                    //triallcs.Main.Results_adverse_events = Utils.HtmlDecode(projectResultByPId.AdverseEventsEN);
                    //triallcs.Main.Results_outcome_measures = Utils.HtmlDecode(projectResultByPId.OutcomeMeasureEN);
                    //triallcs.Main.Results_url_link = Utils.HtmlDecode(projectResultByPId.URLLink);
                    //triallcs.Main.Results_summary = Utils.HtmlDecode(projectResultByPId.SummaryEN);
                    //triallcs.Main.Results_url_protocol = Utils.HtmlDecode(projectResultByPId.URLProtocol);
                    //triallcs.Main.Results_Actual_Enrolment = Utils.HtmlDecode(projectResultByPId.ActualEnrolmentEN);

                    //if (projectResultByPId.IPDPlan.Equals("y"))
                    //{
                    //    triallcs.Main.Results_IPD_plan = "Yes";
                    //}
                    //else if (projectResultByPId.IPDPlan.Equals("n"))
                    //{
                    //    triallcs.Main.Results_IPD_plan = "No";
                    //}
                    //else if (projectResultByPId.IPDPlan.Equals("u"))
                    //{
                    //    triallcs.Main.Results_IPD_plan = "Undecided";
                    //}
                    //triallcs.Main.Results_IPD_description = Utils.HtmlDecode(projectResultByPId.IPDDescriptionEN);
                    //array.SetValue(triallcs, i);
                    list.Add(triallcs);
                }
                exportXmlInfo.Triall = list;
                exportXmlInfo.Subjects = list.Count;
                XmlSerializerNamespaces xmlSerializerNamespaces = new XmlSerializerNamespaces();
                xmlSerializerNamespaces.Add("", "");
                XmlSerializer xmlSerializer = new XmlSerializer(exportXmlInfo.GetType());
                xmlSerializer.Serialize(fs, exportXmlInfo, xmlSerializerNamespaces);

                string arg = Utils.OrderIdCreate("", DateTime.Now);
                return File(fs.ToArray(), "application/octet-stream", $"{arg}.xml");
            }
        }


        [NoAsyncTimeout]
        public FileResult ExportXmlTest2()
        {
            ExportXmlInfo exportXmlInfo = new ExportXmlInfo();
            XmlSerializer xml = new XmlSerializer(exportXmlInfo.GetType());
            using (var ff = System.IO.File.OpenRead(@"D:\Backup\Desktop\20220708\2.xml"))
            {
                exportXmlInfo = (ExportXmlInfo)xml.Deserialize(ff);
            }
            using (MemoryStream zipfs = new MemoryStream())
            {
                using (var archive = new ZipArchive(zipfs, ZipArchiveMode.Create, true))
                {
                    int pageNum = 0;
                    int pageSize = 5000;
                    bool flag = true;
                    while (flag)
                    {
                        var query = exportXmlInfo.Triall.Skip(pageNum * pageSize).Take(pageSize);
                        flag = query.Count() > 0;
                        try
                        {
                            string filename = $"{pageNum}.xml";
                            using (MemoryStream fs = new MemoryStream())
                            {
                                ExportXmlInfo temp = new ExportXmlInfo();
                                temp.Triall = query.ToList();
                                temp.Subjects = query.Count();
                                XmlSerializerNamespaces xmlSerializerNamespaces = new XmlSerializerNamespaces();
                                xmlSerializerNamespaces.Add("", "");
                                XmlSerializer xmlSerializer = new XmlSerializer(exportXmlInfo.GetType());
                                xmlSerializer.Serialize(fs, temp, xmlSerializerNamespaces);
                                var file = archive.CreateEntry(filename);
                                var buffile = fs.ToArray();
                                using (var entryStream = file.Open())
                                {
                                    entryStream.Write(buffile, 0, buffile.Length);
                                }
                            }
                        }
                        catch
                        {

                        }
                        pageNum++;
                    }
                }
                return File(zipfs.ToArray(), "application/octet-stream", $"{DateTime.Now.ToString("yyyyMMdd")}.zip");
            }
        }
        #endregion
        #region 审核疑问站内信
        [ChildActionOnly]
        public ActionResult Message(Guid? Pid)
        {
            new Core.BLL.SA_MessagingBLL().UpdataManagerRead(Pid.Value);
            var msgList = new Core.BLL.SA_MessagingBLL().GetListByPid(Pid.Value).ConvertAll(x => new Core.Models.MessageModel
            {
                MessagingId = x.MessagingId,
                Pid = x.Pid,
                ToUser = x.ToUser,
                ToUserName = x.ToUserName,
                FromUser = x.FromUser,
                FromUserName = x.FromUserName,
                CreateTime = string.Format("{0:yyyy-MM-dd HH:mm:ss}", x.CreateTime),
                UserRead = x.UserRead,
                ManagerRead = x.ManagerRead,
                Content = x.Content,
                isIn = Models.SysUserProfile.CurrentProfile.UserID == x.FromUser.Value ? 1 : 0,
            }).ToList();
            return View(msgList);
        }


        public JsonResult InsertMsg(Guid Pid, string Message)
        {
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            var bllUser = new Core.BLL.SA_UsersBLL();
            var bllMsg = new Core.BLL.SA_MessagingBLL();
            var proInfo = bllProj.GetById(Pid);
            if (proInfo == null)
            {
                return WriteJsonResponse(false, "记录不存在", false);
            }
            var user = bllUser.GetById(proInfo.createUserID.Value);
            var msgEntity = new Core.Models.SA_Messaging();

            msgEntity.MessagingId = Guid.NewGuid();
            msgEntity.Pid = Pid;
            msgEntity.ToUser = user.Uid;
            msgEntity.ToUserName = user.Name;
            msgEntity.FromUser = Models.SysUserProfile.CurrentProfile.UserID;
            msgEntity.FromUserName = Models.SysUserProfile.CurrentProfile.UserModel.Name;
            msgEntity.CreateTime = DateTime.Now;
            msgEntity.UserRead = 0;
            msgEntity.ManagerRead = 1;
            msgEntity.Content = Message;
            msgEntity.isIn = 1;
            var res = bllMsg.Insert(msgEntity);
            if (res)
            {
                List<SA_Messaging> list = new List<SA_Messaging>();
                list.Add(msgEntity);
                var msgList = list.ConvertAll(x => new
                {
                    x.MessagingId,
                    x.Pid,
                    x.ToUser,
                    x.ToUserName,
                    x.FromUser,
                    x.FromUserName,
                    CreateTime = string.Format("{0:yyyy-MM-dd HH:mm:ss}", x.CreateTime),
                    x.UserRead,
                    x.ManagerRead,
                    x.Content,
                    isIn = Models.SysUserProfile.CurrentProfile.UserID == x.FromUser.Value ? 1 : 0,
                }).ToList();
                return Json(new
                {
                    success = true,
                    message = "操作成功",
                    data = JsonConvert.SerializeObject(msgList)
                }, JsonRequestBehavior.AllowGet);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }
        #endregion
        [ChildActionOnly]
        public ActionResult SecondBackResultView(Guid? Pid, string ViewName)
        {

            var list = new List<Core.Models.SA_ProjectVerifyTaskFlowRecord>();
            ViewData["ViewName"] = ViewName;
            #region 二审拒绝原因
            if (Pid.HasValue)
            {
                Core.Models.SA_NewProject proInfo = new Core.BLL.SA_NewProjectBLL().GetById(Pid.Value);
                if (proInfo.taskStatus != (int)Core.ProjectTaskStatus.复核通过 &&
                    proInfo.taskStatus != (int)Core.ProjectTaskStatus.申请通过 || SysUserProfile.CurrentProfile.UserModel.Username == "admin")
                {
                    list = new Core.BLL.SA_ProjectVerifyTaskFlowRecordBLL()
               .GetListByProjectId(proInfo.Pid)
               .Where(x => (x.ProjectStatus == 2 && x.VerifyTaskStatus == 2) || (x.ProjectStatus == 1 && x.VerifyTaskStatus == 5 && !string.IsNullOrEmpty(x.Reason)))
               .OrderByDescending(x => x.CreateTime).ToList();
                    var user = new Core.BLL.SA_SysUserBLL().GetAll();

                    var query = from l in list
                                join u in user on l.SubmitUserId equals u.Uid into bb
                                from bbdata in bb.DefaultIfEmpty()
                                    //where (bbdata is null && l.VerifyTaskStatus == 5) || bbdata.SysRoleId == Guid.Parse("3d64080d-042f-45da-84be-570b360236a6")
                                select l;
                    list = query.ToList();
                }
            }
            #endregion
            return View(list);
        }
        #region 非传统医学 
        public ActionResult ProAlternativeMedicine(int pageNo = 0)
        {
            //ViewBag.txtRoleName = Request["txtRoleName"];
            //ViewBag.txtRoleDesc = Request["txtRoleDesc"];
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            //一级审核员
            if (SysUserProfile.CurrentProfile.UserModel.SysRoleId == Guid.Parse("f2c6f6a5-069a-4f8e-8d11-6d336045529b"))
            {
                sql = sql.Where(" isTraditionalMedicine = 0 ").OrderBy("flowRecordTime desc");
            }
            //二级审核员或三级审核员SingleJudged
            else if (SysUserProfile.CurrentProfile.UserModel.SysRoleId == Guid.Parse("3d64080d-042f-45da-84be-570b360236a6"))
            {
                sql = sql.Where(" executeTaskSysUserId = @0 AND isTraditionalMedicine = 0 ", SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");
            }
            else
            {
                sql = sql.Where(" isTraditionalMedicine = 0 ").OrderBy("flowRecordTime desc");
            }
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            var resPage = ConvertProject(page_ret);
            resPage.Context = GetUrlParms();
            ViewData["SysRoleId"] = SysUserProfile.CurrentProfile.UserModel.SysRoleId;
            return View(resPage);
        }

        public ActionResult ModifyAlternativeMedicineState(Guid? Pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (Pid.HasValue)
            {
                var proInfo = bllProj.GetById(Pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(Pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(Pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(Pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(Pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(Pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(Pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                    model.isTraditionalMedicine = proInfo.isTraditionalMedicine;
                    model.TaskFlowCreateSysUserId = proInfo.executeTaskSysUserId;
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            var isTraditionalMedicine = CommonList.getisTraditionalMedicine();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.ddlisTraditionalMedicine = new SelectList(isTraditionalMedicine, "Key", "Value", model?.listLang);

            var rid = Guid.Parse("3d64080d-042f-45da-84be-570b360236a6");
            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.SysRoleId == rid).ToList();

            var list = new List<Common.dataObjectList>();
            var temp = userList.ConvertAll(x => new Common.dataObjectList
            {
                id = x.Uid.ToString(),
                name = $"{x.Username}[{x.Name}]({bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count})",
                Count = bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count
            });
            list.Add(new Common.dataObjectList { id = "", name = "请选择" });
            list.AddRange(temp.OrderBy(x => x.Count).ToList());
            ViewBag.UserList = new SelectList(list, "id", "name");
            return View(model);
        }

        [HttpPost]
        public JsonResult ModifyAlternativeMedicineState(Guid pid, int isTraditionalMedicine)
        {
            var res = new Core.BLL.SA_NewProjectBLL().ModifyAlternativeMedicineState(pid, isTraditionalMedicine);
            if (res)
            {
                return WriteJsonResponse(true, "更改成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "更改失败", false);
            }
        }
        #endregion
        #region 待判断传统医学列表
        public ActionResult ProTobeJudged(int pageNo = 0)
        {
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            sql = sql.Where(" status = 1 and taskStatus = 1  AND isDeleted = 0 AND (isTraditionalMedicine is null)").OrderBy("flowRecordTime desc");
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 判断传统医学
        public ActionResult SingleJudged(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            var isTraditionalMedicine = CommonList.getisTraditionalMedicine();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.ddlisTraditionalMedicine = new SelectList(isTraditionalMedicine, "Key", "Value", model?.listLang);

            var rid2 = Guid.Parse("49bb2491-2ba5-492d-9f54-6317bcc05baa");
            var rid3 = Guid.Parse("3d64080d-042f-45da-84be-570b360236a6");
            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.SysRoleId == rid2 || x.SysRoleId == rid3).ToList();

            var list = new List<Common.dataObjectList>();
            var temp = userList.ConvertAll(x => new Common.dataObjectList
            {
                id = x.Uid.ToString(),
                name = $"{x.Username}[{x.Name}]({bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count})",
                Count = bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count
            });
            list.Add(new Common.dataObjectList { id = "", name = "请选择" });
            list.AddRange(temp.OrderBy(x => x.Count).ToList());
            ViewBag.UserList = new SelectList(list, "id", "name");
            return View(model);
        }

        [HttpPost]
        public JsonResult SingleJudged(Guid pid,string UserId, int isTraditionalMedicine)
        {
            var res = new Core.BLL.SA_NewProjectBLL().JudgedProject(pid.ToString(), UserId, isTraditionalMedicine, Models.SysUserProfile.CurrentProfile.UserID);

            if (res)
            {
                return WriteJsonResponse(true, "判断成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "判断失败", false);
            }
        }
        #endregion
        #region 待发号
        public ActionResult ProToBeSendNumber(int pageNo = 0)
        {
            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            sql = sql.Where("Status = 1 AND taskStatus = 5 AND (regNumber is null or regNumber = '') AND firstTaskSysUserId =@0 ", Models.SysUserProfile.CurrentProfile.UserID).OrderBy("flowRecordTime desc");//3待复核，且为当前用户，分配任务的人

            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 发号

        public ActionResult SingleSendNumber(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.RegTime = proInfo.regTime.HasValue ? proInfo.regTime.Value.ToString("yyyy-MM-dd") : "";
                    model.createUserName = new Core.BLL.SA_UsersBLL().GetUserName(proInfo.createUserID.Value);
                    //proInfo.
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                    if (!string.IsNullOrWhiteSpace(proInfo.regNumber) && proInfo.regSerialNumber.HasValue)
                    {
                        ViewBag.IsCanModifyRegNum = false;
                        model.regSerialNumber = proInfo.regSerialNumber.Value.ToString();
                        model.regNumber = proInfo.regNumber;
                        model.strRegNumber = model.regNumber;
                    }
                    else
                    {
                        var year = DateTime.Now.Year.ToString();
                        model.regSerialNumber = bllProj.GetMaxRegNumber(DateTime.Now.Year);
                        model.regNumber = "ITMCTR";
                        model.strRegNumber = model.regNumber + year + model.regSerialNumber;
                    }

                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();

            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);

            var list = Common.CommonList.getSingleSecAuditState();
            ViewBag.AuditStateList = new SelectList(list, "id", "name");


            ViewBag.IsBack = new SelectList(CommonList.getFalseOrTrue(), "Key", IsLang("en-US") ? "EN" : "CN");

            return View(model);
        }
        [HttpPost]
        public JsonResult SingleSendNumber(Guid Pid, string regSerialNumber, string regNumber)
        {
            var model = new Core.BLL.SA_NewProjectBLL().GetById(Pid);
            if (string.IsNullOrWhiteSpace(model.regNumber) && regSerialNumber.Length != 10)
            {
                return WriteJsonResponse(false, "注册号必须为8位", false);
            }
            if (new Core.BLL.SA_NewProjectBLL().GetAll().Exists(x => x.Pid != model.Pid && x.publicTitleCN == model.publicTitleCN))
            {
                return WriteJsonResponse(false, "项目名称已存在，请重新填写!", false);
            }
            if (model.taskStatus != 5)
            {
                var strStatus = GetStatusName.GettaskStatusName(model.taskStatus ?? 0);

                return Json(new { success = false, message = $"该项目状态为{strStatus},无法操作此项目", IsRedirect = true }, JsonRequestBehavior.AllowGet);
            }
            var checkNum = new Core.BLL.SA_NewProjectBLL().CheckRegNumber(regSerialNumber, regNumber, Pid);
            if (!checkNum)
            {
                return WriteJsonResponse(false, "注册号重复", false);
            }
            var res = new Core.BLL.SA_NewProjectBLL().SendNumber(Pid.ToString(),1,"", Models.SysUserProfile.CurrentProfile.UserID, regSerialNumber, regNumber);
            if (res)
            {
                return WriteJsonResponse(true, "操作成功/Operated Successfully", false);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }

        [HttpPost]
        public JsonResult SingleSendNumberBack(Guid Pid)
        {
            var res = new Core.BLL.SA_NewProjectBLL().SendNumberBack(Pid);
            if (res)
            {
                return WriteJsonResponse(true, "操作成功/Operated Successfully", false);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }
        #endregion
        #region 0审待审核
        public ActionResult ProFinalReview(int pageNo = 0, string SortName = "flowRecordTime", string OrderByDescending = "desc")
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtRegNo = Request["txtRegNo"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
            sql = sql.Where("taskStatus =9 ").OrderBy($"{SortName} {OrderByDescending}");//2已分配，且执行人为当前用户,
            if (OrderByDescending == "desc")
            {
                ViewData["sort"] = "asc";
            }
            else
            {
                ViewData["sort"] = "desc";
            }
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 单个复审

        public ActionResult SingleFinalAudit(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.RegTime = proInfo.regTime.HasValue ? proInfo.regTime.Value.ToString("yyyy-MM-dd") : "";
                    model.createUserName = new Core.BLL.SA_UsersBLL().GetUserName(proInfo.createUserID.Value);
                    //proInfo.
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                    if (!string.IsNullOrWhiteSpace(proInfo.regNumber) && proInfo.regSerialNumber.HasValue)
                    {
                        ViewBag.IsCanModifyRegNum = false;
                        model.regSerialNumber = proInfo.regSerialNumber.Value.ToString();
                        model.regNumber = proInfo.regNumber;
                        model.strRegNumber = model.regNumber;
                    }
                    else
                    {
                        var year = DateTime.Now.Year.ToString();
                        model.regSerialNumber = bllProj.GetMaxRegNumber(DateTime.Now.Year);
                        model.regNumber = "ITMCTR";
                        model.strRegNumber = model.regNumber + year + model.regSerialNumber;
                    }

                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();

            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);

            var list = Common.CommonList.getSingleSecAuditStateSuccessful();
            ViewBag.AuditStateList = new SelectList(list, "id", "name");
            return View(model);
        }

        [HttpPost]
        public JsonResult SingleFinalAudit(Guid Pid, int AuditState, string RejectReason)
        {
            var model = new Core.BLL.SA_NewProjectBLL().GetById(Pid);
            if (model.taskStatus!=9)
            {
                var strStatus = GetStatusName.GettaskStatusName(model.taskStatus ?? 0);

                return Json(new { success = false, message = $"该项目状态为{strStatus},无法操作此项目", IsRedirect = true }, JsonRequestBehavior.AllowGet);
            }
            var res = new Core.BLL.SA_NewProjectBLL().SingleFinalAudit(Pid.ToString(), AuditState == 3 ? 1 : 2, RejectReason, Models.SysUserProfile.CurrentProfile.UserID);
            if (res)
            {
                return WriteJsonResponse(true, "操作成功/Operated Successfully", false);
            }
            else
            {
                return WriteJsonResponse(false, "操作失败", false);
            }
        }

        #endregion
        #region 全部项目
        public ActionResult ProAll(int pageNo = 0, string SortName = "flowRecordTime", string OrderByDescending = "desc")
        {
            ViewBag.txtTitle = Request["txtTitle"];
            ViewBag.txtRegNo = Request["txtRegNo"];

            Core.BLL.SA_NewProjectBLL bll = new Core.BLL.SA_NewProjectBLL();
            var sql = PetaPoco.Sql.Builder.Select("*").From("[V_NewProject]");
            if (!string.IsNullOrEmpty(ViewBag.txtTitle))
                sql = sql.Where(" (publicTitleCN like @0 or publicTitleEN like @0)", $"%{ViewBag.txtTitle}%");
            if (!string.IsNullOrEmpty(ViewBag.txtRegNo))
                sql = sql.Where(" regNumber = @0 ", ViewBag.txtRegNo);
            sql = sql.Where("taskStatus >= 1 AND Status >=0 ").OrderBy($"{SortName} {OrderByDescending}");//2已分配，且执行人为当前用户,
            if (OrderByDescending == "desc")
            {
                ViewData["sort"] = "asc";
            }
            else
            {
                ViewData["sort"] = "desc";
            }
            var page_ret = bll.GetPageList<Core.Models.V_NewProject>(pageNo, PageSize, sql);
            page_ret.Context = GetUrlParms();
            return View(page_ret);
        }
        #endregion
        #region 召回 Recall
        public ActionResult Recall(Guid? pid)
        {
            UserProjectModel model = new UserProjectModel();
            var bll = new Core.BLL.SA_StudyDictionaryBLL();
            var bllProj = new Core.BLL.SA_NewProjectBLL();
            if (pid.HasValue)
            {
                var proInfo = bllProj.GetById(pid.Value);
                if (proInfo != null)
                {
                    var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
                    var bllInterventions = new Core.BLL.SA_InterventionsBLL();
                    var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
                    var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
                    var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
                    var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

                    model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(pid.Value);
                    model.Interventions = bllInterventions.GetByProjectId(pid.Value);
                    model.Diagnostic = bllDiagnostic.GetByProjectId(pid.Value);
                    model.ResearchAddress = bllResearchAddress.GetByProjectId(pid.Value);
                    model.Outcomes = bllOutcomes.GetByProjectId(pid.Value);
                    model.CollectingSample = bllCollectingSample.GetByProjectId(pid.Value);

                    model.hdnSecSponsorCount = model.SecondarySponsor.Count;
                    model.hdnInterCount = model.Interventions.Count;
                    model.hdnPlaceCount = model.ResearchAddress.Count;
                    model.hdnIndexCount = model.Outcomes.Count;
                    model.hdnSpecimenCount = model.CollectingSample.Count;

                    model.pid = proInfo.Pid;
                    model.Project = proInfo;
                    model.listStudyType = proInfo.studyTypeID;
                    model.listStudyDesign = proInfo.studyDesignID;
                    model.listStudyStage = proInfo.studyPhaseID;
                    model.listLang = proInfo.filloutLanguage;
                    model.listRegStatus = proInfo.registrationStatus;
                    model.txtSubjectID = proInfo.studyID;
                    model.txtSecondaryID = proInfo.secondaryID;
                    model.txtApplierPhone = proInfo.applicantTelephone;
                    model.txtStudyLeaderPhone = proInfo.studyTelephone;
                    model.txtApplierFax = proInfo.applicanFax;
                    model.txtStudyLeaderFax = proInfo.studyFax;
                    model.txtApplierEmail = proInfo.applicantEmail;
                    model.txtStudyLeaderEmail = proInfo.studyEmail;
                    model.txtApplierWebsite = proInfo.applicantWebsite;
                    model.txtStudyLeaderWebsite = proInfo.studyWebsite;
                    model.txtApplierPostcode = proInfo.applicantPostcode;
                    model.txtStudyLeaderPostcode = proInfo.studyPostcode;
                    model.txtStudyAilmentCode = proInfo.targetCode;
                    model.txtNationalFDASanctionDate = proInfo.dataSFDA;
                    model.txtStudyExecuteTime = proInfo.studyTimeStart;
                    model.txtStudyEndTime = proInfo.studyTimeEnd;
                    model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
                    model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
                    model.txtTotalSampleSize = proInfo.totalSampleSize;
                    model.listRecruitmentStatus = proInfo.recruitingStatus;
                    model.txtMinAge = proInfo.ageMin;
                    model.txtMaxAge = proInfo.ageMax;
                    model.listGender = proInfo.sex;
                    model.listAgreeToSign = proInfo.signConsent;
                    model.txtFollowUpFrequency = proInfo.followupTime;
                    model.listFollowUpTimeUnit = proInfo.followup;
                    model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
                    model.txtNationalFDASanctionNO = proInfo.of_SFDA;
                    model.fileNationalFDASanction = proInfo.fileSFDA;
                    model.fileStudyPlan = proInfo.studyPlanfile;
                    model.fileInformedConsent = proInfo.informedConsentfile;
                    model.fileExperimentalresults = proInfo.fileExperimentalresults;
                    model.txtTitleEn = proInfo.publicTitleEN;
                    model.txtTitleAcronymEn = proInfo.titleAcronymEN;
                    model.txtOfficialNameEn = proInfo.scientifirTitleEN;
                    model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
                    model.txtApplierEn = proInfo.applicantEN;
                    model.txtStudyLeaderEn = proInfo.studyLeaderEN;
                    model.txtApplierAddressEn = proInfo.applicantAddressEN;
                    model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
                    model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
                    model.txtSponsorEn = proInfo.primarySponsorEN;
                    model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
                    model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
                    model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
                    model.txtStudyAimEn = proInfo.objectivesStudyEN;
                    model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
                    model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
                    model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
                    model.txtGenerafionMethodEn = proInfo.randomMethodEN;
                    model.txtConcealmentEn = proInfo.processConcealmentEN;
                    model.txtBlindingEn = proInfo.blindingEN;
                    model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
                    model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
                    model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
                    //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
                    model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
                    model.txtDataChargeUnitEn = proInfo.dataManagementEN;
                    model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
                    model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
                    model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
                    model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
                    model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
                    model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
                    model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
                    model.txtTitle = proInfo.publicTitleCN;
                    model.txtTitleAcronym = proInfo.titleAcronymCN;
                    model.txtOfficialName = proInfo.scientifirTitleCN;
                    model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
                    model.txtApplier = proInfo.applicantCN;
                    model.txtStudyLeader = proInfo.studyLeaderCN;
                    model.txtApplierAddress = proInfo.applicantAddressCN;
                    model.txtStudyLeaderAddress = proInfo.studyAddressCN;
                    model.txtApplierCompany = proInfo.applicantInstitutionCN;
                    model.txtSponsor = proInfo.primarySponsorCN;
                    model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
                    model.txtSourceOfSpends = proInfo.sourceFundingCN;
                    model.txtStudyAilment = proInfo.targetDiseaseCN;
                    model.txtStudyAim = proInfo.objectivesStudyCN;
                    model.txtDrugsComposition = proInfo.contentsDrugCN;
                    model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
                    model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
                    model.txtGenerafionMethod = proInfo.randomMethodCN;
                    model.txtConcealment = proInfo.processConcealmentCN;
                    model.txtBlinding = proInfo.blindingCN;
                    model.txtUncoverPrinciple = proInfo.RulesblindingCN;
                    model.txtStatisticalMethod = proInfo.statisticalMethodCN;
                    model.txtStatisticalEffect = proInfo.calculatedResultsCN;
                    model.txtDataChargeUnit = proInfo.dataManagementCN;
                    model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
                    model.txtUTN = proInfo.UTN;
                    model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
                    model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
                    model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
                    model.txtStudyReport = proInfo.studyReport;
                    model.txtStudyReportEN = proInfo.studyReportEN;
                    model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
                    model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
                    model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
                    model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
                    model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
                    model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
                    model.ReleaseNumber = proInfo.ReleaseNumber;
                }
            }
            var dicts = bll.GetALLBySort();
            ViewBag.PagelistStudyType = new SelectList(dicts.Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyDesign = new SelectList(dicts.Where(x => x.StudyName == "StudyDesign" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            ViewBag.PagelistStudyStage = new SelectList(dicts.Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).OrderBy(x => x.Sort).Select(x => new { Key = x.StudyValue, Text = $"{x.DescriptionCn}/{x.DescriptionEn}" }), "Key", "Text");
            var langlist = CommonList.getLanglist();
            var listRegStatuslist = CommonList.getRegStatuslist();
            var listRecruitmentStatuslist = CommonList.getRecruitmentStatuslist();
            var listGenderlist = CommonList.getGenderlist();
            var listFollowUpTimeUnitlist = CommonList.getFollowUpTimeUnitlist();
            var listStatisticalEffectChiCTRPubliclist = CommonList.getStatisticalEffectChiCTRPubliclist();
            var txtDataCollectionUnitlist = CommonList.getDataCollectionUnitlist();
            var txtDataManagemenBoardlist = CommonList.getDataManagemenBoardlist();
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);
            ViewBag.listRegStatusDict = new SelectList(listRegStatuslist, "Key", "Value", model?.listRegStatus);
            ViewBag.listRecruitmentStatusDict = new SelectList(listRecruitmentStatuslist, "Key", "Value", model?.listRecruitmentStatus);
            ViewBag.listGenderDict = new SelectList(listGenderlist, "Key", "Value", model?.listGender);
            ViewBag.listFollowUpTimeUnitDict = new SelectList(listFollowUpTimeUnitlist, "Key", "Value", model?.listFollowUpTimeUnit);
            ViewBag.listStatisticalEffectChiCTRPublicDict = new SelectList(listStatisticalEffectChiCTRPubliclist, "Key", "Value", model?.listStatisticalEffectChiCTRPublic);
            ViewBag.txtDataCollectionUnitDict = new SelectList(txtDataCollectionUnitlist, "Key", "Value", model?.txtDataCollectionUnit);
            ViewBag.txtDataManagemenBoardDict = new SelectList(txtDataManagemenBoardlist, "Key", "Value", model?.txtDataManagemenBoard);
            ViewBag.listLangDict = new SelectList(langlist, "Key", "Value", model?.listLang);

            var rid = Guid.Parse("3d64080d-042f-45da-84be-570b360236a6");
            var userList = new Core.BLL.SA_SysUserBLL().GetAll().Where(x => x.ParentId == SysUserProfile.CurrentProfile.UserID.ToString()).ToList();

            var list = new List<Common.dataObjectList>();
            var temp = userList.ConvertAll(x => new Common.dataObjectList
            {
                id = x.Uid.ToString(),
                name = $"{x.Username}[{x.Name}]({bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count})",
                Count = bllProj.GetListByExecuteTaskSysUserId(x.Uid).Count
            });
            list.Add(new Common.dataObjectList { id = "", name = "请选择" });
            list.AddRange(temp.OrderBy(x => x.Count).ToList());
            ViewBag.UserList = new SelectList(list, "id", "name");


            ViewBag.IsBack = new SelectList(CommonList.getFalseOrTrue(), "Key", IsLang("en-US") ? "EN" : "CN");

            return View(model);
        }

        [HttpPost]
        public JsonResult Recall(Guid pid)
        {
            var res = new Core.BLL.SA_NewProjectBLL().Recall(pid.ToString(),Models.SysUserProfile.CurrentProfile.UserID);

            if (res)
            {
                return WriteJsonResponse(true, "分配成功", false);
            }
            else
            {
                return WriteJsonResponse(false, "分配失败", false);
            }
        }
        #endregion
    }
}