using ITMCTR.App.Common;
using ITMCTR.App.Filter;
using ITMCTR.App.Models;
using PetaPoco;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace ITMCTR.App.Controllers
{
    public class BaseController : Controller
    {
        public readonly int PageSize = 15;
        [NonAction]
        public bool IsLang(string lang)
        {
            return CultureInfo.CurrentUICulture.Name.Equals(lang);
        }
        [NonAction]
        public JsonResult WriteJsonResponse(bool success, string message = "", bool clearform = true, string callback = "", string callparams = null)
        {
            return Json(new
            {
                success = success,
                message = message,
                clearform = clearform,
                callback = callback,
                callparams = callparams
            }, JsonRequestBehavior.AllowGet);
        }
        [NonAction]
        public JsonResult WriteJsonResponse(bool success, string message, string redirect, string target = "")
        {
            return Json(new
            {
                success = success,
                message = message,
                redirect = new
                {
                    url = redirect,
                    target = target
                }
            }, JsonRequestBehavior.AllowGet);
        }
        [NonAction]
        public void SetMessageAlert(bool error,
            string title,
            string content,
            string redictUrl = "",
            string buttonText = "确定")
        {
            ViewBag.MessageAlert = new MessageInfoModel(error, title, content, redictUrl, buttonText);
        }
        [NonAction]

        public Page<Models.UserProjectViewModel> ConvertProject(Page<Core.Models.V_NewProject> page_ret)
        {
            var dict = CommonList.getRecruitmentStatuslist();
            var dictStudyPhase = new Core.BLL.SA_StudyDictionaryBLL().GetALLBySort().Where(x => x.StudyName == "StudyPhase" && x.Ishide == 1).OrderBy(x => x.Sort).ToList();
            var dictStudyType = new Core.BLL.SA_StudyDictionaryBLL().GetALLBySort().Where(x => x.StudyName == "StudyType" && x.Ishide == 1).OrderBy(x => x.Sort).ToList();
            var dictStatus = CommonList.getStatusList();
            var resPage = new Page<Models.UserProjectViewModel>();
            Func<string, int, string> fun = (y, lang) =>
            {
                var info = dictStudyPhase.FirstOrDefault(x => x.StudyValue == y);
                if (info != null)
                {
                    switch (lang)
                    {
                        case 0: return info.DescriptionCn;
                        case 1: return info.DescriptionEn;
                        default:
                            break;
                    }
                }
                return "";
            };
            Func<string, int, string> fun2 = (y, lang) =>
            {
                var info = dict.FirstOrDefault(x => x.Key == y);
                if (info != null)
                {
                    switch (lang)
                    {
                        case 0: return info.CN;
                        case 1: return info.EN;
                        default:
                            break;
                    }
                }
                return "";
            };
            Func<int?, int, string, string> fun3 = (y, lang,number) =>
            {
                if(y==3 && string.IsNullOrWhiteSpace(number))
                {
                    var info = dictStatus.FirstOrDefault(x => x.Key == 1);
                    if (info != null)
                    {
                        switch (lang)
                        {
                            case 0: return info.CN;
                            case 1: return info.EN;
                            default:
                                break;
                        }
                    }
                }
                else
                {
                    var info = dictStatus.FirstOrDefault(x => x.Key == y);
                    if (info != null)
                    {
                        switch (lang)
                        {
                            case 0: return info.CN;
                            case 1: return info.EN;
                            default:
                                break;
                        }
                    }
                }
                
                return "";
            };
            Func<string, int, string> fun4 = (y, lang) =>
            {
                var info = dictStudyType.FirstOrDefault(x => x.StudyValue == y);
                if (info != null)
                {
                    switch (lang)
                    {
                        case 0: return info.DescriptionCn;
                        case 1: return info.DescriptionEn;
                        default:
                            break;
                    }
                }
                return "";
            };

            resPage.Items = page_ret.Items.ConvertAll(x => new Models.UserProjectViewModel
            {
                IsEnglist = x.filloutLanguage == "1009002",
                IsChinese = x.filloutLanguage == "1009001",
                pid = x.Pid,
                publicTitleCN = x.publicTitleCN,
                publicTitleEN = x.publicTitleEN,
                recruitingStatusNameCN = fun2(x.recruitingStatus, 0),
                recruitingStatusNameEN = fun2(x.recruitingStatus, 1),
                regNumber = x.regNumber,
                regSerialNumber = x.regSerialNumber,
                regTime = x.regTime,
                regNumberTime = x.regNumberTime,
                status = x.status,
                taskstatus = x.taskStatus,
                statusCn = fun3(x.status, 0,x.regNumber),
                statusEn = fun3(x.status, 1, x.regNumber),
                studyTypeNameCN = fun4(x.studyTypeID, 0),
                studyTypeNameEN = fun4(x.studyTypeID, 1),
                studyPhaseNameCN = fun(x.studyPhaseID, 0),
                studyPhaseNameEN = fun(x.studyPhaseID, 1),
                applierCompanyCN = x.applicantInstitutionCN,
                applierCompanyEN = x.applicantInstitutionEN,
                ReleaseNumber = x.ReleaseNumber,
                modifyTime = x.modifyTime,
                secondaryID = x.secondaryID,
                executeTaskUser = x.executeTaskUser,
                sendTaskUser = x.sendTaskUser
            }).ToList();
            resPage.ItemsPerPage = page_ret.ItemsPerPage;
            resPage.TotalItems = page_ret.TotalItems;
            resPage.TotalPages = page_ret.TotalPages;
            resPage.CurrentPage = page_ret.CurrentPage;
            return resPage;
        }

        [NonAction]
        public UserProjectModel ConvertUserProject(Core.Models.SA_NewProject proInfo)
        {
            UserProjectModel model = new UserProjectModel();
            if (proInfo == null)
            {
                return model;
            }

            var bll = new Core.BLL.SA_StudyDictionaryBLL();

            var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorBLL();
            var bllInterventions = new Core.BLL.SA_InterventionsBLL();
            var bllDiagnostic = new Core.BLL.SA_DiagnosticBLL();
            var bllResearchAddress = new Core.BLL.SA_ResearchAddressBLL();
            var bllOutcomes = new Core.BLL.SA_OutcomesBLL();
            var bllCollectingSample = new Core.BLL.SA_CollectingSampleBLL();

            model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(proInfo.Pid);
            model.Interventions = bllInterventions.GetByProjectId(proInfo.Pid);
            model.Diagnostic = bllDiagnostic.GetByProjectId(proInfo.Pid);
            model.ResearchAddress = bllResearchAddress.GetByProjectId(proInfo.Pid);
            model.Outcomes = bllOutcomes.GetByProjectId(proInfo.Pid);
            model.CollectingSample = bllCollectingSample.GetByProjectId(proInfo.Pid);

            model.hdnSecSponsorCount = model.SecondarySponsor.Count;
            model.hdnInterCount = model.Interventions.Count;
            model.hdnPlaceCount = model.ResearchAddress.Count;
            model.hdnIndexCount = model.Outcomes.Count;
            model.hdnSpecimenCount = model.CollectingSample.Count;

            model.pid = proInfo.Pid;
            model.regNumber = proInfo.regNumber;
            model.Project = proInfo;
            model.listStudyType = proInfo.studyTypeID;
            model.listStudyDesign = proInfo.studyDesignID;
            model.listStudyStage = proInfo.studyPhaseID;
            model.listLang = proInfo.filloutLanguage;
            model.listRegStatus = proInfo.registrationStatus;
            model.txtSubjectID = proInfo.studyID;
            model.txtSecondaryID = proInfo.secondaryID;
            model.txtApplierPhone = proInfo.applicantTelephone;
            model.txtStudyLeaderPhone = proInfo.studyTelephone;
            model.txtApplierFax = proInfo.applicanFax;
            model.txtStudyLeaderFax = proInfo.studyFax;
            model.txtApplierEmail = proInfo.applicantEmail;
            model.txtStudyLeaderEmail = proInfo.studyEmail;
            model.txtApplierWebsite = proInfo.applicantWebsite;
            model.txtStudyLeaderWebsite = proInfo.studyWebsite;
            model.txtApplierPostcode = proInfo.applicantPostcode;
            model.txtStudyLeaderPostcode = proInfo.studyPostcode;
            model.txtStudyAilmentCode = proInfo.targetCode;
            model.txtNationalFDASanctionDate = proInfo.dataSFDA;
            model.txtStudyExecuteTime = proInfo.studyTimeStart;
            model.txtStudyEndTime = proInfo.studyTimeEnd;
            model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
            model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
            model.txtTotalSampleSize = proInfo.totalSampleSize;
            model.listRecruitmentStatus = proInfo.recruitingStatus;
            model.txtMinAge = proInfo.ageMin;
            model.txtMaxAge = proInfo.ageMax;
            model.listGender = proInfo.sex;
            model.listAgreeToSign = proInfo.signConsent;
            model.txtFollowUpFrequency = proInfo.followupTime;
            model.listFollowUpTimeUnit = proInfo.followup;
            model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
            model.txtNationalFDASanctionNO = proInfo.of_SFDA;
            model.fileNationalFDASanction = proInfo.fileSFDA;
            model.fileStudyPlan = proInfo.studyPlanfile;
            model.fileInformedConsent = proInfo.informedConsentfile;
            model.fileExperimentalresults = proInfo.fileExperimentalresults;
            model.txtTitleEn = proInfo.publicTitleEN;
            model.txtTitleAcronymEn = proInfo.titleAcronymEN;
            model.txtOfficialNameEn = proInfo.scientifirTitleEN;
            model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
            model.txtApplierEn = proInfo.applicantEN;
            model.txtStudyLeaderEn = proInfo.studyLeaderEN;
            model.txtApplierAddressEn = proInfo.applicantAddressEN;
            model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
            model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
            model.txtSponsorEn = proInfo.primarySponsorEN;
            model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
            model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
            model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
            model.txtStudyAimEn = proInfo.objectivesStudyEN;
            model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
            model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
            model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
            model.txtGenerafionMethodEn = proInfo.randomMethodEN;
            model.txtConcealmentEn = proInfo.processConcealmentEN;
            model.txtBlindingEn = proInfo.blindingEN;
            model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
            model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
            model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
            //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
            model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
            model.txtDataChargeUnitEn = proInfo.dataManagementEN;
            model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
            model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
            model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
            model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
            model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
            model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
            model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
            model.txtTitle = proInfo.publicTitleCN;
            model.txtTitleAcronym = proInfo.titleAcronymCN;
            model.txtOfficialName = proInfo.scientifirTitleCN;
            model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
            model.txtApplier = proInfo.applicantCN;
            model.txtStudyLeader = proInfo.studyLeaderCN;
            model.txtApplierAddress = proInfo.applicantAddressCN;
            model.txtStudyLeaderAddress = proInfo.studyAddressCN;
            model.txtApplierCompany = proInfo.applicantInstitutionCN;
            model.txtSponsor = proInfo.primarySponsorCN;
            model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
            model.txtSourceOfSpends = proInfo.sourceFundingCN;
            model.txtStudyAilment = proInfo.targetDiseaseCN;
            model.txtStudyAim = proInfo.objectivesStudyCN;
            model.txtDrugsComposition = proInfo.contentsDrugCN;
            model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
            model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
            model.txtGenerafionMethod = proInfo.randomMethodCN;
            model.txtConcealment = proInfo.processConcealmentCN;
            model.txtBlinding = proInfo.blindingCN;
            model.txtUncoverPrinciple = proInfo.RulesblindingCN;
            model.txtStatisticalMethod = proInfo.statisticalMethodCN;
            model.txtStatisticalEffect = proInfo.calculatedResultsCN;
            model.txtDataChargeUnit = proInfo.dataManagementCN;
            model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
            model.txtUTN = proInfo.UTN;
            model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
            model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
            model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
            model.txtStudyReport = proInfo.studyReport;
            model.txtStudyReportEN = proInfo.studyReportEN;
            model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
            model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
            model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
            model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
            model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
            model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
            model.reverifyFailReason = proInfo.reverifyFailReason;
            model.txtRegNumber = proInfo.regNumber;
            model.txtModifyTime = proInfo.modifyTime;
            model.txtRegTime = proInfo.regTime;
            model.ReleaseNumber = proInfo.ReleaseNumber;
            model.txtRegNumberTime = proInfo.regNumberTime;

            model.Status = -1;
            if (proInfo.status.HasValue)
                model.Status = proInfo.status.Value;
            model.TaskStatus = -1;
            if (proInfo.taskStatus.HasValue)
                model.TaskStatus = proInfo.taskStatus.Value;

            var dicts = bll.GetALLBySort();
            var find = dicts.FirstOrDefault(x => x.StudyName == "StudyType" && x.Ishide == 1 && x.StudyValue == proInfo.studyTypeID);
            model.StudyTypeCn = find?.DescriptionCn;
            model.StudyTypeEn = find?.DescriptionEn;

            find = dicts.FirstOrDefault(x => x.StudyName == "StudyDesign" && x.Ishide == 1 && x.StudyValue == proInfo.studyDesignID);
            model.StudyDesignCn = find?.DescriptionCn;
            model.StudyDesignEn = find?.DescriptionEn;

            find = dicts.FirstOrDefault(x => x.StudyName == "StudyPhase" && x.Ishide == 1 && x.StudyValue == proInfo.studyPhaseID);
            model.StudyPhaseCn = find?.DescriptionCn;
            model.StudyPhaseEn = find?.DescriptionEn;

            var find2 = CommonList.getLanglist().FirstOrDefault(x => x.Key == proInfo.filloutLanguage);
            model.LangCn = find2?.CN;
            model.LangEn = find2?.EN;

            var find3 = CommonList.getRegStatuslist().FirstOrDefault(x => x.Key == proInfo.registrationStatus);
            model.RegStatusCn = find3?.CN;
            model.RegStatusEn = find3?.EN;

            var find4 = CommonList.getRecruitmentStatuslist().FirstOrDefault(x => x.Key == proInfo.recruitingStatus);
            model.RecruitmentStatusCn = find4?.CN;
            model.RecruitmentStatusEn = find4?.EN;

            var find5 = CommonList.getGenderlist().FirstOrDefault(x => x.Key == proInfo.sex);
            model.GenderCn = find5?.CN;
            model.GenderEn = find5?.EN;


            var find6 = CommonList.getFollowUpTimeUnitlist().FirstOrDefault(x => x.Key == proInfo.followup);
            model.FollowUpTimeUnitCn = find6?.CN;
            model.FollowUpTimeUnitEn = find6?.EN;

            var find7 = CommonList.getStatisticalEffectChiCTRPubliclist().FirstOrDefault(x => x.Key == proInfo.whetherPublic);
            model.StatisticalEffectChiCTRPublicCn = find7?.CN;
            model.StatisticalEffectChiCTRPublicEn = find7?.EN;

            var find8 = CommonList.getDataCollectionUnitlist().FirstOrDefault(x => x.Key == proInfo.DataCollectionUnit);
            model.DataCollectionUnitCn = find8?.CN;
            model.DataCollectionUnitEn = find8?.EN;


            var find9 = CommonList.getDataManagemenBoardlist().FirstOrDefault(x => x.Key == proInfo.DataManagemenBoard);
            model.DataManagemenBoardCn = find9?.CN;
            model.DataManagemenBoardEn = find9?.EN;

            return model;
        }

        [NonAction]
        public UserProjectHistoryModel ConvertUserProjectHistory(Core.Models.SA_NewProjectHistory proInfo)
        {
            UserProjectHistoryModel model = new UserProjectHistoryModel();
            if (proInfo == null)
            {
                return model;
            }

            var bll = new Core.BLL.SA_StudyDictionaryBLL();

            var bllSecondarySponsor = new Core.BLL.SA_SecondarySponsorHistoryBLL();
            var bllInterventions = new Core.BLL.SA_InterventionsHistoryBLL();
            var bllDiagnostic = new Core.BLL.SA_DiagnosticHistoryBLL();
            var bllResearchAddress = new Core.BLL.SA_ResearchAddressHistoryBLL();
            var bllOutcomes = new Core.BLL.SA_OutcomesHistoryBLL();
            var bllCollectingSample = new Core.BLL.SA_CollectingSampleHistoryBLL();

            model.SecondarySponsor = bllSecondarySponsor.GetByProjectId(proInfo.Phid);
            model.Interventions = bllInterventions.GetByProjectId(proInfo.Phid);
            model.Diagnostic = bllDiagnostic.GetByProjectId(proInfo.Phid);
            model.ResearchAddress = bllResearchAddress.GetByProjectId(proInfo.Phid);
            model.Outcomes = bllOutcomes.GetByProjectId(proInfo.Phid);
            model.CollectingSample = bllCollectingSample.GetByProjectId(proInfo.Phid);

            model.hdnSecSponsorCount = model.SecondarySponsor.Count;
            model.hdnInterCount = model.Interventions.Count;
            model.hdnPlaceCount = model.ResearchAddress.Count;
            model.hdnIndexCount = model.Outcomes.Count;
            model.hdnSpecimenCount = model.CollectingSample.Count;

            model.pid = proInfo.Pid;
            model.regNumber = proInfo.regNumber;
            model.Project = proInfo;
            model.listStudyType = proInfo.studyTypeID;
            model.listStudyDesign = proInfo.studyDesignID;
            model.listStudyStage = proInfo.studyPhaseID;
            model.listLang = proInfo.filloutLanguage;
            model.listRegStatus = proInfo.registrationStatus;
            model.txtSubjectID = proInfo.studyID;
            model.txtSecondaryID = proInfo.secondaryID;
            model.txtApplierPhone = proInfo.applicantTelephone;
            model.txtStudyLeaderPhone = proInfo.studyTelephone;
            model.txtApplierFax = proInfo.applicanFax;
            model.txtStudyLeaderFax = proInfo.studyFax;
            model.txtApplierEmail = proInfo.applicantEmail;
            model.txtStudyLeaderEmail = proInfo.studyEmail;
            model.txtApplierWebsite = proInfo.applicantWebsite;
            model.txtStudyLeaderWebsite = proInfo.studyWebsite;
            model.txtApplierPostcode = proInfo.applicantPostcode;
            model.txtStudyLeaderPostcode = proInfo.studyPostcode;
            model.txtStudyAilmentCode = proInfo.targetCode;
            model.txtNationalFDASanctionDate = proInfo.dataSFDA;
            model.txtStudyExecuteTime = proInfo.studyTimeStart;
            model.txtStudyEndTime = proInfo.studyTimeEnd;
            model.txtEnlistBeginTime = proInfo.recruitingTimeStart;
            model.txtEnlistEndTime = proInfo.recruitingTimeEnd;
            model.txtTotalSampleSize = proInfo.totalSampleSize;
            model.listRecruitmentStatus = proInfo.recruitingStatus;
            model.txtMinAge = proInfo.ageMin;
            model.txtMaxAge = proInfo.ageMax;
            model.listGender = proInfo.sex;
            model.listAgreeToSign = proInfo.signConsent;
            model.txtFollowUpFrequency = proInfo.followupTime;
            model.listFollowUpTimeUnit = proInfo.followup;
            model.listStatisticalEffectChiCTRPublic = proInfo.whetherPublic;
            model.txtNationalFDASanctionNO = proInfo.of_SFDA;
            model.fileNationalFDASanction = proInfo.fileSFDA;
            model.fileStudyPlan = proInfo.studyPlanfile;
            model.fileInformedConsent = proInfo.informedConsentfile;
            model.fileExperimentalresults = proInfo.fileExperimentalresults;
            model.txtTitleEn = proInfo.publicTitleEN;
            model.txtTitleAcronymEn = proInfo.titleAcronymEN;
            model.txtOfficialNameEn = proInfo.scientifirTitleEN;
            model.txtOfficialNameAcronymEn = proInfo.scientifirAcronymEN;
            model.txtApplierEn = proInfo.applicantEN;
            model.txtStudyLeaderEn = proInfo.studyLeaderEN;
            model.txtApplierAddressEn = proInfo.applicantAddressEN;
            model.txtStudyLeaderAddressEn = proInfo.studyAddressEN;
            model.txtApplierCompanyEn = proInfo.applicantInstitutionEN;
            model.txtSponsorEn = proInfo.primarySponsorEN;
            model.txtSponsorAddressEn = proInfo.primarySponsorAddressEN;
            model.txtSourceOfSpendsEn = proInfo.sourceFundingEN;
            model.txtStudyAilmentEn = proInfo.targetDiseaseEN;
            model.txtStudyAimEn = proInfo.objectivesStudyEN;
            model.txtDrugsCompositionEn = proInfo.contentsDrugEN;
            model.txtSelectionCriteriaEn = proInfo.inclusionCriteriaEN;
            model.txtEliminateCriteriaEn = proInfo.exclusionCrteriaEN;
            model.txtGenerafionMethodEn = proInfo.randomMethodEN;
            model.txtConcealmentEn = proInfo.processConcealmentEN;
            model.txtBlindingEn = proInfo.blindingEN;
            model.txtUncoverPrincipleEn = proInfo.RulesblindingEN;
            model.txtStatisticalMethodEn = proInfo.statisticalMethodEN;
            model.txtStatisticalEffectEn = proInfo.calculatedResultsEN;
            //proInfo.DataCollectionEN =txtDataCollectionUnitEn;
            model.txtDataCollectionUnit = proInfo.DataCollectionUnit;
            model.txtDataChargeUnitEn = proInfo.dataManagementEN;
            model.txtDataAnalysisUnitEn = proInfo.dataAnalysisEN;
            model.listEthicalCommitteeSanction = proInfo.approvedCommittee;
            model.txtEthicalCommitteeFileID = proInfo.ethicalCommitteeFileID;
            model.fileEthicalCommittee = proInfo.fileEthicalCommittee;
            model.txtEthicalCommitteeName = proInfo.ethicalCommitteeName;
            model.txtEthicalCommitteeNameEn = proInfo.ethicalCommitteeNameEN;
            model.txtEthicalCommitteeSanctionDate = proInfo.ethicalCommitteeSanctionDate;
            model.txtTitle = proInfo.publicTitleCN;
            model.txtTitleAcronym = proInfo.titleAcronymCN;
            model.txtOfficialName = proInfo.scientifirTitleCN;
            model.txtOfficialNameAcronym = proInfo.scientifirAcronymCN;
            model.txtApplier = proInfo.applicantCN;
            model.txtStudyLeader = proInfo.studyLeaderCN;
            model.txtApplierAddress = proInfo.applicantAddressCN;
            model.txtStudyLeaderAddress = proInfo.studyAddressCN;
            model.txtApplierCompany = proInfo.applicantInstitutionCN;
            model.txtSponsor = proInfo.primarySponsorCN;
            model.txtSponsorAddress = proInfo.primarySponsorAddressCN;
            model.txtSourceOfSpends = proInfo.sourceFundingCN;
            model.txtStudyAilment = proInfo.targetDiseaseCN;
            model.txtStudyAim = proInfo.objectivesStudyCN;
            model.txtDrugsComposition = proInfo.contentsDrugCN;
            model.txtSelectionCriteria = proInfo.inclusionCriteriaCN;
            model.txtEliminateCriteria = proInfo.exclusionCrteriaCN;
            model.txtGenerafionMethod = proInfo.randomMethodCN;
            model.txtConcealment = proInfo.processConcealmentCN;
            model.txtBlinding = proInfo.blindingCN;
            model.txtUncoverPrinciple = proInfo.RulesblindingCN;
            model.txtStatisticalMethod = proInfo.statisticalMethodCN;
            model.txtStatisticalEffect = proInfo.calculatedResultsCN;
            model.txtDataChargeUnit = proInfo.dataManagementCN;
            model.txtDataAnalysisUnit = proInfo.dataAnalysisCN;
            model.txtUTN = proInfo.UTN;
            model.txtStudyLeaderCompany = proInfo.studyLeaderCompanyCN;
            model.txtStudyLeaderCompanyEn = proInfo.studyLeaderCompanyEN;
            model.txtDataManagemenBoard = proInfo.DataManagemenBoard;
            model.txtStudyReport = proInfo.studyReport;
            model.txtStudyReportEN = proInfo.studyReportEN;
            model.txtEthicalCommitteeCName = proInfo.ethicalCommitteeCName;
            model.txtEthicalCommitteeCNameEN = proInfo.ethicalCommitteeCNameEN;
            model.txtEthicalCommitteeCAddress = proInfo.ethicalCommitteeCAddress;
            model.txtEthicalCommitteeCAddressEN = proInfo.ethicalCommitteeCAddressEN;
            model.txtEthicalCommitteeCPhone = proInfo.ethicalCommitteeCPhone;
            model.txtEthicalCommitteeCEmail = proInfo.ethicalCommitteeCEmail;
            model.reverifyFailReason = proInfo.reverifyFailReason;
            model.Status = -1;
            model.txtRegNumber = proInfo.regNumber;
            model.txtModifyTime = proInfo.modifyTime;
            model.txtRegTime = proInfo.regTime;
            model.txtRegNumberTime = proInfo.regNumberTime;
            model.ReleaseNumber = proInfo.ReleaseNumber;
            if (proInfo.status.HasValue)
                model.Status = proInfo.status.Value;
            model.TaskStatus = -1;
            if (proInfo.taskStatus.HasValue)
                model.TaskStatus = proInfo.taskStatus.Value;

            var dicts = bll.GetALLBySort();
            var find = dicts.FirstOrDefault(x => x.StudyName == "StudyType" && x.Ishide == 1 && x.StudyValue == proInfo.studyTypeID);
            model.StudyTypeCn = find?.DescriptionCn;
            model.StudyTypeEn = find?.DescriptionEn;

            find = dicts.FirstOrDefault(x => x.StudyName == "StudyDesign" && x.Ishide == 1 && x.StudyValue == proInfo.studyDesignID);
            model.StudyDesignCn = find?.DescriptionCn;
            model.StudyDesignEn = find?.DescriptionEn;

            find = dicts.FirstOrDefault(x => x.StudyName == "StudyPhase" && x.Ishide == 1 && x.StudyValue == proInfo.studyPhaseID);
            model.StudyPhaseCn = find?.DescriptionCn;
            model.StudyPhaseEn = find?.DescriptionEn;

            var find2 = CommonList.getLanglist().FirstOrDefault(x => x.Key == proInfo.filloutLanguage);
            model.LangCn = find2?.CN;
            model.LangEn = find2?.EN;

            var find3 = CommonList.getRegStatuslist().FirstOrDefault(x => x.Key == proInfo.registrationStatus);
            model.RegStatusCn = find3?.CN;
            model.RegStatusEn = find3?.EN;

            var find4 = CommonList.getRecruitmentStatuslist().FirstOrDefault(x => x.Key == proInfo.recruitingStatus);
            model.RecruitmentStatusCn = find4?.CN;
            model.RecruitmentStatusEn = find4?.EN;

            var find5 = CommonList.getGenderlist().FirstOrDefault(x => x.Key == proInfo.sex);
            model.GenderCn = find5?.CN;
            model.GenderEn = find5?.EN;


            var find6 = CommonList.getFollowUpTimeUnitlist().FirstOrDefault(x => x.Key == proInfo.followup);
            model.FollowUpTimeUnitCn = find6?.CN;
            model.FollowUpTimeUnitEn = find6?.EN;

            var find7 = CommonList.getStatisticalEffectChiCTRPubliclist().FirstOrDefault(x => x.Key == proInfo.whetherPublic);
            model.StatisticalEffectChiCTRPublicCn = find7?.CN;
            model.StatisticalEffectChiCTRPublicEn = find7?.EN;

            var find8 = CommonList.getDataCollectionUnitlist().FirstOrDefault(x => x.Key == proInfo.DataCollectionUnit);
            model.DataCollectionUnitCn = find8?.CN;
            model.DataCollectionUnitEn = find8?.EN;


            var find9 = CommonList.getDataManagemenBoardlist().FirstOrDefault(x => x.Key == proInfo.DataManagemenBoard);
            model.DataManagemenBoardCn = find9?.CN;
            model.DataManagemenBoardEn = find9?.EN;

            return model;
        }
        [NonAction]
        public NameValueCollection GetUrlParms()
        {
            return Request.QueryString;
        }
        [UserAuth(Validate = false)]
        [SysUserAuth(Validate = false)]
        public ActionResult SetCulture(string culture, string toculture, string url)
        {
            return Redirect(url.Replace(culture, toculture));
        }
        [UserAuth(Validate = false)]
        [SysUserAuth(Validate = false)]
        public ActionResult GetValidateCode()
        {
            ValidateCode vCode = new ValidateCode();
            string code = vCode.CreateValidateCode(4);
            Session["ValidateCode"] = code;
            byte[] bytes = vCode.CreateValidateGraphic(code);
            return File(bytes, @"image/jpeg");
        }
    }
}