<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net461" />
  <package id="Aspose.Cells" version="8.3.2.1" targetFramework="net461" />
  <package id="AutoMapper" version="7.0.1" targetFramework="net461" />
  <package id="bootstrap" version="3.4.1" targetFramework="net461" />
  <package id="jQuery" version="3.4.1" targetFramework="net461" />
  <package id="jQuery.Validation" version="1.17.0" targetFramework="net461" />
  <package id="log4net" version="2.0.14" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc.zh-Hans" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor.zh-Hans" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization.zh-Hans" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages.zh-Hans" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net461" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.11" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net461" />
  <package id="Modernizr" version="2.8.3" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="12.0.2" targetFramework="net461" />
  <package id="NPOI" version="2.5.6" targetFramework="net461" />
  <package id="Portable.BouncyCastle" version="1.8.9" targetFramework="net461" />
  <package id="SharpZipLib" version="1.3.3" targetFramework="net461" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net461" />
  <package id="WebGrease" version="1.6.0" targetFramework="net461" />
</packages>