<?xml version="1.0"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net"/>
  </configSections>
  <log4net>
    <!--错误日志-->
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="log\\LogError\\"/>
      <appendToFile value="true"/>
      <rollingStyle value="Date"/>
      <datePattern value="yyyy\\yyyyMM\\yyyyMMdd'.txt'"/>
      <staticLogFileName value="false"/>
      <param name="MaxSizeRollBackups" value="100"/>
      <layout type="log4net.Layout.PatternLayout">
        <!--每条日志末尾的文字说明-->
        <!--输出格式-->
        <!--样例：2008-03-26 13:42:32,111 [10] INFO  Log4NetDemo.MainClass [(null)] - info-->
        <conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %n日志级别：  %-5level %n错误描述：%message%newline %n"/>
      </layout>
    </appender>
    <!--Info日志-->
    <appender name="InfoAppender" type="log4net.Appender.RollingFileAppender">
      <param name="File" value="Log\\LogInfo\\"/>
      <param name="AppendToFile" value="true"/>
      <param name="MaxFileSize" value="10240"/>
      <param name="MaxSizeRollBackups" value="100"/>
      <param name="StaticLogFileName" value="false"/>
      <param name="DatePattern" value="yyyy\\yyyyMM\\yyyyMMdd'.txt'"/>
      <param name="RollingStyle" value="Date"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %n日志级别：  %-5level %n日志描述：%message%newline %n"/>
      </layout>
    </appender>
    <!--监控日志-->
    <appender name="MonitorAppender" type="log4net.Appender.RollingFileAppender">
      <param name="File" value="Log\\LogMonitor\\"/>
      <param name="AppendToFile" value="true"/>
      <param name="MaxFileSize" value="10240"/>
      <param name="MaxSizeRollBackups" value="100"/>
      <param name="StaticLogFileName" value="false"/>
      <param name="DatePattern" value="yyyy\\yyyyMM\\yyyyMMdd'.txt'"/>
      <param name="RollingStyle" value="Date"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%newline %n记录时间：%date %n线程ID:[%thread] %n日志级别：  %-5level %n跟踪描述：%message%newline %n"/>
      </layout>
    </appender>
    <!--Error日志-->
    <logger name="LogError">
      <level value="ERROR"/>
      <appender-ref ref="RollingLogFileAppender"/>
    </logger>
    <!--Info日志-->
    <logger name="LogInfo">
      <level value="INFO"/>
      <appender-ref ref="InfoAppender"/>
    </logger>
    <!--监控日志-->
    <logger name="LogMonitor">
      <level value="Monitor"/>
      <appender-ref ref="MonitorAppender"/>
    </logger>
  </log4net>
  <connectionStrings>
    <add name="default" connectionString="data source=**********;initial catalog=ITMCTR_Clinical;persist security info=True;user id=sa;password=************;" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <appSettings>
    <add key="webpages:Version" value="*******"/>
    <add key="webpages:Enabled" value="false"/>
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <add key="Email_SmtpServer" value="smtp.vip.sina.com"/>
    <add key="Email_UserName" value="<EMAIL>"/>
    <add key="Email_PassWord" value="clinicalsoft9"/>
    <add key="Email_FromMail" value="<EMAIL>"/>
    <add key="Email_AuthorImg" value=""/>
    <add key="Email_AuthorSign" value="ClinsoftServer"/>
    <add key="Email_SmtpPort" value="587"/>
    <add key="Email_ErrorTel" value="18500151883"/>
  </appSettings>
  <!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.8"/>
    <httpRuntime targetFramework="4.6.1" executionTimeout="300" maxRequestLength="4069000" useFullyQualifiedRedirectUrl="false"/>
    <sessionState mode="StateServer" stateConnectionString="tcpip=localhost:42424" cookieless="false" timeout="1440"/>
  </system.web>
  <system.webServer>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="150000000"/>
      </requestFiltering>
    </security>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f"/>
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed"/>
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.codedom>
    <!--<compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>-->
  </system.codedom>
</configuration>