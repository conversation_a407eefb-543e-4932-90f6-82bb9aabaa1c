using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Common
{
    public static class GetStatusName
    {
        public static string GettaskStatusName(int TaskStatus)
        {
            var name = "";
            switch (TaskStatus)
            {
                case 0:
                    name = "无";
                    break;
                case 1:
                    name = "待分配";
                    break;
                case 2:
                    name = "已分配";
                    break;
                case 3:
                    name = "待复审";
                    break;
                case 4:
                    name = "复审未通过";
                    break;
                case 5:
                    name = "复审通过";
                    break;
                case 6:
                    name = "再修改申请";
                    break;
                case 7:
                    name = "申请通过";
                    break;
                case 8:
                    name = "申请未通过";
                    break;
                default:
                    break;
            }
            return name;
        }
    }
}