using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;

namespace ITMCTR.App.Common
{
    public static class StringHelper
    {
        public static string CleanInvalidCharsForXML(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return "";
            else
            {
                StringBuilder checkedStringBuilder = new StringBuilder();
                Char[] chars = input.ToCharArray();
                for (int i = 0; i < chars.Length; i++)
                {
                    int charValue = Convert.ToInt32(chars[i]);

                    if ((charValue >= 0x00 && charValue <= 0x08) || (charValue >= 0x0b && charValue <= 0x0c) || (charValue >= 0x0e && charValue <= 0x1f))
                        continue;
                    else
                        checkedStringBuilder.Append(chars[i]);
                }
                return checkedStringBuilder.ToString();
            }
        }
        public static string CleanInvalidCharsForExcel(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return "";
            else
            {
                StringBuilder checkedStringBuilder = new StringBuilder();
                Char[] chars = input.ToCharArray();
                for (int i = 0; i < chars.Length; i++)
                {
                    int charValue = Convert.ToInt32(chars[i]);

                    if ((charValue >= 0x00 && charValue <= 0x08) || (charValue >= 0x0b && charValue <= 0x0c) || (charValue >= 0x0e && charValue <= 0x1f))
                        continue;
                    else
                        checkedStringBuilder.Append(chars[i]);
                }
                var str = checkedStringBuilder.ToString();
                if (str.Length > 32767)
                {
                    str = str.Substring(0, 30000);
                }
                return str;
            }
        }
        /// <summary>
        /// 英文字符转为中文字符
        /// </summary>
        /// <param name="text">转换的中文字符串</param>
        /// <returns></returns>
        public static string ConvertToEn(this string text)
        {
            const string ch = "。；，？！、“”‘’（）—";//中文字符
            const string en = @".;,?!\""""''()-";//英文字符
            char[] c = text.ToCharArray();
            for (int i = 0; i < c.Length; i++)
            {
                int n = ch.IndexOf(c[i]);
                if (n != -1) c[i] = en[n];
            }
            var str = new string(c);
            Dictionary<string, string> dict = new Dictionary<string, string>()
            {
                {"①","1)" },
                {"②","2)" },
                {"③","3)" },
                {"④","4)" },
                {"⑤","5)" },
                {"⑥","6)" },
                {"⑦","7)" },
                {"⑧","8)" },
                {"⑨","9)" },
                {"⑩","10)" },
                {"⑪","11)" },
                {"⑫","12)" },
                {"⑬","13)" },
                {"⑭","14)" },
                {"⑮","15)" },
                {"⑯","16)" },
                {"⑰","17)" },
                {"⑱","18)" },
                {"⑲","19)" },
                {"⑳","20)" },
                {"㉑","21)" },
                {"㉒","22)" },
                {"㉓","23)" },
                {"㉔","24)" },
                {"㉕","25)" },
                {"㉖","26)" },
                {"㉗","27)" },
                {"㉘","28)" },
                {"㉙","29)" },
                {"㉚","30)" },
                {"㉛","31)" },
                {"㉜","32)" },
                {"㉝","33)" },
                {"㉞","34)" },
                {"㉟","35)" },
                {"㊱","36)" },
                {"㊲","37)" },
                {"㊳","38)" },
                {"㊴","39)" },
                {"㊵","40)" },
                {"㊶","41)" },
                {"㊷","42)" },
                {"㊸","43)" },
                {"㊹","44)" },
                {"㊺","45)" },
                {"㊻","46)" },
                {"㊼","47)" },
                {"㊽","48)" },
                {"㊾","49)" },
                {"㊿","50)" },
            };
            foreach (var item in dict)
            {
                str = str.Replace(item.Key, item.Value);
            }
            return str;
        }
        /// <summary>
        /// 判断字符串中是否包含中文
        /// </summary>
        /// <param name="str">需要判断的字符串</param>
        /// <returns>判断结果</returns>
        public static bool HasChinese(this string str)
        {
            return Regex.IsMatch(str, @"[\u4e00-\u9fa5]");
        }
        /// <summary>
        /// 判断字符串中是否包含中文
        /// </summary>
        /// <param name="str">需要判断的字符串</param>
        /// <returns>判断结果</returns>
        public static string HasChinese(this string str, ref bool hasChinese, int mode)
        {
            var tmp = str;
            if (mode == 1)
            {
                tmp = ConvertToEn(str);
            }
            if (HasChinese(tmp))
            {
                hasChinese = true;
            }
            return str;
        }
    }
}