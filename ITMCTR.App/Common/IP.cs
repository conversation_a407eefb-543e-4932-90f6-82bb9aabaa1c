using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;

namespace ITMCTR.App.Common
{
    public static class IP
    {
        public static string GetIP4Address()
        {
            string IP4Address = String.Empty;

            foreach (IPAddress IPA in Dns.GetHostAddresses(System.Web.HttpContext.Current.Request.UserHostAddress))
            {
                if (IPA.AddressFamily.ToString() == "InterNetwork")
                {
                    IP4Address = IPA.ToString();
                    break;
                }
            }

            if (IP4Address != String.Empty)
            {
                return IP4Address;
            }

            foreach (IPAddress IPA in Dns.GetHostAddresses(Dns.GetHostName()))
            {
                if (IPA.AddressFamily.ToString() == "InterNetwork")
                {
                    IP4Address = IPA.ToString();
                    break;
                }
            }

            return IP4Address;
        }

        public static string GetIP()
        {
            string empty = string.Empty;
            empty = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            if (string.IsNullOrEmpty(empty))
            {
                empty = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
            }
            if (string.IsNullOrEmpty(empty))
            {
                empty = HttpContext.Current.Request.UserHostAddress;
            }
            if (string.IsNullOrEmpty(empty) || !Utils.IsIP(empty))
            {
                return "127.0.0.1";
            }
            return empty;
        }

    }
}