using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace ITMCTR.App.Common
{
    public static class MD5Utils
    {
        public static string MD5(string str)
        {
            //将字符串编码为字节序列
            byte[] bt = Encoding.UTF8.GetBytes(str);
            //创建默认实现的实例
            var md5 = System.Security.Cryptography.MD5.Create();
            //计算指定字节数组的哈希值。
            var md5bt = md5.ComputeHash(bt);
            //将byte数组转换为字符串
            StringBuilder builder = new StringBuilder();
            foreach (var item in md5bt)
            {
                builder.Append(item.ToString("X2"));
            }
            string md5Str = builder.ToString();
            return builder.ToString();
        }
    }
}