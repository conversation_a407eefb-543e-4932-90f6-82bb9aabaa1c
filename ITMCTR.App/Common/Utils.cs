using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI;
using System.Xml;

namespace ITMCTR.App.Common
{
    public class Utils
    {
        public const string ASSEMBLY_VERSION = "1.0.0";

        private static Regex RegexBr = new Regex("(\\r\\n)", RegexOptions.IgnoreCase);

        public static Regex RegexFont = new Regex("<font color=\".*?\">([\\s\\S]+?)</font>", GetRegexCompiledOptions());

        private static FileVersionInfo AssemblyFileVersion = FileVersionInfo.GetVersionInfo(Assembly.GetExecutingAssembly().Location);

        private static string TemplateCookieName = $"bhsstemplateid_{AssemblyFileVersion.FileMajorPart}_{AssemblyFileVersion.FileMinorPart}_{AssemblyFileVersion.FileBuildPart}";

        private static char[] constant = new char[52]
        {
        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
        'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',
        'u', 'v', 'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D',
        'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',
        'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
        'Y', 'Z'
        };

        public static string[] Monthes => new string[12]
        {
        "January", "February", "March", "April", "May", "June", "July", "August", "September", "October",
        "November", "December"
        };

        public static RegexOptions GetRegexCompiledOptions()
        {
            return RegexOptions.None;
        }

        public static int GetStringLength(string str)
        {
            return Encoding.Default.GetBytes(str).Length;
        }

        public static bool IsCompriseStr(string str, string stringarray, string strsplit)
        {
            if (stringarray == "" || stringarray == null)
            {
                return false;
            }
            str = str.ToLower();
            string[] array = SplitString(stringarray.ToLower(), strsplit);
            for (int i = 0; i < array.Length; i++)
            {
                if (str.IndexOf(array[i]) > -1)
                {
                    return true;
                }
            }
            return false;
        }

        public static int GetInArrayID(string strSearch, string[] stringArray, bool caseInsensetive)
        {
            for (int i = 0; i < stringArray.Length; i++)
            {
                if (caseInsensetive)
                {
                    if (strSearch.ToLower() == stringArray[i].ToLower())
                    {
                        return i;
                    }
                }
                else if (strSearch == stringArray[i])
                {
                    return i;
                }
            }
            return -1;
        }

        public static int GetInArrayID(string strSearch, string[] stringArray)
        {
            return GetInArrayID(strSearch, stringArray, caseInsensetive: true);
        }

        public static bool InArray(string strSearch, string[] stringArray, bool caseInsensetive)
        {
            return GetInArrayID(strSearch, stringArray, caseInsensetive) >= 0;
        }

        public static bool InArray(string str, string[] stringarray)
        {
            return InArray(str, stringarray, caseInsensetive: false);
        }

        public static bool InArray(string str, string stringarray)
        {
            return InArray(str, SplitString(stringarray, ","), caseInsensetive: false);
        }

        public static bool InArray(string str, string stringarray, string strsplit)
        {
            return InArray(str, SplitString(stringarray, strsplit), caseInsensetive: false);
        }

        public static bool InArray(string str, string stringarray, string strsplit, bool caseInsensetive)
        {
            return InArray(str, SplitString(stringarray, strsplit), caseInsensetive);
        }

        public static string RTrim(string str)
        {
            for (int num = str.Length; num >= 0; num--)
            {
                if (str[num].Equals(" ") || str[num].Equals("\r") || str[num].Equals("\n"))
                {
                    str.Remove(num, 1);
                }
            }
            return str;
        }

        public static string ClearBR(string str)
        {
            Match match = null;
            match = RegexBr.Match(str);
            while (match.Success)
            {
                str = str.Replace(match.Groups[0].ToString(), "");
                match = match.NextMatch();
            }
            return str;
        }

        public static string CutString(string str, int startIndex, int length)
        {
            if (startIndex >= 0)
            {
                if (length < 0)
                {
                    length *= -1;
                    if (startIndex - length < 0)
                    {
                        length = startIndex;
                        startIndex = 0;
                    }
                    else
                    {
                        startIndex -= length;
                    }
                }
                if (startIndex > str.Length)
                {
                    return "";
                }
            }
            else
            {
                if (length < 0)
                {
                    return "";
                }
                if (length + startIndex <= 0)
                {
                    return "";
                }
                length += startIndex;
                startIndex = 0;
            }
            if (str.Length - startIndex < length)
            {
                length = str.Length - startIndex;
            }
            return str.Substring(startIndex, length);
        }

        public static string CutString(string str, int startIndex)
        {
            return CutString(str, startIndex, str.Length);
        }

        public static string GetMapPath(string strPath)
        {
            if (HttpContext.Current != null)
            {
                return HttpContext.Current.Server.MapPath("~" + strPath);
            }
            strPath = strPath.Replace("/", "\\");
            if (strPath.StartsWith("\\"))
            {
                strPath = strPath.Substring(strPath.IndexOf('\\', 0)).TrimStart('\\');
            }
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, strPath);
        }

        public static bool FileExists(string filename)
        {
            return File.Exists(filename);
        }

        public static void ResponseFile(string filepath, string filename, string filetype)
        {
            Stream stream = null;
            byte[] buffer = new byte[10000];
            try
            {
                stream = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                long num = stream.Length;
                HttpContext.Current.Response.ContentType = filetype;
                HttpContext.Current.Response.AddHeader("Content-Disposition", "attachment;filename=" + UrlEncode(filename.Trim()).Replace("+", " "));
                while (num > 0)
                {
                    if (HttpContext.Current.Response.IsClientConnected)
                    {
                        int num2 = stream.Read(buffer, 0, 10000);
                        HttpContext.Current.Response.OutputStream.Write(buffer, 0, num2);
                        HttpContext.Current.Response.Flush();
                        buffer = new byte[10000];
                        num -= num2;
                    }
                    else
                    {
                        num = -1L;
                    }
                }
            }
            catch (Exception ex)
            {
                HttpContext.Current.Response.Write("Error : " + ex.Message);
            }
            finally
            {
                stream?.Close();
            }
            HttpContext.Current.Response.End();
        }

        public static bool IsImgFilename(string filename)
        {
            filename = filename.Trim();
            if (filename.EndsWith(".") || filename.IndexOf(".") == -1)
            {
                return false;
            }
            string text = filename.Substring(filename.LastIndexOf(".") + 1).ToLower();
            switch (text)
            {
                default:
                    return text == "gif";
                case "jpg":
                case "jpeg":
                case "png":
                case "bmp":
                    return true;
            }
        }

        public static string IntToStr(int intValue)
        {
            return Convert.ToString(intValue);
        }

        public static string MD5(string str)
        {
            byte[] bytes = Encoding.Default.GetBytes(str);
            bytes = new MD5CryptoServiceProvider().ComputeHash(bytes);
            string text = "";
            for (int i = 0; i < bytes.Length; i++)
            {
                text += bytes[i].ToString("x").PadLeft(2, '0');
            }
            return text;
        }

        public static string SHA256(string str)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            SHA256Managed sHA256Managed = new SHA256Managed();
            byte[] inArray = sHA256Managed.ComputeHash(bytes);
            return Convert.ToBase64String(inArray);
        }

        public static string GetSubString(string p_SrcString, int p_Length, string p_TailString)
        {
            return GetSubString(p_SrcString, 0, p_Length, p_TailString);
        }

        public static string GetUnicodeSubString(string str, int len, string p_TailString)
        {
            string result = string.Empty;
            int byteCount = Encoding.Default.GetByteCount(str);
            int length = str.Length;
            int num = 0;
            int num2 = 0;
            if (byteCount > len)
            {
                for (int i = 0; i < length; i++)
                {
                    num = ((Convert.ToInt32(str.ToCharArray()[i]) <= 255) ? (num + 1) : (num + 2));
                    if (num > len)
                    {
                        num2 = i;
                        break;
                    }
                    if (num == len)
                    {
                        num2 = i + 1;
                        break;
                    }
                }
                if (num2 >= 0)
                {
                    result = str.Substring(0, num2) + p_TailString;
                }
            }
            else
            {
                result = str;
            }
            return result;
        }

        public static string GetSubString(string p_SrcString, int p_StartIndex, int p_Length, string p_TailString)
        {
            string result = p_SrcString;
            byte[] bytes = Encoding.UTF8.GetBytes(p_SrcString);
            char[] chars = Encoding.UTF8.GetChars(bytes);
            foreach (char c in chars)
            {
                if ((c > 'ࠀ' && c < '一') || (c > '가' && c < '힣'))
                {
                    if (p_StartIndex >= p_SrcString.Length)
                    {
                        return "";
                    }
                    return p_SrcString.Substring(p_StartIndex, (p_Length + p_StartIndex > p_SrcString.Length) ? (p_SrcString.Length - p_StartIndex) : p_Length);
                }
            }
            if (p_Length >= 0)
            {
                byte[] bytes2 = Encoding.Default.GetBytes(p_SrcString);
                if (bytes2.Length > p_StartIndex)
                {
                    int num = bytes2.Length;
                    if (bytes2.Length > p_StartIndex + p_Length)
                    {
                        num = p_Length + p_StartIndex;
                    }
                    else
                    {
                        p_Length = bytes2.Length - p_StartIndex;
                        p_TailString = "";
                    }
                    int num2 = p_Length;
                    int[] array = new int[p_Length];
                    byte[] array2 = null;
                    int num3 = 0;
                    for (int j = p_StartIndex; j < num; j++)
                    {
                        if (bytes2[j] > 127)
                        {
                            num3++;
                            if (num3 == 3)
                            {
                                num3 = 1;
                            }
                        }
                        else
                        {
                            num3 = 0;
                        }
                        array[j] = num3;
                    }
                    if (bytes2[num - 1] > 127 && array[p_Length - 1] == 1)
                    {
                        num2 = p_Length + 1;
                    }
                    array2 = new byte[num2];
                    Array.Copy(bytes2, p_StartIndex, array2, 0, num2);
                    result = Encoding.Default.GetString(array2);
                    result += p_TailString;
                }
            }
            return result;
        }

        public static string ReplaceString(string SourceString, string SearchString, string ReplaceString, bool IsCaseInsensetive)
        {
            return Regex.Replace(SourceString, Regex.Escape(SearchString), ReplaceString, IsCaseInsensetive ? RegexOptions.IgnoreCase : RegexOptions.None);
        }

        public static string GetSpacesString(int spacesCount)
        {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < spacesCount; i++)
            {
                stringBuilder.Append(" &nbsp;&nbsp;");
            }
            return stringBuilder.ToString();
        }

        public static bool IsValidEmail(string strEmail)
        {
            return Regex.IsMatch(strEmail, "^[\\w\\.]+@[A-Za-z0-9-_]+[\\.][A-Za-z0-9-_]");
        }

        public static bool IsValidDoEmail(string strEmail)
        {
            return Regex.IsMatch(strEmail, "^@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.)|(([\\w-]+\\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\\]?)$");
        }

        public static bool IsURL(string strUrl)
        {
            return Regex.IsMatch(strUrl, "^(http|https)\\://([a-zA-Z0-9\\.\\-]+(\\:[a-zA-Z0-9\\.&%\\$\\-]+)*@)*((25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])|localhost|([a-zA-Z0-9\\-]+\\.)*[a-zA-Z0-9\\-]+\\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{1,10}))(\\:[0-9]+)*(/($|[a-zA-Z0-9\\.\\,\\?\\'\\\\\\+&%\\$#\\=~_\\-]+))*$");
        }

        public static string GetEmailHostName(string strEmail)
        {
            if (strEmail.IndexOf("@") < 0)
            {
                return "";
            }
            return strEmail.Substring(strEmail.LastIndexOf("@")).ToLower();
        }

        public static bool IsBase64String(string str)
        {
            return Regex.IsMatch(str, "[A-Za-z0-9\\+\\/\\=]");
        }

        public static bool IsSafeSqlString(string str)
        {
            return !Regex.IsMatch(str, "[-|;|,|\\/|\\(|\\)|\\[|\\]|\\}|\\{|%|@|\\*|!|\\']");
        }

        public static string CheckSql(string sql)
        {
            string text = sql;
            if (text == null)
            {
                return text;
            }
            if (text.Length > 20)
            {
                text = text.Replace("script", "&#115;cript");
                text = text.Replace("master", "ma&#115;ter");
                text = text.Replace(" ", "&#32;");
                text = text.Replace("insert", "ins&#101;&#114;t");
                text = text.Replace("delete", "de&#108;&#101;te");
                text = text.Replace("update", "u&#112;&#100;ate");
                text = text.Replace("exec", "&#101;xec");
                text = text.Replace("'", "&#39;");
                text = text.Replace("truncate", "");
            }
            return text;
        }

        public static string RestoreSql(string sql)
        {
            string text = sql;
            if (text == null)
            {
                return text;
            }
            if (text.Length > 20)
            {
                text = text.Replace("&#115;cript", "script");
                text = text.Replace("ma&#115;ter", "master");
                text = text.Replace("&#32;", " ");
                text = text.Replace("ins&#101;&#114;t", "insert");
                text = text.Replace("de&#108;&#101;te", "delete");
                text = text.Replace("u&#112;&#100;ate", "update");
                text = text.Replace("&#101;xec", "exec");
                text = text.Replace("&#39;", "'");
            }
            return text;
        }

        public static bool IsSafeUserInfoString(string str)
        {
            return !Regex.IsMatch(str, "^\\s*$|^c:\\\\con\\\\con$|[%,\\*\"\\s\\t\\<\\>\\&]|游客|^Guest");
        }

        public static string CleanInput(string strIn)
        {
            return Regex.Replace(strIn.Trim(), "[^\\w\\.@-]", "");
        }

        public static string GetFilename(string url)
        {
            if (url == null)
            {
                return "";
            }
            string[] array = url.Split('/');
            return array[array.Length - 1].Split('?')[0];
        }

        public static string StrFormat(string str)
        {
            if (str == null)
            {
                return "";
            }
            str = str.Replace("\r\n", "<br />");
            str = str.Replace("\n", "<br />");
            return str;
        }

        public static string GetDate()
        {
            return DateTime.Now.ToString("yyyy-MM-dd");
        }

        public static string GetDate(string datetimestr, string replacestr)
        {
            if (datetimestr == null)
            {
                return replacestr;
            }
            if (datetimestr.Equals(""))
            {
                return replacestr;
            }
            try
            {
                datetimestr = Convert.ToDateTime(datetimestr).ToString("yyyy-MM-dd").Replace("1900-01-01", replacestr);
                return datetimestr;
            }
            catch
            {
                return replacestr;
            }
        }

        public static string GetTime()
        {
            return DateTime.Now.ToString("HH:mm:ss");
        }

        public static string GetDateTime()
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        public static string GetDateTime(int relativeday)
        {
            return DateTime.Now.AddDays(relativeday).ToString("yyyy-MM-dd HH:mm:ss");
        }

        public static string GetDateTimeF()
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss:fffffff");
        }

        public static string GetStandardDateTime(string fDateTime, string formatStr)
        {
            if (fDateTime == "0000-0-0 0:00:00")
            {
                return fDateTime;
            }
            return Convert.ToDateTime(fDateTime).ToString(formatStr);
        }

        public static string GetStandardDateTime(string fDateTime)
        {
            return GetStandardDateTime(fDateTime, "yyyy-MM-dd HH:mm:ss");
        }

        public static string GetStandardDate(string fDate)
        {
            return GetStandardDateTime(fDate, "yyyy-MM-dd");
        }

        public static bool IsTime(string timeval)
        {
            return Regex.IsMatch(timeval, "^((([0-1]?[0-9])|(2[0-3])):([0-5]?[0-9])(:[0-5]?[0-9])?)$");
        }

        public static string GetRealIP()
        {
            return IP.GetIP();
        }

        public static string mashSQL(string str)
        {
            if (str == null)
            {
                return "";
            }
            str = str.Replace("'", "'");
            return str;
        }

        public static string ChkSQL(string str)
        {
            if (str == null)
            {
                return "";
            }
            str = str.Replace("'", "''");
            return str;
        }
        public static string[] SplitString(string strContent, string strSplit)
        {
            if (!StrIsNullOrEmpty(strContent))
            {
                if (strContent.IndexOf(strSplit) < 0)
                {
                    return new string[1] { strContent };
                }
                return Regex.Split(strContent, Regex.Escape(strSplit), RegexOptions.IgnoreCase);
            }
            return new string[0];
        }

        public static string[] SplitString(string strContent, string strSplit, int count)
        {
            string[] array = new string[count];
            string[] array2 = SplitString(strContent, strSplit);
            for (int i = 0; i < count; i++)
            {
                if (i < array2.Length)
                {
                    array[i] = array2[i];
                }
                else
                {
                    array[i] = string.Empty;
                }
            }
            return array;
        }

        public static string[] PadStringArray(string[] strArray, int minLength, int maxLength)
        {
            if (minLength > maxLength)
            {
                int num = maxLength;
                maxLength = minLength;
                minLength = num;
            }
            int num2 = 0;
            for (int i = 0; i < strArray.Length; i++)
            {
                if (minLength > -1 && strArray[i].Length < minLength)
                {
                    strArray[i] = null;
                    continue;
                }
                if (strArray[i].Length > maxLength)
                {
                    strArray[i] = strArray[i].Substring(0, maxLength);
                }
                num2++;
            }
            string[] array = new string[num2];
            int j = 0;
            int num3 = 0;
            for (; j < strArray.Length; j++)
            {
                if (num3 >= array.Length)
                {
                    break;
                }
                if (strArray[j] != null && strArray[j] != string.Empty)
                {
                    array[num3] = strArray[j];
                    num3++;
                }
            }
            return array;
        }

        public static string[] SplitString(string strContent, string strSplit, bool ignoreRepeatItem, int maxElementLength)
        {
            string[] array = SplitString(strContent, strSplit);
            if (!ignoreRepeatItem)
            {
                return array;
            }
            return DistinctStringArray(array, maxElementLength);
        }

        public static string[] SplitString(string strContent, string strSplit, bool ignoreRepeatItem, int minElementLength, int maxElementLength)
        {
            string[] strArray = SplitString(strContent, strSplit);
            if (ignoreRepeatItem)
            {
                strArray = DistinctStringArray(strArray);
            }
            return PadStringArray(strArray, minElementLength, maxElementLength);
        }

        public static string[] SplitString(string strContent, string strSplit, bool ignoreRepeatItem)
        {
            return SplitString(strContent, strSplit, ignoreRepeatItem, 0);
        }

        public static string[] DistinctStringArray(string[] strArray, int maxElementLength)
        {
            Hashtable hashtable = new Hashtable();
            foreach (string text in strArray)
            {
                string text2 = text;
                if (maxElementLength > 0 && text2.Length > maxElementLength)
                {
                    text2 = text2.Substring(0, maxElementLength);
                }
                hashtable[text2.Trim()] = text;
            }
            string[] array = new string[hashtable.Count];
            hashtable.Keys.CopyTo(array, 0);
            return array;
        }

        public static string[] DistinctStringArray(string[] strArray)
        {
            return DistinctStringArray(strArray, 0);
        }

        public static string EncodeHtml(string strHtml)
        {
            if (strHtml != "")
            {
                strHtml = strHtml.Replace(",", "&def");
                strHtml = strHtml.Replace("'", "&dot");
                strHtml = strHtml.Replace(";", "&dec");
                return strHtml;
            }
            return "";
        }

        public static string StrFilter(string str, string bantext)
        {
            string text = "";
            string text2 = "";
            string[] array = SplitString(bantext, "\r\n");
            for (int i = 0; i < array.Length; i++)
            {
                text = array[i].Substring(0, array[i].IndexOf("="));
                text2 = array[i].Substring(array[i].IndexOf("=") + 1);
                str = str.Replace(text, text2);
            }
            return str;
        }

        public static string GetStaticPageNumbers(int curPage, int countPage, string url, string expname, int extendPage)
        {
            return GetStaticPageNumbers(curPage, countPage, url, expname, extendPage, 0);
        }

        public static string GetStaticPageNumbers(int curPage, int countPage, string url, string expname, int extendPage, int forumrewrite)
        {
            int num = 1;
            int num2 = 1;
            string value = "<a href=\"" + url + "-1" + expname + "\">&laquo;</a>";
            string value2 = "<a href=\"" + url + "-" + countPage + expname + "\">&raquo;</a>";
            if (forumrewrite == 1)
            {
                value = "<a href=\"" + url + "/1/list" + expname + "\">&laquo;</a>";
                value2 = "<a href=\"" + url + "/" + countPage + "/list" + expname + "\">&raquo;</a>";
            }
            if (forumrewrite == 2)
            {
                value = "<a href=\"" + url + "/1/\">&laquo;</a>";
                value2 = "<a href=\"" + url + "/" + countPage + "/\">&raquo;</a>";
            }
            if (countPage < 1)
            {
                countPage = 1;
            }
            if (extendPage < 3)
            {
                extendPage = 2;
            }
            if (countPage > extendPage)
            {
                if (curPage - extendPage / 2 > 0)
                {
                    if (curPage + extendPage / 2 < countPage)
                    {
                        num = curPage - extendPage / 2;
                        num2 = num + extendPage - 1;
                    }
                    else
                    {
                        num2 = countPage;
                        num = num2 - extendPage + 1;
                        value2 = "";
                    }
                }
                else
                {
                    num2 = extendPage;
                    value = "";
                }
            }
            else
            {
                num = 1;
                num2 = countPage;
                value = "";
                value2 = "";
            }
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.Append(value);
            for (int i = num; i <= num2; i++)
            {
                if (i == curPage)
                {
                    stringBuilder.Append("<span>");
                    stringBuilder.Append(i);
                    stringBuilder.Append("</span>");
                    continue;
                }
                stringBuilder.Append("<a href=\"");
                switch (forumrewrite)
                {
                    case 1:
                        stringBuilder.Append(url);
                        stringBuilder.Append("/");
                        stringBuilder.Append(i);
                        stringBuilder.Append("/list");
                        stringBuilder.Append(expname);
                        break;
                    case 2:
                        stringBuilder.Append(url);
                        stringBuilder.Append("/");
                        stringBuilder.Append(i);
                        stringBuilder.Append("/");
                        break;
                    default:
                        stringBuilder.Append(url);
                        stringBuilder.Append("-");
                        stringBuilder.Append(i);
                        stringBuilder.Append(expname);
                        break;
                }
                stringBuilder.Append("\">");
                stringBuilder.Append(i);
                stringBuilder.Append("</a>");
            }
            stringBuilder.Append(value2);
            return stringBuilder.ToString();
        }

        public static string GetPostPageNumbers(int countPage, string url, string expname, int extendPage)
        {
            int num = 1;
            int num2 = 1;
            int num3 = 1;
            string value = "<a href=\"" + url + "-1" + expname + "\">&laquo;</a>";
            string value2 = "<a href=\"" + url + "-" + countPage + expname + "\">&raquo;</a>";
            if (countPage < 1)
            {
                countPage = 1;
            }
            if (extendPage < 3)
            {
                extendPage = 2;
            }
            if (countPage > extendPage)
            {
                if (num3 - extendPage / 2 > 0)
                {
                    if (num3 + extendPage / 2 < countPage)
                    {
                        num = num3 - extendPage / 2;
                        num2 = num + extendPage - 1;
                    }
                    else
                    {
                        num2 = countPage;
                        num = num2 - extendPage + 1;
                        value2 = "";
                    }
                }
                else
                {
                    num2 = extendPage;
                    value = "";
                }
            }
            else
            {
                num = 1;
                num2 = countPage;
                value = "";
                value2 = "";
            }
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.Append(value);
            for (int i = num; i <= num2; i++)
            {
                stringBuilder.Append("<a href=\"");
                stringBuilder.Append(url);
                stringBuilder.Append("-");
                stringBuilder.Append(i);
                stringBuilder.Append(expname);
                stringBuilder.Append("\">");
                stringBuilder.Append(i);
                stringBuilder.Append("</a>");
            }
            stringBuilder.Append(value2);
            return stringBuilder.ToString();
        }

        public static string GetPageNumbers(int curPage, int countPage, string url, int extendPage)
        {
            return GetPageNumbers(curPage, countPage, url, extendPage, "page");
        }

        public static string GetPageNumbers(int curPage, int countPage, string url, int extendPage, string pagetag)
        {
            return GetPageNumbers(curPage, countPage, url, extendPage, pagetag, null);
        }

        public static string GetPageNumbers(int curPage, int countPage, string url, int extendPage, string pagetag, string anchor)
        {
            if (pagetag == "")
            {
                pagetag = "page";
            }
            int num = 1;
            int num2 = 1;
            url = ((url.IndexOf("?") <= 0) ? (url + "?") : (url + "&"));
            string text = "<a href=\"" + url + "&" + pagetag + "=1";
            string text2 = "<a href=\"" + url + "&" + pagetag + "=" + countPage;
            if (anchor != null)
            {
                text += anchor;
                text2 += anchor;
            }
            text += "\">&laquo;</a>";
            text2 += "\">&raquo;</a>";
            if (countPage < 1)
            {
                countPage = 1;
            }
            if (extendPage < 3)
            {
                extendPage = 2;
            }
            if (countPage > extendPage)
            {
                if (curPage - extendPage / 2 > 0)
                {
                    if (curPage + extendPage / 2 < countPage)
                    {
                        num = curPage - extendPage / 2;
                        num2 = num + extendPage - 1;
                    }
                    else
                    {
                        num2 = countPage;
                        num = num2 - extendPage + 1;
                        text2 = "";
                    }
                }
                else
                {
                    num2 = extendPage;
                    text = "";
                }
            }
            else
            {
                num = 1;
                num2 = countPage;
                text = "";
                text2 = "";
            }
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.Append(text);
            for (int i = num; i <= num2; i++)
            {
                if (i == curPage)
                {
                    stringBuilder.Append("<span>");
                    stringBuilder.Append(i);
                    stringBuilder.Append("</span>");
                    continue;
                }
                stringBuilder.Append("<a href=\"");
                stringBuilder.Append(url);
                stringBuilder.Append(pagetag);
                stringBuilder.Append("=");
                stringBuilder.Append(i);
                if (anchor != null)
                {
                    stringBuilder.Append(anchor);
                }
                stringBuilder.Append("\">");
                stringBuilder.Append(i);
                stringBuilder.Append("</a>");
            }
            stringBuilder.Append(text2);
            return stringBuilder.ToString();
        }

        public static string HtmlEncode(string str)
        {
            return HttpUtility.HtmlEncode(str);
        }

        public static string HtmlDecode(string str)
        {
            return HttpUtility.HtmlDecode(str);
        }

        public static string UrlEncode(string str)
        {
            return HttpUtility.UrlEncode(str);
        }

        public static string UrlDecode(string str)
        {
            return HttpUtility.UrlDecode(str);
        }

        public static string[] FindNoUTF8File(string Path)
        {
            StringBuilder stringBuilder = new StringBuilder();
            DirectoryInfo directoryInfo = new DirectoryInfo(Path);
            FileInfo[] files = directoryInfo.GetFiles();
            for (int i = 0; i < files.Length; i++)
            {
                if (files[i].Extension.ToLower().Equals(".htm"))
                {
                    FileStream fileStream = new FileStream(files[i].FullName, FileMode.Open, FileAccess.Read);
                    bool flag = IsUTF8(fileStream);
                    fileStream.Close();
                    if (!flag)
                    {
                        stringBuilder.Append(files[i].FullName);
                        stringBuilder.Append("\r\n");
                    }
                }
            }
            return SplitString(stringBuilder.ToString(), "\r\n");
        }

        private static bool IsUTF8(FileStream sbInputStream)
        {
            bool flag = true;
            long length = sbInputStream.Length;
            byte b = 0;
            for (int i = 0; i < length; i++)
            {
                byte b2 = (byte)sbInputStream.ReadByte();
                if ((b2 & 0x80u) != 0)
                {
                    flag = false;
                }
                if (b == 0)
                {
                    if (b2 >= 128)
                    {
                        do
                        {
                            b2 = (byte)(b2 << 1);
                            b = (byte)(b + 1);
                        }
                        while ((b2 & 0x80u) != 0);
                        b = (byte)(b - 1);
                        if (b == 0)
                        {
                            return false;
                        }
                    }
                }
                else
                {
                    if ((b2 & 0xC0) != 128)
                    {
                        return false;
                    }
                    b = (byte)(b - 1);
                }
            }
            if (b > 0)
            {
                return false;
            }
            if (flag)
            {
                return false;
            }
            return true;
        }

        public static string FormatBytesStr(int bytes)
        {
            if (bytes > 1073741824)
            {
                return ((double)(bytes / 1073741824)).ToString("0") + "G";
            }
            if (bytes > 1048576)
            {
                return ((double)(bytes / 1048576)).ToString("0") + "M";
            }
            if (bytes > 1024)
            {
                return ((double)(bytes / 1024)).ToString("0") + "K";
            }
            return bytes + "Bytes";
        }

        public static int SafeInt32(object objNum)
        {
            if (objNum == null)
            {
                return 0;
            }
            string text = objNum.ToString();
            if (IsNumeric(text))
            {
                if (text.ToString().Length > 9)
                {
                    if (text.StartsWith("-"))
                    {
                        return int.MinValue;
                    }
                    return int.MaxValue;
                }
                return int.Parse(text);
            }
            return 0;
        }

        public static int StrDateDiffSeconds(string Time, int Sec)
        {
            TimeSpan timeSpan = DateTime.Now - DateTime.Parse(Time).AddSeconds(Sec);
            if (timeSpan.TotalSeconds > 2147483647.0)
            {
                return int.MaxValue;
            }
            if (timeSpan.TotalSeconds < -2147483648.0)
            {
                return int.MinValue;
            }
            return (int)timeSpan.TotalSeconds;
        }

        public static int StrDateDiffMinutes(string time, int minutes)
        {
            if (time == "" || time == null)
            {
                return 1;
            }
            TimeSpan timeSpan = DateTime.Now - DateTime.Parse(time).AddMinutes(minutes);
            if (timeSpan.TotalMinutes > 2147483647.0)
            {
                return int.MaxValue;
            }
            if (timeSpan.TotalMinutes < -2147483648.0)
            {
                return int.MinValue;
            }
            return (int)timeSpan.TotalMinutes;
        }

        public static int StrDateDiffHours(string time, int hours)
        {
            if (time == "" || time == null)
            {
                return 1;
            }
            TimeSpan timeSpan = DateTime.Now - DateTime.Parse(time).AddHours(hours);
            if (timeSpan.TotalHours > 2147483647.0)
            {
                return int.MaxValue;
            }
            if (timeSpan.TotalHours < -2147483648.0)
            {
                return int.MinValue;
            }
            return (int)timeSpan.TotalHours;
        }


        public static string ReplaceStrToScript(string str)
        {
            str = str.Replace("\\", "\\\\");
            str = str.Replace("'", "\\'");
            str = str.Replace("\"", "\\\"");
            return str;
        }

        public static bool IsIP(string ip)
        {
            return Regex.IsMatch(ip, "^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)$");
        }

        public static bool IsIPSect(string ip)
        {
            return Regex.IsMatch(ip, "^((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){2}((2[0-4]\\d|25[0-5]|[01]?\\d\\d?|\\*)\\.)(2[0-4]\\d|25[0-5]|[01]?\\d\\d?|\\*)$");
        }

        public static bool InIPArray(string ip, string[] iparray)
        {
            string[] array = SplitString(ip, ".");
            for (int i = 0; i < iparray.Length; i++)
            {
                string[] array2 = SplitString(iparray[i], ".");
                int num = 0;
                for (int j = 0; j < array2.Length; j++)
                {
                    if (array2[j] == "*")
                    {
                        return true;
                    }
                    if (array.Length <= j || !(array2[j] == array[j]))
                    {
                        break;
                    }
                    num++;
                }
                if (num == 4)
                {
                    return true;
                }
            }
            return false;
        }

        public static string GetAssemblyVersion()
        {
            return $"{AssemblyFileVersion.FileMajorPart}.{AssemblyFileVersion.FileMinorPart}.{AssemblyFileVersion.FileBuildPart}";
        }

        public static string GetAssemblyProductName()
        {
            return AssemblyFileVersion.ProductName;
        }

        public static string GetAssemblyCopyright()
        {
            return AssemblyFileVersion.LegalCopyright;
        }


        public static void WriteCookie(string strName, string strValue)
        {
            HttpCookie httpCookie = HttpContext.Current.Request.Cookies[strName];
            if (httpCookie == null)
            {
                httpCookie = new HttpCookie(strName);
            }
            httpCookie.Value = strValue;
            httpCookie.Expires = DateTime.Now.AddMinutes(720.0);
            HttpContext.Current.Response.AppendCookie(httpCookie);
        }

        public static void WriteCookie(string strName, string key, string strValue)
        {
            HttpCookie httpCookie = HttpContext.Current.Request.Cookies[strName];
            if (httpCookie == null)
            {
                httpCookie = new HttpCookie(strName);
            }
            httpCookie[key] = strValue;
            HttpContext.Current.Response.AppendCookie(httpCookie);
        }

        public static void WriteCookie(string strName, string key, string strValue, int expires)
        {
            HttpCookie httpCookie = HttpContext.Current.Request.Cookies[strName];
            if (httpCookie == null)
            {
                httpCookie = new HttpCookie(strName);
                httpCookie.Expires = DateTime.Now.AddMinutes(expires);
            }
            httpCookie[key] = strValue;
            HttpContext.Current.Response.AppendCookie(httpCookie);
        }

        public static void WriteCookie(string strName, string strValue, int expires)
        {
            HttpCookie httpCookie = HttpContext.Current.Request.Cookies[strName];
            if (httpCookie == null)
            {
                httpCookie = new HttpCookie(strName);
            }
            httpCookie.Value = strValue;
            httpCookie.Expires = DateTime.Now.AddMinutes(expires);
            HttpContext.Current.Response.AppendCookie(httpCookie);
        }

        public static string GetCookie(string strName)
        {
            if (HttpContext.Current.Request.Cookies != null && HttpContext.Current.Request.Cookies[strName] != null && StrDateDiffSeconds(HttpContext.Current.Request.Cookies[strName].Expires.ToString(), 0) < 0)
            {
                return HttpContext.Current.Request.Cookies[strName].Value.ToString();
            }
            return "";
        }

        public static string GetCookie(string strName, string key)
        {
            if (HttpContext.Current.Request.Cookies != null && HttpContext.Current.Request.Cookies[strName] != null && HttpContext.Current.Request.Cookies[strName][key] != null && StrDateDiffSeconds(HttpContext.Current.Request.Cookies[strName].Expires.ToString(), 0) < 0)
            {
                return HttpContext.Current.Request.Cookies[strName][key].ToString();
            }
            return "";
        }

        public static string GetTrueForumPath()
        {
            string path = HttpContext.Current.Request.Path;
            if (path.LastIndexOf("/") != path.IndexOf("/"))
            {
                return path.Substring(path.IndexOf("/"), path.LastIndexOf("/") + 1);
            }
            return "/";
        }

        public static bool IsDateString(string str)
        {
            return Regex.IsMatch(str, "(\\d{4})-(\\d{1,2})-(\\d{1,2})");
        }

        public static string RemoveHtml(string content)
        {
            string pattern = "<[^>]*>";
            return Regex.Replace(content, pattern, string.Empty, RegexOptions.IgnoreCase);
        }

        public static string RemoveUnsafeHtml(string content)
        {
            content = Regex.Replace(content, "(\\<|\\s+)o([a-z]+\\s?=)", "$1$2", RegexOptions.IgnoreCase);
            content = Regex.Replace(content, "(script|frame|form|meta|behavior|style)([\\s|:|>])+", "$1.$2", RegexOptions.IgnoreCase);
            return content;
        }

        public static string RemoveFontTag(string title)
        {
            Match match = RegexFont.Match(title);
            if (match.Success)
            {
                return match.Groups[1].Value;
            }
            return title;
        }

        public static bool IsNumeric(object Expression)
        {
            return TypeParse.IsNumeric(Expression);
        }

        public static string GetTextFromHTML(string HTML)
        {
            Regex regex = new Regex("</?(?!br|/?p|img)[^>]*>", RegexOptions.IgnoreCase);
            return regex.Replace(HTML, "");
        }

        public static bool IsDouble(object Expression)
        {
            return TypeParse.IsDouble(Expression);
        }

        public static bool StrToBool(object expression, bool defValue)
        {
            return TypeParse.StrToBool(expression, defValue);
        }

        public static bool StrToBool(string expression, bool defValue)
        {
            return TypeParse.StrToBool(expression, defValue);
        }

        public static int StrToInt(object expression, int defValue)
        {
            return TypeParse.StrToInt(expression, defValue);
        }

        public static int StrToInt(string expression, int defValue)
        {
            return TypeParse.StrToInt(expression, defValue);
        }

        public static float StrToFloat(object strValue, float defValue)
        {
            return TypeParse.StrToFloat(strValue, defValue);
        }

        public static float StrToFloat(string strValue, float defValue)
        {
            return TypeParse.StrToFloat(strValue, defValue);
        }

        public static DateTime StrToDateTime(string strValue, DateTime defValue)
        {
            return TypeParse.StrToDateTime(strValue, defValue);
        }

        public static string DateTimeToStr(DateTime dateValue)
        {
            return DateTimeToStr(dateValue, "yyyy-MM-dd");
        }

        public static string DateTimeToStr(DateTime dateValue, string format)
        {
            string result = "";
            if (dateValue.Year != 1900 || dateValue.Month != 1 || dateValue.Day != 1)
            {
                result = dateValue.ToString(format);
            }
            return result;
        }

        public static bool IsNumericArray(string[] strNumber)
        {
            return TypeParse.IsNumericArray(strNumber);
        }

        public static string AdDeTime(int times)
        {
            return DateTime.Now.AddMinutes(times).ToString();
        }

        public static bool IsInt(string str)
        {
            return Regex.IsMatch(str, "^[0-9]*$");
        }

        public static bool IsRuleTip(Hashtable NewHash, string ruletype, out string key)
        {
            key = "";
            foreach (DictionaryEntry item in NewHash)
            {
                try
                {
                    string[] array = SplitString(item.Value.ToString(), "\r\n");
                    string[] array2 = array;
                    foreach (string text in array2)
                    {
                        if (!(text != ""))
                        {
                            continue;
                        }
                        switch (ruletype.Trim().ToLower())
                        {
                            case "email":
                                if (!IsValidDoEmail(text.ToString()))
                                {
                                    throw new Exception();
                                }
                                break;
                            case "ip":
                                if (!IsIPSect(text.ToString()))
                                {
                                    throw new Exception();
                                }
                                break;
                            case "timesect":
                                {
                                    string[] array3 = text.Split('-');
                                    if (!IsTime(array3[1].ToString()) || !IsTime(array3[0].ToString()))
                                    {
                                        throw new Exception();
                                    }
                                    break;
                                }
                        }
                    }
                }
                catch
                {
                    key = item.Key.ToString();
                    return false;
                }
            }
            return true;
        }

        public static string ClearLastChar(string str)
        {
            if (str == "")
            {
                return "";
            }
            return str.Substring(0, str.Length - 1);
        }

        public static bool BackupFile(string sourceFileName, string destFileName, bool overwrite)
        {
            if (!File.Exists(sourceFileName))
            {
                throw new FileNotFoundException(sourceFileName + "文件不存在！");
            }
            if (!overwrite && File.Exists(destFileName))
            {
                return false;
            }
            try
            {
                File.Copy(sourceFileName, destFileName, overwrite: true);
                return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static bool BackupFile(string sourceFileName, string destFileName)
        {
            return BackupFile(sourceFileName, destFileName, overwrite: true);
        }

        public static bool RestoreFile(string backupFileName, string targetFileName, string backupTargetFileName)
        {
            try
            {
                if (!File.Exists(backupFileName))
                {
                    throw new FileNotFoundException(backupFileName + "文件不存在！");
                }
                if (backupTargetFileName != null)
                {
                    if (!File.Exists(targetFileName))
                    {
                        throw new FileNotFoundException(targetFileName + "文件不存在！无法备份此文件！");
                    }
                    File.Copy(targetFileName, backupTargetFileName, overwrite: true);
                }
                File.Delete(targetFileName);
                File.Copy(backupFileName, targetFileName);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return true;
        }

        public static bool RestoreFile(string backupFileName, string targetFileName)
        {
            return RestoreFile(backupFileName, targetFileName, null);
        }

        public static string GetTemplateCookieName()
        {
            return TemplateCookieName;
        }

        public static string SBCCaseToNumberic(string SBCCase)
        {
            char[] array = SBCCase.ToCharArray();
            for (int i = 0; i < array.Length; i++)
            {
                byte[] bytes = Encoding.Unicode.GetBytes(array, i, 1);
                if (bytes.Length == 2 && bytes[1] == byte.MaxValue)
                {
                    bytes[0] = (byte)(bytes[0] + 32);
                    bytes[1] = 0;
                    array[i] = Encoding.Unicode.GetChars(bytes)[0];
                }
            }
            return new string(array);
        }

        public static Color ToColor(string color)
        {
            int num = 0;
            color = color.TrimStart('#');
            color = Regex.Replace(color.ToLower(), "[g-zG-Z]", "");
            switch (color.Length)
            {
                case 3:
                    {
                        char[] array = color.ToCharArray();
                        int red = Convert.ToInt32(array[0].ToString() + array[0], 16);
                        int green = Convert.ToInt32(array[1].ToString() + array[1], 16);
                        num = Convert.ToInt32(array[2].ToString() + array[2], 16);
                        return Color.FromArgb(red, green, num);
                    }
                case 6:
                    {
                        char[] array = color.ToCharArray();
                        int red = Convert.ToInt32(array[0].ToString() + array[1], 16);
                        int green = Convert.ToInt32(array[2].ToString() + array[3], 16);
                        num = Convert.ToInt32(array[4].ToString() + array[5], 16);
                        return Color.FromArgb(red, green, num);
                    }
                default:
                    return Color.FromName(color);
            }
        }

        public static string ConvertSimpleFileName(string fullname, string repstring, int leftnum, int rightnum, int charnum)
        {
            string text = "";
            string text2 = "";
            string text3 = "";
            string text4 = "";
            string fileExtName = GetFileExtName(fullname);
            if (fileExtName == "" || fileExtName == null)
            {
                throw new Exception("字符串不含有扩展名信息");
            }
            int num = 0;
            int num2 = 0;
            num2 = fullname.LastIndexOf('.');
            text4 = fullname.Substring(0, num2);
            num = text4.Length;
            if (num2 > charnum)
            {
                text2 = text4.Substring(0, leftnum);
                text3 = text4.Substring(num - rightnum, rightnum);
                if (repstring == "" || repstring == null)
                {
                    return text2 + text3 + "." + fileExtName;
                }
                return text2 + repstring + text3 + "." + fileExtName;
            }
            return fullname;
        }

        public static string GetFileExtName(string filename)
        {
            string[] array = filename.Trim().Split('.');
            Array.Reverse(array);
            return array[0].ToString();
        }

        public static StringBuilder DataTableToJSON(DataTable dt)
        {
            return DataTableToJson(dt, dt_dispose: true);
        }

        public static StringBuilder DataTableToJson(DataTable dt, bool dt_dispose)
        {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.Append("[\r\n");
            string[] array = new string[dt.Columns.Count];
            int num = 0;
            string text = "{{";
            string text2 = "";
            foreach (DataColumn column in dt.Columns)
            {
                array[num] = column.Caption.ToLower().Trim();
                text = text + "'" + column.Caption.ToLower().Trim() + "':";
                text2 = column.DataType.ToString().Trim().ToLower();
                if (text2.IndexOf("int") > 0 || text2.IndexOf("deci") > 0 || text2.IndexOf("floa") > 0 || text2.IndexOf("doub") > 0 || text2.IndexOf("bool") > 0)
                {
                    object obj = text;
                    text = string.Concat(obj, "{", num, "}");
                }
                else
                {
                    object obj2 = text;
                    text = string.Concat(obj2, "'{", num, "}'");
                }
                text += ",";
                num++;
            }
            if (text.EndsWith(","))
            {
                text = text.Substring(0, text.Length - 1);
            }
            text += "}},";
            num = 0;
            object[] array2 = new object[array.Length];
            foreach (DataRow row in dt.Rows)
            {
                string[] array3 = array;
                for (int i = 0; i < array3.Length; i++)
                {
                    _ = array3[i];
                    array2[num] = row[array[num]].ToString().Trim().Replace("\\", "\\\\")
                        .Replace("'", "\\'");
                    switch (array2[num].ToString())
                    {
                        case "True":
                            array2[num] = "true";
                            break;
                        case "False":
                            array2[num] = "false";
                            break;
                    }
                    num++;
                }
                num = 0;
                stringBuilder.Append(string.Format(text, array2));
            }
            if (stringBuilder.ToString().EndsWith(","))
            {
                stringBuilder.Remove(stringBuilder.Length - 1, 1);
            }
            if (dt_dispose)
            {
                dt.Dispose();
            }
            return stringBuilder.Append("\r\n];");
        }

        public static bool StrIsNullOrEmpty(string str)
        {
            if (str == null || str.Trim() == "")
            {
                return true;
            }
            return false;
        }

        public static bool IsNumericList(string numList)
        {
            if (numList == "")
            {
                return false;
            }
            string[] array = numList.Split(',');
            foreach (string expression in array)
            {
                if (!IsNumeric(expression))
                {
                    return false;
                }
            }
            return true;
        }

        public static bool CheckColorValue(string color)
        {
            if (StrIsNullOrEmpty(color))
            {
                return false;
            }
            color = color.Trim().Trim('#');
            if (color.Length != 3 && color.Length != 6)
            {
                return false;
            }
            if (!Regex.IsMatch(color, "[^0-9a-f]", RegexOptions.IgnoreCase))
            {
                return true;
            }
            return false;
        }

        public static string GetAjaxPageNumbers(int curPage, int countPage, string callback, int extendPage)
        {
            string text = "page";
            int num = 1;
            int num2 = 1;
            string text2 = "<a href=\"###\" onclick=\"" + string.Format(callback, "&" + text + "=1");
            string text3 = "<a href=\"###\" onclick=\"" + string.Format(callback, "&" + text + "=" + countPage);
            text2 += "\">&laquo;</a>";
            text3 += "\">&raquo;</a>";
            if (countPage < 1)
            {
                countPage = 1;
            }
            if (extendPage < 3)
            {
                extendPage = 2;
            }
            if (countPage > extendPage)
            {
                if (curPage - extendPage / 2 > 0)
                {
                    if (curPage + extendPage / 2 < countPage)
                    {
                        num = curPage - extendPage / 2;
                        num2 = num + extendPage - 1;
                    }
                    else
                    {
                        num2 = countPage;
                        num = num2 - extendPage + 1;
                        text3 = "";
                    }
                }
                else
                {
                    num2 = extendPage;
                    text2 = "";
                }
            }
            else
            {
                num = 1;
                num2 = countPage;
                text2 = "";
                text3 = "";
            }
            StringBuilder stringBuilder = new StringBuilder("");
            stringBuilder.Append(text2);
            for (int i = num; i <= num2; i++)
            {
                if (i == curPage)
                {
                    stringBuilder.Append("<span>");
                    stringBuilder.Append(i);
                    stringBuilder.Append("</span>");
                }
                else
                {
                    stringBuilder.Append("<a href=\"###\" onclick=\"");
                    stringBuilder.Append(string.Format(callback, text + "=" + i));
                    stringBuilder.Append("\">");
                    stringBuilder.Append(i);
                    stringBuilder.Append("</a>");
                }
            }
            stringBuilder.Append(text3);
            return stringBuilder.ToString();
        }

        public static double ConvertToUnixTimestamp(DateTime date)
        {
            DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0);
            return Math.Floor((date - dateTime).TotalSeconds);
        }

        public static string JsonCharFilter(string sourceStr)
        {
            sourceStr = sourceStr.Replace("\\", "\\\\");
            sourceStr = sourceStr.Replace("\b", "\\\b");
            sourceStr = sourceStr.Replace("\t", "\\\t");
            sourceStr = sourceStr.Replace("\n", "\\\n");
            sourceStr = sourceStr.Replace("\n", "\\\n");
            sourceStr = sourceStr.Replace("\f", "\\\f");
            sourceStr = sourceStr.Replace("\r", "\\\r");
            return sourceStr.Replace("\"", "\\\"");
        }

        public static string MergeString(string source, string target)
        {
            return MergeString(source, target, ",");
        }

        public static string MergeString(string source, string target, string mergechar)
        {
            target = ((!StrIsNullOrEmpty(target)) ? (target + mergechar + source) : source);
            return target;
        }

        public static string OrderIdCreate(string flg, DateTime datetime)
        {
            return flg + OrderIdCreate(datetime);
        }

        public static string OrderIdCreate(DateTime dateTime)
        {
            string text = dateTime.ToString("yyyyMMddHHmmss");
            Random random = new Random();
            return text + random.Next(1000, 9999);
        }

        public static string Week(DateTime datetime)
        {
            string[] array = new string[7] { "星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六" };
            return array[Convert.ToInt32(DateTime.Now.DayOfWeek)];
        }

        public static bool InArray(string str, ArrayList al)
        {
            for (int i = 0; i < al.Count; i++)
            {
                if (str == al[i].ToString())
                {
                    return true;
                }
            }
            return false;
        }

        public static XmlDocument getXmlDoc(string xml)
        {
            XmlDocument xmlDocument = new XmlDocument();
            xmlDocument.LoadXml(xml);
            return xmlDocument;
        }

        public static int StrToInt(string p)
        {
            return StrToInt(p, 0);
        }
        public static Guid? StrToGuid(string p)
        {
            Guid uid = new Guid();
            if (Guid.TryParse(p, out uid))
            {
                return uid;
            }
            return null;
        }

        public static void ResponseJSON(string json)
        {
            HttpContext.Current.Response.Clear();
            HttpContext.Current.Response.ContentType = "Text/Plain";
            HttpContext.Current.Response.AddHeader("Access-Control-Allow-Origin", "*");
            HttpContext.Current.Response.Expires = 0;
            HttpContext.Current.Response.Cache.SetNoStore();
            HttpContext.Current.Response.Write(json);
            HttpContext.Current.Response.End();
        }

        public static string SaveRequestFile(string filename, string Path)
        {
            string result = "";
            HttpFileCollection files = HttpContext.Current.Request.Files;
            if (files.Count > 0)
            {
                int contentLength = files[filename].ContentLength;
                if ((double)(contentLength / 1000000) > 25)
                {
                    result = "1";
                }
                else
                {
                    Path = $"{Path}{DateTime.Now:yyyy/MM/dd}/";
                    if (!Directory.Exists(HttpContext.Current.Server.MapPath(Path)))
                    {
                        Directory.CreateDirectory(HttpContext.Current.Server.MapPath(Path));
                    }
                    string fileName = System.IO.Path.GetFileName(files[filename].FileName);
                    if (fileName != string.Empty && IsAllowedExtension(files[filename]))
                    {
                        string extension = System.IO.Path.GetExtension(fileName);
                        files[filename].ContentType.ToString();
                        try
                        {
                            string text = Guid.NewGuid().ToString().Replace("-", "") + extension;
                            string filename2 = HttpContext.Current.Server.MapPath(Path) + text;
                            files[filename].SaveAs(filename2);
                            result = Path + text;
                            return result;
                        }
                        catch (Exception)
                        {
                            return result;
                        }
                    }
                }
            }
            return result;
        }
        private static bool IsAllowedExtension(HttpPostedFile hifile)
        {
            bool ret = false;
            string extension = System.IO.Path.GetExtension(hifile.FileName).ToLower();
            string[] Exts = new[] { ".pdf", ".xls", ".xlsx", ".rar", ".7z", ".zip", ".doc", ".docx", ".jpg", ".png", ".bmp", ".txt" };
            if (Exts.Contains(extension))
            {
                return true;
            }
            return ret;
        }
        public static string SaveRequestFile(string filename, string Path, string filepath)
        {
            string result = "";
            HttpFileCollection files = HttpContext.Current.Request.Files;
            if (files.Count > 0)
            {
                Path = $"{Path}{DateTime.Now:yyyy/MM/dd}/";
                if (!Directory.Exists(HttpContext.Current.Server.MapPath(Path)))
                {
                    Directory.CreateDirectory(HttpContext.Current.Server.MapPath(Path));
                }
                string fileName = System.IO.Path.GetFileName(files[filename].FileName);
                if (fileName != string.Empty)
                {
                    string extension = System.IO.Path.GetExtension(fileName);
                    files[filename].ContentType.ToString();
                    try
                    {
                        string text = Guid.NewGuid().ToString().Replace("-", "") + extension;
                        string filename2 = HttpContext.Current.Server.MapPath(Path) + text;
                        files[filename].SaveAs(filename2);
                        result = Path + text;
                        return result;
                    }
                    catch (Exception)
                    {
                        return result;
                    }
                }
            }
            return result;
        }

        public static string RaadomRegber(int num)
        {
            string text = "";
            Random random = new Random();
            for (int i = 0; i < num; i++)
            {
                string text2 = random.Next(0, 9).ToString();
                text += text2;
            }
            return text.ToString();
        }

        public static string RaadomRegber()
        {
            string text = "ChiCTR-";
            Random random = new Random();
            text += GenerateRandom(6).ToUpperInvariant();
            text += "-";
            text += random.Next(10000000, 99999999);
            return text.ToString();
        }

        public static string GenerateRandom(int Length)
        {
            StringBuilder stringBuilder = new StringBuilder(52);
            Random random = new Random();
            for (int i = 0; i < Length; i++)
            {
                stringBuilder.Append(constant[random.Next(52)]);
            }
            return stringBuilder.ToString();
        }

        public static void WriteSearchCountByIp(string ip, string page, int count = 1)
        {
            string key = string.Format("{2}_{0}_{1:yyyyMMdd}", ip, DateTime.Now, page);
            WriteCookie("PageLimitIP", key, count.ToString(), 1440);
        }

        public static void AddSearchCountByIp(string ip, string page)
        {
            int searchCountByIp = GetSearchCountByIp(ip, page);
            string key = string.Format("{2}_{0}_{1:yyyyMMdd}", ip, DateTime.Now, page);
            WriteCookie("PageLimitIP", key, (searchCountByIp + 1).ToString(), 1440);
        }

        public static int GetSearchCountByIp(string ip, string page)
        {
            int result = 0;
            try
            {
                string key = string.Format("{2}_{0}_{1:yyyyMMdd}", ip, DateTime.Now, page);
                string cookie = GetCookie("PageLimitIP", key);
                if (!(cookie == ""))
                {
                    int.Parse(cookie);
                    return result;
                }
                return result;
            }
            catch
            {
                return result;
            }
        }
    }
    public class TypeParse
    {
        public static bool IsNumeric(object expression)
        {
            if (expression != null)
            {
                return IsNumeric(expression.ToString());
            }
            return false;
        }

        public static bool IsNumeric(string expression)
        {
            if (expression != null)
            {
                if (expression.Length > 0 && expression.Length <= 11 && Regex.IsMatch(expression, "^[-]?[0-9]*[.]?[0-9]*$") && (expression.Length < 10 || (expression.Length == 10 && expression[0] == '1') || (expression.Length == 11 && expression[0] == '-' && expression[1] == '1')))
                {
                    return true;
                }
            }
            return false;
        }

        public static bool IsDouble(object expression)
        {
            if (expression != null)
            {
                return Regex.IsMatch(expression.ToString(), "^([0-9])[0-9]*(\\.\\w*)?$");
            }
            return false;
        }

        public static bool StrToBool(object expression, bool defValue)
        {
            if (expression != null)
            {
                return StrToBool(expression.ToString(), defValue);
            }
            return defValue;
        }

        public static bool StrToBool(string expression, bool defValue)
        {
            if (expression != null)
            {
                if (string.Compare(expression, "true", ignoreCase: true) == 0)
                {
                    return true;
                }
                if (string.Compare(expression, "false", ignoreCase: true) == 0)
                {
                    return false;
                }
            }
            return defValue;
        }

        public static int StrToInt(object expression, int defValue)
        {
            if (expression != null)
            {
                return StrToInt(expression.ToString(), defValue);
            }
            return defValue;
        }

        public static int StrToInt(string str, int defValue)
        {
            if (string.IsNullOrEmpty(str) || str.Trim().Length >= 11 || !Regex.IsMatch(str.Trim(), "^([-]|[0-9])[0-9]*(\\.\\w*)?$"))
            {
                return defValue;
            }
            if (int.TryParse(str, out var result))
            {
                return result;
            }
            return Convert.ToInt32(StrToFloat(str, defValue));
        }

        public static float StrToFloat(object strValue, float defValue)
        {
            if (strValue == null)
            {
                return defValue;
            }
            return StrToFloat(strValue.ToString(), defValue);
        }

        public static float StrToFloat(string strValue, float defValue)
        {
            if (strValue == null || strValue.Length > 10)
            {
                return defValue;
            }
            float result = defValue;
            if (strValue != null && Regex.IsMatch(strValue, "^([-]|[0-9])[0-9]*(\\.\\w*)?$"))
            {
                float.TryParse(strValue, out result);
            }
            return result;
        }

        public static DateTime StrToDateTime(string strValue, DateTime defValue)
        {
            if (strValue == null || !IsDateTime(strValue) || strValue == "")
            {
                return defValue;
            }
            return DateTime.Parse(strValue);
        }

        public static bool IsDateTime(string strValue)
        {
            if (strValue == null || strValue == "")
            {
                return true;
            }
            string pattern = "[1-2]{1}[0-9]{3}((-|[.]){1}(([0]?[1-9]{1})|(1[0-2]{1}))((-|[.]){1}((([0]?[1-9]{1})|([1-2]{1}[0-9]{1})|(3[0-1]{1})))( (([0-1]{1}[0-9]{1})|2[0-3]{1}):([0-5]{1}[0-9]{1}):([0-5]{1}[0-9]{1})(\\.[0-9]{3})?)?)?)?$";
            if (Regex.IsMatch(strValue, pattern))
            {
                int num = -1;
                int num2 = -1;
                int num3 = -1;
                if (-1 != (num = strValue.IndexOf("-")))
                {
                    num2 = strValue.IndexOf("-", num + 1);
                    num3 = strValue.IndexOf(":");
                }
                else
                {
                    num = strValue.IndexOf(".");
                    num2 = strValue.IndexOf(".", num + 1);
                    num3 = strValue.IndexOf(":");
                }
                if (-1 == num2)
                {
                    return true;
                }
                if (-1 == num3)
                {
                    num3 = strValue.Length + 3;
                }
                int num4 = Convert.ToInt32(strValue.Substring(0, num));
                int num5 = Convert.ToInt32(strValue.Substring(num + 1, num2 - num - 1));
                int num6 = Convert.ToInt32(strValue.Substring(num2 + 1, num3 - num2 - 4));
                if ((num5 < 8 && 1 == num5 % 2) || (num5 >= 8 && num5 % 2 == 0))
                {
                    if (num6 < 32)
                    {
                        return true;
                    }
                }
                else if (num5 != 2)
                {
                    if (num6 < 31)
                    {
                        return true;
                    }
                }
                else if (num4 % 400 == 0 || (num4 % 4 == 0 && 0 < num4 % 100))
                {
                    if (num6 < 30)
                    {
                        return true;
                    }
                }
                else if (num6 < 29)
                {
                    return true;
                }
            }
            return false;
        }

        public static bool IsNumericArray(string[] strNumber)
        {
            if (strNumber == null)
            {
                return false;
            }
            if (strNumber.Length < 1)
            {
                return false;
            }
            foreach (string expression in strNumber)
            {
                if (!IsNumeric(expression))
                {
                    return false;
                }
            }
            return true;
        }

        public static int StrToInt(string str)
        {
            return StrToInt(str, 0);
        }

        public static int ObjectToInt(object expression)
        {
            return ObjectToInt(expression, 0);
        }

        public static int ObjectToInt(object expression, int defValue)
        {
            if (expression != null)
            {
                return StrToInt(expression.ToString(), defValue);
            }
            return defValue;
        }
    }
}