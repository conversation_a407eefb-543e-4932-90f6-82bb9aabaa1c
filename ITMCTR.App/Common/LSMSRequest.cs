using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Common
{
    public class LSMSRequest
    {
        public static bool IsPost()
        {
            return HttpContext.Current.Request.HttpMethod.Equals("POST");
        }

        public static bool IsGet()
        {
            return HttpContext.Current.Request.HttpMethod.Equals("GET");
        }

        public static string GetServerString(string strName)
        {
            if (HttpContext.Current.Request.ServerVariables[strName] == null)
            {
                return "";
            }
            return HttpContext.Current.Request.ServerVariables[strName].ToString();
        }

        public static string GetUrlReferrer()
        {
            string text = null;
            try
            {
                text = HttpContext.Current.Request.UrlReferrer.ToString();
            }
            catch
            {
            }
            if (text == null)
            {
                return "";
            }
            return text;
        }

        public static string GetCurrentFullHost()
        {
            HttpRequest request = HttpContext.Current.Request;
            if (!request.Url.IsDefaultPort)
            {
                return $"{request.Url.Host}:{request.Url.Port.ToString()}";
            }
            return request.Url.Host;
        }

        public static string GetHost()
        {
            return HttpContext.Current.Request.Url.Host;
        }

        public static string GetRawUrl()
        {
            return HttpContext.Current.Request.RawUrl;
        }

        public static bool IsBrowserGet()
        {
            string[] array = new string[6] { "ie", "opera", "netscape", "mozilla", "konqueror", "firefox" };
            string text = HttpContext.Current.Request.Browser.Type.ToLower();
            for (int i = 0; i < array.Length; i++)
            {
                if (text.IndexOf(array[i]) >= 0)
                {
                    return true;
                }
            }
            return false;
        }

        public static bool IsSearchEnginesGet()
        {
            if (HttpContext.Current.Request.UrlReferrer == null)
            {
                return false;
            }
            string[] array = new string[15]
            {
            "google", "yahoo", "msn", "baidu", "sogou", "sohu", "sina", "163", "lycos", "tom",
            "yisou", "iask", "soso", "gougou", "zhongsou"
            };
            string text = HttpContext.Current.Request.UrlReferrer.ToString().ToLower();
            for (int i = 0; i < array.Length; i++)
            {
                if (text.IndexOf(array[i]) >= 0)
                {
                    return true;
                }
            }
            return false;
        }

        public static string GetUrl()
        {
            return HttpContext.Current.Request.Url.ToString();
        }

        public static string GetQueryString(string strName)
        {
            return GetQueryString(strName, sqlSafeCheck: false);
        }

        public static string GetQueryString(string strName, bool sqlSafeCheck)
        {
            if (HttpContext.Current.Request.QueryString[strName] == null)
            {
                return "";
            }
            if (sqlSafeCheck && !Utils.IsSafeSqlString(HttpContext.Current.Request.QueryString[strName]))
            {
                return "unsafe string";
            }
            return HttpContext.Current.Request.QueryString[strName];
        }

        public static string GetPageName()
        {
            string[] array = HttpContext.Current.Request.Url.AbsolutePath.Split('/');
            return array[array.Length - 1].ToLower();
        }

        public static int GetParamCount()
        {
            return HttpContext.Current.Request.Form.Count + HttpContext.Current.Request.QueryString.Count;
        }

        public static string GetFormString(string strName)
        {
            return GetFormString(strName, sqlSafeCheck: false);
        }

        public static string GetFormString(string strName, bool sqlSafeCheck)
        {
            if (HttpContext.Current.Request.Form[strName] == null)
            {
                return "";
            }
            if (sqlSafeCheck && !Utils.IsSafeSqlString(HttpContext.Current.Request.Form[strName]))
            {
                return "unsafe string";
            }
            return HttpContext.Current.Request.Form[strName];
        }

        public static string GetString(string strName)
        {
            return GetString(strName, sqlSafeCheck: false);
        }

        public static string GetString(string strName, bool sqlSafeCheck)
        {
            if ("".Equals(GetQueryString(strName)))
            {
                return GetFormString(strName, sqlSafeCheck);
            }
            return GetQueryString(strName, sqlSafeCheck);
        }

        public static int GetQueryInt(string strName, int defValue)
        {
            return Utils.StrToInt(HttpContext.Current.Request.QueryString[strName], defValue);
        }

        public static int GetFormInt(string strName, int defValue)
        {
            return Utils.StrToInt(HttpContext.Current.Request.Form[strName], defValue);
        }

        public static int GetInt(string strName, int defValue)
        {
            if (GetQueryInt(strName, defValue) == defValue)
            {
                return GetFormInt(strName, defValue);
            }
            return GetQueryInt(strName, defValue);
        }

        public static float GetQueryFloat(string strName, float defValue)
        {
            return Utils.StrToFloat(HttpContext.Current.Request.QueryString[strName], defValue);
        }

        public static float GetFormFloat(string strName, float defValue)
        {
            return Utils.StrToFloat(HttpContext.Current.Request.Form[strName], defValue);
        }

        public static float GetFloat(string strName, float defValue)
        {
            if (GetQueryFloat(strName, defValue) == defValue)
            {
                return GetFormFloat(strName, defValue);
            }
            return GetQueryFloat(strName, defValue);
        }

        public static string GetIP()
        {
            string empty = string.Empty;
            empty = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            if (string.IsNullOrEmpty(empty))
            {
                empty = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
            }
            if (string.IsNullOrEmpty(empty))
            {
                empty = HttpContext.Current.Request.UserHostAddress;
            }
            if (string.IsNullOrEmpty(empty) || !Utils.IsIP(empty))
            {
                return "127.0.0.1";
            }
            return empty;
        }

        public static void SaveRequestFile(string path)
        {
            if (HttpContext.Current.Request.Files.Count > 0)
            {
                HttpContext.Current.Request.Files[0].SaveAs(path);
            }
        }
    }
}