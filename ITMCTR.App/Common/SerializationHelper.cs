using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Xml;
using System.Xml.Serialization;

namespace ITMCTR.App.Common
{
    public class SerializationHelper
    {
        private static Dictionary<int, XmlSerializer> serializer_dict = new Dictionary<int, XmlSerializer>();

        private SerializationHelper()
        {
        }

        public static XmlSerializer GetSerializer(Type t)
        {
            int hashCode = t.GetHashCode();
            if (!serializer_dict.ContainsKey(hashCode))
            {
                serializer_dict.Add(hashCode, new XmlSerializer(t));
            }
            return serializer_dict[hashCode];
        }

        public static object Load(Type type, string filename)
        {
            FileStream fileStream = null;
            try
            {
                fileStream = new FileStream(filename, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                XmlSerializer xmlSerializer = new XmlSerializer(type);
                return xmlSerializer.Deserialize(fileStream);
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                fileStream?.Close();
            }
        }

        public static bool Save(object obj, string filename)
        {
            bool flag = false;
            FileStream fileStream = null;
            try
            {
                fileStream = new FileStream(filename, FileMode.OpenOrCreate, FileAccess.Write, FileShare.ReadWrite);
                XmlSerializer xmlSerializer = new XmlSerializer(obj.GetType());
                xmlSerializer.Serialize(fileStream, obj);
                return true;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                fileStream?.Close();
            }
        }

        public static bool Save(object obj, string filename, XmlSerializerNamespaces xn)
        {
            FileStream fileStream = null;
            try
            {
                fileStream = new FileStream(filename, FileMode.Create, FileAccess.Write, FileShare.ReadWrite);
                XmlSerializer xmlSerializer = new XmlSerializer(obj.GetType());
                xmlSerializer.Serialize(fileStream, obj, xn);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
            finally
            {
                fileStream?.Close();
            }
        }

        public static string Serialize(object obj)
        {
            XmlSerializer serializer = GetSerializer(obj.GetType());
            MemoryStream memoryStream = new MemoryStream();
            XmlTextWriter xmlTextWriter = null;
            StreamReader streamReader = null;
            try
            {
                xmlTextWriter = new XmlTextWriter(memoryStream, Encoding.UTF8);
                xmlTextWriter.Formatting = Formatting.Indented;
                serializer.Serialize(xmlTextWriter, obj);
                memoryStream.Seek(0L, SeekOrigin.Begin);
                streamReader = new StreamReader(memoryStream);
                return streamReader.ReadToEnd();
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                xmlTextWriter?.Close();
                streamReader?.Close();
                memoryStream.Close();
            }
        }

        public static object DeSerialize(Type type, string s)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(s);
            try
            {
                XmlSerializer serializer = GetSerializer(type);
                return serializer.Deserialize(new MemoryStream(bytes));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}