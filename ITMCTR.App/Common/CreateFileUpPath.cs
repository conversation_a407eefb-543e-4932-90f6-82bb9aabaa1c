using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.IO;
namespace ITMCTR.App.Common
{
    public static class CreateFileUpPath
    {
        public static string GetSysUserPhotoPath()
        {
            var path = CreateBasePath("/Upload/SysUserPhoto/");
            return path;
        }
       
        private static string CreateBasePath(string Folder)
        {
            string path = HttpContext.Current.Server.MapPath(Folder);
            var year = DateTime.Now.Year.ToString();
            var month = DateTime.Now.Month.ToString();
            var day = DateTime.Now.Day.ToString();
            if (!Directory.Exists(path + year))
            {
                Directory.CreateDirectory(path + year);
            }
            if (!Directory.Exists(path + year + "/" + month))
            {
                Directory.CreateDirectory(path + year + "/" + month);
            }
            if (!Directory.Exists(path + year + "/" + month + "/" + day))
            {
                Directory.CreateDirectory(path + year + "/" + month + "/" + day);
            }
            return Folder + year + "/" + month + "/" + day + "/";
        }
    }
}
