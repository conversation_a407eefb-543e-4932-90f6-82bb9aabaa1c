using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using System.Net.Mail;
using System.Net;
using System.Configuration;
using System.Collections;
using System.Threading;
using System.Net.Mime;
using System.IO;

namespace ITMCTR.App.Common
{
    public class Email
    {
        private static object lockHelper = new object();
        private MailMessage mMailMessage;   //主要处理发送邮件的内容（如：收发人地址、标题、主体、图片等等）
        private SmtpClient mSmtpClient; //主要处理用smtp方式发送此邮件的配置信息（如：邮件服务器、发送端口号、验证方式等等）
        private int mSenderPort;   //发送邮件所用的端口号（htmp协议默认为25）
        private string mSenderServerHost;    //发件箱的邮件服务器地址（IP形式或字符串形式均可）
        private string mSenderPassword;    //发件箱的密码
        private string mSenderUsername;   //发件箱的用户名（即@符号前面的字符串，例如：<EMAIL>，用户名为：hello）
        private bool mEnableSsl;    //是否对邮件内容进行socket层加密传输
        private bool mEnablePwdAuthentication;  //是否对发件人邮箱进行密码验证
        private string mSendUser;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="server">发件箱的邮件服务器地址</param>
        /// <param name="toMail">收件人地址（可以是多个收件人，程序中是以“;"进行区分的）</param>
        /// <param name="fromMail">发件人地址</param>
        /// <param name="subject">邮件标题</param>
        /// <param name="emailBody">邮件内容（可以以html格式进行设计）</param>
        /// <param name="username">发件箱的用户名（即@符号前面的字符串，例如：<EMAIL>，用户名为：hello）</param>
        /// <param name="password">发件人邮箱密码</param>
        /// <param name="port">发送邮件所用的端口号（htmp协议默认为25）</param>
        /// <param name="sslEnable">true表示对邮件内容进行socket层加密传输，false表示不加密</param>
        /// <param name="pwdCheckEnable">true表示对发件人邮箱进行密码验证，false表示不对发件人邮箱进行密码验证</param>
        public Email(string server, string toMail,
            string toCC, string fromMail,
            string subject, string emailBody,
            string username, string password,
            string port, bool sslEnable,
            bool pwdCheckEnable, Encoding encoding)
        {
            try
            {

                mMailMessage = new MailMessage();
                mMailMessage.To.Add(toMail);
                if (!string.IsNullOrEmpty(toCC))
                {
                    mMailMessage.CC.Add(toCC.Replace(";", ",").TrimEnd(','));
                }

                mMailMessage.From = new MailAddress(fromMail);
                mMailMessage.Subject = subject;
                mMailMessage.Body = emailBody;
                mMailMessage.IsBodyHtml = true;
                mMailMessage.BodyEncoding = encoding;
                mMailMessage.Priority = MailPriority.Normal;
                this.mSenderServerHost = server;
                this.mSenderUsername = username;
                this.mSenderPassword = password;
                this.mSenderPort = Convert.ToInt32(port);
                this.mEnableSsl = sslEnable;
                this.mEnablePwdAuthentication = pwdCheckEnable;
                this.mSendUser = toMail + toCC;
                this.ErrorConut = 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        /// <summary>
        /// 添加附件
        /// </summary>
        /// <param name="attachmentsPath">附件的路径集合，以分号分隔</param>
        public void AddAttachments(string attachmentsPath)
        {
            try
            {
                if (string.IsNullOrEmpty(attachmentsPath)) return;
                string[] path = attachmentsPath.Split(';'); //以什么符号分隔可以自定义
                Attachment data;
                ContentDisposition disposition;
                for (int i = 0; i < path.Length; i++)
                {
                    data = new Attachment(path[i], MediaTypeNames.Application.Octet);
                    disposition = data.ContentDisposition;
                    disposition.CreationDate = File.GetCreationTime(path[i]);
                    disposition.ModificationDate = File.GetLastWriteTime(path[i]);
                    disposition.ReadDate = File.GetLastAccessTime(path[i]);
                    mMailMessage.Attachments.Add(data);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }
        public void AddAttachments(string[] path)
        {
            try
            {
                Attachment data;
                ContentDisposition disposition;
                for (int i = 0; i < path.Length; i++)
                {
                    string name = Path.GetFileName(path[i]);
                    string base64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(name));
                    data = new Attachment(path[i], MediaTypeNames.Application.Octet);
                    disposition = data.ContentDisposition;
                    disposition.CreationDate = File.GetCreationTime(path[i]);
                    disposition.ModificationDate = File.GetLastWriteTime(path[i]);
                    disposition.ReadDate = File.GetLastAccessTime(path[i]);
                    disposition.FileName = string.Format("=?utf-8?B?{0}?=", base64);   //指定附件的filename
                    data.Name = "attachment";
                    data.NameEncoding = Encoding.UTF8;
                    mMailMessage.Attachments.Add(data);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }
        public bool Send()
        {
            lock (lockHelper)
            {
                try
                {

                    if (mMailMessage != null)
                    {
                        mSmtpClient = new SmtpClient();
                        //mSmtpClient.Host = "smtp." + mMailMessage.From.Host;
                        mSmtpClient.Host = this.mSenderServerHost;
                        mSmtpClient.Port = this.mSenderPort;
                        //mSmtpClient.UseDefaultCredentials = false;
                        mSmtpClient.EnableSsl = this.mEnableSsl;
                        //mSmtpClient.ClientCertificates

                        if (this.mEnablePwdAuthentication)
                        {
                            System.Net.NetworkCredential nc = new System.Net.NetworkCredential(this.mSenderUsername, this.mSenderPassword);
                            //mSmtpClient.Credentials = new System.Net.NetworkCredential(this.mSenderUsername, this.mSenderPassword);
                            //NTLM: Secure Password Authentication in Microsoft Outlook Express
                            mSmtpClient.Credentials = nc.GetCredential(mSmtpClient.Host, mSmtpClient.Port, "NTLM");
                        }
                        else
                        {

                            mSmtpClient.Credentials = new System.Net.NetworkCredential(this.mSenderUsername, this.mSenderPassword);
                        }
                        mSmtpClient.DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network;
                        mSmtpClient.Send(mMailMessage);
                    }
                    ExceptionHelper.Record(this.mSendUser, this.mMailMessage.Subject, this.mMailMessage.Body);
                    return true;
                }
                catch (Exception ex)
                {
                    this.ErrorConut++;
                    ExceptionHelper.Record("发送异常：" + ex.Message, this.mSendUser, this.mMailMessage.Subject, this.mMailMessage.Body);
                    Console.WriteLine(ex.ToString());
                    return false;
                }
            }
        }
        public int ErrorConut { get; set; }
    }

    public class EmailManager
    {
        static string _smtpServer = ConfigurationManager.AppSettings["Email_SmtpServer"];
        static string _userName = ConfigurationManager.AppSettings["Email_UserName"];
        static string _password = ConfigurationManager.AppSettings["Email_PassWord"];
        static string _strFrom = ConfigurationManager.AppSettings["Email_FromMail"];
        static string _port = ConfigurationManager.AppSettings["Email_SmtpPort"];
        static string _author = ConfigurationManager.AppSettings["Email_AuthorImg"];
        static string _authorsign = ConfigurationManager.AppSettings["Email_AuthorSign"];
        static string _errortel = ConfigurationManager.AppSettings["Email_ErrorTel"];
        static void AsynSendEmail(string strTo, string strCC, string strSubject, string strBody,
                                    string strFrom, string smtpServer, string userName,
                                    string password, string attachments)
        {
            try
            {
                strBody += "<br/>" + _authorsign;
                if (!string.IsNullOrEmpty(_author))
                    strBody = strBody + string.Format("<br/><img src=\"{0}\"/>", _author);
                if (string.IsNullOrEmpty(strTo))
                {
                    strTo = strFrom;
                }
                Email email = new Email(smtpServer, strTo, strCC, _strFrom, strSubject, strBody, userName, password, _port, true, false, System.Text.Encoding.UTF8);
                if (!string.IsNullOrEmpty(attachments))
                    email.AddAttachments(attachments);
                email.ErrorConut = 0;
                bool f = false;
                while (!f && email.ErrorConut < 3)
                {
                    f = email.Send();
                    if (!f)
                        Thread.Sleep(60000);
                }
                if (!f)
                {
                    string[] user = _errortel.Split(';');
                    foreach (var item in user)
                    {
                        if (!string.IsNullOrEmpty(item))
                            LoggerHelper.Error(string.Format("{3:yyyyMMddHHmmss}邮件发送异常,请检查,发送人：{0},{1}",
                                string.IsNullOrEmpty(strTo) ? "" : strTo,
                                string.IsNullOrEmpty(strCC) ? "" : strCC,
                                strSubject, DateTime.Now));
                            //SMSPlugin.SMSManager.SendSMS(item, string.Format("{3:yyyyMMddHHmmss}邮件发送异常,请检查,发送人：{0},{1}",
                            //    string.IsNullOrEmpty(strTo) ? "" : strTo,
                            //    string.IsNullOrEmpty(strCC) ? "" : strCC,
                            //    strSubject, DateTime.Now));
                    }

                }

            }
            catch
            { }
        }
        delegate void takedelegate(string strTo, string CC, string strSubject, string strBody,
                                   string strFrom, string smtpServer, string userName,
                                   string password, string attachments);
        static void takecompleted(IAsyncResult ar)
        {
            object[] myObject = ar.AsyncState as object[];
            if (ar.IsCompleted)
            { }
        }
        public static void SendMail(string strTo, string strCC, string strSubject, string strBody,
                                    string strFrom, string smtpServer, string userName,
                                    string password, string attachments)
        {
            takedelegate dl = AsynSendEmail;
            IAsyncResult ar = dl.BeginInvoke(strTo, strCC, strSubject, strBody, strFrom, smtpServer, userName, password, attachments, takecompleted, new object[] { });

        }
        public static void SendMail(string strTo, string strCC, string strSubject, string strBody, string attachments)
        {
            EmailManager.SendMail(strTo, strCC, strSubject, strBody, _strFrom, _smtpServer, _userName, _password, "");
        }
        public static void SendMailByTemplate(string strTo, string strCC, string strSubject, string TemplateFile, Hashtable replaceKey, string attachments)
        {
            var strBody = System.IO.File.ReadAllText(TemplateFile, Encoding.UTF8); ;
            foreach (DictionaryEntry item in replaceKey)
            {
                string val = string.Format("{0}", item.Value != null ? item.Value.ToString() : "");
                strBody = strBody.Replace(item.Key.ToString(), val);
            }
            EmailManager.SendMail(strTo, strCC, strSubject, strBody, _strFrom, _smtpServer, _userName, _password, "");
        }
        public static void SendMail(string strTo, string strSubject, string strBody, string attachments)
        {
            EmailManager.SendMail(strTo, null, strSubject, strBody, _strFrom, _smtpServer, _userName, _password, "");
        }
        public static void SendMailByTemplate(string strTo, string strSubject, string TemplateFile, Hashtable replaceKey, string attachments)
        {
            var strBody = System.IO.File.ReadAllText(TemplateFile, Encoding.UTF8); ;
            foreach (DictionaryEntry item in replaceKey)
            {
                string val = string.Format("{0}", item.Value != null ? item.Value.ToString() : "");
                strBody = strBody.Replace(item.Key.ToString(), val);
            }
            EmailManager.SendMail(strTo, null, strSubject, strBody, _strFrom, _smtpServer, _userName, _password, "");
        }
        private static bool CheckMailSetting(string smtpServer, string userName, string password, string port)
        {
            return true;
        }

        public static bool CheckMailSetting()
        {
            return CheckMailSetting(_smtpServer, _userName, _password, _port);
        }

    }
}