using System;
using System.Collections.Generic;
using System.Text;
using System.IO;

namespace ITMCTR.App.Common
{

    public class ExceptionHelper
    {
        private static string exceptionFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmailLog");

        static ExceptionHelper()
        {
            if (!Directory.Exists(exceptionFolder))
            {
                Directory.CreateDirectory(exceptionFolder);
            }
        }

        public static void Record(string exceptionMsg)
        {
            string str = "Email-" + DateTime.Now.ToString("yyyyMMdd") + ".log";
            try
            {
                File.AppendAllText(Path.Combine(exceptionFolder, str), DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss: ") + exceptionMsg + "\r\n\r\n", Encoding.UTF8);
            }
            catch (Exception)
            {
            }
        }

        public static void Record(string who, string title, string message)
        {
            Record(string.Format("{0}\r\n{1}\r\n{2}", who, title, message));
        }
        public static void Record(string err, string who, string title, string message)
        {
            Record(string.Format("发送异常：{3}\r\n[{0}\r\n{1}\r\n{2}]", who, title, message, err));
        }
    }
}
