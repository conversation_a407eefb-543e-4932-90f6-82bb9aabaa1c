using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App.Common
{
    public class dataList
    {
        public string id { get; set; }
        public string name { get; set; }
    }
    public class dataObjectList
    {
        public string id { get; set; }
        public string name { get; set; }
        public object Count { get; set; }
    }
    public class LangItem<T>
    {
        public LangItem(T key, string cn, string en)
        {
            Key = key;
            EN = en;
            CN = cn;
        }
        public T Key { get; set; }
        public string Value { get { return $"{CN}/{EN}"; } }
        public string EN { get; set; }
        public string CN { get; set; }
    }
    public static class CommonList
    {
        public static List<dataList> getAuditState()
        {
            List<dataList> list = new List<dataList>();
            list.Add(new dataList { id = "", name = "请选择/Please select" });
            list.Add(new dataList { id = "3", name = "通过审核/Approved" });
            list.Add(new dataList { id = "4", name = "未通过审核/Rejected" });
            return list;
        }
        public static List<dataList> getSingleSecAuditState()
        {
            List<dataList> list = new List<dataList>();
            list.Add(new dataList { id = "", name = "请选择/Please select" });
            list.Add(new dataList { id = "3", name = "通过审核/Successful" });
            list.Add(new dataList { id = "4", name = "未通过审核/Failed" });
            return list;
        }
        public static List<dataList> getSingleSecAuditStateSuccessful()
        {
            List<dataList> list = new List<dataList>();
            list.Add(new dataList { id = "", name = "请选择/Please select" });
            list.Add(new dataList { id = "3", name = "通过审核/Successful" });
            list.Add(new dataList { id = "4", name = "未通过审核/Failed" });
            return list;
        }
        public static List<dataList> getAgainAuditState()
        {
            List<dataList> list = new List<dataList>();
            list.Add(new dataList { id = "", name = "请选择/Please select" });
            list.Add(new dataList { id = "3", name = "同意再修改申请/Agree with Edit Permission" });
            list.Add(new dataList { id = "4", name = "不同意再修改申请/Disagree with Edit Permission" });
            return list;
        }

        public static List<dataList> getCountryList()
        {
            List<dataList> list = new List<dataList>();
            list.Add(new dataList { id = "中国", name = "中国" });
            list.Add(new dataList { id = "其他", name = "其他" });
            return list;
        }
        public static List<dataList> getStudyNameList()
        {
            List<dataList> list = new List<dataList>();
            list.Add(new dataList { id = "研究类型", name = "研究类型" });
            list.Add(new dataList { id = "研究设计", name = "研究设计" });
            list.Add(new dataList { id = "研究阶段", name = "研究阶段" });
            return list;
        }
        public static List<LangItem<int>> getSexList()
        {
            var list = new List<LangItem<int>>() {
               new LangItem<int> ( 1,"男性","Male" ),
               new LangItem<int> ( 0,"女性","Female" )
            };
            return list;
        }
        public static List<LangItem<int>> getTrueOrFalse()
        {
            var list = new List<LangItem<int>>() {
               new LangItem<int> ( 1,"是","Yes" ),
               new LangItem<int> ( 0,"否","No" )
            };
            return list;
        }
        public static List<LangItem<int>> getFalseOrTrue()
        {
            var list = new List<LangItem<int>>() {
               new LangItem<int> ( 0,"否","No" ),
               new LangItem<int> ( 1,"是","Yes" )
            };
            return list;
        }
        public static List<LangItem<int>> getisTraditionalMedicine()
        {
            var list = new List<LangItem<int>>() {
               new LangItem<int> ( 1,"是","Yes" ),
               new LangItem<int> ( 0,"否","No" )
            };
            return list;
        }
        public static List<LangItem<int>> getStatusList()
        {
            var listRecruitmentStatuslist = new List<LangItem<int>>() {
               new LangItem<int> ( 0,"未填完","Incompleted" ),
               new LangItem<int> ( 1,"待审核","Not be verified" ),
               new LangItem<int> ( 2,"未通过审核","Failed" ),
               new LangItem<int> ( 3,"通过审核","Successful" )
            };
            return listRecruitmentStatuslist;
        }
        public static List<LangItem<string>> getLanglist()
        {
            var langlist = new List<LangItem<string>>() {
               new LangItem<string> ( "1009001","中文和英文","Chinese And English" ),
               new LangItem<string> ( "1009002","仅英文","English Only" )
            };
            return langlist;
        }
        public static List<LangItem<string>> getRegStatuslist()
        {
            var listRegStatuslist = new List<LangItem<string>>() {
               new LangItem<string> ( "1008001","预注册","Prospective registration" ),
               new LangItem<string> ( "1008002","补注册","Retrospective registration" )
            };
            return listRegStatuslist;
        }
        public static List<LangItem<string>> getCreateYearlist()
        {
            var list = new List<LangItem<string>>();
            for (int i = 0; i < 10; i++)
            {
                var year = DateTime.Now.AddYears(-1 * i).Year;
                list.Add(new LangItem<string>($"{year}", $"{year}", $"{year}"));
            }
            return list;
        }
        public static List<LangItem<string>> getRecruitmentStatuslist()
        {
            var listRecruitmentStatuslist = new List<LangItem<string>>() {
               new LangItem<string> ( "1004001","尚未开始","Not yet recruiting" ),
               new LangItem<string> ( "1004002","正在进行","Recruiting" ),
               new LangItem<string> ( "1004003","暂停或中断","Suspending" ),
               new LangItem<string> ( "1004004","结束","Completed" )
            };
            return listRecruitmentStatuslist;
        }
        public static List<LangItem<string>> getGenderlist()
        {
            var listGenderlist = new List<LangItem<string>>() {
               new LangItem<string> ( "1006001","男性","Male" ),
               new LangItem<string> ( "1006002","女性","Female" ),
               new LangItem<string> ( "1006003","男女均可","Both" )
            };
            return listGenderlist;
        }
        public static List<LangItem<string>> getFollowUpTimeUnitlist()
        {
            var listFollowUpTimeUnitlist = new List<LangItem<string>>() {
               new LangItem<string> ( "1007001","天","Day(s)" ),
               new LangItem<string> ( "1007002","周","Week(s)" ),
               new LangItem<string> ( "1007003","月","Month(s)" ),
               new LangItem<string> ( "1007004","年","Year(s)" ),
               new LangItem<string> ( "1007005","小时","Hour(s)" )
            };
            return listFollowUpTimeUnitlist;
        }
        public static List<LangItem<int>> getStatisticalEffectChiCTRPubliclist()
        {
            var listStatisticalEffectChiCTRPubliclist = new List<LangItem<int>>() {
               new LangItem<int> ( 1,"公开","Public" ),
               new LangItem<int> ( 0,"不公开","Private" )
            };
            return listStatisticalEffectChiCTRPubliclist;
        }
        public static List<LangItem<string>> getDataCollectionUnitlist()
        {
            var txtDataCollectionUnitlist = new List<LangItem<string>>() {
               new LangItem<string> ( "0","否","No" ),
               new LangItem<string> ( "1","是","Yes" )
            };
            return txtDataCollectionUnitlist;
        }
        public static List<LangItem<string>> getDataManagemenBoardlist()
        {
            var txtDataManagemenBoardlist = new List<LangItem<string>>() {
               new LangItem<string> ( "1","有","Yes" ),
               new LangItem<string> ( "0","无","No" ),
               new LangItem<string> ( "-1","暂未确定","Not yet" )
            };
            return txtDataManagemenBoardlist;
        }
    }
}