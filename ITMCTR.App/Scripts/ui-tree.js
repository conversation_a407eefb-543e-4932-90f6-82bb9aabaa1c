
var UITree = function () {
    var self = this;
    this.data = [];
    //var data = [{
    //    visitid: "",
    //    eventid: "",
    //    bookid: "",
    //    controlid: "",
    //    rules: [
    //        {
    //            group: 1,//[0����]
    //            operation: 1,//[= <> >= <=]
    //            value: "",
    //            relation: "and",
    //            items: [{ key: "", value: "" }]
    //        }
    //    ]
    //}];
    this.uitreefind = function (v, e, b, c) {
        for (var i = 0; i < self.data.length; i++) {
            var d = self.data[i];
            if (d.visitid == v && d.eventid == e && d.bookid == b && d.controlid == c) {
                return d;
            }
        }
        return null;
    };
    var handleAddItem = function (v, e, b, c, n, s, desc, disName) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            Item.name = n;
        } else {
            self.data.push({
                name: disName,
                visitid: v,
                eventid: e,
                bookid: b,
                controlid: c,
                Options: s,
                Desc: desc,
                rules: []
            });
        }
    }
    var handleAddRule = function (v, e, b, c, rules, t) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            rules.display = Item.name
            Item.rules.push(rules);
        }
    }
    var handleFindRule = function (v, e, b, c, index) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            var ru = Item.rules[index];
            if (ru != null)
                return ru;
        }
        return null;
    }
    var handleUpdateRule = function (v, e, b, c, index, rules) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            var ru = Item.rules[index];
            if (ru != null)
                ru = rules;
        }
    }
    var handleDelItem = function (v, e, b, c) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            self.data.splice(jQuery.inArray(Item, self.data), 1);
        }
    }
    var handleDelRule = function (v, e, b, c, index) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            Item.rules.splice(index, 1);
        }
    }
    var handleResult = function () { return data; }
    var handleRefreshUI = function () {
        $("#tableDiv").empty();
        $("#AddNoSelectList").tmpl(data).appendTo("#tableDiv");
    }
    var handleTrialCheckProjectTree = function (data) {
        $('#tree_2').jstree({
            'plugins': ["wholerow", "checkbox", "types"],
            'core': {
                "themes": {
                    "responsive": false
                },
                'data': data
            },
            "types": {
                "default": {
                    "icon": "fa fa-folder icon-state-warning icon-lg"
                },
                "file": {
                    "icon": "fa fa-file icon-state-warning icon-lg"
                }
            }
        });
    }

    return {
        init: function (data) {
            handleTrialCheckProjectTree(data);
        },
        refush: function () {
            handleRefreshUI();
        },
        additem: function (v, e, b, c, n, s, desc, disName) {
            handleAddItem(v, e, b, c, n, s, desc, disName);
        },
        addrule: function (v, e, b, c, rules) {
            handleAddRule(v, e, b, c, rules);
        },
        delrule: function (v, e, b, c, index) {
            handleDelRule(v, e, b, c, index);
        },
        delitem: function (v, e, b, c) {
            handleDelItem(v, e, b, c);
        },
        resultdata: function () {
            return handleResult();
        },
        update: function (v, e, b, c, index, rule) {
            return handleUpdateRule(v, e, b, c, index, rule);
        },
        findrule: function (v, e, b, c, index) {
            return handleFindRule(v, e, b, c, index);
        }
    };
}();
var DocumentTree = function () {
    var self = this;
    this.data = [];
    //var data = [{
    //    visitid: "",
    //    eventid: "",
    //    bookid: "",
    //    controlid: "",
    //    rules: [
    //        {
    //            group: 1,//[0����]
    //            operation: 1,//[= <> >= <=]
    //            value: "",
    //            relation: "and",
    //            items: [{ key: "", value: "" }]
    //        }
    //    ]
    //}];
    this.uitreefind = function (DocId, e, b, c) {
        for (var i = 0; i < self.data.length; i++) {
            var d = self.data[i];
            if (d.DocId == DocId) {
                return d;
            }
        }
        return null;
    };
    var handleAddItem = function (v, e, b, c, n, s, desc, disName) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            Item.name = n;
        } else {
            self.data.push({
                name: disName,
                visitid: v,
                eventid: e,
                bookid: b,
                controlid: c,
                Options: s,
                Desc: desc,
                rules: []
            });
        }
    }
    var handleAddRule = function (v, e, b, c, rules, t) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            rules.display = Item.name
            Item.rules.push(rules);
        }
    }
    var handleFindRule = function (v, e, b, c, index) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            var ru = Item.rules[index];
            if (ru != null)
                return ru;
        }
        return null;
    }
    var handleUpdateRule = function (v, e, b, c, index, rules) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            var ru = Item.rules[index];
            if (ru != null)
                ru = rules;
        }
    }
    var handleDelItem = function (v, e, b, c) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            self.data.splice(jQuery.inArray(Item, self.data), 1);
        }
    }
    var handleDelRule = function (v, e, b, c, index) {
        var Item = self.uitreefind(v, e, b, c);
        if (Item != null) {
            Item.rules.splice(index, 1);
        }
    }
    var handleResult = function () { return data; }
    var handleRefreshUI = function () {
        $("#tableDiv").empty();
        $("#AddNoSelectList").tmpl(data).appendTo("#tableDiv");
    }
    var handleTrialCheckProjectTree = function (data) {
        $('#select_Tree').jstree({
            'plugins': ["wholerow", "checkbox", "types"],
            'core': {
                "themes": {
                    "responsive": false
                },
                'data': data
            },
            "types": {
                "default": {
                    "icon": "fa fa-folder icon-state-warning icon-lg"
                },
                "file": {
                    "icon": "fa fa-file icon-state-warning icon-lg"
                }
            }
        });
    }

    return {
        init: function (data) {
            handleTrialCheckProjectTree(data);
        },
        refush: function () {
            handleRefreshUI();
        },
        additem: function (v, e, b, c, n, s, desc, disName) {
            handleAddItem(v, e, b, c, n, s, desc, disName);
        },
        addrule: function (v, e, b, c, rules) {
            handleAddRule(v, e, b, c, rules);
        },
        delrule: function (v, e, b, c, index) {
            handleDelRule(v, e, b, c, index);
        },
        delitem: function (v, e, b, c) {
            handleDelItem(v, e, b, c);
        },
        resultdata: function () {
            return handleResult();
        },
        update: function (v, e, b, c, index, rule) {
            return handleUpdateRule(v, e, b, c, index, rule);
        },
        findrule: function (v, e, b, c, index) {
            return handleFindRule(v, e, b, c, index);
        }
    };
}();