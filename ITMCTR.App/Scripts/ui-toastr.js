var UIToastr = function () {
    var toastrList = [];
    var clickToastr = function (url) {
        if (url == null || url == "null" && url == "")
            return;
        window.location.href = url;
    }
    var search = function (Nid) {
        for (var i = 0; i < toastrList.length; i++) {
            if (toastrList[i].Nid == Nid)
                return false;
        }
        return true;
    }
    var getLanguage = function () {
        if (document.cookie.length > 0) {
            var cookies = document.cookie.split(";");
            for (var i = 0; i < cookies.length; i++) {
                var items = cookies[i].trim().split("=");
                if (items[0] == "Localization.CurrentUICulture" || items[0] == " Localization.CurrentUICulture") {
                    return items[1];
                }
            }
        }
        return "";
    };
    var toastShow = function (title, content, url, Nid,Type) {
        
        var shortCutFunction = 'info';
        var $timeOut = $('#timeOut');
        var $extendedTimeOut = $('#extendedTimeOut');
        var $showEasing = $('#showEasing');
        var $hideEasing = $('#hideEasing');
        var $showMethod = $('#showMethod');
        var $hideMethod = $('#hideMethod');
        
        toastr.options = {
            "closeButton": true,
            "debug": true,
            "positionClass": "toast-bottom-right",
            "onclick": null,
            "showDuration": "1000",
            "hideDuration": "1000",
            "timeOut": "0",
            "extendedTimeOut": "0",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };
        toastr.options.onclick = function () {
            window.open("/UserPlatform/MessageAction?Nid=" + Nid + "&Type=" + Type);
            //$.ajax({
            //    type: "POST",
            //    dataType: "json",
            //    url: "/UserPlatform/MessageAction?Nid=" + Nid,
            //    success: function (data) {
            //        //Success=true, url = model.AccessUrl
            //        if (data.Success) {
                        
            //            //    window.location.href = '@Url.Action("Notices","UserPlatform")?Nid=' + Nids;
            //        }
            //        else {
            //            alert("վ������תʧ��");
            //        }
            //    }
            //});
           
        };
        var $toast = toastr[shortCutFunction](content, title); // Wire up an event handler to a button in the toast, if it exists
        $toastlast = $toast;
        
        if ($toast.find('#okBtn').length) {
            $toast.delegate('#okBtn', 'click', function () {
                alert('you clicked me. i was toast #' + toastIndex + '. goodbye!');
                $toast.remove();
            });
        }
        if ($toast.find('#surpriseBtn').length) {
            $toast.delegate('#surpriseBtn', 'click', function () {
                alert('Surprise! you clicked me. i was toast #' + toastIndex + '. You could perform an action here.');
            });
        }
    };
    return {
        //main function to initiate the module
        init: function () {
            window.setInterval(function () {
                var language = getLanguage();
                $.ajax({
                    type: "GET",
                    dataType: "json",
                    url: "/UserPlatform/LoadMessage",
                    data: {},
                    success: function (data) {
                        var list = data.dataList;
                        for (var i = 0; i < list.length; i++) {
                            if (search(list[i].Nid)){
                                toastrList.push(list[i]);
                                var Nid = list[i].Nid;
                                var AccessUrl = list[i].AccessUrl;
                                var Title = "";
                                var Content = "";
                                var Type = list[i].Type;
                                if (language == "zh-CN") {
                                    Title = list[i].Title;
                                    Content = list[i].Content;
                                }
                                else {
                                    Title = list[i].TitleEN;
                                    Content = list[i].ContentEN;
                                }
                                toastShow(Title, Content, AccessUrl, Nid, Type)
                            }
                        }
                    }
                });
            }, 3000)
        }
    };
}();
