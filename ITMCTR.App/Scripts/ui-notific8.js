var UINotific8 = function () {
    var notificShow = function (title, content, url) {
        var settings = {
            theme: 'teal',
            sticky: true,
            horizontalEdge: 'bottom',
            verticalEdge: 'right'
        };
        settings.heading = '<a href="' + url + '" style="color:#fff;">' + title + '</a>';
        settings.life = '5000';
        $.notific8('zindex', 11500);
        $.notific8(content, settings);
    };
    return {
        //main function to initiate the module
        init: function () {
            window.setInterval(function () {
                $.ajax({
                    type: "GET",
                    dataType: "json",
                    url: "/UserPlatform/LoadMessage",
                    data: {},
                    success: function (data) {
                        var list = data.dataList;
                        for (var i = 0; i < list.length; i++) {
                            var Title = list[i].Title;
                            var Content = list[i].Content;
                            var AccessUrl = list[i].AccessUrl;
                            notificShow(Title, Content, AccessUrl)
                        }
                    }
                });
            }, 3000)
        }
    };

}();

jQuery(document).ready(function() {    
   UINotific8.init();
});