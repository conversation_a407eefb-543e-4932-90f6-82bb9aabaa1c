using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace ITMCTR.App
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            routes.MapRoute(name: "InformationCode", url: "{lang}/Home/InformationCode/{code}", constraints: new { lang = "zh-CN|en-US" }, defaults: new { controller = "Home", action = "InformationCode" });
            routes.MapRoute(name: "NewsInfo", url: "{lang}/Home/NewsInfo/{Nid}", constraints: new { lang = "zh-CN|en-US" }, defaults: new { controller = "Home", action = "NewsInfo" });
            routes.MapRoute(
                 name: "Localization", // 路由名称
                 url: "{lang}/{controller}/{action}/{id}", // 带有参数的 URL
                 constraints: new { lang = "zh-CN|en-US" }, //限制可输入的语言项
                 defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }//参数默认值
               );

            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
            );
        }
    }
}
