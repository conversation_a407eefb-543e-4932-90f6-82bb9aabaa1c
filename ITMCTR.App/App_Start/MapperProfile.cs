using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using AutoMapper;
using ITMCTR.Core.Models;

namespace ITMCTR.App
{
    public class MapperProfile : Profile
    {
        public MapperProfile()
        {

        }

        protected virtual void CreateMaps()
        {
            CreateMap<SA_NewProject, SA_NewProjectHistory>();
            CreateMap<SA_NewProjectHistory, SA_NewProject>();
            CreateMap<SA_Interventions, SA_InterventionsHistory>();
            CreateMap<SA_InterventionsHistory, SA_Interventions > ();
            CreateMap <SA_Outcomes, SA_OutcomesHistory> ();
            CreateMap <SA_OutcomesHistory, SA_Outcomes > ();
            CreateMap <SA_ResearchAddress, SA_ResearchAddressHistory> ();
            CreateMap <SA_ResearchAddressHistory, SA_ResearchAddress > ();
            CreateMap <SA_Diagnostic, SA_DiagnosticHistory> ();
            CreateMap <SA_DiagnosticHistory, SA_Diagnostic > ();
            CreateMap <SA_SecondarySponsor, SA_SecondarySponsorHistory> ();
            CreateMap <SA_SecondarySponsorHistory, SA_SecondarySponsor > ();
            CreateMap <SA_CollectingSample, SA_CollectingSampleHistory> ();
            CreateMap <SA_CollectingSampleHistory, SA_CollectingSample > ();
        }
    }
}