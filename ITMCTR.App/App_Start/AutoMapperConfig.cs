using AutoMapper;
using ITMCTR.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace ITMCTR.App
{
    public class AutoMapperConfig
    {
        public static void Config()
        {
            AutoMapper.Mapper.Initialize(cfg =>
           {
               cfg.CreateMap<SA_NewProject, SA_NewProjectHistory>();
               cfg.CreateMap<SA_NewProjectHistory, SA_NewProject>();
               cfg.CreateMap<SA_Interventions, SA_InterventionsHistory>();
               cfg.CreateMap<SA_InterventionsHistory, SA_Interventions>();
               cfg.CreateMap<SA_Outcomes, SA_OutcomesHistory>();
               cfg.CreateMap<SA_OutcomesHistory, SA_Outcomes>();
               cfg.CreateMap<SA_ResearchAddress, SA_ResearchAddressHistory>();
               cfg.CreateMap<SA_ResearchAddressHistory, SA_ResearchAddress>();
               cfg.CreateMap<SA_Diagnostic, SA_DiagnosticHistory>();
               cfg.CreateMap<SA_DiagnosticHistory, SA_Diagnostic>();
               cfg.CreateMap<SA_SecondarySponsor, SA_SecondarySponsorHistory>();
               cfg.CreateMap<SA_SecondarySponsorHistory, SA_SecondarySponsor>();
               cfg.CreateMap<SA_CollectingSample, SA_CollectingSampleHistory>();
               cfg.CreateMap<SA_CollectingSampleHistory, SA_CollectingSample>();
               cfg.CreateMap<Models.WebsiteInformationViewModel, Core.Models.SA_WebsiteInformation>();
               cfg.CreateMap<Core.Models.SA_WebsiteInformation, Models.WebsiteInformationViewModel>();
               cfg.CreateMap<Models.NewsEditModel, Core.Models.SA_News>();
               cfg.CreateMap<Core.Models.SA_News, Models.NewsEditModel>();
           });
        }
    }
}