<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="APP_NAME_网站名称" xml:space="preserve">
    <value>国际传统医学临床试验注册平台</value>
    <comment>网站名称</comment>
  </data>
  <data name="APP_TIP_浏览器兼容" xml:space="preserve">
    <value>国际传统医学临床试验注册平台 京ICP备07032215号-5   提示：推荐使用IE8.0以上版本 宽屏显示分辨率下使用系统</value>
  </data>
  <data name="QT_INDEX_中医特色1" xml:space="preserve">
    <value>传统医学领域临床试验（研究）在线注册。</value>
  </data>
  <data name="QT_INDEX_中医特色2" xml:space="preserve">
    <value>包括中医、针灸、推拿、草药、阿育吠陀、顺势疗法、</value>
  </data>
  <data name="QT_INDEX_中医特色3" xml:space="preserve">
    <value>尤那尼医学、补充和替代药物等，不限制地域及国别。</value>
  </data>
  <data name="QT_INDEX_关于我们" xml:space="preserve">
    <value>关于我们</value>
  </data>
  <data name="QT_INDEX_如何参与" xml:space="preserve">
    <value>如何参与</value>
  </data>
  <data name="QT_INDEX_如何检索" xml:space="preserve">
    <value>如何检索</value>
  </data>
  <data name="QT_INDEX_平台介绍" xml:space="preserve">
    <value>平台介绍</value>
  </data>
  <data name="QT_INDEX_平台简介" xml:space="preserve">
    <value>平台简介</value>
  </data>
  <data name="QT_INDEX_平台详介" xml:space="preserve">
    <value>可对传统医学领域开展的临床试验（研究）进行在线注册。该平台由中国中医药循证医学中心负责。</value>
  </data>
  <data name="QT_INDEX_我要检索" xml:space="preserve">
    <value>我要检索</value>
  </data>
  <data name="QT_INDEX_我要注册" xml:space="preserve">
    <value>我要注册</value>
  </data>
  <data name="QT_INDEX_找试验" xml:space="preserve">
    <value>找试验</value>
  </data>
  <data name="QT_INDEX_新闻" xml:space="preserve">
    <value>新闻公告</value>
  </data>
  <data name="QT_INDEX_立即登录" xml:space="preserve">
    <value>立即登录</value>
  </data>
  <data name="QT_INDEX_试验检索" xml:space="preserve">
    <value>试验检索</value>
  </data>
  <data name="QT_INDEX_试验注册" xml:space="preserve">
    <value>试验注册</value>
  </data>
  <data name="QT_INDEX_语言" xml:space="preserve">
    <value>网站语言</value>
  </data>
  <data name="QT_INDEX_首页" xml:space="preserve">
    <value>首页</value>
  </data>
  <data name="UR_Index_语言" xml:space="preserve">
    <value>语言/language</value>
  </data>
  <data name="UR_LOGIN_5秒自动跳转" xml:space="preserve">
    <value>5秒自动跳转</value>
  </data>
  <data name="UR_LOGIN_忘记密码" xml:space="preserve">
    <value>忘记密码</value>
  </data>
  <data name="UR_LOGIN_新用户注册" xml:space="preserve">
    <value>新用户注册</value>
  </data>
  <data name="UR_LOGIN_此账号未通过审核请联系管理员" xml:space="preserve">
    <value>此账号未通过审核请联系管理员</value>
  </data>
  <data name="UR_LOGIN_此账号正在审核中" xml:space="preserve">
    <value>此账号正在审核中</value>
  </data>
  <data name="UR_LOGIN_注册成功" xml:space="preserve">
    <value>注册成功，请等待审核</value>
    <comment>Registration is successful, please wait for the review</comment>
  </data>
  <data name="UR_LOGIN_用户名或密码错误" xml:space="preserve">
    <value>用户名或密码错误</value>
  </data>
  <data name="UR_LOGIN_用户登录" xml:space="preserve">
    <value>用户登录</value>
  </data>
  <data name="UR_LOGIN_秒后自动跳转" xml:space="preserve">
    <value>秒后自动跳转</value>
  </data>
  <data name="UR_LOGIN_立即登录" xml:space="preserve">
    <value>立即登录</value>
  </data>
  <data name="UR_LOGIN_管理员登陆" xml:space="preserve">
    <value>管理员登陆</value>
  </data>
  <data name="UR_LOGIN_系纺提示" xml:space="preserve">
    <value>系统提示</value>
  </data>
  <data name="UR_LOGIN_请输入密码" xml:space="preserve">
    <value>请输入密码</value>
  </data>
  <data name="UR_LOGIN_请输入用户名" xml:space="preserve">
    <value>请输入用户名</value>
  </data>
  <data name="UR_LOGIN_请输验证码" xml:space="preserve">
    <value>请输请输验证码</value>
  </data>
  <data name="UR_LOGIN_返回" xml:space="preserve">
    <value>返回</value>
  </data>
  <data name="UR_LOGIN_锁定" xml:space="preserve">
    <value>账号已锁定，请稍后尝试</value>
  </data>
  <data name="UR_LOGIN_验证码错误" xml:space="preserve">
    <value>验证码错误</value>
  </data>
  <data name="UR_MSG_项目不通过内容" xml:space="preserve">
    <value>点击消息可查看项目详情</value>
  </data>
  <data name="UR_MSG_项目不通过审核主题" xml:space="preserve">
    <value>您的一个项目未能通过审核</value>
  </data>
  <data name="UR_MSG_项目通过内容" xml:space="preserve">
    <value>点击消息可查看项目详情</value>
  </data>
  <data name="UR_MSG_项目通过审核主题" xml:space="preserve">
    <value>您的一个项目通过审核</value>
  </data>
  <data name="UR_REG_固定电话" xml:space="preserve">
    <value>固定电话</value>
  </data>
  <data name="UR_REG_国家" xml:space="preserve">
    <value>国家</value>
  </data>
  <data name="UR_REG_女" xml:space="preserve">
    <value>女</value>
  </data>
  <data name="UR_REG_已有帐号" xml:space="preserve">
    <value>已有帐号</value>
  </data>
  <data name="UR_REG_性别" xml:space="preserve">
    <value>性别</value>
  </data>
  <data name="UR_REG_您的姓名" xml:space="preserve">
    <value>您的姓名</value>
  </data>
  <data name="UR_REG_我要注册" xml:space="preserve">
    <value>我要注册</value>
  </data>
  <data name="UR_REG_手机号码" xml:space="preserve">
    <value>手机号码</value>
  </data>
  <data name="UR_REG_注册单位名称" xml:space="preserve">
    <value>注册单位名称</value>
  </data>
  <data name="UR_REG_用户名" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="UR_REG_用户密码" xml:space="preserve">
    <value>用户密码</value>
  </data>
  <data name="UR_REG_电子邮件" xml:space="preserve">
    <value>电子邮件</value>
  </data>
  <data name="UR_REG_男" xml:space="preserve">
    <value>男</value>
  </data>
  <data name="UR_REG_确认密码" xml:space="preserve">
    <value>确认密码</value>
  </data>
  <data name="UR_REG_立即注册" xml:space="preserve">
    <value>立即注册</value>
  </data>
  <data name="UR_REG_联系地址" xml:space="preserve">
    <value>联系地址</value>
  </data>
  <data name="UR_REG_请输入" xml:space="preserve">
    <value>请输入</value>
  </data>
  <data name="UR_REG_请选择" xml:space="preserve">
    <value>请选择</value>
  </data>
  <data name="View_发布时间" xml:space="preserve">
    <value>发布时间</value>
  </data>
  <data name="View_新闻列表" xml:space="preserve">
    <value>新闻列表</value>
  </data>
  <data name="View_新闻详情" xml:space="preserve">
    <value>新闻详情</value>
  </data>
</root>