<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="APP_NAME_网站名称" xml:space="preserve">
    <value>International Traditional Medicine Clinical Trial Registry</value>
    <comment>网站名称</comment>
  </data>
  <data name="APP_TIP_浏览器兼容" xml:space="preserve">
    <value>ITMCTR  BJ-ICP:07032215-5   Tip: IE8 is recommended Use the system with widescreen display resolution above</value>
  </data>
  <data name="QT_INDEX_中医特色1" xml:space="preserve">
    <value>Trials accepted for registration are those conducted in the field of traditional medicine</value>
  </data>
  <data name="QT_INDEX_中医特色2" xml:space="preserve">
    <value>from any country including Chinese medicine, acupuncture, herbal medicine, ayurvida, homeopathy</value>
  </data>
  <data name="QT_INDEX_中医特色3" xml:space="preserve">
    <value>yonani, complementary and supplementary medicines.</value>
  </data>
  <data name="QT_INDEX_关于我们" xml:space="preserve">
    <value>About Us</value>
  </data>
  <data name="QT_INDEX_如何参与" xml:space="preserve">
    <value>How to Get Involved</value>
  </data>
  <data name="QT_INDEX_如何检索" xml:space="preserve">
    <value>How to Search</value>
  </data>
  <data name="QT_INDEX_平台介绍" xml:space="preserve">
    <value>About ITMCTR</value>
  </data>
  <data name="QT_INDEX_平台简介" xml:space="preserve">
    <value>Introduction</value>
  </data>
  <data name="QT_INDEX_平台详介" xml:space="preserve">
    <value>Online register of clinical trials (researches) being conducted in the field of traditional medicine.&lt;br/&gt;
ITMCTR is operated by the China Center for Evidence Based Traditional Chinese Medicine.</value>
  </data>
  <data name="QT_INDEX_我要检索" xml:space="preserve">
    <value>Search Trials</value>
  </data>
  <data name="QT_INDEX_我要注册" xml:space="preserve">
    <value>Register Now</value>
  </data>
  <data name="QT_INDEX_找试验" xml:space="preserve">
    <value>Find Trials</value>
  </data>
  <data name="QT_INDEX_新闻" xml:space="preserve">
    <value>News</value>
  </data>
  <data name="QT_INDEX_立即登录" xml:space="preserve">
    <value>Log In</value>
  </data>
  <data name="QT_INDEX_试验检索" xml:space="preserve">
    <value>Search Trials</value>
  </data>
  <data name="QT_INDEX_试验注册" xml:space="preserve">
    <value>Register Trials</value>
  </data>
  <data name="QT_INDEX_语言" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="QT_INDEX_首页" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="UR_Index_语言" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="UR_LOGIN_5秒自动跳转" xml:space="preserve">
    <value>jump in 5 seconds</value>
  </data>
  <data name="UR_LOGIN_忘记密码" xml:space="preserve">
    <value>Forgot password?</value>
  </data>
  <data name="UR_LOGIN_新用户注册" xml:space="preserve">
    <value>Sign Up Now</value>
  </data>
  <data name="UR_LOGIN_此账号未通过审核请联系管理员" xml:space="preserve">
    <value>Review faild.Please contact the administrator</value>
  </data>
  <data name="UR_LOGIN_此账号正在审核中" xml:space="preserve">
    <value>This account is being reviewed</value>
  </data>
  <data name="UR_LOGIN_注册成功" xml:space="preserve">
    <value>Registration is successful, please wait for the review</value>
    <comment>Registration is successful, please wait for the review</comment>
  </data>
  <data name="UR_LOGIN_用户名或密码错误" xml:space="preserve">
    <value>Incorrect ID or password. </value>
  </data>
  <data name="UR_LOGIN_用户登录" xml:space="preserve">
    <value>User Login</value>
  </data>
  <data name="UR_LOGIN_秒后自动跳转" xml:space="preserve">
    <value>seconds later jump to back</value>
  </data>
  <data name="UR_LOGIN_立即登录" xml:space="preserve">
    <value>Log In</value>
  </data>
  <data name="UR_LOGIN_管理员登陆" xml:space="preserve">
    <value>Admin login</value>
  </data>
  <data name="UR_LOGIN_系纺提示" xml:space="preserve">
    <value>TIP</value>
  </data>
  <data name="UR_LOGIN_请输入密码" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="UR_LOGIN_请输入用户名" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="UR_LOGIN_请输验证码" xml:space="preserve">
    <value>Captcha</value>
  </data>
  <data name="UR_LOGIN_返回" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="UR_LOGIN_锁定" xml:space="preserve">
    <value>Account locked, please try again later</value>
  </data>
  <data name="UR_LOGIN_验证码错误" xml:space="preserve">
    <value>Incorrect Captcha</value>
  </data>
  <data name="UR_MSG_项目不通过内容" xml:space="preserve">
    <value>view the project details</value>
  </data>
  <data name="UR_MSG_项目不通过审核主题" xml:space="preserve">
    <value>One project has been rejected.</value>
  </data>
  <data name="UR_MSG_项目通过内容" xml:space="preserve">
    <value>view the project details</value>
  </data>
  <data name="UR_MSG_项目通过审核主题" xml:space="preserve">
    <value>your project is approved</value>
  </data>
  <data name="UR_REG_固定电话" xml:space="preserve">
    <value>Landline</value>
  </data>
  <data name="UR_REG_国家" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="UR_REG_女" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="UR_REG_已有帐号" xml:space="preserve">
    <value>Have an Account Already</value>
  </data>
  <data name="UR_REG_性别" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="UR_REG_您的姓名" xml:space="preserve">
    <value>Your Name</value>
  </data>
  <data name="UR_REG_我要注册" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="UR_REG_手机号码" xml:space="preserve">
    <value>Mobile Phone</value>
  </data>
  <data name="UR_REG_注册单位名称" xml:space="preserve">
    <value>Institution</value>
  </data>
  <data name="UR_REG_用户名" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="UR_REG_用户密码" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="UR_REG_电子邮件" xml:space="preserve">
    <value>Email Address</value>
  </data>
  <data name="UR_REG_男" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="UR_REG_确认密码" xml:space="preserve">
    <value>Confirm password</value>
  </data>
  <data name="UR_REG_立即注册" xml:space="preserve">
    <value>Register Now</value>
  </data>
  <data name="UR_REG_联系地址" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="UR_REG_请输入" xml:space="preserve">
    <value>Enter</value>
  </data>
  <data name="UR_REG_请选择" xml:space="preserve">
    <value>Choose from</value>
  </data>
  <data name="View_发布时间" xml:space="preserve">
    <value>Release Time</value>
  </data>
  <data name="View_新闻列表" xml:space="preserve">
    <value>News</value>
  </data>
  <data name="View_新闻详情" xml:space="preserve">
    <value>News Details</value>
  </data>
</root>