$(function () {
    var form1 = $('#registerForm');
    var error1 = $('.alert-danger', form1);
    var success1 = $('.alert-success', form1);
    jQuery.validator.addMethod('isUsername', function (value, element) {
        var reg = /^[a-zA-Z][a-zA-Z0-9]{3,16}$/;
        return this.optional(element) || (reg.test(value));
    }, '用户名字母开头,4-16个字符（字母、数字）');
    jQuery.validator.addMethod("isPassword", function (value, element) {
        var reg = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,20}$/;
        return reg.test(value);
    }, "密码中必须包含含数字、字母、特殊符号");

    form1.validate({
        errorElement: 'span', //default input error message container
        errorClass: 'help-block help-block-error help-inline', // default input error message class
        focusInvalid: false, // do not focus the last invalid input
        ignore: "",  // validate all fields including form hidden input
        invalidHandler: function (event, validator) { //display error alert on form submit
            success1.hide();
            error1.show();
        },
        rules: {
            Username: {
                required: true,
                isUsername: true,
                remote: {
                    url: "/UserPlatform/isExistsUserName",
                    type: "post",
                    dataType: "json",
                    async: false,
                    data: { user_name: function () { return $("#Username").val(); } }
                }
            },
            Password: {
                minlength: 6,
                isPassword: true
            }
        },
        messages: {
            Username: {
                required: "请输入用户名",
                isUsername: "用户名，4-16个字符（字母、数字、下划线），注册后不能更改",
                minlength: "用户名长度不能小于3个字符",
                maxlength: "用户名长度不能大于16个字符",
                remote: "用户名已存在"
            }
        },
        highlight: function (element) { // hightlight error inputs
            $(element)
                .closest('.zhuce_content1_right').addClass('has-error'); // set error class to the control group
        },
        unhighlight: function (element) { // revert the change done by hightlight
            $(element)
                .closest('.zhuce_content1_right').removeClass('has-error'); // set error class to the control group
        },
        success: function (label) {
            label
                .closest('.zhuce_content1_right').removeClass('has-error'); // set success class to the control group
        },
        submitHandler: function (form) {
            success1.show();
            error1.hide();
            form.submit();
        }
    });
})