var frominput = 0;
function CascadeCheck(allSelector, itemSelector, checkedVals) {
    var rootThis = this;
    this.Parent = $(allSelector);
    this.Children = $(itemSelector);
    var load = function () {
        var allChecked = false;
        if (checkedVals && checkedVals.length > 0) {
            allChecked = true;
            rootThis.Children.each(function () {
                var checked = false;
                for (var i = 0; i < checkedVals.length; i++) {
                    var $this = $(this);
                    if ($this.val() == checkedVals[i]) {
                        $this.attr("checked", "checked");
                        checked = true;
                        break;
                    }
                }
                allChecked = allChecked && checked;
                return true;
            });
        }
        rootThis.Parent.attr("checked", allChecked ? "checked" : "");
    };
    this.Init = function () {
        load();
        rootThis.Children.click(function () {
            var allChecked = true;
            if (this.checked) {
                rootThis.Children.each(function () {
                    if (this.checked)
                        return true;
                    allChecked = false;
                    return false;
                });
            }
            else
                allChecked = false;
            rootThis.Parent.attr("checked", allChecked ? "checked" : "");
        });
        rootThis.Parent.click(function () {
            var status = this.checked;
            rootThis.Children.each(function () {
                this.checked = status;
            });
        });
    };
    this.GetCheckedChildren = function () {
        return rootThis.Children.filter(function () {
            return this.checked;
        });
    };
    this.GetCheckedValues = function () {
        var array = new Array();
        this.GetCheckedChildren().each(function () {
            array.push(this.value);
        });
        return array;
    };
    this.CheckAll = function () {
        rootThis.Children.attr("checked", "checked");
        rootThis.Parent.attr("checked", "checked");
    };
    this.ClearAll = function () {
        rootThis.Children.attr("checked", "");
        rootThis.Parent.attr("checked", "");
    };
    this.Inverse = function () {
        return rootThis.Children.attr("checked", function () {
            return !this.checked;
        });
    };
};
function listStudyTypeChange(sel) {
    if (sel == "1001003") {
        $("#divDiagnostic").show();
        $("#divInter").hide();
    } else {
        $("#divInter").show();
        $("#divDiagnostic").hide();
    }
}
$(function () {
    $("#valueRemove1").hide();
    $("#valueRemove2").hide();
    $("#valueRemove3").hide();
    $("#valueRemove4").hide();
    $("#valueRemove5").hide();

    $("#listStudyType").change(function () {
        var sel = $(this).val();
        listStudyTypeChange(sel);
    });
    var sel = $("#listStudyType").val();
    listStudyTypeChange(sel);

    $("#fileEthicalCommittee").change(function () {
        if ($("#fileEthicalCommittee").val() != "") {
            $("#valueRemove1").show();
        }
    });


    $("#fileNationalFDASanction").change(function () {
        if ($("#fileNationalFDASanction").val() != "") {
            $("#valueRemove2").show();
        }
    });
    $("#fileStudyPlan").change(function () {
        if ($("#fileStudyPlan").val() != "") {
            $("#valueRemove3").show();
        }
    });
    $("#fileInformedConsent").change(function () {
        if ($("#fileInformedConsent").val() != "") {
            $("#valueRemove4").show();
        }
    });
    $("#fileExperimentalresults").change(function () {
        if ($("#fileExperimentalresults").val() != "") {
            $("#valueRemove5").show();
        }
    });
    $("#valueRemove1").click(function () {
        $("#fileEthicalCommittee").val("");
        $("#valueRemove1").hide();
    });
    $("#valueRemove2").click(function () {
        $("#fileNationalFDASanction").val("");
        $("#valueRemove2").hide();
    });
    $("#valueRemove3").click(function () {
        $("#fileStudyPlan").val("");
        $("#valueRemove3").hide();
    });
    $("#valueRemove4").click(function () {
        $("#fileInformedConsent").val("");
        $("#valueRemove4").hide();
    });
    $("#valueRemove5").click(function () {
        $("#fileExperimentalresults").val("");
        $("#valueRemove5").hide();
    });

    var $listLang = $("#listLang");
    function LangIsCn() {
        return $listLang.val() == "1009001";
    }

    function ChangeLang() {
        $(".en").show();
        switch ($listLang.val()) {
            case ("1009001"):
                $(".heigthLef").height(48);
                $(".cn").show();
                break;
            case ("1009002"):
                $(".heigthLef").height(0);
                $(".cn").hide();
                break;
        }
    }
    ChangeLang();
    $listLang.change(ChangeLang);

    var $listpublic = $("#listStatisticalEffectChiCTRPublic");
    function Changepublic() {
        $(".publicshow").show();
        switch ($listpublic.val()) {
            case ("1"):
                $(".publicshow").show();
                break;
            case ("0"):
                $(".publicshow").hide();
                break;
        }
    }
    Changepublic();
    $listpublic.change(Changepublic);
})


