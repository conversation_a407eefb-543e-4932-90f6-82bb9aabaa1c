$(function () {
    var form1 = $('#registerForm');
    var error1 = $('.alert-danger', form1);
    var success1 = $('.alert-success', form1);
    jQuery.validator.addMethod('isUsername', function (value, element) {
        var reg = /^[a-zA-Z][a-zA-Z0-9]{3,16}$/;
        return this.optional(element) || (reg.test(value));
    }, 'User name starts with a letter, 4-16 characters (letters, numbers)');
    jQuery.validator.addMethod("isPassword", function (value, element) {
        var reg = /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{6,20}$/;
        return reg.test(value);
    }, "The password must contain numbers, letters and special symbols");

    form1.validate({
        errorElement: 'span', //default input error message container
        errorClass: 'help-block help-block-error help-inline', // default input error message class
        focusInvalid: false, // do not focus the last invalid input
        ignore: "",  // validate all fields including form hidden input
        invalidHandler: function (event, validator) { //display error alert on form submit
            success1.hide();
            error1.show();
        },
        rules: {
            Username: {
                required: true,
                isUsername: true,
                remote: {
                    url: "/UserPlatform/isExistsUserName",
                    type: "post",
                    dataType: "json",
                    async: false,
                    data: { user_name: function () { return $("#Username").val(); } }
                }
            },
            Password: {
                minlength: 6,
                isPassword: true
            }
        },
        messages: {
            Username: {
                required: "username is required.",
                isUsername: "User name, 4-16 characters (letters, numbers, underscores), which cannot be changed after registration",
                remote: "username is exist"
            }
        },
        highlight: function (element) { // hightlight error inputs
            $(element)
                .closest('.zhuce_content1_right').addClass('has-error'); // set error class to the control group
        },
        unhighlight: function (element) { // revert the change done by hightlight
            $(element)
                .closest('.zhuce_content1_right').removeClass('has-error'); // set error class to the control group
        },
        success: function (label) {
            label
                .closest('.zhuce_content1_right').removeClass('has-error'); // set success class to the control group
        },
        submitHandler: function (form) {
            success1.show();
            error1.hide();
            form.submit();
        }
    });
})