
$(function () {
    $("#valueRemove1").hide()
    $("#valueRemove2").hide()
    $("#valueRemove3").hide()
    $("#valueRemove4").hide()
    $("#valueRemove5").hide()
    $("#fileEthicalCommittee").change(function () {
        if ($("#fileEthicalCommittee").val() != "") {
            $("#valueRemove1").show();
        }
    });
    $("#fileNationalFDASanction").change(function () {
        if ($("#fileNationalFDASanction").val() != "") {
            $("#valueRemove2").show();
        }
    });
    $("#fileStudyPlan").change(function () {
        if ($("#fileStudyPlan").val() != "") {
            $("#valueRemove3").show();
        }
    });
    $("#fileInformedConsent").change(function () {
        if ($("#fileInformedConsent").val() != "") {
            $("#valueRemove4").show();
        }
    });
    $("#fileExperimentalresults").change(function () {
        if ($("#fileExperimentalresults").val() != "") {
            $("#valueRemove5").show();
        }
    });
    $("#valueRemove1").click(function () {
        $("#fileEthicalCommittee").val("");
        $("#valueRemove1").hide();
    });
    $("#valueRemove2").click(function () {
        $("#fileNationalFDASanction").val("");
        $("#valueRemove2").hide();
    });
    $("#valueRemove3").click(function () {
        $("#fileStudyPlan").val("");
        $("#valueRemove3").hide();
    });
    $("#valueRemove4").click(function () {
        $("#fileInformedConsent").val("");
        $("#valueRemove4").hide();
    });
    $("#valueRemove5").click(function () {
        $("#fileExperimentalresults").val("");
        $("#valueRemove5").hide();
    });
    $("#listStudyType").change(function () {
        var sel = $(this).val();
        listStudyTypeChange(sel);
    });
    var sel = $("#listStudyType").val();
    listStudyTypeChange(sel);
});
function listStudyTypeChange(sel) {
    if (sel == "1001003") {
        $("#divDiagnostic").show();
        $("#divInter").hide();
    } else {
        $("#divInter").show();
        $("#divDiagnostic").hide();
    }
}

$(function () {
    $(".n2").addClass("menusel");
    if (jQuery().datepicker) {
        $('.date-picker').datepicker({
            rtl: App.isRTL(),
            language: 'zh-CN',
            orientation: "left",
            autoclose: true,
            format: "yyyy-mm-dd",
            todayBtn: "linked"
        });
    }
    var $listEcs = $("input[name='listEthicalCommitteeSanction']");

    function HaveEcs() {
        return $listEcs.filter(":checked").val() == "1";
    }
    function HaveTest() {
        var sel = $("#listStudyType").val();
        return (sel == "1001003");
    }
    function SetEcs() {
        $trEcs = $("#tbodyEcs");
        if (HaveEcs()) {
            $trEcs.show();
        }
        else {
            $trEcs.hide();
        }
    };
    function RegStatus() {
        var sel = $("#listRegStatus").val();
        return (sel == "1008001");
    }

    var messagesEn = {
        required: "This field is required.",
        remote: "Please fix this field.",
        email: "Please enter a valid email address.",
        url: "Please enter a valid URL.",
        date: "Please enter a valid date.",
        dateISO: "Please enter a valid date ( ISO ).",
        number: "Please enter a valid number.",
        digits: "Please enter only digits.",
        creditcard: "Please enter a valid credit card number.",
        equalTo: "Please enter the same value again.",
        maxlength: $.validator.format("Please enter no more than {0} characters."),
        minlength: $.validator.format("Please enter at least {0} characters."),
        rangelength: $.validator.format("Please enter a value between {0} and {1} characters long."),
        range: $.validator.format("Please enter a value between {0} and {1}."),
        max: $.validator.format("Please enter a value less than or equal to {0}."),
        min: $.validator.format("Please enter a value greater than or equal to {0}."),
        compareDate: "The start date cannot be greater than the end date",
        compareDate1: "Beginning of study execute time should not be later than starting of recruiting time",
        compareDate2: "Ending of study execute time should not be earlier than closing of recruiting time",
        compareDate3: "Starting of recruiting time should not be earlier than the date of approved by ethic committee",
        compareDate4: "Prospective registration:Starting of recruiting time is earlier than today,<br/>Retrospective registration:Starting of recruiting time is later than today",
        compareAge: "The minimum age cannot be greater than the maximum age"
    };

    var messagesCn = {
        required: "这是必填字段",
        remote: "请修正此字段",
        email: "请输入有效的电子邮件地址",
        url: "请输入有效的网址",
        date: "请输入有效的日期",
        dateISO: "请输入有效的日期 (YYYY-MM-DD)",
        number: "请输入有效的数字",
        digits: "只能输入数字",
        creditcard: "请输入有效的信用卡号码",
        equalTo: "你的输入不相同",
        extension: "请输入有效的后缀",
        maxlength: $.validator.format("最多可以输入 {0} 个字符"),
        minlength: $.validator.format("最少要输入 {0} 个字符"),
        rangelength: $.validator.format("请输入长度在 {0} 到 {1} 之间的字符串"),
        range: $.validator.format("请输入范围在 {0} 到 {1} 之间的数值"),
        max: $.validator.format("请输入不大于 {0} 的数值"),
        min: $.validator.format("请输入不小于 {0} 的数值"),
        compareDate: "开始日期不能大于结束日期",
        compareDate1: "研究实施开始时间不能晚于征募观察对象开始时间",
        compareDate2: "研究实施结束时间不能早于征募观察对象结束时间",
        compareDate3: "征募观察对象开始时间不能早于伦理批准时间",
        compareDate4: "预注册:征募观察对象开始时间应晚于当前日期,<br/>补注册:征募观察对象开始时间应早于当前日期",
        compareAge: "最小年龄不能大于最大年龄"
    };

    SetEcs();

    $listEcs.click(SetEcs);

    var $listLang = $(":radio[name='listLang']");

    function LangIsCn() {
        return $(":radio[name='listLang']:checked").val() == "1009001";
    }
    function ChangeLang() {
        $(".en").show();
        switch ($(":radio[name='listLang']:checked").val()) {
            case ("1009001"):
                $(".heigthLef").height(48);
                $(".cn").show();
                $.extend($.validator.messages, messagesCn);
                break;
            case ("1009002"):
                $(".heigthLef").height(0);
                $(".cn").hide();
                $.extend($.validator.messages, messagesEn);
                break;
        }
    }
    ChangeLang();
    $listLang.change(ChangeLang);

    var $listpublic = $("#listStatisticalEffectChiCTRPublic");
    function Changepublic() {
        $(".publicshow").show();
        switch ($listpublic.val()) {
            case ("1"):
                $(".publicshow").show();
                break;
            case ("0"):
                $(".publicshow").hide();
                break;
        }
    }
    Changepublic();
    $listpublic.change(Changepublic);

    function RemoveComma() {
        $(".noComma").keydown(function (e) {
            var keyCode = e.keyCode || e.witch;
            return keyCode != 188;
        });
    }
    // RemoveComma();
    function AddInterItem() {
        var $hdnInterCount = $("#hdnInterCount");
        var index = parseInt($hdnInterCount.val());
        if (isNaN(index))
            index = 0;
        index++;
        $hdnInterCount.val(index);
        var $tbPlace = $("#tbInter");
        var $table = $("<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"850\"></table>");
        var $tr = $("<tr></tr>");
        $tr.append("<td width=\"90\"><p class=\"cn\">组别：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"cn\"><input name=\"GroupName" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline GroupName\" /></p></td>");
        $tr.append("<td width=\"110\"><p class=\"cn\">样本量：<span class=\"required\">*</span></p></td>");
        $tr.append("<td rowspan=\"2\" width=\"180\"><p><input name=\"SampleSize" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline SampleSize\" /></p></td>");
        var $td = $("<td rowspan=\"4\"></td>");
        var $p = $("<p></p>");
        var $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除该项干预措施Delete\" title=\"删除该干预措施。Click here to delete the intervention.\" />");
        $input.click(function () {
            if ($tbPlace.find("table").length > 1) {
                $(this).parents("table:first").remove();
                var $hdnInterCount = $("#hdnInterCount");
                var index = parseInt($hdnInterCount.val());
                index--;
                $hdnInterCount.val(index);
            }
            else {
                $tbPlace.find("input:text").val("");
            }
        });
        $p.append($input);
        $td.append($p);
        $p = $("<p></p>");
        $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"增加一项干预措施AddMore\" title=\"点击这里添加更多干预措施。Click here to add more.\" />");
        $input.click(AddInterItem);
        $p.append($input);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"90\"><p class=\"en\">Group：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"en\"><input name=\"GroupNameEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline GroupNameEn\" /></p></td>");
        $tr.append("<td width=\"110\"><p class=\"en\">Sample size：<span class=\"required\">*</span></p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"90\"><p class=\"cn\">干预措施：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"cn\"><input name=\"Measure" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline Measure\" /></p></td>");
        $tr.append("<td width=\"110\"><p class=\"cn\">干预措施代码：</p></td>");
        $tr.append("<td rowspan=\"2\"><p><input name=\"InterCode" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline\" /></p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"90\"><p class=\"en\" title=\"trade name and common name, dosage, usage, TCM formulation name\">Measure：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"en\"><input name=\"MeasureEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline MeasureEn\" /></p></td>");
        $tr.append("<td width=\"110\"><p class=\"en\">Intervention code：</p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    }
    $("#btnAddInter").click(AddInterItem);
    function AddPlaceItem() {
        var $hdnPlaceCount = $("#hdnPlaceCount");
        var index = parseInt($hdnPlaceCount.val());
        if (isNaN(index))
            index = 0;
        index++;
        $hdnPlaceCount.val(index);
        var $tbPlace = $("#tbPlace");
        var $table = $("<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"850\"></table>");
        var $tr = $("<tr></tr>");
        $tr.append("<td width=\"90\"><p class=\"cn\">国家：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"cn\"><input name=\"Country" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline Country\" /></p></td>");
        $tr.append("<td width=\"90\"><p class=\"cn\">省(直辖市)：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"cn\"><input name=\"Province" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline Province\" /></p></td>");
        $tr.append("<td width=\"90\"><p class=\"cn\">市(区县)：</p></td>");
        $tr.append("<td><p class=\"cn\"><input name=\"City" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline City\" /></p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"90\"><p class=\"en\">Country：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"en\"><input name=\"CountryEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline CountryEn\" /></p></td>");
        $tr.append("<td width=\"90\"><p class=\"en\">Province：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"en\"><input name=\"ProvinceEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline ProvinceEn\" /></p></td>");
        $tr.append("<td width=\"90\"><p class=\"en\">City：</p></td>");
        $tr.append("<td><p class=\"en\"><input name=\"CityEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline CityEn\" /></p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"90\"><p class=\"cn\">单位(医院)：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"cn\"><input name=\"Institution" + index + "\" type=\"text\"  maxlength=\"450\" class=\"form-control input-xlarge input-inline Institution\" /></p></td>");
        $tr.append("<td width=\"90\"><p class=\"cn\">单位级别：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"cn\"><input name=\"InstitutionLevel" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline InstitutionLevel\" /></p></td>");
        $tr.append("<td></td>");
        var $td = $("<td colspan=\"2\"></td>");
        var $p = $("<p></p>");
        var $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除该地点Delete\" title=\"删除该地点。Click here to delete the place.\" />");
        $input.click(function () {
            if ($tbPlace.find("table").length > 1) {
                $(this).parents("table:first").remove();
                var $hdnPlaceCount = $("#hdnPlaceCount");
                var index = parseInt($hdnPlaceCount.val());
                index--;
                $hdnPlaceCount.val(index);
            }
            else {
                $tbPlace.find("input:text").val("");
            }
        });
        $p.append($input);
        $td.append($p);
        $p = $("<p></p>");
        $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"增加一个地点More\" title=\"点击这里添加更多地点。Click here to add more.\" />");
        $input.click(AddPlaceItem);
        $p.append($input);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"90\"><p class=\"en\">Institution<br/>/hospital：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"en\"><input name=\"InstitutionEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline InstitutionEn\" /></p></td>");
        $tr.append("<td width=\"90\"><p class=\"en\">Level of the <br/>institution：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"en\"><input name=\"InstitutionLevelEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline InstitutionLevelEn\" /></p></td>");
        $tr.append("<td></td>");
        $tr.append("<td></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    }
    $("#btnAddPlace").click(AddPlaceItem);
    function AddSecSponsorItem() {
        var $hdnPlaceCount = $("#hdnSecSponsorCount");
        var index = parseInt($hdnPlaceCount.val());
        if (isNaN(index))
            index = 0;
        index++;
        $hdnPlaceCount.val(index);
        var $tbPlace = $("#tbSecSponsor");
        var prefix = "SecSponsor";
        var $table = $("<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"850\"></table>");
        var $tr = $("<tr></tr>");
        $tr.append("<td width=\"80\"><p class=\"cn\">国家：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"200\"><p class=\"cn\"><input name=\"" + prefix + "Country" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "Country\" /></p></td>");
        $tr.append("<td width=\"80\"><p class=\"cn\">省(直辖市)：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"200\"><p class=\"cn\"><input name=\"" + prefix + "Province" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "Province\" /></p></td>");
        $tr.append("<td width=\"80\"><p class=\"cn\">市(区县)：</p></td>");
        $tr.append("<td><p class=\"cn\"><input name=\"" + prefix + "City" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "City\" /></p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td><p class=\"en\">Country：<span class=\"required\">*</span></p></td>");
        $tr.append("<td><p class=\"en\"><input name=\"" + prefix + "CountryEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "CountryEn\" /></p></td>");
        $tr.append("<td><p class=\"en\">Province：<span class=\"required\">*</span></p></td>");
        $tr.append("<td><p class=\"en\"><input name=\"" + prefix + "ProvinceEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "ProvinceEn\" /></p></td>");
        $tr.append("<td><p class=\"en\">City：</p></td>");
        $tr.append("<td><p class=\"en\"><input name=\"" + prefix + "CityEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "CityEn\" /></p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td><p class=\"cn\">单位：<span class=\"required\">*</span></p></td>");
        $tr.append("<td><p class=\"cn\"><input name=\"" + prefix + "Institution" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "Institution\" /></p></td>");
        $tr.append("<td><p class=\"cn\">具体地址：<span class=\"required\">*</span></p></td>");
        $tr.append("<td colspan=\"2\"><p class=\"cn\"><input name=\"" + prefix + "Address" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "Address\" size=\"30\" /></p></td>");
        var $td = $("<td rowspan=\"2\"></td>");
        var $p = $("<p></p>");
        var $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除该单位Delete\" title=\"删除该单位。Click here to delete the institution.\" />");
        $input.click(function () {
            if ($tbPlace.find("table").length > 1) {
                $(this).parents("table:first").remove();
                var $hdnPlaceCount = $("#hdnSecSponsorCount");
                var index = parseInt($hdnPlaceCount.val());
                index--;
                $hdnPlaceCount.val(index);
            }
            else {
                $tbPlace.find("input:text").val("");
            }
        });
        $p.append($input);
        $td.append($p);
        $p = $("<p></p>");
        $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"增加一个单位More\" title=\"点击这里添加更多。Click here to add more.\" />");
        $input.click(AddSecSponsorItem);
        $p.append($input);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td><p class=\"en\">Institution：<span class=\"required\">*</span></p></td>");
        $tr.append("<td><p class=\"en\"><input name=\"" + prefix + "InstitutionEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "InstitutionEn\" /></p></td>");
        $tr.append("<td><p class=\"en\">Address：<span class=\"required\">*</span></p></td>");
        $tr.append("<td colspan=\"2\"><p class=\"en\"><input name=\"" + prefix + "AddressEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline " + prefix + "AddressEn\" size=\"30\" /></p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    }
    $("#btnAddSecSponsor").click(AddSecSponsorItem);
    var IndexType = "<select name=IndexTypeId{0} class='IndexTypeId form-control'><option value=4002001>主要指标/Primary indicator</option><option value=4002002>次要指标/Secondary indicator</option><option value=4002003>附加指标/Additional indicator</option><option value=4002004>副作用指标/Adverse events</option></select>";
    function AddIndexItem() {
        var $hdnIndexCount = $("#hdnIndexCount");
        var index = parseInt($hdnIndexCount.val());
        if (isNaN(index))
            index = 0;
        index++;
        $hdnIndexCount.val(index);
        var $tbPlace = $("#tbIndex");
        var $table = $("<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"850\"></table>");
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"110\"><p class=\"cn\">指标中文名：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"cn\"><input name=\"IndexName" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline IndexName\" /></p></td>");
        $tr.append("<td width=\"90\"><p class=\"cn\">指标类型：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"230\" rowspan=\"2\">" + IndexType.replace("{0}", index) + "</td>");
        var $td = $("<td rowspan=\"4\"></td>");
        var $p = $("<p></p>");
        var $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除该指标Delete\" title=\"删除该指标。Click here to delete the index.\" />");
        $input.click(function () {
            if ($tbPlace.find("table").length > 1) {
                $(this).parents("table:first").remove();
                var $hdnIndexCount = $("#hdnIndexCount");
                var index = parseInt($hdnIndexCount.val());
                index--;
                $hdnIndexCount.val(index);
            }
            else {
                $tbPlace.find("input:text").val("");
            }
        });
        $p.append($input);
        $td.append($p);
        $p = $("<p></p>");
        $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"增加一项指标More\" title=\"点击这里添加更多指标。Click here to add more.\" />");
        $input.click(AddIndexItem);
        $p.append($input);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"110\"><p class=\"en\">Outcome Name：<span class=\"required\">*</span></p></td>");
        $tr.append("<td width=\"180\"><p class=\"en\"><input name=\"IndexNameEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline IndexNameEn\" /></p></td>");
        $tr.append("<td width=\"90\"><p class=\"en\">Type：</p></td>");
        $table.append($tr);
        $tbPlace.append($table);

        $tr = $("<tr></tr>");
        $tr.append("<td  class=\"t\"><p class=\"cn\">测量时间点：</p></td>");
        $tr.append("<td><p class=\"cn\"><input type=\"text\" class=\"form-control input-xlarge input-inline\" maxlength=\"450\" name=\"txtIndexNamePointTime" + index + "\" /></p></td>");
        $tr.append("<td  class=\"t\"><p class=\"cn\">测量方法：</p></td>");
        $tr.append("<td><p class=\"cn\"><input type=\"text\" class=\"form-control input-xlarge input-inline\" maxlength=\"450\" name=\"txtIndexNameMeasureMethod" + index + "\" /></p></td>");
        $table.append($tr);
        $tbPlace.append($table);

        $tr = $("<tr></tr>");
        $tr.append("<td  class=\"t\"><p class=\"en\">Measure time point of outcome：</p></td>");
        $tr.append("<td><p class=\"en\"><input type=\"text\" class=\"form-control input-xlarge input-inline\" maxlength=\"450\" name=\"txtIndexNamePointTimeEn" + index + "\" /></p></td>");
        $tr.append("<td  class=\"t\"><p class=\"en\">Measure method：</p></td>");
        $tr.append("<td><p class=\"en\"><input type=\"text\" class=\"form-control input-xlarge input-inline\" maxlength=\"450\" name=\"txtIndexNameMeasureMethodEn" + index + "\" /></p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    }
    $("#btnAddIndex").click(AddIndexItem);
    var SpecimenAfterUse = "<select name=SpecimenAfterUse{0} class='SpecimenAfterUse form-control'><option value=1012001>使用后销毁/Destruction after use</option><option value=1012002>使用后保存/Preservation after use</option><option value=1012003>其它/Others</option></select>";
    function AddSpecimenItem() {
        var $hdnSpecimenCount = $("#hdnSpecimenCount");
        var index = parseInt($hdnSpecimenCount.val());
        if (isNaN(index))
            index = 0;
        index++;
        $hdnSpecimenCount.val(index);
        var $tbPlace = $("#tbSpecimen");
        var $table = $("<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"850\"></table>");
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"110\"><p class=\"cn\">标本中文名：</p></td>");
        $tr.append("<td width=\"240\"><p class=\"cn\"><input name=\"SpecimenName" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline SpecimenName\" /></p></td>");
        $tr.append("<td width=\"60\"><p class=\"cn\">组织：</p></td>");
        $tr.append("<td width=\"270\"><p class=\"cn\"><input name=\"SpecimenTakeFrom" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline\" /></p></td>");
        var $td = $("<td rowspan=\"4\"></td>");
        var $p = $("<p></p>");
        var $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除该标本Delete\" title=\"删除该标本。Click here to delete the sample.\" />");
        $input.click(function () {
            if ($tbPlace.find("table").length > 1) {
                $(this).parents("table:first").remove();
                var $hdnSpecimenCount = $("#hdnSpecimenCount");
                var index = parseInt($hdnSpecimenCount.val());
                index--;
                $hdnSpecimenCount.val(index);
            }
            else {
                $tbPlace.find("input:text").val("");
            }
        });
        $p.append($input);
        $td.append($p);
        $p = $("<p></p>");
        $input = $("<input type=\"button\" class=\"btn btn-default\" value=\"增加一项AddMore\" title=\"点击这里添加更多标本。Click here to add more.\" />");
        $input.click(AddSpecimenItem);
        $p.append($input);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"110\"><p class=\"en\">Sample Name：</p></td>");
        $tr.append("<td width=\"240\"><p class=\"en\"><input name=\"SpecimenNameEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline SpecimenNameEn\" /></p></td>");
        $tr.append("<td width=\"60\"><p class=\"en\">Tissue：</p></td>");
        $tr.append("<td width=\"270\"><p class=\"en\"><input name=\"SpecimenTakeFromEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline\" /></p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"110\"><p class=\"cn\">人体标本去向：</p></td>");
        $tr.append("<td rowspan=\"2\" width=\"240\">" + SpecimenAfterUse.replace("{0}", index) + "</td>");
        $tr.append("<td width=\"60\"><p class=\"cn\">说明：</p></td>");
        $tr.append("<td width=\"270\"><p class=\"cn\"><input name=\"SpecimenNote" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline\" /></p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"110\"><p class=\"en\">Fate of sample：</p></td>");
        $tr.append("<td width=\"60\"><p class=\"en\">Note：</p></td>");
        $tr.append("<td width=\"270\"><p class=\"en\"><input name=\"SpecimenNoteEn" + index + "\" type=\"text\" maxlength=\"450\" class=\"form-control input-xlarge input-inline\" /></p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    }
    $("#btnAddSpecimen").click(AddSpecimenItem);
    jQuery.validator.addMethod("compareDate", function (value, element, param) {
        var startDate = jQuery(param).val();
        if (startDate.length == 0 || value.length == 0) return true;
        var date1 = new Date(Date.parse(startDate.replace(/-/g, '/')));
        var date2 = new Date(Date.parse(value.replace(/-/g, '/')));
        return date1 < date2;
    }, "开始日期不能大于结束日期");

    jQuery.validator.addMethod("compareDate1", function (value, element, param) {
        var startDate = jQuery(param).val();
        if (startDate.length == 0 || value.length == 0) return true;
        var date1 = new Date(Date.parse(startDate.replace(/-/g, '/')));
        var date2 = new Date(Date.parse(value.replace(/-/g, '/')));
        return date1 <= date2;
    }, "研究实施开始时间不能晚于征募观察对象开始时间");

    jQuery.validator.addMethod("compareDate2", function (value, element, param) {
        var startDate = jQuery(param).val();
        if (startDate.length == 0 || value.length == 0) return true;
        var date1 = new Date(Date.parse(startDate.replace(/-/g, '/')));
        var date2 = new Date(Date.parse(value.replace(/-/g, '/')));
        return date1 <= date2;
    }, "研究实施结束时间不能早于征募观察对象结束时间");

    jQuery.validator.addMethod("compareDate3", function (value, element, param) {
        var startDate = jQuery(param).val();
        if (startDate.length == 0 || value.length == 0) return true;
        var date1 = new Date(Date.parse(startDate.replace(/-/g, '/')));
        var date2 = new Date(Date.parse(value.replace(/-/g, '/')));
        return date1 >= date2;
    }, "征募观察对象开始时间不能早于伦理批准时间");

    jQuery.validator.addMethod("compareDate4", function (value, element, param) {
        console.log("compareDate4:" + value);
        if (value.length == 0) return true;
        var date1 = new Date(Date.parse(value.replace(/-/g, '/')));
        var date2 = new Date();
        if (RegStatus())
            return date1 >= date2;
        else
            return date1 <= date2;
    }, "预注册:征募观察对象开始时间不能早于当前日期,<br/>补注册:征募观察对象开始时间不能晚于当前日期");


    jQuery.validator.addMethod("compareAge", function (value, element, param) {
        var startDate = jQuery(param).val();
        if (startDate.length == 0 || value.length == 0) return true;
        var date1 = Number(startDate);
        var date2 = Number(value);
        return date1 < date2;
    }, "最小年龄不能大于最大年龄");
    var rulesCn = {
        listRegStatus: { required: true },
        listStudyType: { required: true },
        listStudyDesign: { required: true },
        listStudyStage: { required: true },
        listGender: { required: true },
        txtDataCollectionUnit: { required: true },
        txtDataManagemenBoard: { required: true },
        listRecruitmentStatus: { required: true },
        listStatisticalEffectChiCTRPublic: { required: true },

        txtTitle: { required: true },
        txtOfficialName: { required: true },
        txtApplier: { required: true },
        txtApplierCompany: { required: true },
        txtApplierAddress: { required: true },
        txtStudyLeader: { required: true },
        txtStudyLeaderAddress: { required: true },
        txtSponsor: { required: true },
        txtSponsorAddress: { required: true },
        SecSponsorInstitution0: { required: true },
        SecSponsorAddress0: { required: true },
        txtSourceOfSpends: { required: true },
        txtStudyAilment: { required: true },
        txtStudyAim: { required: true },
        txtSelectionCriteria: { required: true },
        txtEliminateCriteria: { required: true },
        SecSponsorCountry0: { required: true },
        SecSponsorProvince0: { required: true },
        GroupName0: { required: true },
        Measure0: { required: true },
        Country0: { required: true },
        Province0: { required: true },
        Institution0: { required: true },
        InstitutionLevel0: { required: true },
        IndexName0: { required: true },
        SpecimenName0: { required: true },
        txtGenerafionMethod: { required: true },
        // add by liubo 2016-03-14
        txtDataChargeUnit: { required: true },
        txtDataAnalysisUnit: { required: true },
        // end add

        txtStudyExecuteTime: { required: true },
        //研究实施开始时间 txtStudyExecuteTime 不能晚于征募观察对象开始时间 txtEnlistBeginTime
        txtEnlistBeginTime: { required: true, compareDate1: "#txtStudyExecuteTime", compareDate4: "#txtEnlistBeginTime" },

        // end add
        txtStudyEndTime: { compareDate: "#txtStudyExecuteTime", compareDate2: "#txtEnlistEndTime", required: true },
        //研究实施结束时间 txtStudyEndTime 不能早于征募观察对象结束时间 txtEnlistEndTime
        txtEnlistEndTime: { compareDate: "#txtEnlistBeginTime", required: true },

        txtStudyLeaderCompany: { required: true },
        fileStudyPlan: { required: true },
        txtEthicalCommitteeCName: { required: true },
        txtEthicalCommitteeCAddress: { required: true },
        txtEthicalCommitteeCEmail: { required: true },
        fileInformedConsent: { required: true },
    };
    var rulesEn = {
        listRegStatus: { required: true },
        listStudyType: { required: true },
        listStudyDesign: { required: true },
        listStudyStage: { required: true },
        listGender: { required: true },
        txtDataCollectionUnit: { required: true },
        txtDataManagemenBoard: { required: true },
        listRecruitmentStatus: { required: true },
        listStatisticalEffectChiCTRPublic: { required: true },

        txtTitleEn: { required: true },
        txtOfficialNameEn: { required: true },
        txtApplierEn: { required: true },
        txtApplierCompanyEn: { required: true },
        txtApplierPhone: { required: true },
        txtApplierEmail: { required: true, email: true },
        txtStudyLeaderEn: { required: true },
        txtStudyLeaderPhone: { required: true },
        txtStudyLeaderEmail: { required: true, email: true },
        txtApplierAddressEn: { required: true },
        txtStudyLeaderAddressEn: { required: true },
        txtFollowUpFrequency: { digits: true },
        txtSponsorEn: { required: true },
        txtSponsorAddressEn: { required: true },
        SecSponsorInstitutionEn0: { required: true },
        SecSponsorAddressEn0: { required: true },
        txtSourceOfSpendsEn: { required: true },
        txtStudyAilmentEn: { required: true },
        txtStudyAimEn: { required: true },
        listStudyDesign: { required: true },
        txtSelectionCriteriaEn: { required: true },
        txtEliminateCriteriaEn: { required: true },
    
        txtMinAge: { required: true, digits: true, min: 0 },
        txtMaxAge: { required: true, digits: true, min: 0, compareAge: "#txtMinAge" },

        listAgreeToSign: { required: true },
        txtTotalSampleSize: { required: true, number: true },

        SecSponsorCountryEn0: { required: true },
        SecSponsorProvinceEn0: { required: true },
        GroupNameEn0: { required: true },
        SampleSize0: { required: true, number: true },
        MeasureEn0: { required: true },
        CountryEn0: { required: true },
        ProvinceEn0: { required: true },
        InstitutionEn0: { required: true },
        InstitutionLevelEn0: { required: true },
        IndexNameEn0: { required: true },
        SpecimenNameEn0: { required: true },
        txtGenerafionMethodEn: { required: true },

        // add by liubo 2016-03-14
        txtDataChargeUnitEn: { required: true },
        txtDataAnalysisUnitEn: { required: true },

        txtStudyExecuteTime: { required: true },
        //研究实施开始时间 txtStudyExecuteTime 不能晚于征募观察对象开始时间 txtEnlistBeginTime
        txtEnlistBeginTime: { required: true, compareDate1: "#txtStudyExecuteTime", compareDate4: "#txtEnlistBeginTime" },

        // end add
        txtStudyEndTime: { compareDate: "#txtStudyExecuteTime", compareDate2: "#txtEnlistEndTime", required: true },
        //研究实施结束时间 txtStudyEndTime 不能早于征募观察对象结束时间 txtEnlistEndTime
        txtEnlistEndTime: { compareDate: "#txtEnlistBeginTime", required: true },


        txtStudyLeaderCompanyEn: { required: true },
        fileStudyPlan: { required: true },
        txtEthicalCommitteeCNameEN: { required: true },
        txtEthicalCommitteeCAddressEN: { required: true },
        txtEthicalCommitteeCEmail: { required: true },
        fileInformedConsent: { required: true },
    };
    var rulesEcsCn = {
        txtEthicalCommitteeFileID: { required: true },
        txtEthicalCommitteeName: { required: true },
        fileEthicalCommittee: { required: true },
        //开始【征募研究对象时间】不得早于【伦理批准时间】 txtEnlistBeginTime   >= txtEthicalCommitteeSanctionDate
        txtEthicalCommitteeSanctionDate: { required: true, compareDate3: "#txtEnlistBeginTime" },
        txtEthicalCommitteeCPhone: { required: true },
    };
    var rulesEcsEn = {
        txtEthicalCommitteeFileID: { required: true },
        txtEthicalCommitteeNameEn: { required: true },
        fileEthicalCommittee: { required: true },
        //开始【征募研究对象时间】不得早于【伦理批准时间】 txtEnlistBeginTime   >= txtEthicalCommitteeSanctionDate
        txtEthicalCommitteeSanctionDate: { required: true, compareDate3: "#txtEnlistBeginTime" },
        txtEthicalCommitteeCPhone: { required: true },
    };

    var rulesTestCn = {
        txtTargetCondition: { required: true },
        txtDifficultCondition: { required: true },
        txtStandard: { required: true },
        txtIndexTest: { required: true },
        txtSampleSizeT: { required: true },
        txtSampleSizeD: { required: true },
    };

    var rulesTestEn = {
        txtTargetConditionEn: { required: true },
        txtDifficultConditionEn: { required: true },
        txtStandardEn: { required: true },
        txtIndexTestEn: { required: true },
        txtSampleSizeT: { required: true },
        txtSampleSizeD: { required: true },
    };

    var rules = $.extend({}, rulesCn, rulesEn, rulesEcsCn, rulesEcsEn, rulesTestCn, rulesTestEn);

    var validator = $("#fromProjectAdd").validate({
        focusInvalid: true,
        errorElement: 'span',
        errorClass: 'help-block help-block-error help-inline',
        highlight: function (element) { // hightlight error inputs
            $(element).closest('td').addClass('has-error'); // set error class to the control group
            $(element).closest('p').addClass('has-error');
        },
        unhighlight: function (element) { // revert the change done by hightlight
            $(element).closest('td').removeClass('has-error'); // set error class to the control group
            $(element).closest('p').removeClass('has-error');
        },
        success: function (label) {
            label.closest('td').removeClass('has-error'); // set success class to the control group
            label.closest('p').removeClass('has-error');
        },
        rules: rules,
        //messages: messages,
        onsubmit: false
    });

    function GroupValidate(group, evt) {
        var isValid = true;
        for (var i in group) {
            var $item = $("*[name='" + i + "']");
            var t = $item.valid();
            if (!t) {
                if (isValid)
                    $item.focus();
                isValid = false;
            }
        }
        if (isValid) {
        }
        else {
            validator.focusInvalid();
            evt.preventDefault();
        }
        return isValid;
    }
    function ValidateExt(params) {
        var result = true;
        params[1].each(function () {
            var $this = $(this);
            if ($this.is(":visible")) {
                var val = $.trim($this.val());
                switch (params[0]) {
                    case ("required"):
                        result = (val != '');
                        break;
                    case ("number"):
                        result = (!isNaN(parseFloat(val)));
                        break;
                }
                if (!result) {
                    $this.focus();
                    alert(params[2]);
                }
            }
            return result;
        });
        return result;
    }
    function ValidateInOrder(order) {
        var result = true;
        for (var i in order) {
            var j = order[i];
            result = ValidateExt(j);
            if (!result)
                break;
        }
        return result;
    }
    $("#btnAdd").click(function (evt) {
        if (!confirm('是否确认提交?\rAre you sure you want to sumbmit it?')) {
            return false;
        }
        //if (!HaveEcs()) {
        //    if (LangIsCn())
        //        alert('请先获得本单位伦理委员会批准后再进行注册\rPlease obtain approval from the ethics committee before proceeding with the registration');
        //    else
        //        alert('Please obtain approval from the ethics committee before proceeding with the registration');
        //    return false;
        //}
        var rules2;
        var theForm = $('#fromProjectAdd');
        if (!theForm) {
            theForm = document.aspnetForm;
        }
        rules2 = $.extend({}, rulesEn);
        if (HaveEcs()) {
            $.extend(rules2, rulesEcsEn);
        }
        if (HaveTest()) {
            $.extend(rules2, rulesTestEn);
        }

        if (LangIsCn()) {
            $.extend(rules2, rulesCn);
            if (HaveEcs()) {
                $.extend(rules2, rulesEcsCn);
            }
            if (HaveTest()) {
                $.extend(rules2, rulesTestCn);
            }
        }
        var result = GroupValidate(rules2, evt);
        if (result) {
            var orderEn = [
                ["required", $(".GroupNameEn"), "Group name is required!"],
                ["required", $(".SampleSize"), "样本量不能为空!\rSample size is required!"],
                ["number", $(".SampleSize"), "样本量必须是数字!\rSample size must be number!"],
                ["required", $(".MeasureEn"), "Measure required!"],
                ["required", $(".CountryEn"), "Country is required!"],
                ["required", $(".ProvinceEn"), "Province is required!"],
                ["required", $(".InstitutionEn"), "Institution/hospital is required!"],
                ["required", $(".InstitutionLevelEn"), "Level of the institution is required!"],
                ["required", $(".IndexNameEn"), "Index name is required!"],
                ["required", $(".SecSponsorInstitutionEn"), "Institution is required!"],
                ["required", $(".SecSponsorAddressEn"), "Address is required!"]
            ];
            var orderCn = [
                ["required", $(".GroupName"), "组别不能为空"],
                ["required", $(".Measure"), "干预措施不能为空!"],
                ["required", $(".Country"), "国家不能为空!"],
                ["required", $(".Province"), "省(直辖市)不能为空!"],
                ["required", $(".Institution"), "单位(医院)不能为空!"],
                ["required", $(".InstitutionLevel"), "单位级别不能为空!"],
                ["required", $(".IndexName"), "指标名称不能为空!"],
                ["required", $(".SecSponsorInstitution"), "单位名称不能为空!"],
                ["required", $(".SecSponsorAddress"), "具体地址不能为空!"]
            ];
            var order = [].concat(orderEn);
            if (LangIsCn())
                order = order.concat(orderCn);
            var result = ValidateInOrder(order);

        }
        //POST表单提交from.js
        if (result == true) {
            $("#btn").val("submit");
            $("#btnAdd").attr({ "disabled": "disabled" });
            $("#btnAdd").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#btnSave").attr({ "disabled": "disabled" });
            $("#btnSave").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#fromProjectAdd").submit();

        }
        return result;
    });
    $("#btnSave").click(function () {
        if (!confirm('是否确认保存?\rAre you sure you want to save the changes?')) {
            return false;
        }
        $("#btn").val("save");
        $("#btnAdd").attr({ "disabled": "disabled" });
        $("#btnAdd").css("background", "none repeat scroll 0 0 #CCCCCC");
        $("#btnSave").attr({ "disabled": "disabled" });
        $("#btnSave").css("background", "none repeat scroll 0 0 #CCCCCC");
        $("#fromProjectAdd").submit();
    });
});
