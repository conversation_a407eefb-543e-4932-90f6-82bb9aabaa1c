var frominput = 0;
function CascadeCheck(allSelector, itemSelector, checkedVals) {
    var rootThis = this;
    this.Parent = $(allSelector);
    this.Children = $(itemSelector);
    var load = function () {
        var allChecked = false;
        if (checkedVals && checkedVals.length > 0) {
            allChecked = true;
            rootThis.Children.each(function () {
                var checked = false;
                for (var i = 0; i < checkedVals.length; i++) {
                    var $this = $(this);
                    if ($this.val() == checkedVals[i]) {
                        $this.attr("checked", "checked");
                        checked = true;
                        break;
                    }
                }
                allChecked = allChecked && checked;
                return true;
            });
        }
        rootThis.Parent.attr("checked", allChecked ? "checked" : "");
    };
    this.Init = function () {
        load();
        rootThis.Children.click(function () {
            var allChecked = true;
            if (this.checked) {
                rootThis.Children.each(function () {
                    if (this.checked)
                        return true;
                    allChecked = false;
                    return false;
                });
            }
            else
                allChecked = false;
            rootThis.Parent.attr("checked", allChecked ? "checked" : "");
        });
        rootThis.Parent.click(function () {
            var status = this.checked;
            rootThis.Children.each(function () {
                this.checked = status;
            });
        });
    };
    this.GetCheckedChildren = function () {
        return rootThis.Children.filter(function () {
            return this.checked;
        });
    };
    this.GetCheckedValues = function () {
        var array = new Array();
        this.GetCheckedChildren().each(function () {
            array.push(this.value);
        });
        return array;
    };
    this.CheckAll = function () {
        rootThis.Children.attr("checked", "checked");
        rootThis.Parent.attr("checked", "checked");
    };
    this.ClearAll = function () {
        rootThis.Children.attr("checked", "");
        rootThis.Parent.attr("checked", "");
    };
    this.Inverse = function () {
        return rootThis.Children.attr("checked", function () {
            return !this.checked;
        });
    };
};
function listStudyTypeChange(sel) {
    if (sel == "1001003") {
        $("#divDiagnostic").show();
        $("#divInter").hide();
    } else {
        $("#divInter").show();
        $("#divDiagnostic").hide();
    }
}
function html2Escape(sHtml) {
    return sHtml.replace(/[<>&"]/g, function (c) { return { '<': '&lt;', '>': '&gt;', '&': '&amp;', '"': '&quot;' }[c]; });
}
$(function () {

    var messagesEn = {
        required: "This field is required.",
        remote: "Please fix this field.",
        email: "Please enter a valid email address.",
        url: "Please enter a valid URL.",
        date: "Please enter a valid date.",
        dateISO: "Please enter a valid date ( ISO ).",
        number: "Please enter a valid number.",
        digits: "Please enter only digits.",
        creditcard: "Please enter a valid credit card number.",
        equalTo: "Please enter the same value again.",
        maxlength: $.validator.format("Please enter no more than {0} characters."),
        minlength: $.validator.format("Please enter at least {0} characters."),
        rangelength: $.validator.format("Please enter a value between {0} and {1} characters long."),
        range: $.validator.format("Please enter a value between {0} and {1}."),
        max: $.validator.format("Please enter a value less than or equal to {0}."),
        min: $.validator.format("Please enter a value greater than or equal to {0}."),
        compareDate: "The start date cannot be greater than the end date",
        compareAge: "The minimum age cannot be greater than the maximum age"
    };

    var messagesCn = {
        required: "这是必填字段",
        remote: "请修正此字段",
        email: "请输入有效的电子邮件地址",
        url: "请输入有效的网址",
        date: "请输入有效的日期",
        dateISO: "请输入有效的日期 (YYYY-MM-DD)",
        number: "请输入有效的数字",
        digits: "只能输入数字",
        creditcard: "请输入有效的信用卡号码",
        equalTo: "你的输入不相同",
        extension: "请输入有效的后缀",
        maxlength: $.validator.format("最多可以输入 {0} 个字符"),
        minlength: $.validator.format("最少要输入 {0} 个字符"),
        rangelength: $.validator.format("请输入长度在 {0} 到 {1} 之间的字符串"),
        range: $.validator.format("请输入范围在 {0} 到 {1} 之间的数值"),
        max: $.validator.format("请输入不大于 {0} 的数值"),
        min: $.validator.format("请输入不小于 {0} 的数值"),
        compareDate: "开始日期不能大于结束日期",
        compareAge: "最小年龄不能大于最大年龄"
    };

    $("#valueRemove1").hide();
    $("#valueRemove2").hide();
    $("#valueRemove3").hide();
    $("#valueRemove4").hide();
    $("#valueRemove5").hide();

    $("#listStudyType").change(function () {
        var sel = $(this).val();
        listStudyTypeChange(sel);
    });
    var sel = $("#listStudyType").val();
    listStudyTypeChange(sel);

    $("#fileEthicalCommittee").change(function () {
        if ($("#fileEthicalCommittee").val() != "") {
            $("#valueRemove1").show();
        }
    });


    $("#fileNationalFDASanction").change(function () {
        if ($("#fileNationalFDASanction").val() != "") {
            $("#valueRemove2").show();
        }
    });
    $("#fileStudyPlan").change(function () {
        if ($("#fileStudyPlan").val() != "") {
            $("#valueRemove3").show();
        }
    });
    $("#fileInformedConsent").change(function () {
        if ($("#fileInformedConsent").val() != "") {
            $("#valueRemove4").show();
        }
    });
    $("#fileExperimentalresults").change(function () {
        if ($("#fileExperimentalresults").val() != "") {
            $("#valueRemove5").show();
        }
    });
    $("#valueRemove1").click(function () {
        $("#fileEthicalCommittee").val("");
        $("#valueRemove1").hide();
    });
    $("#valueRemove2").click(function () {
        $("#fileNationalFDASanction").val("");
        $("#valueRemove2").hide();
    });
    $("#valueRemove3").click(function () {
        $("#fileStudyPlan").val("");
        $("#valueRemove3").hide();
    });
    $("#valueRemove4").click(function () {
        $("#fileInformedConsent").val("");
        $("#valueRemove4").hide();
    });
    $("#valueRemove5").click(function () {
        $("#fileExperimentalresults").val("");
        $("#valueRemove5").hide();
    });
    if (jQuery().datepicker) {
        $('.date-picker').datepicker({
            rtl: App.isRTL(),
            language: 'zh-CN',
            orientation: "left",
            autoclose: true,
            format: "yyyy-mm-dd",
            todayBtn: "linked"
        });
    }
    var $listEcs = $("input[name='listEthicalCommitteeSanction']");
    function HaveEcs() {
        return $listEcs.filter(":checked").val() == "1";
    }
    function SetEcs() {
        $trEcs = $("#tbodyEcs");
        if (HaveEcs()) {
            $trEcs.show();
        }
        else {
            $trEcs.hide();
        }
    };
    SetEcs();
    $listEcs.click(SetEcs);

    var $listLang = $("#listLang");
    function LangIsCn() {
        return $listLang.val() == "1009001";
    }

    function ChangeLang() {
        $(".en").show();
        switch ($listLang.val()) {
            case ("1009001"):
                $(".heigthLef").height(48);
                $(".cn").show();
                $.extend($.validator.messages, messagesCn);
                break;
            case ("1009002"):
                $(".heigthLef").height(0);
                $(".cn").hide();
                $.extend($.validator.messages, messagesEn);
                break;
        }
    }
    ChangeLang();
    $listLang.change(ChangeLang);

    var $listpublic = $("#listStatisticalEffectChiCTRPublic");
    function Changepublic() {
        $(".publicshow").show();
        switch ($listpublic.val()) {
            case ("1"):
                $(".publicshow").show();
                break;
            case ("0"):
                $(".publicshow").hide();
                break;
        }
    }
    Changepublic();
    $listpublic.change(Changepublic);

    var $listVerifyStatus = $("#listVerifyStatus");
    function Verified() {
        return $listVerifyStatus.val() == "1010003";
    }

    jQuery.validator.addMethod("compareDate", function (value, element, param) {
        var startDate = jQuery(param).val();
        if (startDate.length == 0 || value.length == 0) return true;
        var date1 = new Date(Date.parse(startDate.replace(/-/g, '/')));
        var date2 = new Date(Date.parse(value.replace(/-/g, '/')));
        return date1 < date2;
    }, "开始日期不能大于结束日期");

    jQuery.validator.addMethod("compareAge", function (value, element, param) {
        var startDate = jQuery(param).val();
        if (startDate.length == 0 || value.length == 0) return true;
        var date1 = Number(startDate);
        var date2 = Number(value);
        return date1 < date2;
    }, "最小年龄不能大于最大年龄");


    var rulesEcsCn = {
        txtEthicalCommitteeName: { required: true }
    };
    var rulesEcsEn = {
        txtEthicalCommitteeFileID: { required: true },
        txtEthicalCommitteeNameEn: { required: true }
    };
    var rulesSecondaryCn = {
        txtCountryX: { required: true },
        txtProvinceX: { required: true },
        txtInstitutionX: { required: true },
        txtAddressX: { required: true }
    };
    var rulesSecondaryEn = {
        txtCountryEnX: { required: true },
        txtProvinceEnX: { required: true },
        txtInstitutionEnX: { required: true },
        txtAddressEnX: { required: true }
    };
    var rulesInterCn = {
        txtGroup: { required: true },
        txtMeasure: { required: true }
    };
    var rulesInterEn = {
        txtGroupEn: { required: true },
        txtSampleSize: { required: true, number: true },
        txtMeasureEn: { required: true }
    };
    var rulesLocCn = {
        txtCountry: { required: true },
        txtProvince: { required: true },
        txtInstitution: { required: true },
        txtInstitutionLevel: { required: true }
    };
    var rulesLocEn = {
        txtCountryEn: { required: true },
        txtProvinceEn: { required: true },
        txtInstitutionEn: { required: true },
        txtInstitutionLevelEn: { required: true }
    };
    var rulesIndexCn = {
        txtIndexName: { required: true }
    };
    var rulesIndexEn = {
        txtIndexNameEn: { required: true }
    };
    var rulesSpecimenCn = {
        txtSpecimenName: { required: true }
    };
    var rulesSpecimenEn = {
        txtSpecimenNameEn: { required: true }
    };
    // 非 “诊断研究 ”需验证字段
    var rulesNoDiagnostic = {
        txtTotalSampleSize: { required: true, number: true }
    };

    var rulesDiagnostic = {
        txtSampleSizeT: { required: true, number: true },
        txtSampleSizeD: { required: true, number: true }
    };

    var rulesCn = {
        txtTitle: { required: true },
        txtOfficialName: { required: true },
        txtApplier: { required: true },
        txtApplierCompany: { required: true },

        txtApplierAddress: { required: true },
        txtStudyLeader: { required: true },

        txtStudyLeaderAddress: { required: true },

        txtSponsor: { required: true },
        txtSponsorAddress: { required: true },
        txtSourceOfSpends: { required: true },
        txtStudyAilment: { required: true },
        txtStudyAim: { required: true },
        txtSelectionCriteria: { required: true },
        txtEliminateCriteria: { required: true },
        txtGenerafionMethod: { required: true },
        // add by liubo 2016-03-14
        txtDataChargeUnit: { required: true },
        txtDataAnalysisUnit: { required: true },
        // end add

        txtStudyExecuteTime: { required: true },
        txtEnlistBeginTime: { required: true },

        // end add
        txtStudyEndTime: { compareDate: "#txtStudyExecuteTime", required: true },
        txtEnlistEndTime: { compareDate: "#txtEnlistBeginTime", required: true }
    };

    var rulesEn = {
        txtTitleEn: { required: true },
        txtOfficialNameEn: { required: true },
        txtApplierEn: { required: true },
        txtApplierCompanyEn: { required: true },
        txtApplierPhone: { required: true },
        txtApplierEmail: { required: true, email: true },

        txtApplierAddressEn: { required: true },
        txtStudyLeaderEn: { required: true },
        txtStudyLeaderPhone: { required: true },
        txtStudyLeaderEmail: { required: true, email: true },

        txtStudyLeaderAddressEn: { required: true },
        txtFollowUpFrequency: { digits: true },

        txtSponsorEn: { required: true },
        txtSponsorAddressEn: { required: true },
        txtSourceOfSpendsEn: { required: true },
        txtStudyAilmentEn: { required: true },
        txtStudyAimEn: { required: true },
        listStudyDesign: { required: true },
        txtSelectionCriteriaEn: { required: true },
        txtEliminateCriteriaEn: { required: true },
        listRecruitmentStatus: { required: true },
        txtMinAge: { digits: true, min: 0 },
        txtMaxAge: { digits: true, min: 0, compareAge: "#txtMinAge" },
        txtGenerafionMethodEn: { required: true },
        listGender: { required: true },
        listAgreeToSign: { required: true },
        //txtTotalSampleSize: { required: true, number: true },

        // add by liubo 2016-03-14
        txtDataChargeUnitEn: { required: true },
        txtDataAnalysisUnitEn: { required: true },


        txtStudyExecuteTime: { required: true },
        txtEnlistBeginTime: { required: true },

        // end add
        txtStudyEndTime: { compareDate: "#txtStudyExecuteTime", required: true },
        txtEnlistEndTime: { compareDate: "#txtEnlistBeginTime", required: true }
    };

    var rulesDiagnosticCn = {
        txtStandard: { required: true },
        txtIndexTest: { required: true }
    };
    var rulesDiagnosticEn = {
        txtStandardEn: { required: true },
        txtIndexTestEn: { required: true }
    };
    var rules = $.extend({}, rulesSecondaryCn, rulesSecondaryEn, rulesCn, rulesEn, rulesEcsCn, rulesEcsEn,
        rulesInterCn, rulesInterEn, rulesLocCn, rulesLocEn, rulesIndexCn, rulesIndexEn, rulesSpecimenCn, rulesSpecimenEn,
        rulesNoDiagnostic, rulesDiagnostic, rulesDiagnosticCn, rulesDiagnosticEn); // add by liubo 2016-12-26


    var validator = $("#fromProjectEdit").validate({
        focusInvalid: true,
        errorElement: 'span',
        errorClass: 'help-block help-block-error help-inline',
        highlight: function (element) { // hightlight error inputs
            $(element)
                .closest('tr').addClass('has-error'); // set error class to the control group
        },
        unhighlight: function (element) { // revert the change done by hightlight
            $(element)
                .closest('tr').removeClass('has-error'); // set error class to the control group
        },
        success: function (label) {
            label
                .closest('tr').removeClass('has-error'); // set success class to the control group
        },
        rules: rules,
        onsubmit: false
    });
    function GroupValidate(group, evt) {
        var isValid = true;
        for (var i in group) {
            var $item = $("*[name='" + i + "']");
            var t = $item.valid();
            if (!t) {
                if (isValid)
                    $item.focus();
                isValid = false;
            }
        }
        if (isValid) {
        }
        else {
            validator.focusInvalid();
            evt.preventDefault();
        }
        return isValid;
    }
    function ValidateExt(params) {
        var result = true;
        params[1].each(function () {
            var $this = $(this);
            var val = $.trim($this.val());
            if ($this.is(":visible")) {
                switch (params[0]) {
                    case ("required"):
                        result = (val != '');
                        break;
                    case ("number"):
                        result = (!isNaN(parseFloat(val)));
                        break;
                }
                if (!result) {
                    $this.focus();
                    alert(params[2]);
                }
            }
            return result;
        });
        return result;
    }
    function ValidateInOrder(order) {
        var result = true;
        for (var i in order) {
            var j = order[i];
            result = ValidateExt(j);
            if (!result)
                break;
        }
        return result;
    }

    $(".p_btnEdit").click(function () {
        ChangeLang();
    });

    function FireGroupValidate() {
        $("#btnAddSecondary").click(function (evt) {
            var rules2;
            rules2 = $.extend({}, rulesSecondaryEn);
            if (LangIsCn()) {
                $.extend(rules2, rulesSecondaryCn);
            }
            if (GroupValidate(rules2, evt)) {
                var pid = $("#pid").val();
                var txtCountryX = $("#txtCountryX").val();
                var txtProvinceX = $("#txtProvinceX").val();
                var txtCityX = $("#txtCityX").val();
                var txtCountryEnX = $("#txtCountryEnX").val();
                var txtProvinceEnX = $("#txtProvinceEnX").val();
                var txtCityEnX = $("#txtCityEnX").val();
                var txtInstitutionX = $("#txtInstitutionX").val();
                var txtAddressX = $("#txtAddressX").val();
                var txtInstitutionEnX = $("#txtInstitutionEnX").val();
                var txtAddressEnX = $("#txtAddressEnX").val();

                $.post("/Manager/EditProjectInfo?type=ssinfo", {
                    caozuo: "add",
                    country: txtCountryX,
                    province: txtProvinceX,
                    city: txtCityX,
                    countryen: txtCountryEnX,
                    provinceen: txtProvinceEnX,
                    cityen: txtCityEnX,
                    institution: txtInstitutionX,
                    address: txtAddressX,
                    institutionen: txtInstitutionEnX,
                    addressen: txtAddressEnX,
                    pid: pid
                }, function (res) {
                    if (res.succ) {
                        AddIndexItem_Secondarysponsor(res.data);
                        $("#tableadd_ss").find("input:text").val("");
                    }
                    else {
                        alert("<试验主办单位(即项目批准或申办者)>此项添加失败！检查后再试");
                    }
                }, "json");
            }
        });
        $("#btnAddInter").click(function (evt) {
            var rules2;
            rules2 = $.extend({}, rulesInterEn);
            if (LangIsCn()) {
                $.extend(rules2, rulesInterCn);
            }
            if (GroupValidate(rules2, evt)) {
                var pid = $("#pid").val();
                var txtGroup = $("#txtGroup").val();
                var txtSampleSize = $("#txtSampleSize").val();
                var txtGroupEn = $("#txtGroupEn").val();
                var txtMeasure = $("#txtMeasure").val();
                var txtInterCode = $("#txtInterCode").val();
                var txtMeasureEn = $("#txtMeasureEn").val();

                $.post("/Manager/EditProjectInfo?type=ininfo", {
                    caozuo: "add",
                    group: txtGroup,
                    samplesize: txtSampleSize,
                    groupen: txtGroupEn,
                    measure: txtMeasure,
                    intercode: txtInterCode,
                    measureen: txtMeasureEn,
                    pid: pid
                }, function (res) {
                    if (res.succ) {
                        AddIndexItem_Interventions(res.data);
                        $("#tableadd_in").find("input:text").val("");
                    }
                    else {
                        alert("干预措施，此项添加失败！检查后再试");
                    }
                }, "json");
            }
        });
        $("#btnAddLoc").click(function (evt) {
            var rules2;
            rules2 = $.extend({}, rulesLocEn);
            if (LangIsCn()) {
                $.extend(rules2, rulesLocCn);
            }
            if (GroupValidate(rules2, evt)) {
                var pid = $("#pid").val();
                var txtCountry = $("#txtCountry").val();
                var txtProvince = $("#txtProvince").val();
                var txtCity = $("#txtCity").val();
                var txtCountryEn = $("#txtCountryEn").val();
                var txtProvinceEn = $("#txtProvinceEn").val();
                var txtCityEn = $("#txtCityEn").val();
                var txtInstitution = $("#txtInstitution").val();
                var txtInstitutionLevel = $("#txtInstitutionLevel").val();
                var txtInstitutionEn = $("#txtInstitutionEn").val();
                var txtInstitutionLevelEn = $("#txtInstitutionLevelEn").val();

                $.post("/Manager/EditProjectInfo?type=rainfo", {
                    caozuo: "add",
                    country: txtCountry,
                    province: txtProvince,
                    city: txtCity,
                    countryen: txtCountryEn,
                    provinceen: txtProvinceEn,
                    cityen: txtCityEn,
                    institution: txtInstitution,
                    institutionLevel: txtInstitutionLevel,
                    institutionen: txtInstitutionEn,
                    institutionLevelen: txtInstitutionLevelEn,
                    pid: pid
                }, function (res) {
                    if (res.succ) {
                        AddIndexItem_CountriesOfRecruitment(res.data);
                        $("#tableadd_ra").find("input:text").val("");
                    }
                    else {
                        alert("研究实施地点，此项添加失败！检查后再试");
                    }
                }, "json");
            }
        });
        $("#btnAddIndex").click(function (evt) {
            var rules2;
            rules2 = $.extend({}, rulesIndexEn);
            if (LangIsCn()) {
                $.extend(rules2, rulesIndexCn);
            }
            if (GroupValidate(rules2, evt)) {
                var pid = $("#pid").val();
                var IndexName = $.trim($("#txtIndexName").val());
                var IndexNameEn = $.trim($("#txtIndexNameEn").val());
                var listIndexType = $("#listIndexType").val();
                var IndexTime = $.trim($("#txtIndexNamePointTime").val());
                var IndexMethod = $.trim($("#txtIndexNameMeasureMethod").val());
                var IndexTimeEn = $.trim($("#txtIndexNamePointTimeEn").val());
                var IndexMethodEn = $.trim($("#txtIndexNameMeasureMethodEn").val());

                $.post("/Manager/EditProjectInfo?type=outinfo", {
                    caozuo: "add",
                    pid: pid,
                    name: IndexName,
                    nameen: IndexNameEn,
                    outype: listIndexType,
                    pointtime: IndexTime,
                    measuremethod: IndexMethod,
                    pointtimeen: IndexTimeEn,
                    measuremethoden: IndexMethodEn
                }, function (res) {
                    if (res.succ) {
                        AddIndexItem_Outcomes(res.data);
                        $("#tableadd_out").find("input:text").val("");
                    }
                    else {
                        alert("测量指标此项添加失败！请检查是否填写错误");
                    }
                }, "json");
            }
        });
        $("#btnAddSpecimen").click(function (evt) {
            var rules2;
            rules2 = $.extend({}, rulesSpecimenEn);
            if (LangIsCn()) {
                $.extend(rules2, rulesSpecimenCn);
            }
            if (GroupValidate(rules2, evt)) {
                var pid = $("#pid").val();
                var txtSampleNameCN = $("#txtSpecimenName").val();
                var txtSampleNameEN = $("#txtSpecimenNameEn").val();
                var txtTissueCN = $("#txtSpecimenTakeFrom").val();
                var txtTissueEN = $("#txtSpecimenTakeFromEn").val();
                var txtNoteCN = $("#txtSpecimenNote").val();
                var txtNoteEN = $("#txtSpecimenNoteEn").val();
                var txtFateSample = $("#listSpecimenAfterUse").val();

                $.post("/Manager/EditProjectInfo?type=collinfo", {
                    caozuo: "add",
                    pid: pid,
                    SampleNameCN: txtSampleNameCN,
                    SampleNameEN: txtSampleNameEN,
                    TissueCN: txtTissueCN,
                    TissueEN: txtTissueEN,
                    NoteCN: txtNoteCN,
                    NoteEN: txtNoteEN,
                    FateSample: txtFateSample
                }, function (res) {
                    ;
                    if (res.succ) {
                        AddSpecimenItem(res.data);
                        $("#tableadd_cs").find("input:text").val("");
                    }
                    else {
                        alert("采集人体标本填写有误，此项添加失败，检查后再试");
                    }
                }, "json");
            }
        });
    }
    FireGroupValidate();


    function AddIndexItem_Secondarysponsor(ssid) {
        var pguid = $("#proj").val();
        var txtCountryX = html2Escape($("#txtCountryX").val());
        var txtProvinceX = html2Escape($("#txtProvinceX").val());
        var txtCityX = html2Escape($("#txtCityX").val());
        var txtCountryEnX = html2Escape($("#txtCountryEnX").val());
        var txtProvinceEnX = html2Escape($("#txtProvinceEnX").val());
        var txtCityEnX = html2Escape($("#txtCityEnX").val());
        var txtInstitutionX = html2Escape($("#txtInstitutionX").val());
        var txtAddressX = html2Escape($("#txtAddressX").val());
        var txtInstitutionEnX = html2Escape($("#txtInstitutionEnX").val());
        var txtAddressEnX = html2Escape($("#txtAddressEnX").val());

        var $tbPlace = $("#tabSs");
        var $table = $("<table id=\"table_ss" + ssid + "\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"900\"></table>");
        var $tr = $("<tr></tr>");
        $tr.append("<td width=\"100\" class=\"t\"><p class=\"cn\">国家：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_country" + ssid + "\" class=\"cn\">" + txtCountryX + "</p></td>");
        $tr.append("<td width=\"70\" class=\"t\"><p class=\"cn\">省(直辖市)：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_province" + ssid + "\" class=\"cn\">" + txtProvinceX + "</p></td>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"cn\">市(区县)：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_city" + ssid + "\" class=\"cn\">" + txtCityX + "</p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td class=\"t\"><p class=\"en\">Country：</p></td>");
        $tr.append("<td><p id=\"pen_country" + ssid + "\" class=\"en\">" + txtCountryEnX + "</p></td>");
        $tr.append("<td class=\"t\"><p class=\"en\">Province：</p></td>");
        $tr.append("<td><p id=\"pen_province" + ssid + "\" class=\"en\">" + txtProvinceEnX + "</p></td>");
        $tr.append("<td class=\"t\"><p class=\"en\">City：</p></td>");
        $tr.append("<td><p id=\"pen_city" + ssid + "\" class=\"en\">" + txtCityEnX + "</p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td class=\"t\"><p class=\"cn\"> 单位(医院)：：</p></td>");
        $tr.append("<td><p id=\"pcn_Institution" + ssid + "\" class=\"cn\">" + txtInstitutionX + "</p></td>");
        $tr.append("<td class=\"t\"><p class=\"cn\">具体地址:</p></td>");
        $tr.append("<td><p id=\"pcn_Address" + ssid + "\" class=\"cn\">" + txtAddressX + "</p></td>");
        var $td = $("<td colspan=\"2\" rowspan=\"2\"></td>");
        var $p = $("<p></p>");
        var $inputEdit = $("<input class=\"btn btn-default\" type=\"button\" value=\"编辑Edit\" name=\"ssbtnEdit" + ssid + "\" />");
        $inputEdit.click(function () {
            Edit("ss|" + ssid, "ss");
            ChangeLang();
        });
        $p.append($inputEdit);
        $td.append($p);
        $p = $("<p></p>");
        var $inputDel = $("<input class=\"btn btn-default\" type=\"button\" value=\"删除Del\" name=\"ssbtnDelete" + ssid + "\" />");
        $inputDel.click(function () {
            DelssID(ssid)
        });
        $p.append($inputDel);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td class=\"t\"><p class=\"en\">Institution/hospital：</p></td>");
        $tr.append("<td><p id=\"pen_Institution" + ssid + "\" class=\"en\">" + txtInstitutionEnX + "</p></td>");
        $tr.append("<td class=\"t\"><p class=\"en\">Level of the institution：</p></td>");
        $tr.append("<td><p id=\"pen_Address" + ssid + "\" class=\"en\">" + txtAddressEnX + "</p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    };
    $('body').on("click", "#Secondary_CancelButton", function () {
        //$("#p_input").hide(); //显示,参数说明同上
        $("#p_input").hide(); //显示,参数说明同上
        $("#table_ss" + GetSsid).show();
        frominput = 0;
    });
    $('body').on("click", "#Secondary_UpdateButton", function (evt) {
        var txtCountry1 = $("#txtCountry1").val();
        var txtProvince1 = $("#txtProvince1").val();
        var txtCity1 = $("#txtCity1").val();
        var txtCountryEn1 = $("#txtCountryEn1").val();
        var txtProvinceEn1 = $("#txtProvinceEn1").val();

        var txtCityEn1 = $("#txtCityEn1").val();
        var txtInstitution1 = $("#txtInstitution1").val();
        var txtAddress1 = $("#txtAddress1").val();
        var txtInstitutionEn1 = $("#txtInstitutionEn1").val();
        var txtAddressEn1 = $("#txtAddressEn1").val();

        $.post("/Manager/EditProjectInfo?type=ssinfo", {
            caozuo: "up",
            country: txtCountry1,
            province: txtProvince1,
            city: txtCity1,
            countryen: txtCountryEn1,
            provinceen: txtProvinceEn1,
            cityen: txtCityEn1,
            institution: txtInstitution1,
            address: txtAddress1,
            institutionen: txtInstitutionEn1,
            addressen: txtAddressEn1,
            ssid: GetSsid
        }, function (res) {
            if (res.succ) {
                qiehuan_ss(GetSsid, "up");
                frominput = 0;
            }
            else {
                alert("有错误，检查后再试");
                //处理失败错误信息
            }
        }, "json");
    });

    function AddIndexItem_Interventions(inid) {
        var pguid = $("#proj").val();
        var txtGroup = html2Escape($("#txtGroup").val());
        var txtSampleSize = html2Escape($("#txtSampleSize").val());
        var txtGroupEn = html2Escape($("#txtGroupEn").val());
        var txtMeasure = html2Escape($("#txtMeasure").val());
        var txtInterCode = html2Escape($("#txtInterCode").val());
        var txtMeasureEn = html2Escape($("#txtMeasureEn").val());

        var $tbPlace = $("#tabInter");
        var $table = $("<table id=\"table_inter" + inid + "\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"900\"></table>");
        var $tr = $("<tr></tr>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"cn\">组别：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_groups" + inid + "\" class=\"cn\">" + txtGroup + "</p></td>");
        $tr.append("<td width=\"110\" class=\"t\"><p class=\"cn\">样本量：</p></td>");
        $tr.append("<td width=\"200\" rowspan=\"2\" id=\"p_sampleSize" + inid + "\">" + txtSampleSize + "</td>");
        var $td = $("<td rowspan=\"4\"></td>");
        var $p = $("<p></p>");
        var $inputEdit = $("<input type=\"button\" class=\"btn btn-default\" value=\"编辑Edit\" name=\"inbtnEdit" + inid + "\" />");
        $inputEdit.click(function () {
            Edit("inter|" + inid, "inter");
            ChangeLang();
        });
        $p.append($inputEdit);
        $td.append($p);
        $p = $("<p></p>");
        var $inputDel = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除Del\" name=\"inbtnDelete" + inid + "\" />");
        $inputDel.click(function () {
            DelinID(inid)
        });
        $p.append($inputDel);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"en\">Group：</p></td>");
        $tr.append("<td width=\"200\" id=\"pen_groups" + inid + "\">" + txtGroupEn + "</td>");
        $tr.append("<td width=\"110\" class=\"t\"><p class=\"en\">Sample size：</p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"cn\">干预措施：</p></td>");
        $tr.append("<td width=\"200\" id=\"pcn_intervention" + inid + "\">" + txtMeasure + "</td>");
        $tr.append("<td width=\"110\" class=\"t\"><p class=\"cn\">干预措施代码：</p></td>");
        $tr.append("<td rowspan=\"2\" id=\"p_interventionCode" + inid + "\">" + txtInterCode + "</td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"en\">Intervention：</p></td>");
        $tr.append("<td width=\"200\" id=\"pen_intervention" + inid + "\">" + txtMeasureEn + "</td>");
        $tr.append("<td width=\"110\" class=\"t\"><p class=\"en\">Intervention code：</p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    };
    $('body').on("click", "#Intervention_CancelButton", function () {
        $("#p_input").hide(); //显示,参数说明同上
        $("#table_inter" + GetInid).show();
        frominput = 0;
    });
    $('body').on("click", "#Intervention_UpdateButton", function () {
        var txtGroup0 = $("#txtGroup0").val();
        var txtSampleSize0 = $("#txtSampleSize0").val();
        var txtGroupEn0 = $("#txtGroupEn0").val();
        var txtMeasure0 = $("#txtMeasure0").val();
        var txtInterCode0 = $("#txtInterCode0").val();
        var txtMeasureEn0 = $("#txtMeasureEn0").val();

        if (txtSampleSize0 == "") {
            alert("样本量不能为空");
            $("#txtSampleSize0").focus();
            return false;
        }
        $.post("/Manager/EditProjectInfo?type=ininfo", {
            caozuo: "up",
            group: txtGroup0,
            samplesize: txtSampleSize0,
            groupen: txtGroupEn0,
            measure: txtMeasure0,
            intercode: txtInterCode0,
            measureen: txtMeasureEn0,
            inid: GetInid
        }, function (res) {
            //var rs = eval("(" + res + ")");
            if (res.succ) {
                qiehuan_in(GetInid, "up");
                frominput = 0;
            }
            else {
                alert("研究实施地点此项未更新！请检查<干预措施>选项卡信息填写是否正确");
            }
        }, "json");
    });

    function AddIndexItem_CountriesOfRecruitment(raid) {
        var pguid = $("#proj").val();
        var txtCountry = html2Escape($("#txtCountry").val());
        var txtProvince = html2Escape($("#txtProvince").val());
        var txtCity = html2Escape($("#txtCity").val());
        var txtCountryEn = html2Escape($("#txtCountryEn").val());
        var txtProvinceEn = html2Escape($("#txtProvinceEn").val());
        var txtCityEn = html2Escape($("#txtCityEn").val());
        var txtInstitution = html2Escape($("#txtInstitution").val());
        var txtInstitutionLevel = html2Escape($("#txtInstitutionLevel").val());
        var txtInstitutionEn = html2Escape($("#txtInstitutionEn").val());
        var txtInstitutionLevelEn = html2Escape($("#txtInstitutionLevelEn").val());

        var $tbPlace = $("#tabra");
        var $table = $("<table id=\"table_ra" + raid + "\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"900\"></table>");
        var $tr = $("<tr></tr>");
        $tr.append("<td width=\"100\" class=\"t\"><p class=\"cn\">国家：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_country_ra" + raid + "\" class=\"cn\">" + txtCountry + "</p></td>");
        $tr.append("<td width=\"70\" class=\"t\"><p class=\"cn\">省(直辖市)：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_province_ra" + raid + "\" class=\"cn\">" + txtProvince + "</p></td>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"cn\">市(区县)：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_city_ra" + raid + "\" class=\"cn\">" + txtCity + "</p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td class=\"t\"><p class=\"en\">Country：</p></td>");
        $tr.append("<td><p id=\"pen_country_ra" + raid + "\" class=\"en\">" + txtCountryEn + "</p></td>");
        $tr.append("<td class=\"t\"><p class=\"en\">Province：</p></td>");
        $tr.append("<td><p id=\"pen_province_ra" + raid + "\" class=\"en\">" + txtProvinceEn + "</p></td>");
        $tr.append("<td class=\"t\"><p class=\"en\">City：</p></td>");
        $tr.append("<td><p id=\"pen_city_ra" + raid + "\" class=\"en\"></p>" + txtCityEn + "</td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td class=\"t\"><p class=\"cn\">单位：</p></td>");
        $tr.append("<td><p id=\"pcn_hospital_ra" + raid + "\" class=\"cn\">" + txtInstitution + "</p></td>");
        $tr.append("<td class=\"t\"><p class=\"cn\">单位级别:</p></td>");
        $tr.append("<td><p id=\"pcn_institution_ra" + raid + "\" class=\"cn\">" + txtInstitutionLevel + "</p></td>");
        var $td = $("<td colspan=\"2\" rowspan=\"2\"></td>");
        var $p = $("<p></p>");
        var $inputEdit = $("<input type=\"button\" class=\"btn btn-default\" value=\"编辑Edit\" name=\"rabtnEdit" + raid + "\" />");
        $inputEdit.click(function () {
            Edit("ra|" + raid, "ra");
            ChangeLang();
        });
        $p.append($inputEdit);
        $td.append($p);
        $p = $("<p></p>");
        var $inputDel = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除Del\" name=\"rabtnDelete" + raid + "\" />");
        $inputDel.click(function () {
            DelraID(raid)
        });
        $p.append($inputDel);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td class=\"t\"><p class=\"en\">Institution/hospital：</p></td>");
        $tr.append("<td><p id=\"pen_hospital_ra" + raid + "\" class=\"en\">" + txtInstitutionEn + "</p></td>");
        $tr.append("<td class=\"t\"><p class=\"en\">Level of the institution：</p></td>");
        $tr.append("<td><p id=\"pen_institution_ra" + raid + "\" class=\"en\">" + txtInstitutionLevelEn + "</p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    };
    $('body').on("click", "#raCancelButton0", function () {
        $("#p_input").hide(); //显示,参数说明同上
        $("#table_ra" + GetRaid + "").show();
        frominput = 0;
    });
    $('body').on("click", "#raUpdateButton0", function () {
        var txtCountry0 = $("#txtCountry0").val();
        var txtProvince0 = $("#txtProvince0").val();
        var txtCity0 = $("#txtCity0").val();
        var txtCountryEn0 = $("#txtCountryEn0").val();
        var txtProvinceEn0 = $("#txtProvinceEn0").val();

        var txtCityEn0 = $("#txtCityEn0").val();
        var txtInstitution0 = $("#txtInstitution0").val();
        var txtInstitutionLevel0 = $("#txtInstitutionLevel0").val();
        var txtInstitutionEn0 = $("#txtInstitutionEn0").val();
        var txtInstitutionLevelEn0 = $("#txtInstitutionLevelEn0").val();

        $.post("/Manager/EditProjectInfo?type=rainfo", {
            caozuo: "up",
            country0: txtCountry0,
            province0: txtProvince0,
            city0: txtCity0,
            countryen0: txtCountryEn0,
            provinceen0: txtProvinceEn0,
            cityen0: txtCityEn0,
            institution0: txtInstitution0,
            institutionLevel0: txtInstitutionLevel0,
            institutionen0: txtInstitutionEn0,
            institutionLevelen0: txtInstitutionLevelEn0,
            raid: GetRaid
        }, function (res) {
            if (res.succ) {
                qiehuan_ra(GetRaid, "up");
                frominput = 0;
            }
            else {
                alert("研究实施地点此项未更新！请检查<研究实施地点>选项卡信息填写是否正确");
                //处理失败错误信息
            }
        }, "json");

    });

    function AddIndexItem_Outcomes(ouid) {
        var tIndexName = html2Escape($.trim($("#txtIndexName").val()));
        var tIndexNameEn = html2Escape($.trim($("#txtIndexNameEn").val()));
        var tIndexType = html2Escape($.trim($("#listIndexType").val()));
        var tIndexTime = html2Escape($.trim($("#txtIndexNamePointTime").val()));
        var tIndexMethod = html2Escape($.trim($("#txtIndexNameMeasureMethod").val()));
        var tIndexTimeEn = html2Escape($.trim($("#txtIndexNamePointTimeEn").val()));
        var tIndexMethodEn = html2Escape($.trim($("#txtIndexNameMeasureMethodEn").val()));
        var pcn_type = "";
        var pen_type = "";
        if (tIndexType == "4002001") {
            pcn_type = "主要指标&nbsp;";
            pen_type = "Primary indicator &nbsp;";
        }
        else if (tIndexType == "4002002") {
            pcn_type = "次要指标&nbsp;";
            pen_type = "Secondary indicator &nbsp;";
        } else if (tIndexType == "4002003") {
            pcn_type = "附加指标&nbsp;";
            pen_type = "Additional indicator &nbsp;";
        } else if (tIndexType == "4002004") {
            pcn_type = "副作用指标&nbsp;";
            pen_type = "Adverse events &nbsp;";
        } else {
            pcn_type = "";
            pen_type = "";
        }
        var $hdnIndexCount = $("#hdnIndexCount");
        var index = parseInt($hdnIndexCount.val());
        if (isNaN(index))
            index = 0;
        index++;
        $hdnIndexCount.val(index);
        var $tbPlace = $("#tabout");
        var $table = $("<table id=\"table_out" + ouid + "\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"900\"></table>");
        var $hidden = $("<input type=\"hidden\" style=\"display: none\" value=\"" + ouid + "\" id=\"Ouid" + index + "\" name=\"txtOuid\" />")
        $table.append($hidden);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"73\" class=\"t\"><p class=\"cn\">指标中文名：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_name" + ouid + "\" class=\"cn\">" + tIndexName + "</p></td>");
        $tr.append("<td width=\"40\" class=\"t\"><p class=\"cn\">类型：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_type" + ouid + "\" class=\"cn\">" + pcn_type + "</p></td>");
        var $td = $("<td rowspan=\"4\"></td>");
        var $p = $("<p></p>");
        var $inputEdit = $("<input type=\"button\" class=\"btn btn-default\" value=\"编辑Edit\" name=\"oubtnEdit" + ouid + "\" />");
        $inputEdit.click(function () {
            Edit("out|" + ouid, "out");
            ChangeLang();
        });
        $p.append($inputEdit);
        $td.append($p);
        $p = $("<p></p>");
        var $inputDel = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除Del\" name=\"oubtnDelete" + ouid + "\" />");
        $inputDel.click(function () {
            DeloutID(ouid)
        });
        $p.append($inputDel);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"en\">Outcome：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pen_name" + ouid + "\" class=\"en\">" + tIndexNameEn + "</p></td>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"en\">Type：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pen_type" + ouid + "\" class=\"en\">" + pen_type + "</p></td>");
        $table.append($tr);
        $tbPlace.append($table);

        $tr = $("<tr></tr>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"cn\">测量时间点：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_time" + ouid + "\" class=\"cn\">" + tIndexTime + "</p></td>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"cn\">测量方法：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pcn_method" + ouid + "\" class=\"cn\">" + tIndexMethod + "</p></td>");
        $table.append($tr);
        $tbPlace.append($table);

        $tr = $("<tr></tr>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"en\">Measure time point of outcome：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pen_time" + ouid + "\" class=\"en\">" + tIndexTimeEn + "</p></td>");
        $tr.append("<td width=\"60\" class=\"t\"><p class=\"en\">Measure method：</p></td>");
        $tr.append("<td width=\"200\"><p id=\"pen_method" + ouid + "\" class=\"en\">" + tIndexMethodEn + "</p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    };
    $('body').on("click", "#ouCancelButton0", function () {
        $("#p_input").hide(); //显示,参数说明同上
        $("#table_out" + GetOuid + "").show();
        frominput = 0;
    });
    $('body').on("click", "#ouUpdateButton0", function () {
        var IndexName = $("#txtIndexName0").val();
        var IndexNameEn = $("#txtIndexNameEn0").val();
        var IndexTime = $.trim($("#txtIndexNamePointTime0").val());
        var IndexMethod = $.trim($("#txtIndexNameMeasureMethod0").val());
        var IndexTimeEn = $.trim($("#txtIndexNamePointTimeEn0").val());
        var IndexMethodEn = $.trim($("#txtIndexNameMeasureMethodEn0").val());

        $.post("/Manager/EditProjectInfo?type=outinfo", {
            caozuo: "up",
            name: IndexName,
            nameen: IndexNameEn,
            pointtime: IndexTime,
            measuremethod: IndexMethod,
            pointtimeen: IndexTimeEn,
            measuremethoden: IndexMethodEn,
            ouid: GetOuid
        }, function (res) {
            //var rs = eval("(" + res + ")");
            if (res.succ) {
                qiehuan_out(GetOuid, "up");
                frominput = 0;
            } else {
                alert("测量指标此项此项未更新！请检查是否有错误信息");
                //失败提示
            }
        }, "json");

    });

    function AddSpecimenItem(csid) {
        var txtSampleNameCN = html2Escape($("#txtSpecimenName").val());
        var txtSampleNameEN = html2Escape($("#txtSpecimenNameEn").val());
        var txtTissueCN = html2Escape($("#txtSpecimenTakeFrom").val());
        var txtTissueEN = html2Escape($("#txtSpecimenTakeFromEn").val());
        var txtNoteCN = html2Escape($("#txtSpecimenNote").val());
        var txtNoteEN = html2Escape($("#txtSpecimenNoteEn").val());
        var txtFateSample = html2Escape($("#listSpecimenAfterUse").val());
        var pcn_type = "";
        var pen_type = "";
        if (txtFateSample == "1012001") {
            pcn_type = "使用后销毁&nbsp;";
            pen_type = "Destruction after use &nbsp;";
        }
        else if (txtFateSample == "1012002") {
            pcn_type = "使用后保存&nbsp;";
            pen_type = "Preservation after use &nbsp;";
        } else if (txtFateSample == "1012003") {
            pcn_type = "其它&nbsp;";
            pen_type = "Others &nbsp;";
        } else {
            pcn_type = "";
            pen_type = "";
        }

        var $hdnSpecimenCount = $("#hdnSpecimenCount");
        var index = parseInt($hdnSpecimenCount.val());
        if (isNaN(index))
            index = 0;
        index++;
        $hdnSpecimenCount.val(index);
        var $tbPlace = $("#tabcoll");
        var $table = $("<table id=\"table_cs" + csid + "\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"900\"></table>");
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"93\"><p class=\"cn\">标本中文名：</p></td>");
        $tr.append("<td width=\"240\"><p id=\"pcn_SaName" + csid + "\" class=\"cn\">" + txtSampleNameCN + "</p></td>");
        $tr.append("<td width=\"50\"><p class=\"cn\">组织：</p></td>");
        $tr.append("<td width=\"270\"><p id=\"pcn_tissue" + csid + "\" class=\"cn\">" + txtTissueCN + "</p></td>");
        var $td = $("<td rowspan=\"4\"></td>");
        var $p = $("<p></p>");
        var $inputEdit = $("<input type=\"button\"  class=\"btn btn-default\" value=\"编辑Edit\" name=\"collbtnEdit" + csid + "\" />");
        $inputEdit.click(function () {
            Edit("cs|" + csid, "cs");
            ChangeLang();
        });
        $p.append($inputEdit);
        $td.append($p);
        $p = $("<p></p>");
        var $inputDel = $("<input type=\"button\" class=\"btn btn-default\" value=\"删除Del\" name=\"collbtnDelete" + csid + "\" />");
        $inputDel.click(function () {
            DelCollID(csid);
        });
        $p.append($inputDel);
        $td.append($p);
        $tr.append($td);
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td width=\"110\"><p class=\"en\">Sample Name：</p></td>");
        $tr.append("<td width=\"240\"><p id=\"pen_SaName" + csid + "\" class=\"en\">" + txtSampleNameEN + "</p></td>");
        $tr.append("<td width=\"60\"><p class=\"en\">Tissue：</p></td>");
        $tr.append("<td width=\"270\"><p id=\"pen_tissue" + csid + "\" class=\"en\">" + txtTissueEN + "</p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td><p class=\"cn\">人体标本去向：</p></td>");
        $tr.append("<td><input type=\"hidden\" id=\"fate" + csid + "\" value=\"" + txtFateSample + "\"/><p id=\"pcn_fate" + csid + "\" class=\"cn\">" + pcn_type + "</p></td>");
        $tr.append("<td><p class=\"cn\">说明：</p></td>");
        $tr.append("<td><p id=\"pcn_note" + csid + "\" class=\"cn\">" + txtNoteCN + "</p></td>");
        $table.append($tr);
        $tr = $("<tr></tr>");
        $tr.append("<td><p class=\"en\">Fate of sample：</p></td>");
        $tr.append("<td><p id=\"pen_fate" + csid + "\" class=\"en\">" + pen_type + "</p></td>");
        $tr.append("<td><p class=\"en\">Note：</p></td>");
        $tr.append("<td><p id=\"pen_note" + csid + "\" class=\"en\">" + txtNoteEN + "</p></td>");
        $table.append($tr);
        $tbPlace.append($table);
        ChangeLang();
        // RemoveComma();
    };
    $('body').on("click", "#lvSpecimen_CancelButton", function () {
        $("#p_input").hide(); //显示,参数说明同上
        $("#table_cs" + GetCsid + "").show();
        frominput = 0;
    });
    $('body').on("click", "#lvSpecimen_UpdateButton", function () {
        var txtSampleNameCN = $("#txtSpecimenName0").val();
        var txtSampleNameEN = $("#txtSpecimenNameEn0").val();
        var txtTissueCN = $("#txtSpecimenTakeFrom0").val();
        var txtTissueEN = $("#txtSpecimenTakeFromEn0").val();
        var txtNoteCN = $("#txtSpecimenNote0").val();
        var txtNoteEN = $("#txtSpecimenNoteEn0").val();
        var txtFateSample = $("#listSpecimenAfterUse0").val();
        $.post("/Manager/EditProjectInfo?type=collinfo", {
            caozuo: "up",
            csid: GetCsid,
            SampleNameCN: txtSampleNameCN,
            SampleNameEN: txtSampleNameEN,
            TissueCN: txtTissueCN,
            TissueEN: txtTissueEN,
            NoteCN: txtNoteCN,
            NoteEN: txtNoteEN,
            FateSample: txtFateSample
        }, function (res) {
            if (res.succ) {
                qiehuan_cs(GetCsid, "up");
                frominput = 0;
            }
            else {
                alert("采集人体标本填写有错误，此项未更新！检查后再试");
                //失败处理信息
            }
        }, "json");
    });


    $("#btnAdd").click(function (evt) {
        if (!confirm('是否确认提交?\rConfirm Submit?')) {
            return false;
        }
        var rules2;
        rules2 = $.extend({}, rulesEn);
        if (HaveEcs()) {
            $.extend(rules2, rulesEcsEn);
        }
        if (LangIsCn()) {
            $.extend(rules2, rulesCn);
            if (HaveEcs())
                $.extend(rules2, rulesEcsCn);
        }
        var studyType = $("#listStudyType").val();
        if (studyType != "1001003") {
            $.extend(rules2, rulesNoDiagnostic);
        }
        var result = GroupValidate(rules2, evt);

        if (result == true) {
            $("#btn").val("");
            $("#btnAdd").attr({ "disabled": "disabled" });
            $("#btnAdd").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#btnSave").attr({ "disabled": "disabled" });
            $("#btnSave").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#lnkBack").attr({ "disabled": "disabled" });
            $("#lnkBack").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#lnkBack2").attr({ "disabled": "disabled" });
            $("#lnkBack2").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#fromProjectEdit").submit();
        }
    });
    $("#btnSave").click(function (evt) {
        if (!confirm('是否确认保存?\rConfirm Save?')) {
            return false;
        }
        var result = true;
        var VerStus = $("#listVerifyStatus").val();

        if (VerStus == "1010003") {
            var rules2;
            rules2 = $.extend({}, rulesEn);
            if (HaveEcs()) {
                $.extend(rules2, rulesEcsEn);
            }
            if (LangIsCn()) {
                $.extend(rules2, rulesCn);
                if (HaveEcs())
                    $.extend(rules2, rulesEcsCn);
            }
            var studyType = $("#listStudyType").val();
            if (studyType == "1001003") {
                $.extend(rules2, rulesDiagnostic);
                if (LangIsCn()) {
                    $.extend(rules2, rulesDiagnosticCn);
                } else {
                    $.extend(rules2, rulesDiagnosticEn);
                }
            } else {
                $.extend(rules2, rulesNoDiagnostic);
            }
            result = GroupValidate(rules2, evt);
        }
        if (result == true) {
            $("#btn").val("save");
            $("#btnAdd").attr({ "disabled": "disabled" });
            $("#btnAdd").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#btnSave").attr({ "disabled": "disabled" });
            $("#btnSave").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#lnkBack").attr({ "disabled": "disabled" });
            $("#lnkBack").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#lnkBack2").attr({ "disabled": "disabled" });
            $("#lnkBack2").css("background", "none repeat scroll 0 0 #CCCCCC");
            $("#fromProjectEdit").submit();
        }
    });

})

TabId = "";
TabShow = "";

function Edit(intId, checkhtm) {
    frominput++;  //记录第几次操作编辑
    if ($("#p_input").length > 0) {
        if (frominput > 1) {
            if (confirm("存在未保存项目存！是否放弃编辑其它项目")) {
                return false;
            }
        }
    }
    var reintId = intId.replace("|", "");
    var subintId = intId.substr(intId.indexOf('|') + 1);

    $("#p_input").remove();
    if (checkhtm == "inter") {
        $("#table_" + TabShow + TabId).show();
        //in
        $("#table_" + reintId).after("<table id='p_input' width='900' cellspacing='0' cellpadding='0' border='0'> <tbody> <tr> <td width='60' class='t'> <p class='cn'> 组别：</p> </td> <td width='200'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtGroup0' name='txtGroup0' /></p> </td> <td width='110' class='t'> <p class='cn'> 样本量：</p> </td> <td width='200' rowspan='2'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtSampleSize0' name='txtSampleSize0' /> </td> <td rowspan='4'> <p> <input type='button' class='btn btn-default' id='Intervention_UpdateButton' value='更新Update' name='Intervention_UpdateButton'> </p> <p> <input type='button' id='Intervention_CancelButton' value='取消Cancel' class='btn btn-default' name='Intervention_CancelButton'></p> </td> </tr> <tr> <td width='60' class='t'> <p class='en'> Group：</p> </td> <td width='200'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtGroupEn0' name='txtGroupEn0' /> </td> <td width='110' class='t'> <p class='en'> Sample size：</p> </td> </tr> <tr> <td width='60' class='t'> <p class='cn'> 干预措施：</p> </td> <td width='200'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtMeasure0' name='txtMeasure0' /> </p> <p class='cn help-block'> 每格只填写一个干预措施</p> </td> <td width='110' class='t'> <p class='cn'> 干预措施代码：</p> </td> <td width='200' rowspan='2'> <p class='cn help-block'> 每格只填写一个代码</p> <p> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtInterCode0' name='txtInterCode0' /></p> <p class='cn help-block'> Please input only one code</p> </td> </tr> <tr> <td width='60' class='t'> <p title='trade name and common name, dosage, usage, TCM formulation name' class='en'> Intervention：</p> </td> <td width='200'> <p> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtMeasureEn0' name='txtMeasureEn0' /></p> <p class='en help-block'> Please input only one Measure</p> </td> <td width='110' class='t'> <p class='en'> Intervention code：</p> </td> </tr> </tbody> </table>");

        qiehuan_in(intId, "edit");
    }
    if (checkhtm == "cs") {
        $("#table_" + TabShow + TabId).show();
        //coll
        $("#table_" + reintId).after("<table id='p_input' width='900' cellspacing='0' cellpadding='0' border='0'> <tbody> <tr> <td width='110'> <p class='cn'> 标本中文名：</p> </td> <td width='240'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtSpecimenName0' name='txtSpecimenName0' value='' /></p> <p class='cn help-block'> 每格只填写一个，如'血液'、'唾液'等</p> </td> <td width='50'> <p class='cn'> 组织：</p> </td> <td width='270'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtSpecimenTakeFrom0' name='txtSpecimenTakeFrom0' /></p> <p class='cn help-block'> 请说明取自何组织或器官</p> </td> <td rowspan ='4'> <p> <input type='button' id='lvSpecimen_UpdateButton' class='btn btn-default' value='更新Update' name='lvSpecimen_UpdateButton'/> </p> <p> <input type='button' id='lvSpecimen_CancelButton' class='btn btn-default' value='取消Cancel' name='lvSpecimen_CancelButton'/></td> </tr> <tr> <td> <p class='en'> Sample Name：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtSpecimenNameEn0' name='txtSpecimenNameEn0' /></p> <p class='en help-block'> Input only one,such as 'Blood','Saliva' etc.</p> </td> <td> <p class='en'> Tissue：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtSpecimenTakeFromEn0' name='txtSpecimenTakeFromEn0' /></p> <p class='en help-block'> Specify take from which tissue or organ</p> </td> </tr> <tr> <td> <p class='cn'> 人体标本去向</p> </td> <td rowspan='2'> <select id='listSpecimenAfterUse0' class='form-control' name='listSpecimenAfterUse0'> <option value='1012001' selected='selected'>使用后销毁/Destruction after use</option> <option value='1012002'>使用后保存/Preservation after use</option> <option value='1012003'>其它/Others</option> </select> </td> <td> <p class='cn'> 说明</p> </td> <td> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtSpecimenNote0' name='txtSpecimenNote0' /></p> <p class='cn help-block'> 说明保存年限或其他去向等</p> </td> </tr> <tr> <td> <p class='en'> Fate of sample：</p> </td> <td> <p class='en'> Note：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtSpecimenNoteEn0' name='txtSpecimenNoteEn0' /></p> <p class='en help-block'> Specify expected time of preservation or other fate</p> </td> </tr> </tbody> </table>");
        qiehuan_cs(intId, "edit");
    }
    if (checkhtm == "ra") {
        $("#table_" + TabShow + TabId).show();
        //rea
        $("#table_" + reintId).after("<table id='p_input' width='900' cellspacing='0' cellpadding='0' border='0'> <tbody> <tr> <td width='100' class='t'> <p class='cn'> 国家：</p> </td> <td width='200'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtCountry0' name='txtCountry0' /></p> </td> <td width='70' class='t'> <p class='cn'> 省(直辖市)：</p> </td> <td width='200'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtProvince0' name='txtProvince0' /></p> </td> <td width='60' class='t'> <p class='cn'> 市(区县)：</p> </td> <td width='200'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtCity0' name='txtCity0' /></p> </td> </tr> <tr> <td class='t'> <p class='en'> Country：</p> </td> <td> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtCountryEn0' name='txtCountryEn0' /> </td> <td class='t'> <p class='en'> Province：</p> </td> <td> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtProvinceEn0' name='txtProvinceEn0' /> </td> <td class='t'> <p class='en'> City：</p> </td> <td> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtCityEn0' name='txtCityEn0' /> </td> </tr> <tr> <td class='t'> <p class='cn'> 单位(医院)：</p> </td> <td> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtInstitution0' name='txtInstitution0' /> </p> <p class='cn help-block'> 每格只填写一个单位(医院)</p> </td> <td class='t'> <p class='cn'> 单位级别：</p> </td> <td> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtInstitutionLevel0' name='txtInstitutionLevel0' /> </p> </td> <td rowspan='2' colspan='2'> <p> <input type='button' id='raUpdateButton0' class='btn btn-default' value='更新Update' name='raUpdateButton0' /></p> <p> <input type='button' id='raCancelButton0' class='btn btn-default' value='取消Cancel' name='raCancelButton0' /></p> </td> </tr> <tr> <td class='t'> <p class='en'> Institution/hospital：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtInstitutionEn0' name='txtInstitutionEn0' /></p> <p class='en help-block'> Input only one institution</p> </td> <td class='t'> <p class='en'> Level of the institution：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtInstitutionLevelEn0' name='txtInstitutionLevelEn0' /></p> </td> </tr> </tbody> </table>");
        qiehuan_ra(intId, "edit");
    }
    if (checkhtm == "ss") {
        $("#table_" + TabShow + TabId).show();
        //ss
        $("#table_" + reintId).after("<table id='p_input' width='900' cellspacing='0' cellpadding='0' border='0'> <tbody> <tr> <td width='100' class='t'> <p class='cn'> 国家：</p> </td> <td width='200'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtCountry1' name='txtCountry1' /></p> </td> <td width='70' class='t'> <p class='cn'> 省(直辖市)：</p> </td> <td width='200'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtProvince1' name='txtProvince1' /></p> </td> <td width='60' class='t'> <p class='cn'> 市(区县)：</p> </td> <td width='200'> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtCity1' name='txtCity1' /></p> </td> </tr> <tr> <td class='t'> <p class='en'> Country：</p> </td> <td> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtCountryEn1' name='txtCountryEn1' /> </td> <td class='t'> <p class='en'> Province：</p> </td> <td> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtProvinceEn1' name='txtProvinceEn1' /> </td> <td class='t'> <p class='en'> City：</p> </td> <td> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtCityEn1' name='txtCityEn1' /> </td> </tr> <tr> <td class='t'> <p class='cn'> 单位(医院)：</p> </td> <td> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtInstitution1' name='txtInstitution1' /> </p> <p class='cn help-block'> 每格只填写一个单位(医院)</p> </td> <td class='t'> <p class='cn'> 具体地址：</p> </td> <td> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtAddress1' name='txtAddress1' /> </p> </td> <td rowspan='2' colspan='2'> <p> <input type='button' id='Secondary_UpdateButton' value='更新Update' class='btn btn-default' name='Secondary_UpdateButton'> </p> <p> <input type='button' id='Secondary_CancelButton'  class='btn btn-default' value='取消Cancel' name='Secondary_CancelButton'> </p> </td> </tr> <tr> <td class='t'> <p class='en'> Institution/hospital：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtInstitutionEn1' name='txtInstitutionEn1' /></p> <p class='en help-block'> Input only one institution</p> </td> <td class='t'> <p class='en'> Address：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtAddressEn1' name='txtAddressEn1' /></p> </td> </tr> </tbody> </table>");
        qiehuan_ss(intId, "edit");
    }
    if (checkhtm == "out") {
        $("#table_" + TabShow + TabId).show();
        //out
        $("#table_" + reintId).after("<table id='p_input' style='' width='900' cellspacing='0' cellpadding='0' border='0'><tbody><tr><td width='73' class='t'><p class='cn'>指标中文名：</p></td><td width='200'><p class='cn'><input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtIndexName0' name=='txtIndexName0' value='' /></p><p class='cn help-block'>每格只填写一个指标</p></td><td width='40' class='t'><p class='cn'>类型：</p></td><td width='200'><p class='cn' id='txtIndexType0' name='txtIndexType0'></p></td><td rowspan='4'><p><input type='button' id='ouUpdateButton0' value='更新Update' class='btn btn-default' name='ouUpdateButton0' /></p><p><input type='button' id='ouCancelButton0' value='取消Cancel' class='btn btn-default' name='ouCancelButton0' /></p></td></tr><tr><td class='t'><p class='en'>Outcome：</p></td><td><p class='en'><input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtIndexNameEn0' name='txtIndexNameEn0' value='' /></p><p class='en help-block'>Input only one outcome name</p></td><td class='t'><p class='en'>Type：</p></td><td><p class='en' id='txtIndexTypeen0'></p></td></tr><tr> <td  class='t'> <p class='cn'> 测量时间点：</p> </td> <td> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtIndexNamePointTime0' name='txtIndexNamePointTime0' /></p> </td> <td  class='t'> <p class='cn'> 测量方法：</p> </td> <td> <p class='cn'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtIndexNameMeasureMethod0' name='txtIndexNameMeasureMethod0' /></p> </td> </tr> <tr> <td  class='t'> <p class='en'> Measure time point of outcome：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtIndexNamePointTimeEn0' name='txtIndexNamePointTimeEn0' /></p> </td> <td  class='t'> <p class='en'> Measure method：</p> </td> <td> <p class='en'> <input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtIndexNameMeasureMethodEn0' name='txtIndexNameMeasureMethodEn0' /></p> </td> </tr></tbody></table>");
        qiehuan_out(intId, "edit");
    }
    TabId = subintId;
    TabShow = checkhtm;
};

var GetSsid = 0;
function Editss(ssId) {
    $("#p_input").remove();
    showid(GetSsid);
    GetSsid = ssId;
    qiehuan_ss(ssId, "edit");
};
function DelssID(ssId) {
    if (confirm('确定要删除吗？Are you sure to delete it?')) {
        $.post("/Manager/EditProjectInfo?type=ssinfo", {
            caozuo: "del",
            ssid: ssId
        }, function (res) {
            if (!res.succ) {
                alert(res + "要删除的数据不存在")
            } else {
                $("#table_ss" + ssId).remove();
            }
        }, "json");
    }
};
function qiehuan_ss(ssid, caozuo) {
    ssid = ssid.substr(ssid.indexOf('|') + 1);
    GetSsid = ssid;

    var pcountry = $("#pcn_country" + ssid + "");
    var pcountryen = $("#pen_country" + ssid + "");

    var pprovince = $("#pcn_province" + ssid + "");
    var pprovinceen = $("#pen_province" + ssid + "");

    var pcity = $("#pcn_city" + ssid + "");
    var pcityen = $("#pen_city" + ssid + "");

    var pinstitution = $("#pcn_Institution" + ssid + "");
    var pinstitutionen = $("#pen_Institution" + ssid + "");

    var paddress = $("#pcn_Address" + ssid + "");
    var paddressen = $("#pen_Address" + ssid + "");

    //TableInput
    var txtCountry1 = $("#txtCountry1");
    var txtProvince1 = $("#txtProvince1");
    var txtCity1 = $("#txtCity1");

    var txtCountryEn1 = $("#txtCountryEn1");
    var txtProvinceEn1 = $("#txtProvinceEn1");
    var txtCityEn1 = $("#txtCityEn1");

    var txtInstitution1 = $("#txtInstitution1");
    var txtAddress1 = $("#txtAddress1");

    var txtInstitutionEn1 = $("#txtInstitutionEn1");
    var txtAddressEn1 = $("#txtAddressEn1");

    if (caozuo == "edit") {
        txtCountry1.val($.trim(pcountry.text()));
        txtProvince1.val($.trim(pprovince.text()));
        txtCity1.val($.trim(pcity.text()));

        txtCountryEn1.val($.trim(pcountryen.text()));
        txtProvinceEn1.val($.trim(pprovinceen.text()));
        txtCityEn1.val($.trim(pcityen.text()));

        txtInstitution1.val($.trim(pinstitution.text()));
        txtAddress1.val($.trim(paddress.text()));
        txtInstitutionEn1.val($.trim(pinstitutionen.text()));
        txtAddressEn1.val($.trim(paddressen.text()));
        $("#table_ss" + ssid).hide();
        $("#p_input").show();
    } else if (caozuo == "up") {
        pcountry.text(function (n) {
            return txtCountry1.val();
        });
        pprovince.text(function (n) {
            return txtProvince1.val();
        });
        pcity.text(function (n) {
            return txtCity1.val();
        });
        pcountryen.text(function (n) {
            return txtCountryEn1.val();
        });
        pprovinceen.text(function (n) {
            return txtProvinceEn1.val();
        });
        pcityen.text(function (n) {
            return txtCityEn1.val();
        });
        pinstitution.text(function (n) {
            return txtInstitution1.val();
        });
        paddress.text(function (n) {
            return txtAddress1.val();
        });
        pinstitutionen.text(function (n) {
            return txtInstitutionEn1.val();
        });
        paddressen.text(function (n) {
            return txtAddressEn1.val();
        });
        $("#table_ss" + ssid + "").show();
        $("#p_input").hide();
    } else {

    }
};
var GetInid = 0;
function Editin(inId) {
    $("#p_input").remove();
    showid(GetInid);
    GetInid = inId;
    qiehuan_in(inId, "edit");
};
function qiehuan_in(inid, caozuo) {
    inid = inid.substr(inid.indexOf('|') + 1);
    GetInid = inid;

    var pgroups = $("#pcn_groups" + inid + "");
    var psamplesize = $("#p_sampleSize" + inid + "");
    var pgroupsen = $("#pen_groups" + inid + "");
    var pintervention = $("#pcn_intervention" + inid + "");
    var pinterventionCode = $("#p_interventionCode" + inid + "");
    var pinterventionen = $("#pen_intervention" + inid + "");

    //TableInput
    var txtGroup0 = $("#txtGroup0");
    var txtSampleSize0 = $("#txtSampleSize0");
    var txtGroupEn0 = $("#txtGroupEn0");
    var txtMeasure0 = $("#txtMeasure0");
    var txtInterCode0 = $("#txtInterCode0");
    var txtMeasureEn0 = $("#txtMeasureEn0");

    if (caozuo == "edit") {
        txtGroup0.val($.trim(pgroups.text()));
        txtSampleSize0.val($.trim(psamplesize.text()));
        txtGroupEn0.val($.trim(pgroupsen.text()));
        txtMeasure0.val($.trim(pintervention.text()));
        txtInterCode0.val($.trim(pinterventionCode.text()));
        txtMeasureEn0.val($.trim(pinterventionen.text()));
        $("#table_inter" + inid).hide();
        $("#p_input").show();
    } else if (caozuo == "up") {
        pgroups.text(function (n) {
            return txtGroup0.val();
        });
        psamplesize.text(function (n) {
            return txtSampleSize0.val();
        });
        pgroupsen.text(function (n) {
            return txtGroupEn0.val();
        });
        pintervention.text(function (n) {
            return txtMeasure0.val();
        });
        pinterventionCode.text(function (n) {
            return txtInterCode0.val();
        });
        pinterventionen.text(function (n) {
            return txtMeasureEn0.val();
        });
        $("#table_inter" + inid).show();
        $("#p_input").hide();
    } else {

    }
};
function DelinID(inId) {
    if (confirm('确定要删除吗？Are you sure to delete it?')) {
        $.post("/Manager/EditProjectInfo?type=ininfo", {
            caozuo: "del",
            inid: inId
        }, function (res) {
            if (!res.succ) {
                alert(res + "此条数据不存在，删除失败")
            } else {
                $("#table_inter" + inId).remove();
            }
        }, "json");
    }
};
var GetRaid = 0;
function Editra(raId) {
    $("#p_input").remove();
    showid(GetRaid);
    GetRaid = raId;
    qiehuan_ra(raId, "edit");
};
function DelraID(raId) {
    if (confirm('确定要删除吗？Are you sure to delete it?')) {
        $.post("/Manager/EditProjectInfo?type=rainfo", {
            caozuo: "del",
            raid: raId
        }, function (res) {
            if (!res.succ) {
                alert(res + "此条数据不存在，删除失败")
            } else {
                $("#table_ra" + raId).remove();
            }
        }, "json");
    }
};
function qiehuan_ra(raid, caozuo) {
    raid = raid.substr(raid.indexOf('|') + 1);
    GetRaid = raid;

    var pcountry = $("#pcn_country_ra" + raid + "");
    var pcountryn = $("#pen_country_ra" + raid + "");
    var pprovince = $("#pcn_province_ra" + raid + "");
    var pprovinceen = $("#pen_province_ra" + raid + "");
    var pcity = $("#pcn_city_ra" + raid + "");

    var pcityen = $("#pen_city_ra" + raid + "");
    var phospital = $("#pcn_hospital_ra" + raid + "");
    var phospitalen = $("#pen_hospital_ra" + raid + "");
    var pinstitution = $("#pcn_institution_ra" + raid + "");
    var ptinstitution = $("#pen_institution_ra" + raid + "");

    //TableInput
    var txtCountry0 = $("#txtCountry0");
    var txtProvince0 = $("#txtProvince0");
    var txtCity0 = $("#txtCity0");
    var txtCountryEn0 = $("#txtCountryEn0");
    var txtProvinceEn0 = $("#txtProvinceEn0");

    var txtCityEn0 = $("#txtCityEn0");
    var txtInstitution0 = $("#txtInstitution0");
    var txtInstitutionLevel0 = $("#txtInstitutionLevel0");
    var txtInstitutionEn0 = $("#txtInstitutionEn0");
    var txtInstitutionLevelEn0 = $("#txtInstitutionLevelEn0");

    if (caozuo == "edit") {
        txtCountry0.val($.trim(pcountry.text()));
        txtProvince0.val($.trim(pprovince.text()));
        txtCity0.val($.trim(pcity.text()));
        txtCountryEn0.val($.trim(pcountryn.text()));
        txtProvinceEn0.val($.trim(pprovinceen.text()));

        txtCityEn0.val($.trim(pcityen.text()));
        txtInstitution0.val($.trim(phospital.text()));
        txtInstitutionLevel0.val($.trim(pinstitution.text()));
        txtInstitutionEn0.val($.trim(phospitalen.text()));
        txtInstitutionLevelEn0.val($.trim(ptinstitution.text()));
        $("#table_ra" + raid + "").hide();
        $("#p_input").show();
    } else if (caozuo == "up") {
        pcountry.text(function (n) {
            return txtCountry0.val();
        });
        pcountryn.text(function (n) {
            return txtCountryEn0.val();
        });
        pprovince.text(function (n) {
            return txtProvince0.val();
        });
        pprovinceen.text(function (n) {
            return txtProvinceEn0.val();
        });
        pcity.text(function (n) {
            return txtCity0.val();
        });
        pcityen.text(function (n) {
            return txtCityEn0.val();
        });
        phospital.text(function (n) {
            return txtInstitution0.val();
        });
        phospitalen.text(function (n) {
            return txtInstitutionEn0.val();
        });
        pinstitution.text(function (n) {
            return txtInstitutionLevel0.val();
        });
        ptinstitution.text(function (n) {
            return txtInstitutionLevelEn0.val();
        });
        $("#table_ra" + raid + "").show();
        $("#p_input").hide();
    } else {

    }
};
var GetOuid = 0;
function Editout(outId) {
    $("#p_input").remove();
    showid(GetOuid);
    GetOuid = outId;
    //$("#table" + outId).live(.after().wrap("<table id='p_input' style='' width='900' cellspacing='0' cellpadding='0' border='0'><tbody><tr><td width='60' class='t'><p class='cn'>指标中文名：</p></td><td width='200'><p class='cn'><input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtIndexName0' value='' /></p><p class='cn help-block'>每格只填写一个指标</p></td><td width='60' class='t'><p class='cn'>类型：</p></td><td width='200'><p class='cn' id='txtIndexType0'></p></td><td rowspan='2'><p><input type='button' id='ouUpdateButton0' value='更新Update' name='ouUpdateButton0' /></p><p><input type='button' id='ouCancelButton0' value='取消Cancel' name='ouCancelButton0' /></p></td></tr><tr><td class='t'><p class='en'>Outcome：</p></td><td><p class='en'><input type='text'  maxlength = '450' class='form-control input-medium input-inline' id='txtIndexNameEn0' value='' /></p><p class='en help-block'>Input only one outcome name</p></td><td class='t'><p class='en'>Type：</p></td><td><p class='en' id='txtIndexTypeen0'></p></td></tr></tbody></table>"))
    qiehuan_out(outId, "edit");
};
function DeloutID(outid) {
    if (confirm('确定要删除吗？Are you sure to delete it?')) {
        $.post("/Manager/EditProjectInfo?type=outinfo", {
            caozuo: "del",
            ouid: outid
        }, function (res) {
            if (!res.succ) {
                alert(res + "未找到您要删除的数据，刷新后在试")
            } else {
                $("#table_out" + outid).remove();
            }
        }, "json");
    }
};
function qiehuan_out(ouid, caozuo) {
    ouid = ouid.substr(ouid.indexOf('|') + 1);
    GetOuid = ouid;

    var pname = $.trim($("#pcn_name" + ouid + "").text());
    var pnameen = $.trim($("#pen_name" + ouid + "").text());
    var ptype = $.trim($("#pcn_type" + ouid + "").text());
    var ptypeen = $.trim($("#pen_type" + ouid + "").text());

    var ptime = $.trim($("#pcn_time" + ouid + "").text());
    var ptimeen = $.trim($("#pen_time" + ouid + "").text());
    var pmethod = $.trim($("#pcn_method" + ouid + "").text());
    var pmethoden = $.trim($("#pen_method" + ouid + "").text());

    if (caozuo == "edit") {
        $("#txtIndexName0").val(pname);
        $("#txtIndexType0").text(ptype);
        $("#txtIndexTypeen0").text(ptypeen);
        $("#txtIndexNameEn0").val(pnameen);
        $("#txtIndexNamePointTime0").val(ptime);
        $("#txtIndexNamePointTimeEn0").val(ptimeen);
        $("#txtIndexNameMeasureMethod0").val(pmethod);
        $("#txtIndexNameMeasureMethodEn0").val(pmethoden);

        $("#table_out" + ouid + "").hide();
        $("#table_out" + ouid + "").parent().find("#p_input").show();
    } else if (caozuo == "up") {
        $("#pcn_name" + ouid + "").text(function (n) {
            return $("#txtIndexName0").val();
        });
        $("#pen_name" + ouid + "").text(function (n) {
            return $("#txtIndexNameEn0").val();
        });

        $("#pcn_time" + ouid + "").text(function (n) {
            return $("#txtIndexNamePointTime0").val();
        });
        $("#pen_time" + ouid + "").text(function (n) {
            return $("#txtIndexNamePointTimeEn0").val();
        });

        $("#pcn_method" + ouid + "").text(function (n) {
            return $("#txtIndexNameMeasureMethod0").val();
        });
        $("#pen_method" + ouid + "").text(function (n) {
            return $("#txtIndexNameMeasureMethodEn0").val();
        });
        $("#table_out" + ouid + "").show();
        $("#p_input").hide();
    } else {
        return false;
    }
};
var GetCsid = 0;
function EditColl(collId) {
    $("#p_input").remove();
    showid(GetCsid);
    GetCsid = collId;
    qiehuan_cs(collId, "edit");
};
function DelCollID(collid) {
    if (confirm('确定要删除吗？Are you sure to delete it?')) {
        $.post("/Manager/EditProjectInfo?type=collinfo", {
            caozuo: "del",
            csid: collid
        }, function (res) {
            if (!res.succ) {
                alert(res + "要删除的数据不存在")
            } else {
                $("#table_cs" + collid).remove();
            }
        }, "json");
    }
};
function qiehuan_cs(csid, caozuo) {
    csid = csid.substr(csid.indexOf('|') + 1);
    GetCsid = csid;

    var psaname = $.trim($("#pcn_SaName" + csid + "").text());
    var psanameen = $.trim($("#pen_SaName" + csid + "").text());
    var ptissue = $.trim($("#pcn_tissue" + csid + "").text());
    var ptissueen = $.trim($("#pen_tissue" + csid + "").text());
    var pnote = $.trim($("#pcn_note" + csid + "").text());
    var pnoteen = $.trim($("#pen_note" + csid + "").text());
    var pfate = $.trim($("#pcn_fate" + csid + "").val());

    if (caozuo == "edit") {
        $("#txtSpecimenName0").val(psaname);
        $("#txtSpecimenNameEn0").val(psanameen);
        $("#txtSpecimenTakeFrom0").val(ptissue);
        $("#txtSpecimenTakeFromEn0").val(ptissueen);
        $("#txtSpecimenNote0").val(pnote);
        $("#txtSpecimenNoteEn0").val(pnoteen);
        var fate = $.trim($("#fate" + csid + "").val());
        var opt = $("#listSpecimenAfterUse0 option");
        for (var i = 0; i < opt.length; i++) {
            if ($("#listSpecimenAfterUse0").get(0).options[i].value == fate) {
                $("#listSpecimenAfterUse0").get(0).options[i].selected = true;
                break;
            }
        };
        $("#table_cs" + csid + "").hide();
        $("#p_input").show();
    } else if (caozuo == "up") {
        $("#pcn_SaName" + csid + "").text(function (n) {
            return $("#txtSpecimenName0").val();
        });
        $("#pen_SaName" + csid + "").text(function (n) {
            return $("#txtSpecimenNameEn0").val();
        });
        $("#pcn_tissue" + csid + "").text($("#txtSpecimenTakeFrom0").val());
        $("#pen_tissue" + csid + "").text($("#txtSpecimenTakeFromEn0").val());
        $("#pcn_note" + csid + "").text($("#txtSpecimenNote0").val());
        $("#pen_note" + csid + "").text($("#txtSpecimenNoteEn0").val());
        $("#pcn_fate" + csid + "").text(function (n) {
            var cnfate = "";
            var listsp = $("#listSpecimenAfterUse0").val();
            if (listsp == '1012001') {
                cnfate = "使用后销毁";
            } else if (listsp == '1012002') {
                cnfate = "使用后保存";
            } else if (listsp == '1012003') {
                cnfate = "其它";
            } else {
                cnfate = "";
            }
            return cnfate;
        });
        $("#pen_fate" + csid + "").text(function (n) {
            var enfate = "";
            var listsp = $("#listSpecimenAfterUse0").val();
            if (listsp == '1012001') {
                enfate = "Destruction after use";
            } else if (listsp == '1012002') {
                enfate = "Preservation after use";
            } else if (listsp == '1012003') {
                enfate = "Others";
            } else {
                enfate = "";
            }
            return enfate;
        });

        $("#table_cs" + csid + "").show();
        $("#p_input").hide();
    } else {
        return false;
    }
};
