/**
 * Project: Bootstrap Hover Dropdown
 * Author: <PERSON>
 * Contributors: <PERSON><PERSON>
 *
 * Dependencies: Bootstrap's Dropdown plugin, jQuery
 *
 * A simple plugin to enable Bootstrap dropdowns to active on hover and provide a nice user experience.
 *
 * License: MIT
 *
 * http://cameronspear.com/blog/bootstrap-dropdown-on-hover-plugin/
 */
!function(e,n){var o=e();e.fn.dropdownHover=function(t){return"ontouchstart"in document?this:(o=o.add(this.parent()),this.each(function(){var i,r=e(this),s=r.parent(),d={delay:500,instantlyCloseOthers:!0},a={delay:e(this).data("delay"),instantlyCloseOthers:e(this).data("close-others")},h="show.bs.dropdown",u="hide.bs.dropdown",l=e.extend(!0,{},d,t,a);s.hover(function(e){return s.hasClass("open")||r.is(e.target)?(o.find(":focus").blur(),l.instantlyCloseOthers===!0&&o.removeClass("open"),n.clearTimeout(i),s.addClass("open"),void r.trigger(h)):!0},function(){i=n.setTimeout(function(){s.removeClass("open"),r.trigger(u)},l.delay)}),r.hover(function(){o.find(":focus").blur(),l.instantlyCloseOthers===!0&&o.removeClass("open"),n.clearTimeout(i),s.addClass("open"),r.trigger(h)}),s.find(".dropdown-submenu").each(function(){var o,t=e(this);t.hover(function(){n.clearTimeout(o),t.children(".dropdown-menu").show(),t.siblings().children(".dropdown-menu").hide()},function(){var e=t.children(".dropdown-menu");o=n.setTimeout(function(){e.hide()},l.delay)})})}))},e(document).ready(function(){e('[data-hover="dropdown"]').not(".hover-initialized").each(function(){e(this).dropdownHover(),e(this).addClass("hover-initialized")})})}(jQuery,this);
