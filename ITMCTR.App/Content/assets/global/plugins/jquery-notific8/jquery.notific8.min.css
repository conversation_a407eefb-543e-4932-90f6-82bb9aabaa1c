/** <AUTHOR> for the notific8 plug-in for jQuery  Copyright (c)2013, <PERSON> Licensed under the BSD license. http://opensource.org/licenses/BSD-3-Clause */
/** <AUTHOR> for the notific8 plug-in for jQuery  Copyright (c)2013, <PERSON> Licensed under the BSD license. http://opensource.org/licenses/BSD-3-Clause */
.jquery-notific8-container { display: block; padding: 0; margin: 0; position: fixed; }
.jquery-notific8-container.top { top: 0; }
.jquery-notific8-container.top.right { right: 0; }
.jquery-notific8-container.top.left { left: 0; }
.jquery-notific8-container.bottom { bottom: 0; }
.jquery-notific8-container.bottom.right { right: 0; }
.jquery-notific8-container.bottom.left { left: 0; }

.jquery-notific8-notification { position: relative; display: none; padding: 0.625em; color: #fff; height: 4.125em; max-height: 4.125em; overflow: hidden; border-style: solid; border-width: 2px; width: 21.4375em; }
.jquery-notific8-notification:hover .jquery-notific8-close { display: block; }

.jquery-notific8-heading { font-weight: bold; margin-bottom: 0.3125em; }

.jquery-notific8-message { font-size: 0.875em; }

.jquery-notific8-close { position: absolute; padding: 0 0.25em; cursor: pointer; top: 0; display: none; }

.jquery-notific8-close-sticky { text-align: center; position: absolute; width: 5.5625em; padding: 0.125em 0; cursor: pointer; text-transform: uppercase; }
.jquery-notific8-close-sticky span { font-size: 0.625em; }

.right .jquery-notific8-notification { float: right; clear: right; padding-right: 2.0625em; }
.right .jquery-notific8-close { right: -2px; }
.right .jquery-notific8-close-sticky { top: 1.9375em; right: -2.375em; -webkit-transform: rotate(90deg); -moz-transform: rotate(90deg); -ms-transform: rotate(90deg); -o-transform: rotate(90deg); transform: rotate(90deg); }

.left .jquery-notific8-notification { float: left; clear: left; padding-left: 2.0625em; }
.left .jquery-notific8-close { left: -2px; }
.left .jquery-notific8-close-sticky { top: 1.9375em; left: -2.375em; -webkit-transform: rotate(270deg); -moz-transform: rotate(270deg); -ms-transform: rotate(270deg); -o-transform: rotate(270deg); transform: rotate(270deg); }

.top .jquery-notific8-notification { margin-top: 0.625em; }

.bottom .jquery-notific8-notification { margin-bottom: 0.625em; }

/** <AUTHOR> Steinmetz  Themes for the notific8 plug-in for jQuery  Copyright (c)2013, Will Steinmetz Licensed under the BSD license. http://opensource.org/licenses/BSD-3-Clause */
.jquery-notific8-notification.teal { border-color: #0099cc; background-color: #006699; color: white; }
.jquery-notific8-notification.teal .jquery-notific8-close, .jquery-notific8-notification.teal .jquery-notific8-close-sticky { background-color: #0099cc; color: white; }

.jquery-notific8-notification.amethyst { border-color: #915faa; background-color: #5d2d77; color: white; }
.jquery-notific8-notification.amethyst .jquery-notific8-close, .jquery-notific8-notification.amethyst .jquery-notific8-close-sticky { background-color: #915faa; color: white; }

.jquery-notific8-notification.ruby { border-color: #dd1100; background-color: #aa1100; color: white; }
.jquery-notific8-notification.ruby .jquery-notific8-close, .jquery-notific8-notification.ruby .jquery-notific8-close-sticky { background-color: #dd1100; color: white; }

.jquery-notific8-notification.tangerine { border-color: #ffb23f; background-color: #e88f00; color: white; }
.jquery-notific8-notification.tangerine .jquery-notific8-close, .jquery-notific8-notification.tangerine .jquery-notific8-close-sticky { background-color: #ffb23f; color: white; }

.jquery-notific8-notification.lemon { border-color: #ffde00; background-color: #ffcc00; color: #333333; }
.jquery-notific8-notification.lemon .jquery-notific8-close, .jquery-notific8-notification.lemon .jquery-notific8-close-sticky { background-color: #ffde00; color: #333333; }

.jquery-notific8-notification.lime { border-color: #38d315; background-color: #32b512; color: white; }
.jquery-notific8-notification.lime .jquery-notific8-close, .jquery-notific8-notification.lime .jquery-notific8-close-sticky { background-color: #38d315; color: white; }

.jquery-notific8-notification.ebony { border-color: #666666; background-color: #121212; color: white; }
.jquery-notific8-notification.ebony .jquery-notific8-close, .jquery-notific8-notification.ebony .jquery-notific8-close-sticky { background-color: #666666; color: white; }

.jquery-notific8-notification.smoke { border-color: #ababab; background-color: #efefef; color: #333333; }
.jquery-notific8-notification.smoke .jquery-notific8-close, .jquery-notific8-notification.smoke .jquery-notific8-close-sticky { background-color: #ababab; color: white; }
