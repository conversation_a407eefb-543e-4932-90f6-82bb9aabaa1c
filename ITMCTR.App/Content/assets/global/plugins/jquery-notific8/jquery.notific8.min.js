/**
 * <AUTHOR>
 * 
 * jQuery notification plug-in inspired by the notification style of Windows 8
 * 
 * Copyright (c)2013, <PERSON>
 * Licensed under the BSD license.
 * http://opensource.org/licenses/BSD-3-Clause
 */
!function(t){var i={life:1e4,theme:"teal",sticky:!1,verticalEdge:"right",horizontalEdge:"top",zindex:1100},e={init:function(n,a){return this.each(function(){var o=t(this),d=o.data("notific8");o.data("notific8",{target:o,settings:{},message:""}),d=o.data("notific8"),d.message=n,t.extend(d.settings,i,a),e._buildNotification(o)})},destroy:function(i){i.data("notific8"),t(window).unbind(".notific8"),i.removeData("notific8")},_buildNotification:function(i){var e=i.data("notific8"),n=t("<div />"),a=Number(t("body").attr("data-notific8s"));if(a++,n.addClass("jquery-notific8-notification").addClass(e.settings.theme),n.attr("id","jquery-notific8-notification-"+a),t("body").attr("data-notific8s",a),e.settings.hasOwnProperty("heading")&&"string"==typeof e.settings.heading&&n.append(t("<div />").addClass("jquery-notific8-heading").html(e.settings.heading)),e.settings.sticky){var o=t("<div />").addClass("jquery-notific8-close-sticky").append(t("<span />").html("close x"));o.click(function(){n.animate({width:"hide"},{duration:"fast",complete:function(){n.remove()}})}),n.append(o),n.addClass("sticky")}else{var o=t("<div />").addClass("jquery-notific8-close").append(t("<span />").html("X"));o.click(function(){n.animate({width:"hide"},{duration:"fast",complete:function(){n.remove()}})}),n.append(o)}n.append(t("<div />").addClass("jquery-notific8-message").html(e.message)),t(".jquery-notific8-container."+e.settings.verticalEdge+"."+e.settings.horizontalEdge).append(n),n.animate({width:"show"},{duration:"fast",complete:function(){e.settings.sticky||!function(t,i){setTimeout(function(){t.animate({width:"hide"},{duration:"fast",complete:function(){t.remove()}})},i)}(n,e.settings.life),e.settings={}}})},configure:function(e){t.extend(i,e)},zindex:function(t){i.zindex=t}};t.notific8=function(n,a){switch(n){case"configure":case"config":return e.configure.apply(this,[a]);case"zindex":return e.zindex.apply(this,[a]);default:if("undefined"==typeof a&&(a={}),0===t(".jquery-notific8-container").size()){var o=t("body");o.attr("data-notific8s",0),o.append(t("<div />").addClass("jquery-notific8-container").addClass("top").addClass("right")),o.append(t("<div />").addClass("jquery-notific8-container").addClass("top").addClass("left")),o.append(t("<div />").addClass("jquery-notific8-container").addClass("bottom").addClass("right")),o.append(t("<div />").addClass("jquery-notific8-container").addClass("bottom").addClass("left")),t(".jquery-notific8-container").css("z-index",i.zindex)}(!a.hasOwnProperty("verticalEdge")||"right"!=a.verticalEdge.toLowerCase()&&"left"!=a.verticalEdge.toLowerCase())&&(a.verticalEdge=i.verticalEdge),(!a.hasOwnProperty("horizontalEdge")||"top"!=a.horizontalEdge.toLowerCase()&&"bottom"!=a.horizontalEdge.toLowerCase())&&(a.horizontalEdge=i.horizontalEdge),a.verticalEdge=a.verticalEdge.toLowerCase(),a.horizontalEdge=a.horizontalEdge.toLowerCase(),t(".jquery-notific8-container."+a.verticalEdge+"."+a.horizontalEdge).notific8(n,a)}},t.fn.notific8=function(i){return"string"==typeof i?e.init.apply(this,arguments):(t.error("jQuery.notific8 takes a string message as the first parameter"),void 0)}}(jQuery);