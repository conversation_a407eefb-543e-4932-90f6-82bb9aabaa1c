/**
 * <AUTHOR>
 * 
 * Styles for the notific8 plug-in for jQ<PERSON>y
 * 
 * Copyright (c)2013, <PERSON>
 * Licensed under the BSD license.
 * http://opensource.org/licenses/BSD-3-Clause
 */

// compass includes
@import "compass/css3/transform";

// mixins
@mixin theme($name, $border-color, $background-color, $close-background-color: $border-color, $text-color: #fff, $close-text-color: $text-color) {
  .jquery-notific8-notification.#{$name} {
    border-color: $border-color;
    background-color: $background-color;
    color: $text-color;
    .jquery-notific8-close, .jquery-notific8-close-sticky {
      background-color: $close-background-color;
      color: $close-text-color;
    }
  }
}

// definitions
.jquery-notific8-container {
  display: block;
  padding: 0;
  margin: 0;
  position: fixed;
  &.top {
    top: 0;
    &.right {
      right: 0;
    }
    &.left {
      left: 0;
    }
  }
  &.bottom {
    bottom: 0;
    &.right {
      right: 0;
    }
    &.left {
      left: 0;
    }
  }
}

.jquery-notific8-notification {
  position: relative;
  display: none;
  padding: (10em/16);
  color: #fff;
  height: (66em/16);
  max-height: (66em/16);
  overflow: hidden;
  border-style: solid;
  border-width: 2px;
  width: (343em/16);
  &:hover {
    .jquery-notific8-close {
      display: block;
    }
  }
}

.jquery-notific8-heading {
  font-weight: bold;
  margin-bottom: (5em/16);
}

.jquery-notific8-message {
  font-size: (14em/16);
}

.jquery-notific8-close {
  position: absolute;
  padding: 0 0.25em;
  cursor: pointer;
  top: 0;
  display: none;
}

.jquery-notific8-close-sticky {
  text-align: center;
  position: absolute;
  width: (89em/16);
  padding: (2em/16) 0;
  cursor: pointer;
  text-transform: uppercase;
  span {
    font-size: (10em/16);
  }
}

.right {
  .jquery-notific8-notification {
    float: right;
    clear: right;
    padding-right: (33em/16);
  }
  .jquery-notific8-close {
    right: -2px;
  }
  .jquery-notific8-close-sticky {
    top: (31em/16);
    right: (-38em/16);
    @include rotate(90deg);
  }
}

.left {
  .jquery-notific8-notification {
    float: left;
    clear: left;
    padding-left: (33em/16);
  }
  .jquery-notific8-close {
    left: -2px;
  }
  .jquery-notific8-close-sticky {
    top: (31em/16);
    left: (-38em/16);
    @include rotate(270deg);
  }
}

.top .jquery-notific8-notification {
  margin-top: (10em/16);
}

.bottom .jquery-notific8-notification {
  margin-bottom: (10em/16);
}
