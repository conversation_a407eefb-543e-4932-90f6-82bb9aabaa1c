{"name": "select2", "title": "Select2", "description": "Select2 is a jQuery based replacement for select boxes. It supports searching, remote data sets, and infinite scrolling of results.", "keywords": ["select", "autocomplete", "typeahead", "dropdown", "multiselect", "tag", "tagging"], "version": "3.4.6", "author": {"name": "<PERSON>", "url": "https://github.com/ivaynberg"}, "licenses": [{"type": "Apache", "url": "http://www.apache.org/licenses/LICENSE-2.0"}, {"type": "GPL v2", "url": "http://www.gnu.org/licenses/gpl-2.0.html"}], "bugs": "https://github.com/ivaynberg/select2/issues", "homepage": "http://ivaynberg.github.com/select2", "docs": "http://ivaynberg.github.com/select2/", "download": "https://github.com/ivaynberg/select2/tags", "dependencies": {"jquery": ">=1.7.1"}}