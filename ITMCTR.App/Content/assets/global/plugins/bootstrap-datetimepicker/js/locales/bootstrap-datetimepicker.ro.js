/**
 * Romanian translation for bootstrap-datetimepicker
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates['ro'] = {
		days: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
		daysShort: ["Du<PERSON>", "Lu<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Du<PERSON>"],
		daysMin: ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"],
		months: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "August", "Sept<PERSON>brie", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
		monthsShort: ["<PERSON>", "<PERSON>", "<PERSON>", "Apr", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Aug", "Sep", "Oct", "Nov", "Dec"],
		today: "<PERSON><PERSON><PERSON><PERSON>",
		suffix: [],
		meridiem: [],
		weekStart: 1
	};
}(jQ<PERSON>y));
