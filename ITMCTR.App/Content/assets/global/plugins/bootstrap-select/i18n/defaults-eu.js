/*
 * Translated default messages for bootstrap-select.
 * Locale: EU (Basque)
 * Region: 
 */
(function($) {
	$.fn.selectpicker.defaults = {
        style: 'btn-default',
        size: 'auto',
        title: null,
        selectedTextFormat : 'values',
        noneSelectedText : 'Hautapenik ez',
		noneResultsText : 'Emaitzarik ez',
		countSelectedText : '{1}(e)tik {0} hautatuta',
        maxOptionsText: ['Mugara iritsita ({n} {var} gehienez)', 'Taldearen mugara iritsita ({n} {var} gehienez)', ['elementu','elementu']],
        width: false,
        container: false,
        hideDisabled: false,
        showSubtext: false,
        showIcon: true,
        showContent: true,
        dropupAuto: true,
        header: false,
        liveSearch: false,
        multipleSeparator: ', ',
        iconBase: 'glyphicon',
        tickIcon: 'glyphicon-ok'
    };
}(jQuery));

