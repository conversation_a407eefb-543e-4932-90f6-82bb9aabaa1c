/*
 * Translated default messages for bootstrap-select.
 * Locale: PT (Portuguese; português)
 * Region: BR (Brazil; Brasil)
 * Author: <PERSON> <<EMAIL>>
 */
(function($) {
	$.fn.selectpicker.defaults = {
        style: 'btn-default',
        size: 'auto',
        title: null,
        selectedTextFormat : 'values',
        noneSelectedText : 'Nada selecionado',
		noneResultsText : 'Nada encontrado contendo',
		countSelectedText : 'Selecionado {0} de {1}',
        maxOptionsText: ['Limite excedido (máx. {n} {var})', 'Limite do grupo excedido (máx. {n} {var})', ['itens','item']],
        width: false,
        container: false,
        hideDisabled: false,
        showSubtext: false,
        showIcon: true,
        showContent: true,
        dropupAuto: true,
        header: false,
        liveSearch: false,
        actionsBox: false,
        multipleSeparator: ', ',
        iconBase: 'glyphicon',
        tickIcon: 'glyphicon-ok',
        maxOptions: false
    };
}(jQuery));
        