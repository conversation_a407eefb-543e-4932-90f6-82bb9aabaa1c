var FormValidation = function () {
    var handleFormClearAndFocus = function (form) {
        $(':input:not(.table :input)', form)
            .not(':button, :submit', form)
            .not(':button, :submit, :reset, :hidden,.no-clear-data')
            .removeAttr('checked')
            .removeAttr('selected')
            .not(':checkbox, :radio')
            .val('');
        $('[daterange] span').html('&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;');
        $('[autofocus]:first', form).focus();
    }
    var handleBootstrapMaxlength = function (form) {
        $('.form-group', form).find('textarea[maxlength],:text[maxlength]').maxlength({
            limitReachedClass: "label label-danger",
            alwaysShow: false
        });
    }
    var handleValidation = function () {
        var forms = $('form');
        $.each(forms, function () {
            var form = $(this);
            var closeTag = false;
            var closeOjbect = null;
            var redirect = "";
            if (form.data('init') == true)
                return;
            handleBootstrapMaxlength(form);
            var options = {
                dataType: "json",
                headers: {
                    "XSRF-TOKEN": $('input:hidden[name="__RequestVerificationToken"]').val()
                },
                beforeSubmit: function (formData, jqForm, options) {
                    redirect = "";
                    redirectTarget = "";
                    form.find(".has-error").removeClass("has-error");
                    //form.find(".help-block").remove();
                    //form.validate().resetForm();
                    if (form.data("validator").cancelSubmit !== true) {
                        if (!form.valid())
                            return false;
                    }
                    if (form.data('reject') == true) {
                        form.data('reject', false);
                        return false;
                    }
                    $.each(formData, function () {
                        if (this.type == 'submit' && $("[name='" + this.name + "']", form).attr('data-success-dismiss')) {
                            closeTag = true;
                        }
                        if (this.type == 'submit' && $("[name='" + this.name + "']", form).attr('data-success-redirect')) {
                            redirect = $("[name='" + this.name + "']", form).attr('data-success-redirect');
                            redirectTarget = $("[name='" + this.name + "']", form).attr('data-success-redirect-target');
                        }
                        if (this.type == 'submit' && $("[name='" + this.name + "']", form).attr('data-success-dismiss-object')) {
                            closeOjbect = $($("[name='" + this.name + "']", form).attr('data-success-dismiss-object'));
                        }
                    });
                    //$(':input,button,a,.btn', form).attr('disabled', 'disabled');
                    $(':submit', form).attr('disabled', 'disabled');
                    App.startPageLoading();
                    return true;
                },
                success: function (response, statusText) {
                    errorDialog.hide();
                    successDialog.hide();
                    response = eval(response);
                    $('[data-dismiss]', form).removeAttr('disabled');
                    //$(':input,button,a,.btn', form).removeAttr('disabled');
                    $(':submit', form).removeAttr('disabled');
                    var messageBody = $("div.row:has(.page-title) ~ div:first", form);
                    if (messageBody.length == 0)
                        messageBody = $('.form-body', form);
                    App.stopPageLoading();
                    if (response.clearform)
                        handleFormClearAndFocus(form);
                    if (response.success) {
                        if (FormValidation.onPostbackSuccess)
                            FormValidation.onPostbackSuccess();
                  
                        if (response.message != null && response.message.length > 0) {
                            App.alert({
                                type: 'success', icon: 'check', message: response.message, container: messageBody, place: 'prepend', focus: true, closeInSeconds: 5
                            });
                        }
                        if (closeTag) {
                            if (closeOjbect != null && closeOjbect.hasClass('in')) {
                                closeOjbect.modal('hide');
                            } else {
                                var modalObj = form.closest('.modal');
                                if (modalObj.length > 0 && modalObj.hasClass('in')) {
                                    modalObj.modal('hide');
                                }
                            }
                            window.location.href = window.location.href;
                        }

                        var set = $(':input', form);
                        //$.uniform.update(set);
                        if (response.callback != null && response.callback.length > 0)
                            window[response.callback](response.callparams);

                    }
                    else {
                        if (response.message) {
                            App.alert({
                                type: 'danger', icon: 'warning', message: response.message, container: messageBody, place: 'prepend', focus: true, closeInSeconds: 5
                            });
                        }
                    }
                },
                error: function (XmlHttpRequest, textStatus, errorThrown) {
                    $('[data-dismiss]', form).removeAttr('disabled');
                    $(':input,button,a,.btn', form).removeAttr('disabled');
                    App.stopPageLoading();
                    //����ӣ��е����������˫���Ž���ʧ��jQuery.parseJSON()
                    if (textStatus == "parsererror" && XmlHttpRequest.status == 200)
                        return;
                    App.alert({
                        type: 'danger', icon: 'warning', message: textStatus, container: form, place: 'prepend', focus: true, closeInSeconds: 5
                    });
                }
            };
            form.ajaxForm(options);
            form.data('init', true);
            form.data('reject', false);
            var errorDialog = $('.alert-danger', form);
            var successDialog = $('.alert-success', form);
            form.validate({
                errorElement: 'span', //default input error message container
                errorClass: 'help-block help-block-error', // default input error message class
                focusInvalid: false, // do not focus the last invalid input
                ignore: "", // validate all fields including form hidden input
                errorPlacement: function (error, element) { // render error placement for each input type
                    if (form.data("validator").cancelSubmit == true) return;
                    if (element.parent(".input-group").size() > 0) {
                        error.insertAfter(element.parent(".input-group"));
                    } else if (element.attr("data-error-container")) {
                        error.appendTo(element.attr("data-error-container"));
                    } else if (element.parents('.radio-list').size() > 0) {
                        error.appendTo(element.parents('.radio-list').attr("data-error-container"));
                    } else if (element.parents('.radio-inline').size() > 0) {
                        error.appendTo(element.parents('.radio-inline').attr("data-error-container"));
                    } else if (element.parents('.checkbox-list').size() > 0) {
                        error.appendTo(element.parents('.checkbox-list').attr("data-error-container"));
                    } else if (element.parents('.checkbox-inline').size() > 0) {
                        error.appendTo(element.parents('.checkbox-inline').attr("data-error-container"));
                    } else {
                        element.parent().append(error);
                        //error.insertAfter(element); // for other inputs, just perform default behavior
                    }
                },
                invalidHandler: function (event, validator) { //display error alert on form submit   
                    errorDialog.hide();
                    successDialog.hide();
                    App.scrollTo(errorDialog, -200);
                },
                highlight: function (element) { // hightlight error inputs
                    $(element)
                        .closest('.form-group').addClass('has-error');
                },
                unhighlight: function (element) { // revert the change done by hightlight
                    $(element)
                        .closest('.form-group').removeClass('has-error');
                },
                success: function (label) {
                    label.closest('.form-group').removeClass('has-error'); // set success class to the control group
                },
                submitHandler: function (form) {
                    //successDialog.show();
                    errorDialog.hide();
                }
            });
            $('.date-picker .form-control').change(function () {
                form.validate().element($(this)); //revalidate the chosen dropdown value and show error or success message for the input 
            })
            $('[data-clear]', form).click(function () {
                handleFormClearAndFocus(form);
            });
        });
    }
    return {
        init: function () {
            handleValidation();
        }
    };
}();