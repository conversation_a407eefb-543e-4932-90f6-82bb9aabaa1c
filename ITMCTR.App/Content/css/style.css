/* CSS Document */
.index_en1 {
    font-size: 16px;
    color: #333333;
}

    .index_en1 a {
        font-size: 16px;
        color: #333333;
    }

        .index_en1 a:hover {
            font-size: 16px;
            color: #6167fb;
        }
@media screen and(-ms-high-contrast:active),(-ms-high-contrast:none) {
    .validation-summary-errors {
        text-align: center;
        width: 400px;
    }
}

* {
    box-sizing: content-box;
    -moz-box-sizing: inherit;
    -webkit-box-sizing: inherit;
}

body {
    margin: 0;
    padding: 0;
}

    body#NNITCOMMasterBody {
        background: #fff;
        font: 13px "Microsoft YaHei","微软雅黑","宋体",Verdana,Helvetica,Arial,sans-serif;
    }

img {
    border: 0
}

a {
    text-decoration: none;
}

    a:hover {
        text-decoration: underline;
    }

.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.slh2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.slh1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}


.top {
    width: 100%;
    height: 602px;
    background: #6167fb;
    min-width: 1150px;
    border-radius: 0 0 0 250px;
    text-align: center;
    background-size: 100% 100%;
}

.top1 {
    width: 1150px;
    margin: 0px auto;
    padding: 30px 0;
    box-sizing: border-box;
    color: #FFF;
    font-size: 18px;
    line-height: 30px;
    font-weight: 700;
}

.top2 {
    display: flex;
    height: 54px;
    line-height: 54px;
}

.top3 {
    width: 30%;
    text-align: left;
}

.top4 {
    width: 70%;
    flex-direction: row-reverse;
    display: flex;
}

.top5 {
    padding: 10px 15px 0 10px;
    box-sizing: border-box;
}
.top5_a {
    padding: 0 15px 0 10px;
    box-sizing: border-box;
}

.top1 a {
    color: #FFF;
}

    .top1 a:hover {
        color: #FFF;
        text-decoration: underline;
    }

.dh {
    width: 1150px;
    display: flex;
    height: 54px;
    line-height: 54px;
}


.index_en {
    font-size: 16px;
}

.index_en a {
        font-size: 16px;
        color: #ffffff;
    }

.index_en a:hover {
            font-size: 16px;
            color: #fcff00;
        }
.dh_left {
    width: 30%;
    text-align: left;
}
.dh_right {
    flex-direction:row-reverse;
    width: 70%;
    display:flex;
}
.dh_right_aa{padding:10px;}
.dh_right_a a {
    color: #FFF;
}

    .dh_right_a a:hover {
        color: #FFF;
    }
	

.dh_left_ny {
    width: 60%;
	text-align:left;
}
.dh_right_ny {
    text-align: right;
    width: 40%;
    display:flex;
}



.top_but1 {
    width: 260px;
    height: 70px;
    line-height: 70px;
    text-align: center;
    text-align: center;
    font-size: 24px;
    color: #FFF;
    background: none;
    border: 2px #FFFFFF solid;
    font-weight: 700;
    margin: 0 30px;
}

.top_but2 {
    width: 260px;
    height: 70px;
    line-height: 70px;
    text-align: center;
    text-align: center;
    font-size: 24px;
    color: #6167fb;
    background: #FFF;
    border: 0;
    font-weight: 700;
    margin: 0 30px;
}

.nr_center {
    width: 1100px;
    margin: 0px auto;
}

.nr_center1 {
    background: #eff0ff;
    height: 300px;
    display: flex;
    padding: 60px;
    box-sizing: border-box;
    margin: 100px 0;
}

.nr_center2 {
    font-size: 18px;
    color: #333333;
    height: 160px;
    width: 500px;
    word-wrap: break-word;
    font-weight: 700;
    line-height: 45px;
    padding-top: 20px;
    box-sizing: border-box;
    position: absolute;
}

.nr_center3 {
    width: 466px;
    height: 387px;
    border-radius: 10px;
    box-shadow: 0 0 10px #9296fb;
    padding: 10px 20px;
    box-sizing: border-box;
    border: 3px #6167fb solid;
    position: relative;
    top: -110px;
    right: 50px;
    left: 500px;
    background: #FFF;
}

.nr_center3_new {
    width: 100%;
    padding: 10px 15px 10px 20px;
    box-sizing: border-box;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
    box-shadow: 0 0 10px #eeeeee;
    background: #FFF;
    line-height: 25px;
    margin: 15px 0;
}

    .nr_center3_new a {
        color: #333333;
    }

        .nr_center3_new a:hover {
            color: #333333;
            text-decoration: underline;
        }

.lj {
    width: 1100px;
    margin: 0px auto;
    display: flex;
}

    .lj li {
        width: 275px;
        text-align: center;
        margin: 80px 0;
        list-style: none;
    }

.footer {
    width: 100%;
    background: #f3f3f3;
    padding: 20px 0;
    min-width: 1150px;
}

.footer1 {
    width: 1100px;
    margin: 0px auto;
    display: flex;
}

.footer2 {
    padding: 20px 0 60px 0;
    padding-left: 80px;
    padding-right: 160px;
}

.footer3 {
    width: 34px;
    height: 4px;
    background: #6167fb;
}

.footer4 {
    font-size: 17px;
    font-weight: 700;
    color: #333333;
    padding: 20px 0;
}

.footer5 {
    font-size: 14px;
    color: #333333;
    line-height: 30px;
}

    .footer5 a {
        color: #333333;
    }

        .footer5 a:hover {
            color: #333333;
            text-decoration: underline;
        }

.footer6 {
    width: 1100px;
    height: 80px;
    line-height: 80px;
    text-align: center;
    color: #333333;
    font-size: 14px;
    margin: 0px auto;
}


/* 设置下拉按钮的样式 */
.dropbtn {
    background-color: #6167fb;
    color: white;
    padding: 20px 16px 0 16px;
    font-weight: bold;
    font-size: 16px;
    border: none;
    cursor: pointer;
}

/* 容器 <div> - 需要放置下拉内容 */
.dropdown {
    position: relative;
    display: inline-block;
}

/* 下拉内容（默认隐藏） */
.dropdown-content {
    display: none;
    position: absolute;
    text-align: center;
    min-width: 90px;
    box-shadow: 0px 0px 0px 0px rgba(0,0,0,0.2);
    z-index: 1;
}

    /* 下拉链接 */
    .dropdown-content a {
        color: black;
        text-decoration: none;
        display: block;
        color: #FFF;
        height: 40px;
    }


/* 悬停时显示下拉菜单 */
.dropdown:hover .dropdown-content {
    display: block;
}

.login_cw ul {
    padding-inline-start: 0px;
    margin-inline-end: 0px;
    margin-block-end: 0px;
    margin-block-start: 0px;
    list-style-type: none;
}

.login_cw {
    width: 410px;
    height: 30px;
    line-height: 30px;
    background-color: rgba(0,0,0,0.1);
    color: #ff0000;
    font-size: 14px;
    font-weight: 700;
    position: absolute;
    top: 400px;
    text-align: center;
}

.login_bg {
    height: 330px;
    width: 100%;
    min-width: 1150px;
    background: #6167fb;
    margin-top: 13%;
}

.login_box {
    width: 1150px;
    margin: 0px auto;
    display: flex;
}

.login_box1 {
    width: 684px;
    padding-top: 70px;
    box-sizing: border-box;
}

.login_box2 {
    width: 466px;
    position: relative;
    height: 450px;
    border: 4px #6167fb solid;
    background: #FFF;
    top: -60px;
    border-radius: 15px;
    color: #333333;
    padding: 25px;
    box-sizing: border-box;
}

.login_but {
    border: 1px #dcdcdc solid;
    width: 100%;
    height: 52px;
    line-height: 52px;
    margin: 20px 0;
    display: flex;
}

.login_but1 {
    padding: 5px 20px 0 10px;
}

.login_but2 {
    width: 340px;
    height: 45px;
    line-height: 45px;
    border: 1px;
    color: #6f6e6e;
    border: 1px;
}

.login_but3 {
    width: 100%;
    height: 52px;
    line-height: 52px;
    background: #6167fb;
    border: 0;
    color: #FFF;
    font-weight: 700;
    font-size: 20px;
    margin-top: 10px;
}

.login_but4 {
    display: flex;
    font-size: 12px;
    color: #6167fb;
    height: 50px;
    line-height: 50px;
}

    .login_but4 a {
        font-size: 12px;
        color: #6167fb;
    }

        .login_but4 a:hover {
            font-size: 12px;
            color: #6167fb;
            text-decoration: underline;
        }

.login_footer {
    font-size: 14px;
    color: #333333;
    width: 100%;
    line-height: 50px;
    text-align: center;
    margin-top: 80px;
}

.login_footer1 {
    font-size: 14px;
    color: #333333;
    width: 100%;
    line-height: 50px;
    text-align: center;
    margin: 50px 0;
}

.zhuce_top {
    width: 100%;
    min-width: 1150px;
    background: #6167fb;
    height: 160px;
    color: #FFF;
    font-size: 18px;
    line-height: 30px;
    font-weight: 700;
}

    .zhuce_top a {
        color: #FFF;
        font-size: 18px;
        font-weight: 700;
    }

        .zhuce_top a:hover {
            color: #FFF;
            font-size: 18px;
            font-weight: 700;
            text-decoration: underline;
        }

.zhuce01 {
    width: 50%;
    padding-top: 30px;
    box-sizing: border-box;
    font-size: 16px;
}

.zhuce02 {
    padding-left: 290px;
    box-sizing: border-box;
}


.zhuce_top4 {
    width: 100%;
    display: flex;
}

.zhuce_top5 {
    box-sizing: border-box;
}

.zhuce_nr {
    width: 1150px;
    margin: 0px auto;
}

.zhuce_nr_title2 {
    width: 1150px;
    color: #333333;
    font-size: 14px;
}

.zhuce_nr_title {
    width: 1150px;
    height: 80px;
    line-height: 80px;
    color: #333333;
    font-size: 14px;
}

.zhuce_nr_title1 a {
    color: #6167fb;
}

    .zhuce_nr_title1 a:hover {
        color: #6167fb;
        text-decoration: underline;
    }

.zhuce_content {
    width: 100%;
    border: 1px #dcdcdc solid;
    padding: 30px;
    box-sizing: border-box;
    font-size: 14px;
    color: #333333;
}

.zhuce_content1 {
    width: 100%;
    height: 45px;
    line-height: 45px;
    display: flex;
    margin: 20px 0;
}

.zhuce_content1_left {
    width: 15%;
    text-align: right;
}

.zhuce_content1_right {
    width: 85%;
    text-align: left;
    padding-left: 20px;
    box-sizing: border-box;
}

    .zhuce_content1_right input {
        width: 363px;
        height: 45px;
        line-height: 40px;
        background: #eeeeee;
        padding-left: 10px;
        box-sizing: border-box;
        color: #676565;
    }

.zhuce_content2 {
    width: 100%;
}

.zhuce_content2_left {
    height: 45px;
    line-height: 45px;
    width: 100%;
    text-align: left;
}

.zhuce_content2_right {
    height: 45px;
    line-height: 45px;
    width: 100%;
    text-align: left;
}

    .zhuce_content2_right input {
        width: 500px;
        height: 45px;
        line-height: 40px;
        background: #eeeeee;
        border: 0;
        padding-left: 10px;
        box-sizing: border-box;
        color: #c1c1c1;
    }

.zhuce_content_but_aa {
    width: 260px;
    height: 60px;
    line-height: 60px;
    color: #6167fb;
    font-size: 20px;
    font-weight: 700;
    border: 0;
    background: #fff;
    border: 2px #6167fb solid;
}

.zhuce_content_but {
    width: 260px;
    height: 60px;
    line-height: 60px;
    color: #FFF;
    font-size: 20px;
    font-weight: 700;
    border: 0;
    background: #6167fb;
}

.jiansuo_content {
    width: 100%;
    background: #f5f5f5;
    padding: 10px 10px;
    box-sizing: border-box;
    font-size: 16px;
    color: #333333;
    font-weight: 700;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
}

.jiansuo_content_cx {
    width: 356px;
    height: 45px;
    line-height: 45px;
    margin: 5px 10px;
}



.jiansuo_content_input {
    width: 356px;
    height: 45px;
    line-height: 45px;
    border: 1px #e8e8e8 solid;
    background: #FFF;
    border-radius: 5px;
    font-size: 14px;
    color: #bbbbbb;
    font-weight: 700;
    padding-left: 20px;
    box-sizing: border-box;
}

.jiansuo_content1 {
    width: 100%;
    background: #ffffff;
    font-size: 16px;
    color: #333333;
    font-weight: 700;
    margin-bottom: 20px;
}

.jiansuo_center3_new {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    font-size: 18px;
    color: #333333;
    box-shadow: 0 0 10px #eeeeee;
    background: #FFF;
    line-height: 35px;
    margin: 25px 0;
}

    .jiansuo_center3_new a {
        color: #333333;
    }

        .jiansuo_center3_new a:hover {
            color: #333333;
            text-decoration: underline;
        }

.jiansuo_center3_new1 {
    color: #777777;
    font-size: 14px;
    display: flex;
    font-weight: normal;
}

.jiansuo_center3_new2 {
    width: 85%;
    text-align: left;
}

.jiansuo_center3_new3 {
    width: 15%;
    text-align: right;
}

.jiansuo_fl {
    width: 1150px;
    margin: 30px 0;
    text-align: center;
}


ul.pagination {
    display: inline-block;
    list-style-type: none;
    /*padding: 0;*/
    /*margin: 0;*/
}
    /*设置为内联标签 然后就变为横排了*/
    ul.pagination li {
        display: inline;
    }

        ul.pagination li a {
            color: blue;
            /*float: left;*/
            padding: 8px 16px;
            border-radius: 10px;
            text-decoration: none;
            color: #314fc4;
        }

    ul.pagination li {
        margin-right: 1em;
    }

    ul.pagination a:hover, ul.pagination a:focus {
        color: white;
        background-color: #4a71ff;
    }

.zhuce_tz {
    width: 80%;
    text-align: center;
    margin: 0px auto;
    padding: 40px 0 0 0;
}

.zhuce_tz1 {
    line-height: 80px;
    font-size: 20px;
    color: #333;
    font-weight: 700;
}

.zhuce_tz11 {
    line-height: 80px;
    font-size: 20px;
    color: #ff0000;
    font-weight: 700;
}

.zhuce_tz2 {
    line-height: 60px;
    line-height: 35px;
    font-size: 16px;
    color: #777676;
}

.zhuce_tz3 {
    width: 600px;
    margin: 0px auto;
    padding: 50px 0;
    display: flex;
}

.new_xq {
    width: 1150px;
    margin: 0px auto;
    padding: 20px;
    box-sizing: border-box;
}

.new_xq1 {
    font-size: 30px;
    font-weight: bold;
    color: #323232;
    line-height: 40px;
    text-align: center;
}

.new_xq2 {
    font-size: 14px;
    color: #323232;
    text-align: center;
    line-height: 25px;
    height: 70px;
    display:flex;
    align-items:center;
    border-bottom: 1px #dcdcdc solid;
}

.new_xq3 {
    font-size: 14px;
    color: #323232;
    text-align: left;
    line-height: 30px;
    padding: 30px 0;
}

.jiansuo_content2 {
    width: 100%;
    background: #ffffff;
    font-size: 14px;
    color: #333333;
    margin-bottom: 20px;
    border: 1px #dcdcdc solid;
    margin-top: 20px;
}

.jiansuo_content2_title {
    width: 100%;
    height: 45px;
    line-height: 45px;
    background: #dfe4fa;
    display: flex;
    text-align: left;
}

.jiansuo_content2_title1 {
    width: 10%;
    padding: 0 10px;
    box-sizing: border-box;
}

.jiansuo_content2_title2 {
    width: 15%;
    padding: 0 5px;
    box-sizing: border-box;
}

.jiansuo_content2_title3 {
    width: 55%;
    padding: 0 5px;
    box-sizing: border-box;
}

.jiansuo_content2_title4 {
    width: 10%;
    padding: 0 5px;
    box-sizing: border-box;
}

.jiansuo_content2_title5 {
    width: 10%;
    padding: 0 5px;
    box-sizing: border-box;
}

.jiansuo_content2_title_a {
    width: 100%;
    min-height: 45px;
    line-height: 25px;
    padding: 10px 0;
    box-sizing: border-box;
    display: flex;
    text-align: left;
}

.jiansuo_content2_title_b {
    width: 100%;
    min-height: 45px;
    line-height: 25px;
    padding: 10px 0;
    box-sizing: border-box;
    display: flex;
    text-align: left;
    background: #f5f5f5;
}

.jiansuo01_content_cx {
    width: 356px;
    height: 45px;
    line-height: 45px;
    margin: 5px 10px;
    border: 1px #e8e8e8 solid;
    background: #FFF;
    color: #4c4c4c;
    font-size: 15px;
    border-radius: 5px;
    padding-left: 15px;
    box-sizing: border-box;
}

.jiansuo01_content_input {
    width: 250px;
    min-height: 25px;
    border: 0px;
    font-size: 14px;
    color: #bbbbbb;
    font-weight: 700;
    margin-left: 10px;
}

.jiansuo01_content_input1 {
    width: 180px;
    min-height: 25px;
    border: 0px;
    font-size: 14px;
    color: #bbbbbb;
    font-weight: 700;
    margin-left: 10px;
}

.jiansuo01_content_but {
    width: 160px;
    height: 42px;
    line-height: 42px;
    color: #4a71ff;
    font-size: 16px;
    font-weight: 700;
    border: 1px #4a71ff solid;
    background: #f5f5f5;
    border-radius: 5px;
}
