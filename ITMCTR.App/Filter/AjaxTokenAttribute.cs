using System;
using System.Linq;
using System.Net;
using System.Web.Helpers;
using System.Web.Mvc;

namespace ITMCTR.App.Filter
{

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class AjaxTokenAttribute : AuthorizeAttribute
    {
        public override void OnAuthorization(AuthorizationContext filterContext)
        {
            var request = filterContext.HttpContext.Request;
            if (request.HttpMethod == WebRequestMethods.Http.Post)
            {
                if (request.IsAjaxRequest())
                {
                    var antiForgeryCookie = request.Cookies[AntiForgeryConfig.CookieName];
                    var cookieValue = antiForgeryCookie != null ? antiForgeryCookie.Value : null;
                    string formToken = "";
                    if (request.Headers.AllKeys.Contains("XSRF-TOKEN"))
                        formToken = request.Headers["XSRF-TOKEN"];
                    AntiForgery.Validate(cookieValue, formToken);
                }
            }
        }
    }
}