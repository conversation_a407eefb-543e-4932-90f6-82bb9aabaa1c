using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Web;
using System.Web.Mvc;

namespace ITMCTR.App.Filter
{

    public class LocalizationAttribute : ActionFilterAttribute
    {
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            bool isSkipLocalization = filterContext.ActionDescriptor.IsDefined(typeof(WithoutLocalizationAttribute), inherit: true) || filterContext.ActionDescriptor.ControllerDescriptor.IsDefined(typeof(WithoutLocalizationAttribute), inherit: true);

            if (!isSkipLocalization)
            {
                if (filterContext.RouteData.Values["lang"] != null && !string.IsNullOrWhiteSpace(filterContext.RouteData.Values["lang"].ToString()))
                {
                    ///从路由数据(url)里设置语言
                    var lang = filterContext.RouteData.Values["lang"].ToString();
                    Thread.CurrentThread.CurrentUICulture = CultureInfo.CreateSpecificCulture(lang);
                }
                else
                {
                    ///从cookie里读取语言设置
                    var cookie = filterContext.HttpContext.Request.Cookies["Localization.CurrentUICulture"];
                    var langHeader = string.Empty;
                    if (cookie != null && cookie.Value != "")
                    {
                        ///根据cookie设置语言
                        langHeader = cookie.Value;
                        Thread.CurrentThread.CurrentUICulture = CultureInfo.CreateSpecificCulture(langHeader);
                    }
                    else
                    {
                        var userLanguages = filterContext.HttpContext.Request.UserLanguages;
                        CultureInfo ci;
                        if (userLanguages != null && userLanguages.Count() > 0)
                        {
                            try
                            {
                                ci = new CultureInfo(userLanguages[0]);
                            }
                            catch (CultureNotFoundException)
                            {
                                ci = CultureInfo.InvariantCulture;
                            }
                        }
                        else
                        {
                            ci = new CultureInfo("zh-CN");
                        }
                        Thread.CurrentThread.CurrentUICulture = ci;
                    }
                    ///把语言值设置到路由值里
                    filterContext.RouteData.Values["lang"] = langHeader;
                }

                /// 把设置保存进cookie
                HttpCookie _cookie = new HttpCookie("Localization.CurrentUICulture", Thread.CurrentThread.CurrentUICulture.Name);
                _cookie.Expires = DateTime.Now.AddYears(1);
                filterContext.HttpContext.Response.SetCookie(_cookie);

                base.OnActionExecuting(filterContext);
            }

        }
    }

    public class WithoutLocalizationAttribute : Attribute
    {
    }
}