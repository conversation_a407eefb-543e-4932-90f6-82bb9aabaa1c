using System.Web.Mvc;

namespace ITMCTR.App.Filter
{
    public class LogExceptionAttribute : HandleErrorAttribute
    {
        public override void OnException(ExceptionContext filterContext)
        {
            if (!filterContext.ExceptionHandled)
            {
                string errorMsg = $"请求：[{filterContext.RequestContext.HttpContext.Request.RawUrl}] 异常";
                LoggerHelper.Error(errorMsg, filterContext.Exception);
            }
            if (filterContext.Result is JsonResult)
            {
                //当结果为json时，设置异常已处理
                filterContext.ExceptionHandled = true;
            }
            else
            {
                //否则调用原始设置
                base.OnException(filterContext);
            }
        }
    }
}