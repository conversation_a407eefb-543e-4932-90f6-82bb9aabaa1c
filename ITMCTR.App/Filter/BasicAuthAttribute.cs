using ITMCTR.App.Models;
using System;
using System.Web.Mvc;
using System.Web.Mvc.Filters;

namespace ITMCTR.App.Filter
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = false)]
    public class UserAuth : FilterAttribute, IAuthenticationFilter, IActionFilter
    {
        public bool Validate { get; set; } = true;
        public string LoginUrl { get; set; } = string.Empty;
        public UserAuth() : base()
        {

        }
        public void OnAuthentication(AuthenticationContext filterContext)
        {
            if (!Validate) return;
#if DEBUG
            //var user_id = new Guid("b3ebeecb-a308-43c4-b66b-03185e8d9d40");
            //Models.UserProfile.CurrentProfile.UserID = user_id;
            //Models.UserProfile.CurrentProfile.InitUser(user_id);
            //Models.UserProfile.CurrentProfile.SaveProfile();
#endif
            //未登录验证
            if (CurrentProfile == null || CurrentProfile.IsLogin == false)
            {
                var CurrentUrl = filterContext.HttpContext.Request.RawUrl;//获取当前所在页面的URL
                var UrlHelper = new UrlHelper(filterContext.RequestContext);
                var url = LoginUrl + "?ReturnUrl=" + UrlHelper.Encode(CurrentUrl);
                if (string.IsNullOrEmpty(LoginUrl))
                {
                    url = UrlHelper.Action("Login", "User", new { ReturnUrl = CurrentUrl });
                }
                filterContext.Result = new RedirectResult(url);
                return;
            }
        }
        public void OnAuthenticationChallenge(AuthenticationChallengeContext filterContext)
        {

        }
        public void OnActionExecuting(ActionExecutingContext filterContext)
        {
            filterContext.Controller.ViewBag.Url = filterContext.HttpContext.Request.RawUrl;
            filterContext.Controller.ViewBag.UserInfo = CurrentProfile;
        }
        public void OnActionExecuted(ActionExecutedContext filterContext)
        {

        }
        public UserProfile CurrentProfile
        {
            get
            {
                return UserProfile.CurrentProfile;
            }
        }
    }
}