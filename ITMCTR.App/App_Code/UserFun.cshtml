@using ITMCTR.Core;
@using ITMCTR.App.Models;
@functions {
    public static bool IsEdit(UserProjectViewModel info)
    {
        if (info.status == (int)ProjectStatus.未通过审核 && info.taskstatus == (int)ProjectTaskStatus.复核未通过
            || info.status == (int)ProjectStatus.未填完 && (info.taskstatus == (int)ProjectTaskStatus.无 || info.taskstatus == null))
            return true;
        return false;
    }
    public static bool IsReEdit(UserProjectViewModel info)
    {
        if (info.status == (int)ProjectStatus.未填完 && info.taskstatus == (int)ProjectTaskStatus.申请通过 ||
            (info.status == (int)ProjectStatus.未填完 && info.taskstatus == (int)ProjectTaskStatus.复核未通过))
            return true;
        return false;
    }
    public static bool IsRequestEdit(UserProjectViewModel info)
    {
        if (info.status == (int)ProjectStatus.通过审核 &&
            (info.taskstatus == (int)ProjectTaskStatus.复核通过 ||
             info.taskstatus == (int)ProjectTaskStatus.申请未通过))
        {
            if (
                (info.status == (int)ProjectStatus.通过审核 && info.taskstatus == (int)ProjectTaskStatus.三审最终审核)){

                return false;
            }
            return true;
        }
        return false;
    }

    public static bool IsEdit(UserProjectModel info)
    {
        if (info.Status == (int)ProjectStatus.未通过审核 && info.TaskStatus == (int)ProjectTaskStatus.复核未通过
            || info.Status == (int)ProjectStatus.未填完 && (info.TaskStatus == (int)ProjectTaskStatus.无 || info.TaskStatus == null))
            return true;
        return false;
    }
    public static bool IsReEdit(UserProjectModel info)
    {
        if (info.Status == (int)ProjectStatus.未填完 && info.TaskStatus == (int)ProjectTaskStatus.申请通过 ||
            (info.Status == (int)ProjectStatus.未填完 && info.TaskStatus == (int)ProjectTaskStatus.复核未通过))
            return true;
        return false;
    }
    public static bool IsRequestEdit(UserProjectModel info)
    {
        if (info.Status == (int)ProjectStatus.通过审核 &&
            (info.TaskStatus == (int)ProjectTaskStatus.复核通过 ||
             info.TaskStatus == (int)ProjectTaskStatus.申请未通过))
        {
            return true;
        }
        return false;
    }

    public static string GetStatus(ITMCTR.Core.Models.V_NewProject info)
    {
        if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.待分配 && info.isTraditionalMedicine==null)
        {
            return "待判断";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.待分配 && info.isTraditionalMedicine==1)
        {
            return "待分配";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.待分配 && info.isTraditionalMedicine==0)
        {
            return "非传统医学";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.已分配)
        {
            return "待审核";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.待复核)
        {
            return "待复核";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.复核通过)
        {
            return "待发号";
        }
        else if (info.status == (int)ProjectStatus.通过审核 && info.taskStatus == (int)ProjectTaskStatus.复核通过)
        {
            return "已发号";
        }
        else if (info.status == (int)ProjectStatus.未通过审核 && info.taskStatus == (int)ProjectTaskStatus.复核未通过)
        {
            return "未通过审核";
        }
        else if (info.status == (int)ProjectStatus.通过审核 && info.taskStatus == (int)ProjectTaskStatus.再修改申请)
        {
            return "再修改申请";
        }
        else if (info.status == (int)ProjectStatus.未填完 && info.taskStatus == (int)ProjectTaskStatus.申请通过)
        {
            return "再修改待提交";
        }
        else if (info.status == (int)ProjectStatus.通过审核 && info.taskStatus == (int)ProjectTaskStatus.申请未通过)
        {
            return "再修改未通过";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.三审最终审核)
        {
            return "待审核";
        }
        return "";
    }

    public static string GetCurrentUser(ITMCTR.Core.Models.V_NewProject info)
    {
        if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.待分配 && info.isTraditionalMedicine == null)
        {
            return "所有一审";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.待分配 && info.isTraditionalMedicine==1)
        {
            return info.sendTaskUser;
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.待分配 && info.isTraditionalMedicine==0)
        {
            return "前台用户";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.已分配)
        {
            return info.executeTaskUser;
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.待复核)
        {
            return info.sendTaskUser;
        }
        else  if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.复核通过)
        {
            return info.firstTaskUser;
        }
        else if (info.status == (int)ProjectStatus.通过审核 && info.taskStatus == (int)ProjectTaskStatus.复核通过)
        {
            return "前台用户";
        }
        else if (info.status == (int)ProjectStatus.未通过审核 && info.taskStatus == (int)ProjectTaskStatus.复核未通过)
        {
            return "前台用户";
        }
        else if (info.status == (int)ProjectStatus.通过审核 && info.taskStatus == (int)ProjectTaskStatus.再修改申请)
        {
            return info.firstTaskUser;
        }
        else if (info.status == (int)ProjectStatus.未填完 && info.taskStatus == (int)ProjectTaskStatus.申请通过)
        {
            return "前台用户";
        }
        else if (info.status == (int)ProjectStatus.通过审核 && info.taskStatus == (int)ProjectTaskStatus.申请未通过)
        {
            return "前台用户";
        }
        else if (info.status == (int)ProjectStatus.待审核 && info.taskStatus == (int)ProjectTaskStatus.三审最终审核)
        {
            return info.firstTaskUser;
        }
        return "";
    }
}
}