@using System.Globalization;
@using System.Collections;
@functions {
    public static bool IsLang(string lang)
    {
        return CultureInfo.CurrentUICulture.Name.Equals(lang);
    }
    public static string LangFormat(params string[] arys)
    {
        for (var i = 0; i < arys.Length; i = i + 2)
        {
            if (i + 1 < arys.Length && CultureInfo.CurrentUICulture.Name.Equals(arys[i]))
            {
                return arys[i + 1];
            }
        }
        return "";
    }
    public static string Lang(params string[] arys)
    {
        var LangDefs = new List<string>() { "zh-CN", "en-US" };
        for (int i = 0; i < LangDefs.Count; i++)
        {
            if (CultureInfo.CurrentUICulture.Name.Equals(LangDefs[i]) && i < arys.Length)
            {
                return arys[i];
            }
        }
        return "";
    }
    public static string AppVersion { get { return "v20230825"; } }
    public static string Applang { get { return CultureInfo.CurrentUICulture.Name; } }
}